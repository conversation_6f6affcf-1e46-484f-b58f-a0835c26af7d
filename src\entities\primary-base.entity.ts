import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Exclude } from 'class-transformer';
import { IsUUID } from 'class-validator';
import {
  Column,
  CreateDateColumn,
  getRepository,
  Index,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
  VersionColumn,
} from 'typeorm';

export class PrimaryBaseEntity {
  @ApiProperty()
  @PrimaryGeneratedColumn('uuid')
  @IsUUID(4)
  id?: string;

  @Index()
  @ApiProperty()
  @CreateDateColumn({ type: 'timestamptz', nullable: true })
  createdDate?: Date;

  @Index()
  @ApiPropertyOptional()
  @UpdateDateColumn({ type: 'timestamptz', nullable: true })
  updatedDate?: Date;

  @Index()
  @ApiPropertyOptional()
  @Column({ type: 'uuid', nullable: true })
  createdBy?: string | null;

  @Index()
  @ApiPropertyOptional()
  @Column({ type: 'uuid', nullable: true })
  updatedBy?: string | null;

  @Index()
  @ApiPropertyOptional()
  @Column({ type: 'varchar', nullable: true })
  createdByUser?: string | null;

  @Index()
  @ApiPropertyOptional()
  @Column({ type: 'varchar', nullable: true })
  updatedByUser?: string | null;

  @Exclude()
  @VersionColumn({ default: 0 })
  version?: number;

  /** Tên bảng trong database */
  static get tableName(): string {
    return getRepository(this).metadata.tableName;
  }
}
