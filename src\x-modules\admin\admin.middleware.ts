import {
  BadRequestException,
  Injectable,
  NestMiddleware,
  UnauthorizedException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { Request, Response } from 'express';
import { KeySessionContext } from '~/common/constants/KeySessionContext';
import { configEnv } from '~/@config/env';
import { KeyHeader } from '~/common/constants/KeyHeader';
import { RequestContext } from '~/@core/context';
import { AuthService } from './services/auth.service';
import { MemberSessionDto } from '~/dto/auth.dto';
import { BindRepo } from '~/@core/decorator';
import { UserRepo } from '~/repositories/primary';
import { SystemValue } from '~/common/constants/SystemValue';
@Injectable()
export class AdminMiddleware implements NestMiddleware {
  constructor(private authService: AuthService) {}

  @BindRepo(UserRepo)
  private userRepo: UserRepo;
  async use1(req: Request, res: Response, next: Function) {
    // console.log('--------AdminMiddleware-----------');
    try {
      // TODO VALIDATE TOKEN OR API KEY
      next();
    } catch (error) {
      next(new UnauthorizedException('Unauthorized'));
    }
  }

  async use(req: Request, res: Response, next: Function) {
    try {
      const { headers = {} } = req;
      if (!headers || !headers[KeyHeader.AUTHORIZATION]) {
        throw new UnauthorizedException('Unauthorized');
      }

      const checkObject = (obj: any): boolean => {
        if (!obj || typeof obj !== 'object') return false;

        for (const value of Object.values(obj)) {
          if (typeof value === 'string' && SystemValue.FORBIDDEN_PATTERN.test(value)) {
            return true;
          } else if (typeof value === 'object') {
            if (checkObject(value)) return true; // đệ quy nếu nested
          }
        }

        return false;
      };

      if (checkObject(req.query) || checkObject(req.body)) {
        throw new BadRequestException('SQL Injection detected!');
      }

      const accessTokenBearer = headers[KeyHeader.AUTHORIZATION] as string;
      const accessToken = accessTokenBearer.replace('Bearer', '').trim();

      if (!accessToken) {
        throw new UnauthorizedException('Unauthorized');
      }

      // Validate Microsoft SSO Token
      const validationResult = await this.authService.validateMsToken(accessToken);

      if (!validationResult.isValid) {
        console.error('Microsoft token validation failed:', validationResult.error);
        throw new UnauthorizedException('Invalid Microsoft token');
      }

      const payload = validationResult.payload;

      const user = await this.userRepo.findOne({ where: { email: payload.preferred_username } });
      if (!user) {
        console.error('User not found:', payload.preferred_username);
        throw new UnauthorizedException('User not found');
      }

      // Lưu thông tin session vào context để dùng sau
      const session: MemberSessionDto = {
        ...payload,
        ...user,
        accessToken,
        refreshToken: '', // nếu bạn không dùng refreshToken thì có thể bỏ field này trong DTO
        tokenType: 'Bearer',
      };

      RequestContext.setAttribute<MemberSessionDto>(KeySessionContext.ADMIN_SESSION, session);
      next();
    } catch (error) {
      console.error('Auth Middleware Error:', error);
      next(new UnauthorizedException('Unauthorized'));
    }
  }
}
