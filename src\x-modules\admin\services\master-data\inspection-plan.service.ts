import { BadRequestException, Injectable } from '@nestjs/common';
import { Between, FindManyOptions, ILike, In, IsNull, Like, Not, Raw } from 'typeorm';
import { v4 as uuidv4 } from 'uuid';
import { BindRepo } from '~/@core/decorator';
import { BusinessException } from '~/@systems/exceptions';
import { convertToDisplayTimezone } from '~/@systems/utils';
import { NSItem } from '~/common/enums/NSItem';
import {
  InspectionPageReq,
  InspectionPlanCreateDto,
  InspectionPlanUpdateDto,
} from '~/dto/inspection-plan.dto';
import {
  GeneralDataEntity,
  InspectionPlanEntity,
  ItemEntity,
  MachineEntity,
  OrganizationUnitEntity,
} from '~/entities/primary';
import { InspectionPlanRepo, ItemRepo, MachineRepo } from '~/repositories/primary';
import { GeneralDataDetailRepo, GeneralDataRepo } from '~/repositories/primary/general-data.repo';
import { OrganizationUnitRepo } from '~/repositories/primary/organization-unit.repo';
@Injectable()
export class InspectionPlanService {
  constructor() {}

  @BindRepo(InspectionPlanRepo)
  private repo: InspectionPlanRepo;

  @BindRepo(ItemRepo)
  private itemRepo: ItemRepo;

  @BindRepo(MachineRepo)
  private machineRepo: MachineRepo;

  @BindRepo(OrganizationUnitRepo)
  private organizationUnitRepo: OrganizationUnitRepo;

  @BindRepo(GeneralDataRepo)
  private generalDataRepo: GeneralDataRepo;

  @BindRepo(GeneralDataDetailRepo)
  private generalDataDetailRepo: GeneralDataDetailRepo;

  async create(body: InspectionPlanCreateDto) {
    // if (body.itemId) {
    const foundItem = await this.itemRepo.findOne({ where: { id: body.itemId } });
    if (!foundItem) throw new BusinessException('Item not found');

    // const checkPlanTaken = await this.checkTakenItem({
    //   itemId: body.itemId,
    //   organizationUnitId: body.organizationUnitId,
    //   dataType: body.dataType,
    // });
    // if (!checkPlanTaken) throw new BusinessException('Inspection Plan for Item exist');
    // }
    let foundUnit: any = {};
    if (body.organizationUnitId) {
      foundUnit = await this.organizationUnitRepo.findOne({
        where: { id: body.organizationUnitId },
      });
      if (!foundUnit) throw new BusinessException('Organization unit not found');
    }
    let foundMachine: any = {};
    if (body.machineId) {
      foundMachine = await this.machineRepo.findOne({
        where: { id: body.machineId },
      });
      if (!foundMachine) throw new BusinessException('Machine not found');
    }
    const newInspectionPlan = this.repo.create(body);
    // newInspectionPlan.createdBy = 'admin';
    newInspectionPlan.createdDate = new Date();
    newInspectionPlan.updatedDate = new Date();
    newInspectionPlan.id = uuidv4();
    newInspectionPlan.code = await this.genCode(foundItem);
    await this.repo.insert(newInspectionPlan);
    return { message: 'create success' };
  }

  async update(body: InspectionPlanUpdateDto) {
    const foundInspectionPlan = await this.repo.findOne({
      where: { id: body.id },
      relations: ['organizationUnit', 'machine'],
    });
    if (!foundInspectionPlan) throw new BusinessException('Inspection plan not found');

    // if (body.itemId) {
    const foundItem = await this.itemRepo.findOne({ where: { id: body.itemId } });
    if (!foundItem) throw new BusinessException('Item not found');
    // }
    if (body.organizationUnitId) {
      const foundUnit = await this.organizationUnitRepo.findOne({
        where: { id: body.organizationUnitId },
      });
      if (!foundUnit) throw new BusinessException('Organization unit not found');
    }

    // const isChange =
    //   body.itemId !== foundInspectionPlan.itemId ||
    //   body.dataType !== foundInspectionPlan.dataType ||
    //   body.organizationUnitId !== foundInspectionPlan.organizationUnitId;
    // if ((body.isActive && !foundInspectionPlan.isActive) || isChange) {
    //   const checkPlanTaken = await this.checkTakenItem({
    //     itemId: body.itemId,
    //     organizationUnitId: body.organizationUnitId,
    //     dataType: body.dataType,
    //   });
    //   if (!checkPlanTaken) throw new BusinessException('Inspection Plan for Item exist');
    // }
    const newInspectionPlan = this.repo.create(body);
    // newInspectionPlan.updatedBy = 'admin';
    newInspectionPlan.updatedDate = new Date();
    // if (foundInspectionPlan) {
    //   newInspectionPlan.code = await this.genCode(foundItem);
    // }
    await this.repo.update(newInspectionPlan.id, newInspectionPlan);
    return { message: 'update success' };
  }

  async detail(id: string) {
    if (!id) throw new BadRequestException('ID không hợp lệ');
    return this.repo.findOne({ where: { id } });
  }

  async loadDataSelectForCreate() {
    const items = await this.itemRepo.find({ where: { status: NSItem.Status.Active } });

    const generalDatas = await this.generalDataRepo.find();

    const datetimeUnits = await this.getDetailGeneralByCode('DATETIME_UNIT', generalDatas);
    const sampleTypes = await this.getDetailGeneralByCode('QA_SAMPLE_TYPE', generalDatas);
    const qaDepartments = await this.getDetailGeneralByCode('QA_DEPARTMENT', generalDatas);
    const orgs = await this.getDetailGeneralByCode('QA_SITE', generalDatas);
    const uoms = await this.getDetailGeneralByCode('UOM', generalDatas);
    const categorys = await this.getDetailGeneralByCode('QA_CATEGORY', generalDatas);
    const inspectionLevels = await this.getDetailGeneralByCode('QA_INSPECTION_LEVEL', generalDatas);
    const aqlSeveritys = await this.getDetailGeneralByCode('QA_SEVERITY', generalDatas);

    const levels = await this.getDetailGeneralByCode('ORG_LEVEL', generalDatas);
    const levelSite = levels.find((level) => level.code === 'SITE');

    const units = await this.organizationUnitRepo.find({
      where: { isActive: true, levelId: levelSite?.id },
      order: { name: 'ASC' },
    });

    return {
      items,
      units,
      datetimeUnits,
      sampleTypes,
      qaDepartments,
      orgs,
      categorys,
      inspectionLevels,
      aqlSeveritys,
      uoms,
    };
  }

  async pagination(params: InspectionPageReq) {
    const whereCon: any = {};
    if (params.oraganizationCode) {
      // Nhập 'all' thì tìm các record có organizationUnitId = null
      if (params.oraganizationCode.toUpperCase().trim() === 'ALL') {
        whereCon.organizationUnitId = IsNull();
      } else {
        const lstUnit = await this.organizationUnitRepo.find({
          where: {
            code: Raw((alias) => `LOWER(${alias}) LIKE LOWER(:oraganizationCode)`, {
              oraganizationCode: `%${params.oraganizationCode}%`,
            }),
          },
          select: ['id'],
        });
        whereCon.organizationUnitId = In(lstUnit.map((u) => u.id));
      }
    }
    if (params.code) {
      whereCon.code = Raw((alias) => `LOWER(${alias}) LIKE LOWER(:code)`, {
        code: `%${params.code}%`,
      });
    }
    const whereItem: any = {};
    if (params.itemCode)
      whereItem.code = Raw((alias) => `LOWER(${alias}) LIKE LOWER(:itemCode)`, {
        itemCode: `%${params.itemCode}%`,
      });
    if (params.itemName)
      whereItem.name = Raw((alias) => `LOWER(${alias}) LIKE LOWER(:itemName)`, {
        itemName: `%${params.itemName}%`,
      });
    if (Object.keys(whereItem).length) {
      const lstItem = await this.itemRepo.find({
        where: whereItem,
        select: ['id'],
      });
      whereCon.itemId = In(lstItem.map((u) => u.id));
    }
    if (params.dataType)
      whereCon.dataType = Raw((alias) => `LOWER(${alias}) LIKE LOWER(:dataType)`, {
        dataType: `%${params.dataType}%`,
      });
    if (params.updatedDate?.length === 2) {
      const fd = this.getFirstDay(params.updatedDate[0]);
      const ld = this.getLastDay(params.updatedDate[1]);
      whereCon.updatedDate = Between(fd, ld);
    }
    if ([true, false, 0, 1].includes(params.isActive)) whereCon.isActive = params.isActive;

    const res: any = await this.repo.findPagination(
      {
        where: whereCon,
        order: { createdDate: 'DESC' },
        relations: ['organizationUnit', 'item'],
      },
      params,
    );

    for (let item of res.data) {
      item.oraganizationCode = item.organizationUnit?.code || 'All';
      item.oraganizationName = item.organizationUnit?.name || 'All';
      item.itemCode = item.item?.code;
      item.itemName = item.item?.name;
      item.sampleTypeAQL = item.sampleType;

      delete item.organizationUnit;
      delete item.item;
    }
    return res;
  }

  private async checkTakenItem(data: {
    itemId: string;
    dataType: string;
    organizationUnitId: string;
  }) {
    const checkItem = await this.repo.findOne({
      where: { itemId: data.itemId, dataType: data.dataType, isActive: true },
    });
    if (!checkItem) return true;

    if (!data.organizationUnitId) return false;

    const checkItemV2 = await this.repo.findOne({
      where: [
        {
          itemId: data.itemId,
          dataType: data.dataType,
          isActive: true,
          organizationUnitId: data.organizationUnitId,
        },
        {
          itemId: data.itemId,
          dataType: data.dataType,
          isActive: true,
          organizationUnitId: IsNull(),
        },
      ],
    });

    if (checkItemV2) return false;
    return true;
  }

  private async getDetailGeneralByCode(generalCode: string, generalDatas: GeneralDataEntity[]) {
    const checkGeneral = generalDatas.find((general) => general.code === generalCode);
    if (!checkGeneral) return [];
    let details = await this.generalDataDetailRepo.find({
      where: { generalId: checkGeneral.id, isActive: true },
    });
    return details;
  }

  private getFirstDay(date: Date): Date {
    return new Date(new Date(date).setHours(0, 0, 0, 0));
  }

  private getLastDay(date: Date): Date {
    return new Date(new Date(date).setHours(23, 59, 59));
  }

  private async genCode(item: ItemEntity) {
    const count = await this.repo.count({
      itemId: item.id,
      code: Not(IsNull()),
    });
    let countStr = `00${count + 1}`;
    countStr = countStr.slice(countStr.length - 3, countStr.length);
    // console.log(`${organization.code}${machine.code}${countStr}`);
    return `${item.code}-${countStr}`;
  }
}
