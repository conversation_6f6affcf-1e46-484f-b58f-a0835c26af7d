export namespace NSGeneralData {
  export enum EGeneralDataCode {
    /** <PERSON><PERSON>ch tính điểm KPI */
    CAL_METHOD = 'CAL_METHOD',
    /** Phương thức tính điểm KPI cuối kỳ */
    CAL_TYPE = 'CAL_TYPE',
    /** <PERSON><PERSON><PERSON> dữ liệu - Type of data */
    DATA_TYPE = 'DATA_TYPE',
    /** Đơn vị thời gian */
    DATETIME_UNIT = 'DATETIME_UNIT',
    /** Tần suất nhập điểm KPI trong tháng */
    INPUT_FREQUENCY = 'INPUT_FREQUENCY',
    /** IOTSITEWISE_MODEL_PROPERTY_TYPE */
    IOTSITEWISE_MODEL_PROPERTY_TYPE = 'IOTSITEWISE_MODEL_PROPERTY_TYPE',
    /** Nhóm lý do - Sub Group of reason master data */
    REASON_SUB_GROUP = 'REASON_SUB_GROUP',
    /** Nhóm lý do */
    REASON_GROUP = 'REASON_GROUP',
    /** Lý do - Group of reason master data */
    REASON = 'REASON',
    /** Phòng QA */
    QA_DEPARTMENT = 'QA_DEPARTMENT',
    /** Loại mẫu QA */
    QA_SAMPLE_TYPE = 'QA_SAMPLE_TYPE',
    /** Danh mục QA */
    QA_CATEGORY = 'QA_CATEGORY',
    /** Cấp độ kiểm tra AQL */
    QA_INSPECTION_LEVEL = 'QA_INSPECTION_LEVEL',
    /** Mức độ nghiêm trọng AQL */
    QA_SEVERITY = 'QA_SEVERITY',
    /** Loại sản xuất */
    PRODUCTION_CATEGORY = 'PRODUCTION_CATEGORY',
    /** Loại thuộc tính Model trong IOT Sitewise */
    MODEL_PROPERTY_TYPE = 'MODEL_PROPERTY_TYPE',
    /** Loại đồng hồ đo tiện ích */
    UTILITY_METER_TYPE = 'UTILITY_METER_TYPE',
    /** Loại tham số máy móc - Sử dụng khi khai báo machine parameter */
    MACHINE_PARAMETER_TYPE = 'MACHINE_PARAMETER_TYPE',
    /** Phương thức ghi nhận điểm KPI */
    KPI_METHOD = 'KPI_METHOD',
    /** Phân loại nhóm các loại KPI tính thưởng */
    KPI_GROUP = 'KPI_GROUP',
    /** Chu kỳ lặp của Period Rule */
    KPI_PERIOD_CYCLE = 'KPI_PERIOD_CYCLE',
    /** Trạng thái kỳ KPI - Trạng thái đóng/mở kỳ KPI */
    KPI_PERIOD_STATUS = 'KPI_PERIOD_STATUS',
    /** Đơn vị đo lường */
    UOM = 'UOM',
    /** Cấp độ tổ chức */
    ORG_LEVEL = 'ORG_LEVEL',
    /** Loại giao dịch từ chối */
    REJECTION_TRANSACTION_TYPE = 'REJECTION_TRANSACTION_TYPE',
    /** Report */
    REPORT = 'REPORT',
    /** Trạng thái đơn hàng */
    ORDER_STATUS = 'ORDER_STATUS',
    /** Nhóm người dùng */
    USER_GROUP = 'USER_GROUP',
    /** Station Type */
    STATION_TYPE = 'STATION_TYPE',
    /** Device Type */
    DEVICE_TYPE = 'DEVICE_TYPE',
  }

  export enum ECalMethod {
    /** Tuyệt đối */
    ABSOLUTE = '1',
    /** Càng cao càng tốt */
    HIGHER_BETTER = '2',
    /** Càng thấp càng tốt */
    LOWER_BETTER = '3',
  }

  export enum ECalType {
    /** Tính theo flowrate */
    FLOWRATE_BASED = 'FLOWRATE-BASED',
    /** Tính trung bình */
    AVERAGE = 'AVG',
    /** Tính tổng */
    SUM = 'SUM',
    /** Tính theo giá trị sau trừ giá trị trước */
    DIFFERENCE_BASED = 'DIFFERENCE_BASED',
  }

  export enum EDataType {
    /** Kiểu chuỗi */
    STRING = 'STRING',
    /** Kiểu số thực */
    DOUBLE = 'DOUBLE',
  }

  export enum EDatetimeUnit {
    /** Giờ */
    HOURS = 'H',
    /** Giây */
    SECONDS = 'S',
    /** Ngày */
    DAYS = 'D',
    /** Năm */
    YEARS = 'Y',
    /** Tháng */
    MONTHS = 'MON',
    /** Phút */
    MINUTES = 'MIN',
  }

  export enum EInputFrequency {
    /** Nhập theo ngày */
    DAILY = 'D',
    /** Nhập theo tháng */
    MONTHLY = 'M',
  }

  export enum EMachineParameterType {
    /** Tốc độ tiêu chuẩn */
    STANDARD_SPEED = '8',
    /** Từ chối */
    REJECTION = '9',
    /** Không áp dụng */
    N_A = '7',
    /** Tham số quy trình */
    PROCESS_PARAMETER = '1',
    /** Tiêu thụ tài nguyên */
    RESOURCE_CONSUMPTION = '5',
    /** Số lô */
    BATCH_NUMBER = '2',
    /** Số lệnh sản xuất */
    PRODUCTION_ORDER_NUMBER = '3',
    /** Nguyên liệu */
    INGREDIENTS = '6',
  }

  export enum EKpiMethod {
    /** Thủ công */
    MANUAL = 'M',
    /** Tự động */
    AUTOMATION = 'A',
  }

  export enum EKpiPeriodCycle {
    /** Hàng tháng */
    MONTHLY = '1',
    /** Hàng quý */
    QUARTERLY = '2',
    /** Hàng năm */
    YEARLY = '3',
    /** Test */
    TEST = '4',
    /** Test 2 */
    TEST_2 = '5',
  }

  export enum EKpiPeriodStatus {
    /** Đóng băng */
    FROZEN = '0',
    /** Mở */
    OPEN = '1',
    /** Đóng */
    CLOSED = '2',
  }

  export enum EUom {
    /** Lần/ngày */
    TIMES_PER_DAY = 'LẦN/NGÀY',
    /** Giờ/1000 sản phẩm */
    HOURS_PER_1000_PRODUCTS = 'GIỜ/1000SP',
    /** Kg/ngày */
    KG_PER_DAY = 'KG/NGÀY',
    /** Lần/tháng */
    TIMES_PER_MONTH = 'LẦN/THÁNG',
    /** Lần/năm */
    TIMES_PER_YEAR = 'LẦN/NĂM',
    /** Đồng/gói */
    DONG_PER_PACKAGE = 'ĐỒNG/GÓI',
    /** Kg/1000 gói */
    KG_PER_1000_PACKAGES = 'KG/1000GÓI',
    /** KW/1000 gói */
    KW_PER_1000_PACKAGES = 'KW/1000GÓI',
    /** DPMO */
    DPMO = 'DPMO',
    /** Độ C */
    CELSIUS = '℃',
    /** Kg */
    KG = 'KGS',
    /** Phần trăm */
    PERCENTAGE = '%',
    /** Vụ */
    SEASON = 'VỤ',
    /** Mét */
    METER = 'MET',
  }

  export enum EOrgLevel {
    /** Nhóm */
    GROUP = 'GROUP',
    /** Site */
    SITE = 'SITE',
    /** Nhà máy */
    FACTORY = 'FACTORY',
    /** Khu vực sản xuất */
    PROCESS = 'PROCESS',
    /** Dây chuyền sản xuất */
    LINE = 'LINE',
  }

  export enum ERejectionTransactionType {
    /** Từ chối */
    REJECTION = '1',
    /** Sửa lại */
    REWORK = '2',
    /** Đạt */
    PASS = '3',
  }

  export enum EReasonSubGroup {
    /** An toàn */
    SAFETY = '1',
    /** Chất lượng */
    QUALITY = '2',
    /** Sản xuất */
    PRODUCTION = '3',
    /** GHP */
    GHP = '4',
  }

  export enum EOrderStatus {
    /** Pending */
    PENDING = '1',
    /** WIP */
    WIP = '2',
    /** Complete */
    COMPLETE = '3',
    /** Test */
    TEST = '4',
  }

  export enum EReportStatus {
    /** Running */
    RUNNING = 'RUNNING',
    /** Complete */
    COMPLETED = 'COMPLETED',
    /** Error */
    ERROR = 'ERROR',
    /** Cancel */
    CANCEL = 'CANCEL',
  }

  export enum EMatterialLineType {
    /** In progress */
    ING = 'ING',
    /** Product */
    PRO = 'PRO',
    /** By product */
    BY_PRO = 'BY_PRO',
  }

  export enum ETransactionType {
    /** Giao dịch tăng ingredient input */
    WIP_ISSUE = 'WIP_ISSUE',
    /** Giao dịch tăng  product output */
    WIP_COMPLETION = 'WIP_COMPLETION',
    /**Giao dịch tăng by product output */
    BYPRODUCT_COMPLETION = 'BYPRODUCT_COMPLETION',
    /** Giao dịch giảm ingredient input */
    WIP_ISSUE_RETURN = 'WIP_ISSUE_RETURN',
    /** Giao dịch giảm  product output */
    WIP_COMPLETION_RETURN = 'WIP_COMPLETION_RETURN',
    /**Giao dịch giảm by product output */
    BYPRODUCT_COMPLETION_RETURN = 'BYPRODUCT_COMPLETION_RETURN',
  }
}
