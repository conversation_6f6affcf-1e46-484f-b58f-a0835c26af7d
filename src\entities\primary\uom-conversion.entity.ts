import { Column, Entity, Index, Join<PERSON>olumn, ManyToOne } from "typeorm";
import { PrimaryBaseEntity } from "../primary-base.entity";
import { ApiProperty } from "@nestjs/swagger";
import { ItemEntity } from "./item.entity";

@Entity('uom_conversions')
export class UomConversionEntity extends PrimaryBaseEntity {
  /** Mã sản phẩm/thành phần */
  @ApiProperty({ description: 'Mã sản phẩm/thành phần' })
  @Column({ type: 'uuid' })
  @Index({ unique: false })
  itemId: string;
  
  /** Từ đơn vị */
  @ApiProperty({ description: 'Từ đơn vị', example: 'Kgs' })
  @Column()
  fromUnit: string;
  
  /** Đến đơn vị */
  @ApiProperty({ description: 'Đến đơn vị', example: 'Kgs' })
  @Column()
  toUnit: string;

  /** <PERSON><PERSON> số quy đổi */
  @ApiProperty({ description: '<PERSON>ệ số quy đổi', example: 1 })
  @Column({ type: 'numeric' })
  conversion: number;

  /** 
   * relationships
   */
  @ManyToOne(() => ItemEntity, item => item.uomConversions)
  @JoinColumn({ name: 'itemId' })
  item: ItemEntity;
}