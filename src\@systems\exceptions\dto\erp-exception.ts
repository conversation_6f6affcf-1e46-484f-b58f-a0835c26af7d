import { HttpException, HttpStatus } from '@nestjs/common';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class ErpApiResponse<T = any> {
  @ApiProperty()
  public status: 'success' | 'Error' = 'Error';

  @ApiPropertyOptional()
  public data?: T;

  @ApiPropertyOptional({ type: HttpStatus, example: HttpStatus.INTERNAL_SERVER_ERROR })
  public code?: HttpStatus;

  @ApiPropertyOptional({ type: String })
  public message?: string;

  @ApiPropertyOptional()
  public errors?: T;

  constructor(
    message: string,
    httpCode: HttpStatus = HttpStatus.INTERNAL_SERVER_ERROR,
    errors: T = undefined,
    status: 'success' | 'Error' = 'Error',
    data: T = undefined,
  ) {
    this.code = httpCode;
    this.message = message;
    this.status = status;
    this.errors = errors;
    this.data = data;
  }
  toString() {
    return JSON.stringify({
      status: this.status,
      code: this.code,
      message: this.message,
      errors: this.errors,
      data: this.data,
    });
  }
}
export class ErpBusinessException<T = any> extends ErpApiResponse<T> {
  constructor(message: string = '', errors: T = undefined, httpCode: HttpStatus = HttpStatus.BAD_REQUEST) {
    super(message, httpCode, errors, 'Error');
  }
}

export class ValidateException extends HttpException {
  public messages: {
    [key: string]: string;
  };
  constructor(messages: { [key: string]: string }, status: number) {
    super(JSON.stringify(messages), status);
    this.messages = messages;
  }
}