import { Body, Param, Query, UseGuards } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { DefController, DefGet, DefPost, DefPut } from '~/@core/decorator';
import { Roles } from '~/@core/decorator/roles.decorator';
import { RoleGuard } from '~/@systems/guard/role.guard';
import { LogJobService } from '~/x-modules/sqs/log-job.service';

@ApiTags('Log Job')
@DefController('log-job')
export class LogJobController {
  constructor(private readonly service: LogJobService) {}

  @ApiOperation({ summary: 'Danh sách log job' })
  @DefGet('')
  @Roles('/system-configuration/log-scheduling', 'View')
  @UseGuards(RoleGuard)
  async list(@Query() params: any) {
    return this.service.pagination(params);
  }
}
