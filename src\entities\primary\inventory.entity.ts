import { ApiProperty } from '@nestjs/swagger';
import { Column, Entity, Index, PrimaryGeneratedColumn } from 'typeorm';
import { PrimaryBaseEntity } from '../primary-base.entity';

@Entity('inventory')
export class InventoryEntity extends PrimaryBaseEntity {
  @Index()
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @ApiProperty({ description: 'Mã vật liệu' })
  @Column({ type: 'text' })
  materialCode!: string;

  @ApiProperty({ description: 'Tên vật liệu' })
  @Column({ type: 'text', nullable: true })
  materialName: string;

  @ApiProperty({ description: 'Nhà máy' })
  @Column({ type: 'text' })
  plant!: string;

  @ApiProperty({ description: 'Kho phụ' })
  @Column({ type: 'text' })
  subInventory!: string;

  @ApiProperty({ description: 'Vị trí lưu kho' })
  @Column({ type: 'text' })
  locator!: string;

  @ApiProperty({ description: 'Trạng thái lô hàng' })
  @Column({ type: 'text' })
  lotStatus!: string;

  @ApiProperty({ description: 'Ngày nhận hàng' })
  @Column({ type: 'timestamptz', nullable: true })
  receiptDate!: Date;

  @ApiProperty({ description: 'Ngày hết hạn' })
  @Column({ type: 'timestamptz', nullable: true })
  expiredDate!: Date;

  @ApiProperty({ description: 'Ngày sản xuất' })
  @Column({ type: 'timestamptz', nullable: true })
  productionDate!: Date;

  @ApiProperty({ description: 'Số lượng cơ bản' })
  @Column({ type: 'float' })
  baseQty!: number;

  @ApiProperty({ description: 'Số lượng tồn kho' })
  @Column({ type: 'float' })
  inventoryQty!: number;

  @ApiProperty({ description: 'Đơn vị cơ bản' })
  @Column({ type: 'text' })
  baseUom!: string;

  @ApiProperty({ description: 'Đơn vị tồn kho' })
  @Column({ type: 'text' })
  inventoryUom!: string;

  @ApiProperty({ description: 'Số lượng pallet' })
  @Column({ type: 'float' })
  palletQty!: number;

  @ApiProperty({ description: 'Nhà cung cấp' })
  @Column({ type: 'text' })
  supplier!: string;

  @ApiProperty({ description: 'Nhà sản xuất' })
  @Column({ type: 'text' })
  manufacture!: string;

  @ApiProperty({ description: 'Số lô vật liệu' })
  @Column({ type: 'text' })
  materialLotNumber!: string;

  @ApiProperty({ description: 'Trạng thái đã send scada' })
  @Column({ type: 'boolean', default: false })
  isSyncScada?: boolean;

  @ApiProperty({ description: 'Ngày đã send scada' })
  @Column({ type: 'timestamptz', nullable: true })
  dateSyncScada?: Date;

  @ApiProperty({ description: 'Lỗi khi send scada' })
  @Column({ type: 'text', nullable: true })
  errorSyncScada?: string;

  // @ApiProperty({ description: 'Ngày sản xuất' })
  // @Column({ type: 'timestamptz', nullable: true })
  // lastUpdate: Date;
}
