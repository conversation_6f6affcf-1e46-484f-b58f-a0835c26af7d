// x_module/integration/controller/my-controller.controller.ts

import { Controller, Post, Body, Inject, forwardRef, UseGuards } from '@nestjs/common';
import { NSSQSS } from '~/common/enums/NSSQS';
import { DataCalculationByJobReq } from '~/dto/calculation.dto';
import { SQSService } from '~/x-modules/sqs/sqs.service';
import { UtilityConsumptionKpi } from '~/x-modules/integration/services';
import { Roles } from '~/@core/decorator/roles.decorator';
import { RoleGuard } from '~/@systems/guard/role.guard';
@Controller('data-calculation-by-job')
export class DataCalculationByJob {
  constructor(
    private readonly sqsService: SQSService,
    @Inject(forwardRef(() => UtilityConsumptionKpi))
    private readonly utilityConsumptionKpi: UtilityConsumptionKpi,
  ) {}

  @Post('handler-re-calculate-data')
  @Roles('/system-configuration/log-scheduling', 'View')
  @UseGuards(RoleGuard)
  async handlerReCalculateData(@Body() data: DataCalculationByJobReq) {
    switch (data.type) {
      case NSSQSS.EMessageType.OnProdBatchParamaters:
        this.sqsService.sendMessage(
          {
            message: 'Action Re Calculate Data Pro Batch Paramaters',
            data: data,
          },
          NSSQSS.EMessageType.OnProdBatchParamaters,
        );
        break;
      case NSSQSS.EMessageType.OnProductOutputByRejection:
        this.sqsService.sendMessage(
          {
            message: 'Action Re Calculate Data Product Output By Rejection',
            data: data,
          },
          NSSQSS.EMessageType.OnProductOutputByRejection,
        );
        break;
      case NSSQSS.EMessageType.OnProductionOrderMaterialTransaction:
        this.sqsService.sendMessage(
          {
            message: 'Action Re Calculate Data Production Order Material Transaction',
            data: data,
          },
          NSSQSS.EMessageType.OnProductionOrderMaterialTransaction,
        );
        break;
      case NSSQSS.EMessageType.OnCalculateUtilityUsage:
        this.sqsService.sendMessage(
          {
            message: 'Action Re Calculate Data Calculate Utility Usage',
            data: data,
          },
          NSSQSS.EMessageType.OnCalculateUtilityUsage,
        );
        break;
      case NSSQSS.EMessageType.OnOeeCalculation:
        this.sqsService.sendMessage(
          {
            message: 'Action Re Calculate Data Production Oee',
            data: data,
          },
          NSSQSS.EMessageType.OnOeeCalculation,
        );
        break;
      case NSSQSS.EMessageType.OnKansuiAndSeasoningCalculation:
        this.sqsService.sendMessage(
          {
            message: 'Action Re Calculate Data Calculate Kansui And Seasoning',
            data: data,
          },
          NSSQSS.EMessageType.OnKansuiAndSeasoningCalculation,
        );
        break;
      case NSSQSS.EMessageType.OnTransactionProductOutput:
        this.sqsService.sendMessage(
          {
            message: 'Action Re Calculate Data Transaction Product Output',
            data: data,
          },
          NSSQSS.EMessageType.OnTransactionProductOutput,
        );
        break;
      case NSSQSS.EMessageType.OnUtilityTransactionAllocation:
        this.sqsService.sendMessage(
          {
            message: 'Action Re Calculate Data Utility Transaction Allocation',
            data: data,
          },
          NSSQSS.EMessageType.OnUtilityTransactionAllocation,
        );
        break;
      case NSSQSS.EMessageType.OnManhourTransactionAllocation:
        this.sqsService.sendMessage(
          {
            message: 'Action Re Calculate Data Manhour Transaction Allocation',
            data: data,
          },
          NSSQSS.EMessageType.OnManhourTransactionAllocation,
        );
        break;
      case NSSQSS.EMessageType.OnCreateManhourTransactions:
        this.sqsService.sendMessage(
          {
            message: 'Action Re Calculate Data Manhour Transactions',
            data: data,
          },
          NSSQSS.EMessageType.OnCreateManhourTransactions,
        );
        break;
      case NSSQSS.EMessageType.OnCreateUtilityConsumptionKpi:
        this.sqsService.sendMessage(
          {
            message: 'Action Re Calculate Data Utility Consumption Kpi',
            data: data,
          },
          NSSQSS.EMessageType.OnCreateUtilityConsumptionKpi,
        );
        break;
      case NSSQSS.EMessageType.OnCreateManhourKpi:
        this.sqsService.sendMessage(
          {
            message: 'Action Re Calculate Data Manhour Kpi',
            data: data,
          },
          NSSQSS.EMessageType.OnCreateManhourKpi,
        );
        break;
      case NSSQSS.EMessageType.SyncInventoryToScada:
        this.sqsService.sendMessage(
          {
            message: 'Action Re Calculate Data SyncInventoryToScada',
            data: data,
          },
          NSSQSS.EMessageType.SyncInventoryToScada,
        );
        break;
      case NSSQSS.EMessageType.SyncRecipeToScada:
        this.sqsService.sendMessage(
          {
            message: 'Action Re Calculate Data SyncRecipeToScada',
            data: data,
          },
          NSSQSS.EMessageType.SyncRecipeToScada,
        );
        break;
      case NSSQSS.EMessageType.SyncProductionOrderToScada:
        this.sqsService.sendMessage(
          {
            message: 'Action Re Calculate Data SyncProductionOrderToScada',
            data: data,
          },
          NSSQSS.EMessageType.SyncProductionOrderToScada,
        );
        break;
      case NSSQSS.EMessageType.TestSNS:
        this.sqsService.sendMessage(
          {
            message: 'Action Re Calculate Data TestSNS',
            data: data,
          },
          NSSQSS.EMessageType.TestSNS,
        );
        break;
      case NSSQSS.EMessageType.RejectReasonMidNight:
        this.sqsService.sendMessage(
          {
            message: 'Action Re Calculate rejection reason midnight',
            data: data,
          },
          NSSQSS.EMessageType.RejectReasonMidNight,
        );
        break;
      case NSSQSS.EMessageType.OnCreateOeeKpi:
        this.sqsService.sendMessage(
          {
            message: 'Action Re Calculate Create Oee Kpi',
            data: data,
          },
          NSSQSS.EMessageType.OnCreateOeeKpi,
        );
        break;
      case NSSQSS.EMessageType.OnRejectionKpi:
        this.sqsService.sendMessage(
          {
            message: 'Action Re Calculate Create Oee Kpi',
            data: data,
          },
          NSSQSS.EMessageType.OnRejectionKpi,
        );
        break;
      case NSSQSS.EMessageType.OnLossPowderKpi:
        this.sqsService.sendMessage(
          {
            message: 'Action Re Calculate Create Oee Kpi',
            data: data,
          },
          NSSQSS.EMessageType.OnLossPowderKpi,
        );
        break;
      default:
        break;
    }
    return { message: 'Data is processing' };
  }
}
