import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';
import { PageRequest } from '~/@systems/utils';

export class CreateDeviceDto {
  @IsOptional()
  @IsNumber()
  code?: number;

  @IsOptional()
  @IsString()
  description?: string;

  @IsNotEmpty()
  @IsString()
  deviceTypeCode: string;

  @IsNotEmpty()
  @IsString()
  deviceTypeDetailCode: string;

  @IsOptional()
  @IsString()
  serialNumber?: string;

  @IsOptional()
  ipAddress?: string;

  @IsOptional()
  @IsNumber()
  devicePort?: number;

  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @IsOptional()
  @IsString()
  note?: string;
}
export class UpdateDeviceDto {
  @IsOptional()
  @IsNumber()
  code?: number;

  @IsOptional()
  @IsString()
  description?: string;

  @IsNotEmpty()
  @IsString()
  deviceTypeCode: string;

  @IsNotEmpty()
  @IsString()
  deviceTypeDetailCode: string;

  @IsOptional()
  @IsString()
  serialNumber?: string;

  @IsOptional()
  ipAddress?: string;

  @IsOptional()
  @IsNumber()
  devicePort?: number;

  @IsOptional()
  @IsBoolean()
  isActive?: boolean;

  @IsOptional()
  @IsString()
  note?: string;

  @IsOptional()
  createdDate?: Date;

  @IsOptional()
  @IsString()
  createdBy?: string;

  @IsOptional()
  updatedDate?: Date;

  @IsOptional()
  updatedBy?: string;
}

export class ListDeviceReq extends PageRequest {
  @ApiProperty({ description: 'Mã thiết bị', required: false })
  code?: number;

  @ApiProperty({ description: 'Mô tả thiết bị', required: false })
  description?: string;

  @ApiProperty({ description: 'Loại thiết bị', required: false })
  deviceTypeDetailCode?: string;

  @ApiProperty({ description: 'Trạng thái', type: 'boolean', required: false })
  isActive?: boolean;
}
