import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { Between, EntityManager, ILike, In } from 'typeorm';
import * as _ from 'lodash';
import * as Papa from 'papapar<PERSON>';
import JSZip from 'jszip';
import { BindRepo } from '~/@core/decorator';
import { PageResponse } from '~/@systems/utils/page.utils';
import { NSGeneralData } from '~/common/enums';
import { dayjs, TZ } from '~/common/helpers/date.helper';
import { adminSessionContext } from '~/x-modules/admin/admin-session.context';
import { S3PrivateService } from '~/x-modules/@global/services';
import {
  ProductionOrderRepo,
  ReportHistoryRepo,
  UniqueCodeActivationRepo,
} from '~/repositories/primary';
import { CreateCZCodeShipmentReportDto, ListFilterReportDto } from '~/dto/report-history.dto';

const REPORT = { reportCode: '4', reportName: 'CZ codes - shipment' };
const ORDER_STATUS_CODE_COMPLETED = '3'; // Đơn hàng đã hoàn thành
const CZ_STATUS_ACTIVATED = 1; // Trạng thái mã CZ đã được kích hoạt

@Injectable()
export class CZCodeShipmentReportService {
  constructor(
    private readonly entityManager: EntityManager,
    private readonly s3PrivateService: S3PrivateService,
  ) {}

  @BindRepo(ReportHistoryRepo)
  private reportHistoryRepo: ReportHistoryRepo;
  @BindRepo(ProductionOrderRepo)
  private productionOrderRepo: ProductionOrderRepo;
  @BindRepo(UniqueCodeActivationRepo)
  private uniqueCodeActivationRepo: UniqueCodeActivationRepo;

  async search(req: ListFilterReportDto): Promise<PageResponse> {
    const { createdByUser, email, fromDate, toDate, pageIndex, pageSize } = req;
    // Parse date dạng ISO -> Date object để truy vấn chuẩn
    const start = fromDate ? dayjs(fromDate).toDate() : undefined;
    const end = toDate ? dayjs(toDate).toDate() : undefined;

    const [data, total] = await this.reportHistoryRepo.findAndCount({
      where: {
        reportCode: REPORT.reportCode,
        ...(createdByUser && { createdByUser: ILike(`%${createdByUser}%`) }),
        ...(email && { email: ILike(`%${email}%`) }),
        ...(start && end && { createdDate: Between(start, end) }),
      },
      order: { createdDate: 'DESC' },
      skip: pageIndex && pageSize ? (pageIndex - 1) * pageSize : undefined,
      take: pageIndex && pageSize ? pageSize : undefined,
    });

    const signedUrls = await Promise.all(
      data.map(async (item) => {
        const signedUrl = await this.s3PrivateService.getS3Link(item.linkDownload ?? ''); // 1h
        return signedUrl;
      }),
    );
    data.forEach((item, index) => {
      item.linkDownload = signedUrls[index];
    });

    return { data: data, meta: { totalRecords: total, request: req } };
  }

  async submitReport(req: CreateCZCodeShipmentReportDto) {
    const beginAt = dayjs.utc().tz(TZ).format('YYYYMMDDHHmmss');
    const { userId, userName, userEmail } = adminSessionContext;

    const report = this.reportHistoryRepo.create({
      reportCode: REPORT.reportCode,
      reportName: REPORT.reportName,
      status: NSGeneralData.EReportStatus.RUNNING,
      input: req,
      createdBy: userId,
      createdByUser: userName,
      email: userEmail,
    });
    const reportSaved = await this.reportHistoryRepo.save(report);

    try {
      const listData: CZShipmentReportRow[] = await this.buildReportData(req);

      const grouped = _.groupBy(listData, (item) => item.orderNo);
      const files: { buffer: Buffer; name: string }[] = [];

      for (const [orderNo, records] of Object.entries(grouped)) {
        const first = records[0];
        const fileName = `${first.customerOrderNo}_${first.customerOrderId}_${first.gtin}_${first.orderNo}`;

        const csv =
          '\uFEFF' +
          Papa.unparse(records, {
            columns: [
              // 'orderId',
              'orderNo',
              // 'customerOrderId',
              'customerOrderNo',
              'gtin',
              'uniqueCode',
              'productionDate',
              'expiredDate',
              'customerProductName',
            ],
          });

        files.push({ buffer: Buffer.from(csv, 'utf-8'), name: fileName });
      }

      // Nếu chỉ 1 file thì xuất thẳng, nhiều file thì zip
      let fileBuffer: Buffer;
      let fileName: string;
      let originalname: string;

      if (files.length === 1) {
        fileBuffer = files[0].buffer;
        fileName = files[0].name;
        originalname = `${fileName}.csv`;
      } else {
        const zip = new JSZip();
        for (const file of files) {
          zip.file(file.name + '.csv', file.buffer);
        }
        fileBuffer = await zip.generateAsync({ type: 'nodebuffer' });
        fileName = `CZCode_Shipment_${beginAt}`;
        originalname = `${fileName}.zip`;
      }

      const { fileUrl } = await this.s3PrivateService.uploadS3(
        {
          buffer: fileBuffer,
          originalname: originalname,
        } as Express.Multer.File,
        fileName,
        'private',
      );

      const reportCompleted = await this.reportHistoryRepo.save({
        ...reportSaved,
        linkDownload: fileUrl,
        output: [],
        outputTotal: listData.length,
        status: NSGeneralData.EReportStatus.COMPLETED,
        completedDate: new Date(),
      });

      return { data: reportCompleted, meta: { request: req } };
    } catch (err) {
      Logger.error(err);
      await this.reportHistoryRepo.save({
        ...reportSaved,
        status: NSGeneralData.EReportStatus.ERROR,
        output: {
          errorMessage: err?.message ?? 'Unknown Error',
        },
        completedDate: new Date(),
      });
      throw new BadRequestException(`Submit report failed: ${err.message}`);
    }
  }

  private async buildReportData(req: CreateCZCodeShipmentReportDto) {
    const { customerOrderNo } = req;

    const orders = await this.productionOrderRepo.find({
      where: {
        ...(customerOrderNo && { customerOrderNo: customerOrderNo }),
        orderStatusCode: ORDER_STATUS_CODE_COMPLETED,
      },
    });

    const orderIds = orders.map((order) => order.id);
    if (!orderIds.length) {
      return [];
    }

    const czCodes = await this.uniqueCodeActivationRepo.find({
      where: {
        orderId: In(orderIds),
        status: CZ_STATUS_ACTIVATED,
      },
    });

    const orderMap = new Map(orders.map((o) => [o.id, o]));

    const data: CZShipmentReportRow[] = czCodes.map((cz) => {
      const order = orderMap.get(cz.orderId);
      return {
        orderId: order?.id, // production_order.id
        orderNo: order?.orderNo, // production_order.orderNo
        customerOrderId: order?.customerOrderId, // Order ID
        customerOrderNo: order?.customerOrderNo, // Order Number
        gtin: order?.gtin, // GTIN
        uniqueCode: cz.uniqueCode, // Making Code
        productionDate: dayjs.utc(cz.productionDate).tz(TZ).format('DD-MM-YYYY HH:mm:ss'), // Production Date
        expiredDate: dayjs.utc(cz.expiredDate).tz(TZ).format('DD-MM-YYYY HH:mm:ss'), // Expired Date
        customerProductName: order?.customerProductName,
      };
    });

    return data;
  }
}

export interface CZShipmentReportRow {
  orderId: string; // production_order.id
  orderNo: string; // production_order.orderNo
  customerOrderId: string; // production_order.customerOrderId
  customerOrderNo: string; // production_order.customerOrderNo
  gtin: string; // production_order.gtin
  uniqueCode: string; // unique_code_activation.uniqueCode
  productionDate: string; // unique_code_activation.production_date
  expiredDate: string; // unique_code_activation.expired_date
  customerProductName: string; // production_order.customerProductName
}
