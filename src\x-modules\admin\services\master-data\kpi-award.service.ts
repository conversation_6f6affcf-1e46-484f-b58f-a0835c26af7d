import { Injectable, NotFoundException } from '@nestjs/common';
import { v4 as uuidv4 } from 'uuid';
import {
  Between,
  DeepPartial,
  In,
  IsNull,
  LessThanOrEqual,
  Like,
  MoreThanOrEqual,
  Not,
  Raw,
} from 'typeorm';
import { BindRepo, DefTransaction } from '~/@core/decorator';
import { KpiAwardEntity } from '~/entities/primary';
import {
  HolidayRepo,
  KpiAwardRepo,
  KpiRepo,
  KpiScoreRepo,
  KpiSetDetailRepo,
  KpiSetGroupRepo,
  KpiSetHeaderRepo,
  KpiSetProductAreaRepo,
  KpiStandardRepo,
  KpiStandardScoreRepo,
  TimeSheetRecordRepository,
  UserRepo,
} from '~/repositories/primary';
import moment from 'moment';
import { dateHelper, dayjs } from '~/common/helpers';
@Injectable()
export class KPIAwardService {
  constructor() {}

  @BindRepo(KpiSetHeaderRepo)
  private repo: KpiSetHeaderRepo;
  @BindRepo(KpiSetGroupRepo)
  private kpiSetGroupRepo: KpiSetGroupRepo;
  @BindRepo(KpiSetDetailRepo)
  private kpiSetDetailRepo: KpiSetDetailRepo;
  @BindRepo(KpiSetProductAreaRepo)
  private kpiSetProductAreaRepo: KpiSetProductAreaRepo;

  @BindRepo(KpiScoreRepo)
  private kpiScoreRepo: KpiScoreRepo;

  @BindRepo(KpiRepo)
  private kpiRepo: KpiRepo;

  @BindRepo(KpiAwardRepo)
  private kpiAwardRepo: KpiAwardRepo;

  @BindRepo(KpiStandardScoreRepo)
  private kpiStandardScoreRepo: KpiStandardScoreRepo;
  @BindRepo(HolidayRepo)
  private holidayRepo: HolidayRepo;

  @BindRepo(KpiStandardRepo)
  private kpiStandardRepo: KpiStandardRepo;

  @BindRepo(UserRepo)
  private userRepo: UserRepo;

  @BindRepo(TimeSheetRecordRepository)
  private timeSheetRecordRepo: TimeSheetRecordRepository;

  @DefTransaction()
  async calculateKpiDataForDate() {
    const lstUser = await this.userRepo.find({ where: { isActive: true } });
    const today = dayjs().tz('Asia/Ho_Chi_Minh').startOf('day');
    const datesToCheck = [today.subtract(2, 'day'), today.subtract(1, 'day'), today];
    const startDates = datesToCheck.map((d) => d.startOf('day').toDate());
    const endDates = datesToCheck.map((d) => d.endOf('day').toDate());
    const dictAward: any = {};
    {
      const awards = await this.kpiAwardRepo.find({
        where: {
          userId: In(lstUser.map((u) => u.id)),
          workDate: Between(startDates[0], endDates[2]),
        },
      });
      awards.forEach((award) => {
        if (!dictAward[award.userId]) {
          dictAward[award.userId] = [];
        }
        dictAward[award.userId].push(award);
      });
    }
    for (const user of lstUser) {
      const awards = dictAward[user.id] || [];
      const existingDays = new Set(awards.map((a) => dayjs(a.workDate).format('YYYY-MM-DD')));

      const allExist = datesToCheck.every((d) => existingDays.has(d.format('YYYY-MM-DD')));

      const datesToCalculate = allExist ? datesToCheck : [today];

      let allResults: any[] = [];

      for (const date of datesToCalculate) {
        const resultsForDate = await this.calculateKpiDataForOneDay(user.id, date);
        if (resultsForDate && resultsForDate.length > 0) {
          allResults.push(...resultsForDate);
        }
      }
    }
  }

  async calculateKpiDataForOneDay(userId: string, date: dayjs.Dayjs): Promise<any[]> {
    const currentDate = date.tz('Asia/Ho_Chi_Minh');
    const startOfDay = currentDate.startOf('day').toDate();
    const endOfDay = currentDate.endOf('day').toDate();

    const user = await this.userRepo.findOne({ where: { id: userId } });
    if (!user) throw new Error('User not found');

    const productionAreaId = user.productionAreaId;

    const [kpiSetProductionArea, existingAwards] = await Promise.all([
      this.kpiSetProductAreaRepo.findOne({ where: { productionAreaId, status: 1 } }),
      this.kpiAwardRepo.find({ where: { userId, workDate: Between(startOfDay, endOfDay) } }),
    ]);

    if (!kpiSetProductionArea) return [];

    const validKpiSetHeader = await this.kpiStandardScoreRepo.findOne({
      where: {
        kpiSetId: kpiSetProductionArea.kpiSetHeaderId,
        effectiveDateTo: IsNull(),
      },
      order: { effectiveDateFrom: 'DESC' },
    });

    if (!validKpiSetHeader) throw new Error('KPI Standard Score not found');

    const [kpiSetHeader, kpiGroups] = await Promise.all([
      this.repo.findOne({ where: { id: validKpiSetHeader.kpiSetId } }),
      this.kpiSetGroupRepo.find({ where: { kpiSetHeaderId: validKpiSetHeader.kpiSetId } }),
    ]);

    if (!kpiSetHeader) throw new Error('KPI Set Header not found');

    const kpiSetGroupIds = kpiGroups.map((g) => g.id);

    const [kpiSetDetails, kpiStandardScoreData, kpiStandardData] = await Promise.all([
      this.kpiSetDetailRepo.find({ where: { kpiSetGroupId: In(kpiSetGroupIds) } }),
      this.kpiStandardScoreRepo.find({ where: { kpiSetId: validKpiSetHeader.kpiSetId } }),
      this.kpiStandardRepo.find({ where: { kpiSetId: validKpiSetHeader.kpiSetId } }),
    ]);

    const kpiIds = [...new Set(kpiSetDetails.map((d) => d.kpiId))];

    const kpiScores = await this.kpiScoreRepo.find({
      where: { productionAreaId, kpiId: In(kpiIds) },
    });

    const avgScoreMap = kpiScores.reduce((map, score) => {
      if (!map[score.kpiId]) map[score.kpiId] = { total: 0, count: 0 };
      map[score.kpiId].total += score.actualScore;
      map[score.kpiId].count += 1;
      return map;
    }, {});

    const kpis = await this.kpiRepo.find({ where: { id: In(kpiIds) } });

    kpis.forEach((kpi) => {
      const score = avgScoreMap[kpi.id];
      (kpi as any).actualScore = score ? score.total / score.count : 0;
    });

    const existingAwardsMap = new Map(existingAwards.map((a) => [a.kpiCodeId, a]));

    const kpiAwardEntities: KpiAwardEntity[] = [];

    for (const kpi of kpis) {
      const kpiStandard = kpiStandardData.find((s) => s.kpiCodeId === kpi.id);
      if (!kpiStandard) continue;

      const weightageKpi = kpiStandard.weightageKpi || 0;
      const budget = kpiSetHeader.budget || 0;

      const rewardPercentage = this.getKpiLevel(
        kpi.calMethod,
        (kpi as any).actualScore,
        kpiStandardScoreData.filter((s) => s.groupId === kpiStandard.groupId),
      );

      const rewardActual = (budget * weightageKpi * rewardPercentage.rewardPercentage) / 100 / 26;
      const rewardStandard = (budget * weightageKpi * 100) / 26;

      const commonAwardData = {
        userId,
        workDate: startOfDay,
        kpiSetHeaderId: kpiSetHeader.id,
        kpiSetGroupId: kpiStandard.kpiSetGroupId,
        factoryId: kpiSetProductionArea.factoryId,
        productionAreaId,
        actualAward: rewardActual,
        standardAward: rewardStandard,
        budget,
        weightageKpi: weightageKpi.toString(),
        rewardPercentage: rewardPercentage.rewardPercentage.toString(),
        level: rewardPercentage.levelScore.toString(),
      };

      const existingAward = existingAwardsMap.get(kpi.id);

      if (existingAward) {
        Object.assign(existingAward, commonAwardData);
        kpiAwardEntities.push(existingAward);
      } else {
        const newAward = this.kpiAwardRepo.create({ ...commonAwardData, kpiCodeId: kpi.id });
        kpiAwardEntities.push(newAward);
      }
    }

    if (kpiAwardEntities.length) {
      await this.kpiAwardRepo.save(kpiAwardEntities);
    }

    return kpiAwardEntities.map((award) => ({
      kpiId: award.kpiCodeId,
      actualAward: award.actualAward,
      budget: award.budget,
      weightageKpi: award.weightageKpi,
      rewardPercentage: award.rewardPercentage,
      level: award.level,
      workDate: currentDate.format('YYYY-MM-DD'),
    }));
  }

  private getKpiLevel(calculationMethod: string, actualScore: number, standardScores: any[]) {
    if (calculationMethod === '1') {
      // Tuyệt đối
      const level = standardScores.find((score) => score.levelScore === actualScore);
      if (level) {
        return { rewardPercentage: level.rewardPercentage, levelScore: level.level };
      }
    }

    if (calculationMethod === '2') {
      // Càng cao càng tốt
      // Sắp xếp theo giá trị tăng dần của level_score
      standardScores.sort((a, b) => a.levelScore - b.levelScore);
      const level = standardScores.find((score) => actualScore >= score.levelScore);
      if (level) {
        return { rewardPercentage: level.rewardPercentage, levelScore: level.level };
      }
    }

    if (calculationMethod === '3') {
      // Càng thấp càng tốt
      // Sắp xếp theo giá trị giảm dần của level_score
      standardScores.sort((a, b) => b.levelScore - a.levelScore);
      const level = standardScores.find((score) => actualScore <= score.levelScore);
      if (level) {
        return { rewardPercentage: level.rewardPercentage, levelScore: level.level };
      }
    }

    return { rewardPercentage: 0, levelScore: 'Không đạt' };
  }

  async checkUserWorkedToday(userId: string) {
    const user = await this.userRepo.findOne({ where: { id: userId } });
    if (!user) throw new NotFoundException('User not found');

    const productionAreaId = user.productionAreaId;

    const todayStr = moment().format('YYYY-MM-DD');
    const record = await this.timeSheetRecordRepo.findOne({
      where: {
        productionAreaId,
        workDate: todayStr,
      },
    });

    return { workedToday: !!record };
  }

  async findByUserIdWithWorkDateToday(userId: string) {
    if (!userId) throw new Error('UserId is required');

    const startOfDay = dayjs().tz('Asia/Ho_Chi_Minh').startOf('day').toDate();
    const endOfDay = dayjs().tz('Asia/Ho_Chi_Minh').endOf('day').toDate();

    const startOfMonth = dayjs().tz('Asia/Ho_Chi_Minh').startOf('month').toDate();
    const endOfMonth = dayjs().tz('Asia/Ho_Chi_Minh').endOf('month').toDate();

    // Lấy trong ngày hôm nay
    const awardsToday = await this.kpiAwardRepo.find({
      where: {
        userId,
        workDate: Between(startOfDay, endOfDay),
      },
      order: { workDate: 'DESC' },
    });
    const kpiCodeIds = [...new Set(awardsToday.map((a) => a.kpiCodeId).filter(Boolean))];

    const kpis = await this.kpiRepo.findByIds(kpiCodeIds);

    const kpiNameMap = new Map(kpis.map((kpi) => [kpi.id, kpi.shortName]));

    const awardsWithName = awardsToday.map((award) => ({
      ...award,
      kpiName: kpiNameMap.get(award.kpiCodeId) || null,
    }));

    const awardsMonth = await this.kpiAwardRepo.find({
      where: {
        userId,
        workDate: Between(startOfMonth, endOfDay),
      },
      order: { workDate: 'DESC' },
    });
    const budget = Math.max(...awardsMonth.map((award) => Number(award.budget ?? 0)));

    // Tính tổng actualAward ngày hôm nay
    const totalActualAward = awardsToday.reduce(
      (sum, award) => sum + Number(award.actualAward ?? 0),
      0,
    );

    // Tính tổng standardAward ngày hôm nay
    const totalStandardAward = awardsToday.reduce(
      (sum, award) => sum + Number(award.standardAward ?? 0),
      0,
    );

    // Tính tổng actualAward tháng lũy kế
    const totalMonthlyAward = awardsMonth.reduce(
      (sum, award) => sum + Number(award.actualAward ?? 0),
      0,
    );

    const percentageMonth = Math.round((totalMonthlyAward / budget) * 100);
    const percentageToday = Math.round((totalActualAward / totalStandardAward) * 100);

    return {
      percentageMonth,
      percentageToday,
      awardsWithName,
      totalActualAward,
      totalMonthlyAward,
    };
  }
}
