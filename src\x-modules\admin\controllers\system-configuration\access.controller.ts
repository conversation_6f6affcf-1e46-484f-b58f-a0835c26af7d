import { Get, Query, Put, Param, Body, UseGuards } from '@nestjs/common';
import { ApiQuery, ApiTags, ApiOperation } from '@nestjs/swagger';
import { DefController, DefGet, DefPost, DefPut } from '~/@core/decorator';
import { AccessService } from '../../services/system-configuration/access.service';
import { PageRequest } from '~/@systems/utils';
import { Roles } from '~/@core/decorator/roles.decorator';
import { RoleGuard } from '~/@systems/guard/role.guard';

@ApiTags('Access')
@DefController('access')
export class AccessController {
  constructor(private readonly accessService: AccessService) {}

  @Get('organization-units')
  @ApiQuery({
    name: 'group',
    required: false,
    type: String,
    description: 'Tìm kiếm theo mã hoặc tên của Group',
  })
  @ApiQuery({
    name: 'site',
    required: false,
    type: String,
    description: 'Tìm kiếm theo mã hoặc tên của Site',
  })
  @ApiQuery({
    name: 'factory',
    required: false,
    type: String,
    description: 'Tìm kiếm theo mã hoặc tên của Factory',
  })
  @ApiQuery({
    name: 'line',
    required: false,
    type: String,
    description: 'Tìm kiếm theo mã hoặc tên của Line',
  })
  @ApiQuery({
    name: 'process',
    required: false,
    type: String,
    description: 'Tìm kiếm theo mã hoặc tên của Process Area',
  })
  @ApiQuery({
    name: 'page',
    required: false,
    type: Number,
    description: 'Số trang (bắt đầu từ 1)',
  })
  @ApiQuery({
    name: 'pageSize',
    required: false,
    type: Number,
    description: 'Số lượng bản ghi trên mỗi trang',
  })
  @Roles('/system-configuration/access', 'View')
  @UseGuards(RoleGuard)
  async getOrganizationList(
    @Query('group') group?: string,
    @Query('site') site?: string,
    @Query('factory') factory?: string,
    @Query('line') line?: string,
    @Query('process') process?: string,
    @Query('page') page: number = 1,
    @Query('pageSize') pageSize: number = 10,
  ) {
    return this.accessService.getOrganizationList({
      group,
      site,
      factory,
      line,
      process,
      page,
      pageSize,
    });
  }

  @Put(':userId')
  @Roles('/system-configuration/access', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Cập nhật quyền truy cập của người dùng' })
  async updateAccess(
    @Param('userId') userId: string,
    @Body() body: { addedIds?: string[]; removedIds?: string[] },
  ) {
    return this.accessService.updateAccess(userId, body.addedIds || [], body.removedIds || []);
  }

  @DefGet('user-list')
  @Roles('/system-configuration/access', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Lấy danh sách user' })
  async getUserList() {
    return await this.accessService.getUserList();
  }

  @DefPut('status/:userId')
  @Roles('/system-configuration/access', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Cập nhật trạng thái quyền truy cập' })
  async updateStatus(@Param('userId') userId: string, @Body() body: { organizationId: string }) {
    return this.accessService.updateStatus(userId, body.organizationId);
  }

  @DefGet('load-site')
  @Roles('/system-configuration/access', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Danh sách SITE' })
  async loadSiteData() {
    return this.accessService.loadSiteData();
  }

  @DefGet('load-factory')
  @Roles('/system-configuration/access', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Danh sách FACTORY' })
  async loadFactoryData() {
    return this.accessService.loadFactoryData();
  }

  @DefPost('fetch-default-access-for-user')
  @Roles('/system-configuration/access', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Lấy quyền truy cập mặc định của user' })
  async getDefaultAccess() {
    return this.accessService.getDefaultAccessForCurrentUser();
  }

  @DefPost('fetch-site-access-for-current-user')
  @Roles('/system-configuration/access', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Lấy danh sách site của user' })
  async getSiteAccessForCurrentUser() {
    return this.accessService.getSiteAccessForCurrentUser();
  }

  @DefPost('fetch-factory-access-by-site-for-current-user')
  @Roles('/system-configuration/access', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Lấy danh sách nhà máy theo site của user' })
  async getFactoryAccessBySiteIdLstForCurrentUser(@Body() req: { siteIdLst?: string[] }) {
    return this.accessService.getFactoryAccessBySiteIdLstForCurrentUser(req);
  }

  @DefGet(':userId')
  @Roles('/system-configuration/access', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Danh sách quyền truy cập' })
  async pagination(@Param('userId') userId: string, @Query() params: any) {
    return this.accessService.pagination(userId, params);
  }

  @DefPost(':userId')
  @Roles('/system-configuration/access', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Danh sách quyền truy cập' })
  async create(@Param('userId') userId: string, @Body() data: any) {
    return this.accessService.createAccess(userId, data);
  }

  @DefPut('default/:userId')
  @Roles('/system-configuration/access', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Cập nhật mặc định quyền truy cập' })
  async updateDefault(@Param('userId') userId: string, @Body() body: any) {
    return this.accessService.updateDefault(userId, body.organizationId);
  }

  @DefGet('/default-access-by-user/:userId')
  @ApiOperation({ summary: 'Lấy quyền truy cập mặc định theo user' })
  async getDefaultAccessByUser(@Param('userId') userId: string) {
    return this.accessService.getDefaultAccessByUser(userId);
  }
}
