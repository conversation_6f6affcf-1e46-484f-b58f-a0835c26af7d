import { Body, Param, ParseU<PERSON><PERSON><PERSON>e, UseGuards } from '@nestjs/common';
import { ApiBody, ApiOperation, ApiTags } from '@nestjs/swagger';
import { DefController, DefGet, DefPost, DefPut } from '~/@core/decorator';
import {
  InspectionPageReq,
  InspectionPlanCreateDto,
  InspectionPlanUpdateDto,
} from '~/dto/inspection-plan.dto';
import { InspectionPlanService } from '../../services';
import { Roles } from '~/@core/decorator/roles.decorator';
import { RoleGuard } from '~/@systems/guard/role.guard';

@ApiTags('InspectionPlan')
@DefController('inspection-plan')
export class InspectionPlanController {
  constructor(private service: InspectionPlanService) {}

  @ApiOperation({ summary: 'Tạo mới', description: 'Tạo mới kế hoạch kiểm tra' })
  @DefPost('')
  @Roles('/master-data/inspection-plan', 'View')
  @UseGuards(RoleGuard)
  async create(@Body() body: InspectionPlanCreateDto) {
    return this.service.create(body);
  }

  @ApiOperation({ summary: 'Cập nhật', description: 'Cập nhật kế hoạch kiểm tra' })
  @DefPut('')
  @Roles('/master-data/inspection-plan', 'View')
  @UseGuards(RoleGuard)
  async update(@Body() body: InspectionPlanUpdateDto) {
    return this.service.update(body);
  }

  @ApiOperation({ summary: 'Phân trang', description: 'Phân trang kế hoạch kiểm tra' })
  @DefPost('pagination')
  @Roles('/master-data/inspection-plan', 'View')
  @UseGuards(RoleGuard)
  async pagination(@Body() body: InspectionPageReq) {
    return this.service.pagination(body);
  }

  @ApiOperation({ summary: 'Lấy thông tin selectbox', description: 'Lấy thông tin selectbox' })
  @DefGet('dataselect-for-create')
  @Roles('/master-data/inspection-plan', 'View')
  @UseGuards(RoleGuard)
  async loadDataSelectForCreate() {
    return this.service.loadDataSelectForCreate();
  }

  @ApiOperation({ summary: 'Chi tiết', description: 'Lấy thông tin chi tiết' })
  @DefGet(':id')
  @Roles('/master-data/inspection-plan', 'View')
  @UseGuards(RoleGuard)
  async detail(@Param('id', new ParseUUIDPipe()) id: string) {
    return this.service.detail(id);
  }
}
