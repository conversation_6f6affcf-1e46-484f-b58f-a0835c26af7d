import { ApiProperty } from '@nestjs/swagger';
import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { PrimaryBaseEntity } from '~/entities/primary-base.entity';

@Entity('kpi_standard_score')
export class KpiStandardScoreEntity extends PrimaryBaseEntity {
  @ApiProperty({
    description: 'Nhóm id của cùng 1 KPI Set',
  })
  @Column({ type: 'uuid', nullable: true })
  groupId: string;
  /** KPI set id: lấy ID từ bảng KPI_Standard */
  @ApiProperty({ description: 'KPI set id: lấy ID từ bảng KPI_Standard' })
  @Column({ type: 'uuid', nullable: true })
  kpiSetId: string;

  /** kpi_code_id link theo kpi_set_id từ bảng kpi_standard */
  @ApiProperty({ description: 'kpi_code_id link theo kpi_set_id từ bảng kpi_standard' })
  @Column({ type: 'uuid', nullable: true })
  kpiCodeId: string;

  /** <PERSON><PERSON> thống tự sinh theo số thứ tự tăng dần, mỗi KPI standard score cho từng KPI code là 1 ID duy nhất */
  @ApiProperty({
    description:
      'Hệ thống tự sinh theo số thứ tự tăng dần, mỗi KPI standard score cho từng KPI code là 1 ID duy nhất',
  })
  kpiScoreId: number;

  /** Hệ thống tự sinh theo số thứ tự tăng dần, 1 KPI có nhiều mức điểm đánh giá xếp loại, mỗi mức điểm là 1 line_id */
  @ApiProperty({
    description:
      'Hệ thống tự sinh theo số thứ tự tăng dần, 1 KPI có nhiều mức điểm đánh giá xếp loại, mỗi mức điểm là 1 line_id',
  })
  @PrimaryGeneratedColumn('increment')
  kpiScoreLineId: number;

  /** Điểm đánh giá xếp loại */
  @ApiProperty({ description: 'Điểm đánh giá xếp loại' })
  @Column({ type: 'numeric' })
  levelScore: number;

  /** Xếp loại E, HE, O, ... */
  @ApiProperty({ description: 'Xếp loại E, HE, O, ...' })
  @Column({ type: 'varchar', length: 15 })
  level: string;

  /** % nhận thưởng theo xếp loại */
  @ApiProperty({ description: '% nhận thưởng theo xếp loại' })
  @Column({ type: 'numeric' })
  rewardPercentage: number;

  /** Ngày bắt đầu hiệu lực */
  @ApiProperty({ description: 'Ngày bắt đầu hiệu lực' })
  @Column({ type: 'timestamptz', nullable: true })
  effectiveDateFrom?: Date;

  /** Ngày hết hạn hiệu lực */
  @ApiProperty({ description: 'Ngày hết hạn hiệu lực' })
  @Column({ type: 'timestamptz', nullable: true })
  effectiveDateTo?: Date;
}
