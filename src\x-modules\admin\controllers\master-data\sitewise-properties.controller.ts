import { Query } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { DefController, DefGet } from '~/@core/decorator';
import { ListSitewisePropertiesReq } from '~/dto/sitewise.dto';
import { SitewisePropertiesService } from '../../services';

@ApiTags('Sitewise Properties')
@DefController('sitewise-properties')
export class SitewisePropertiesController {
  constructor(private readonly service: SitewisePropertiesService) {}

  @DefGet('find')
  async find(@Query() data: any) {
    return await this.service.find(data);
  }

  @DefGet('')
  async pagination(@Query() data: ListSitewisePropertiesReq) {
    return await this.service.pagination(data);
  }
}
