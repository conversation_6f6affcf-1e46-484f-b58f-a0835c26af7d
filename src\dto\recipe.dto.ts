import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsArray, IsEnum, IsOptional } from 'class-validator';
import { PageRequest } from '~/@systems/utils';
import { NSRecipe } from '~/common/enums';

export class ListRecipeReq extends PageRequest {
  @ApiPropertyOptional({
    description: '<PERSON>h sách các trường dữ liệu liên kết',
    enum: NSRecipe.RecipeRelations,
    default: [NSRecipe.RecipeRelations.Product],
    isArray: true,
  })
  @IsOptional()
  @IsEnum(NSRecipe.RecipeRelations, { each: true })
  relations?: NSRecipe.RecipeRelations[];

  @ApiProperty({})
  @IsOptional()
  processArea?: string;

  @ApiProperty({})
  @IsOptional()
  organizationCode?: string;

  @ApiProperty({})
  @IsOptional()
  organizationUnit?: string;

  @ApiProperty({})
  @IsOptional()
  recipeNo?: string;

  @ApiProperty({})
  @IsOptional()
  recipeVer?: string;

  @ApiProperty({})
  @IsOptional()
  recipeDescription?: string;

  @ApiProperty({})
  @IsOptional()
  productCode?: string;

  @ApiProperty({})
  @IsOptional()
  productName?: string;

  @ApiProperty({})
  @IsOptional()
  formulaNo?: string;

  @ApiProperty({})
  @IsOptional()
  formulaVer?: string;

  @ApiProperty({})
  @IsOptional()
  routingNo?: string;

  @ApiProperty({})
  @IsOptional()
  routingVer?: string;

  @ApiProperty({})
  @IsOptional()
  recipeStatus?: string;

  @ApiProperty({
    example: '2024-03-25T14:30:00.000Z',
  })
  @IsOptional()
  startDate?: string;

  @ApiProperty({
    example: '2024-03-25T14:30:00.000Z',
  })
  @IsOptional()
  endDate?: string;

  @ApiProperty({
    example: true,
  })
  @IsOptional()
  isSyncScada?: string;

  @ApiProperty({
    example: '2024-03-25T14:30:00.000Z',
  })
  @IsOptional()
  startSyncDate?: string;

  @ApiProperty({
    example: '2024-03-25T14:30:00.000Z',
  })
  @IsOptional()
  endSyncDate?: string;
}
