import { Body, Param, Query, UseGuards } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { DefController, DefGet, DefPost, DefPut } from '~/@core/decorator';
import { MaterialGroupService } from '../../services';
import { MaterialGroupDto, MaterialGroupReq } from '~/dto/material-group.dto';
import { Roles } from '~/@core/decorator/roles.decorator';
import { RoleGuard } from '~/@systems/guard/role.guard';

@ApiTags('Material Group')
@DefController('material-group')
export class MaterialGroupController {
  constructor(private readonly service: MaterialGroupService) {}

  @ApiOperation({ summary: 'Danh sách nhóm nguyên vật liệu' })
  @DefGet('')
  @Roles('/master-data/material-group', 'View')
  @UseGuards(RoleGuard)
  async list(@Query() params: MaterialGroupReq) {
    return this.service.pagination(params);
  }

  @ApiOperation({ summary: 'Danh sách item' })
  @DefGet('load-item')
  @Roles('/master-data/material-group', 'View')
  @UseGuards(RoleGuard)
  async loadItem() {
    return this.service.loadItem();
  }

  @ApiOperation({ summary: 'Danh sách nhóm nguyên vật liệu' })
  @DefGet(':id')
  @Roles('/master-data/material-group', 'View')
  @UseGuards(RoleGuard)
  async getMaterialDetail(@Param('id') id: string) {
    return this.service.getMaterialDetail(id);
  }

  @ApiOperation({ summary: 'Tạo nhóm nguyên vật liệu' })
  @DefPost('')
  @Roles('/master-data/material-group', 'View')
  @UseGuards(RoleGuard)
  async create(@Body() body: MaterialGroupDto) {
    return this.service.create(body);
  }

  @ApiOperation({ summary: 'Cập nhật nhóm nguyên vật liệu' })
  @DefPut(':id')
  @Roles('/master-data/material-group', 'View')
  @UseGuards(RoleGuard)
  async update(@Param('id') id: string, @Body() body: MaterialGroupDto) {
    return this.service.update(id, body);
  }

  @ApiOperation({ summary: 'Cập nhật trạng thái nhóm nguyên vật liệu' })
  @DefPut('active/:id')
  @Roles('/master-data/material-group', 'View')
  @UseGuards(RoleGuard)
  async updateActive(@Param('id') id: string) {
    return this.service.updateActive(id);
  }
}
