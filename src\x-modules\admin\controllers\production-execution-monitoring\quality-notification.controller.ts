import { Body, Query, UseGuards } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { DefController, DefGet, DefPost } from '~/@core/decorator';

import { QualityNotificationService } from '~/x-modules/admin/services/production-execution-monitoring/quality-notification.service';
import {
  QualityNotificationDetailDto,
  QualityNotificationPaginationDto,
} from '~/dto/quality-notification.dto';
import { Roles } from '~/@core/decorator/roles.decorator';
import { RoleGuard } from '~/@systems/guard/role.guard';

@ApiTags('Quality Notification')
@DefController('quality-notification')
export class QualityNotificationController {
  constructor(private readonly service: QualityNotificationService) {}

  @ApiOperation({ summary: 'Danh sách phân trang Quality Notification' })
  @DefGet('')
  @Roles('/production-execution/quality-notification', 'View')
  @UseGuards(RoleGuard)
  async list(@Query() params: QualityNotificationPaginationDto) {
    return this.service.pagination(params);
  }

  @ApiOperation({ summary: 'Danh sách Quality Notification Detail' })
  @DefPost('get-info')
  @Roles('/production-execution/quality-notification', 'View')
  @UseGuards(RoleGuard)
  async getInfo(@Body('id') id: string) {
    return this.service.getQualityInformation(id);
  }

  @ApiOperation({ summary: 'Hệ thống QLONE gọi để gửi kết quả của sample' })
  @DefPost()
  @Roles('/production-execution/quality-notification', 'View')
  @UseGuards(RoleGuard)
  async resultQLONESample(@Body() body: any) {
    return this.service.resultQLONESample(body);
  }
}
