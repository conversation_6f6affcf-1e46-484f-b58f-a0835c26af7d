import * as aws from 'aws-sdk';
import { Injectable } from '@nestjs/common';
import { NSSQSS } from '~/common/enums';
import { configEnv } from '~/@config/env';
import { LogJobService } from '~/x-modules/sqs/log-job.service';
const {
  AWS_SQS_URL,
  AWS_SQS_REGION,
  AWS_SQS_ACCESS_KEY_ID,
  AWS_SQS_SECRET_ACCESS_KEY,
  AWS_API_VERSION,
} = configEnv();
@Injectable()
export class SQSService {
  private readonly sqs: aws.SQS;
  private queueUrl = '';

  constructor(private readonly logJobService: LogJobService) {
    this.queueUrl = AWS_SQS_URL || '';
    aws.config.update({
      region: AWS_SQS_REGION || 'ap-southeast-1',
      accessKeyId: AWS_SQS_ACCESS_KEY_ID || '',
      secretAccessKey: AWS_SQS_SECRET_ACCESS_KEY || '',
    });
    const sqs = new aws.SQS({
      apiVersion: AWS_API_VERSION || '2023-03-09',
    });
    this.sqs = sqs;
  }

  sendMessage(
    message: {
      message: string;
      data: any;
    },
    type?: string,
    delaySeconds = 0,
  ) {
    if (this.sqs) {
      return new Promise(async (resolve, reject) => {
        try {
          const messageBody = {
            ...message,
            type: type,
          };
          const params = {
            DelaySeconds: delaySeconds,
            MessageBody: JSON.stringify(messageBody),
            QueueUrl: this.queueUrl,
          };

          // const job = await this.logJobService.checkJobRunning(type);
          // if (job && type === NSSQSS.EMessageType.OnProdBatchParamaters) {
          // if (job) {
          //   // console.log('Job already running');
          //   resolve(new Error('Job already running'));
          // } else {
          this.sqs.sendMessage(params, async (err, data) => {
            if (err) {
              // console.log('Error', err);
              console.log('Error sending message to SQS');
              // reject(err);
            } else {
              // console.log('SQS SendMessage Success!', data.MessageId);
              // Kiểm tra xem có job tương tự đang running không.

              await this.logJobService.createLogJob({
                message: message.message,
                status: NSSQSS.EStatus.Running,
                type: type,
                messageSNSId: data.MessageId,
                metadata: message?.data,
              });
              resolve(data);
            }
          });
          // }
        } catch (error) {
          console.error('Error sending message to SQS');
          // reject(error);
        }
      });
    }
  }
}
