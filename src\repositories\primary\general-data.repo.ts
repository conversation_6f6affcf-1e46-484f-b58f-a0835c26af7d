import { EntityRepository } from 'typeorm';
import { PrimaryRepo } from '../primary.repo';
import { GeneralDataDetailEntity, GeneralDataEntity } from '~/entities/primary';

@EntityRepository(GeneralDataEntity)
export class GeneralDataRepo extends PrimaryRepo<GeneralDataEntity> {}

@EntityRepository(GeneralDataDetailEntity)
export class GeneralDataDetailRepo extends PrimaryRepo<GeneralDataDetailEntity> {}
