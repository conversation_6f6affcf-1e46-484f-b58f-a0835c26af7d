import { NestFactory } from '@nestjs/core';
import { ApiProperty } from '@nestjs/swagger';
import { APIGatewayProxyEvent, APIGatewayProxyResult, Handler } from 'aws-lambda';
import { IsUUID } from 'class-validator';
import { Column, PrimaryGeneratedColumn } from 'typeorm';
import { setupTransactionContext } from '~/@core/decorator';
import { AppModule } from '~/app.module';
import { CustomError, ICustomError, LambdaError } from '~/common/constants/Error.constant';
import { UserActiveStatus } from '~/dto/user.dto';
import { UserEntity } from '~/entities/primary/user.entity';
import { UserService } from '~/x-modules/admin/services';

// Constants
const CORS_HEADERS = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Credentials': true,
  'Content-Type': 'application/json',
};

export interface IFilterGetUserList {
  employeeCode?: string;
  fullName?: string;
  email?: string;
  isActive?: UserActiveStatus;
  page?: number;
  limit?: number;
}

export type ICreateUserRequest = Pick<
  UserEntity,
  | 'employeeCode'
  | 'fullName'
  | 'email'
  | 'phone'
  | 'isActive'
  | 'validFrom'
  | 'validTo'
  | 'productionAreaId'
  | 'permissionId'
  | 'defaultRoute'
  | 'hanetSyncStatus'
  | 'hanetSyncDate'
> & { imageUrl?: string; avatarFile?: Express.Multer.File };

export type IUpdateUserRequest = ICreateUserRequest;

// Singleton pattern cho app instance
class AppInstance {
  private static instance: any = null;

  static async getInstance() {
    if (!AppInstance.instance) {
      try {
        const app = await NestFactory.createApplicationContext(AppModule);
        setupTransactionContext();
        await app.init();
        AppInstance.instance = app;
      } catch (error) {
        console.error('Failed to initialize app:', error);
        throw LambdaError.INTERNAL_ERROR();
      }
    }
    return AppInstance.instance;
  }
}

// Error handler
const handleError = (error: ICustomError): APIGatewayProxyResult => {
  console.error('Error occurred:', error);
  return {
    statusCode: 400,
    headers: CORS_HEADERS,
    body: JSON.stringify(error),
  };
};

// Main handler wrapper
const handler = async <T>(func: () => Promise<T>): Promise<APIGatewayProxyResult> => {
  try {
    const result = await func();
    return {
      statusCode: 200,
      headers: CORS_HEADERS,
      body: JSON.stringify(result),
    };
  } catch (error) {
    if (error instanceof CustomError) {
      return handleError(error);
    }
    return handleError(LambdaError.INTERNAL_ERROR());
  }
};

// Lambda handlers
export const getUserList: Handler = async (
  event: APIGatewayProxyEvent,
): Promise<APIGatewayProxyResult> => {
  const app = await AppInstance.getInstance();
  try {
    const userService = app.get(UserService);
    const params = event.queryStringParameters as unknown as IFilterGetUserList;
    const result = await userService.getUserList(params);

    return {
      statusCode: 200,
      body: JSON.stringify({
        data: result.items,
        pagination: {
          total: result.total,
          page: result.page,
          limit: result.limit,
          totalPages: result.totalPages,
        },
      }),
    };
  } catch (error) {
    return {
      statusCode: 500,
      body: JSON.stringify({ message: error.message }),
    };
  }
};

export const getUser: Handler = async (
  event: APIGatewayProxyEvent,
): Promise<APIGatewayProxyResult> => {
  const app = await AppInstance.getInstance();
  try {
    const userService = app.get(UserService);
    const id = event.pathParameters?.id;
    const result = await userService.getUser(id!);

    if (!result) {
      return {
        statusCode: 404,
        body: JSON.stringify({ message: 'User không tồn tại!' }),
      };
    }

    return {
      statusCode: 200,
      body: JSON.stringify({ data: result }),
    };
  } catch (error) {
    return {
      statusCode: 500,
      body: JSON.stringify({ message: error.message }),
    };
  }
};

export const createUser: Handler = async (
  event: APIGatewayProxyEvent,
): Promise<APIGatewayProxyResult> => {
  const app = await AppInstance.getInstance();
  try {
    const userService = app.get(UserService);
    const user = JSON.parse(event.body || '{}') as ICreateUserRequest;
    await userService.createUser(user);

    return {
      statusCode: 200,
      body: JSON.stringify({ message: 'success' }),
    };
  } catch (error) {
    return {
      statusCode: 500,
      body: JSON.stringify({ message: error.message }),
    };
  }
};

export const updateUser: Handler = async (
  event: APIGatewayProxyEvent,
): Promise<APIGatewayProxyResult> => {
  const app = await AppInstance.getInstance();
  try {
    const userService = app.get(UserService);
    const id = event.pathParameters?.id;
    const updateUser = JSON.parse(event.body || '{}') as IUpdateUserRequest;

    const user = await userService.getUser(id!);
    if (!user) {
      return {
        statusCode: 404,
        body: JSON.stringify({ message: 'User không tồn tại!' }),
      };
    }

    await userService.updateUser(user, updateUser);

    return {
      statusCode: 200,
      body: JSON.stringify({ message: 'success' }),
    };
  } catch (error) {
    return {
      statusCode: 500,
      body: JSON.stringify({ message: error.message }),
    };
  }
};
