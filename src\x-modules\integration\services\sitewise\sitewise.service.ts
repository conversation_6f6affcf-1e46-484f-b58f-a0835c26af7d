import { Injectable } from '@nestjs/common';
import { BindRepo } from '~/@core/decorator';
import { MesDxMeasurementRepo, MesDxMetricsRepo, MesDxTransformRepo } from '~/repositories/scada';
import { ListMeasurementReq, ListMetricsReq, ListTransformReq } from '~/dto/sitewise.dto';
import { SocketGateway } from '../socket.gateway';
import { SitewisePropertiesRepo } from '~/repositories/primary/sitewise.repo';
import { MachineParameterRepo } from '~/repositories/primary';
import { ProdBatchStatusService } from '~/x-modules/integration/services/prod-batch-status';
import { UtilityMetersRepo } from '~/repositories/primary';
import { NSSocket } from '~/common/enums';

@Injectable()
export class SitewiseService {
  constructor(
    private socketGateway: SocketGateway,
    private readonly prodBatchStatusService: ProdBatchStatusService,
  ) {}

  @BindRepo(MesDxMeasurementRepo)
  private mesDxMeasurementRepo: MesDxMeasurementRepo;
  @BindRepo(MesDxMetricsRepo)
  private mesDxMetricsRepo: MesDxMetricsRepo;
  @BindRepo(MesDxTransformRepo)
  private mesDxTransformRepo: MesDxTransformRepo;
  @BindRepo(SitewisePropertiesRepo)
  private sitewisePropertiesRepo: SitewisePropertiesRepo;
  @BindRepo(MachineParameterRepo)
  private machineParameterRepo: MachineParameterRepo;
  @BindRepo(UtilityMetersRepo)
  private utilityMetersRepo: UtilityMetersRepo;

  // Handle logic
  private async processData(data: any) {
    return {
      value: data.pValue !== null && data.pValue !== undefined ? data.pValue.toString() : null,
      timestamp: data.pTimestamp,
      gatewayName: '',
      gatewayId: '',
      datasourceName: '',
      datasourceId: '',
      modelName: '',
      modelId: '',
      assetName: '',
      assetId: data.pAssetId,
      datetime: new Date(data.pTimestamp * 1000).toISOString(),
    };
  }

  private async handleMesDxMeasurement(body: ListMeasurementReq) {
    try {
      this.socketGateway.emitMonitorLineDashboard(body?.assetId, body?.measurementId);
      this.socketGateway.emitRejectionMonitoring(body?.assetId, body?.measurementId);
      return await this.mesDxMeasurementRepo.save({ ...body });
    } catch (error) {
      console.log(error, 'error');
    }
  }

  private async handleMesDxMetrics(body: ListMetricsReq) {
    try {
      const data: any = { ...body };
      this.socketGateway.emitMonitorLineDashboard(
        data?.assetId,
        data?.metricId,
        NSSocket.EType.METRIC,
        data?.value,
      );
      this.socketGateway.emitUtilityMonitoring(data?.assetId, data?.metricId);
      return await this.mesDxMetricsRepo.save({ ...body });
    } catch (error) {
      console.log(error, 'error');
    }
  }

  private async handleMesDxTransform(body: ListTransformReq) {
    try {
      await this.prodBatchStatusService.createProdBatchStatus({ ...body });
      return await this.mesDxTransformRepo.save({ ...body });
    } catch (error) {
      console.log(error, 'error');
    }
  }

  async createMesDXSitewise(data: any) {
    try {
      const pPropertyId = data?.payload?.propertyId ? data.payload.propertyId : '';
      const pAssetId = data?.payload?.assetId ? data?.payload?.assetId : '';
      // Tìm thêm dữ liệu được khai báo trong utility_meter table
      const sitewisePropertiesData = await this.machineParameterRepo.findOne({
        where: { iotsitewisePropertyId: pPropertyId, iotsitewiseAssetId: pAssetId },
      });
      // Tìm thêm dữ liệu được khai báo trong utility_meter table
      const utilityMeterData = await this.utilityMetersRepo.findOne({
        where: [
          { measurementId: pPropertyId, assetId: pAssetId },
          { metricId: pPropertyId, assetId: pAssetId },
        ],
      });

      // Nếu cả 2 table không khai báo thì không xử lý tiếp
      if (!sitewisePropertiesData && !utilityMeterData) return;

      // getTypeSitewise = '3' => 'TRANSFORM' / getTypeSitewise = '2' => 'METRIC' / getTypeSitewise = '1' => 'MEASUREMENT'
      let getTypeSitewise = '';
      if (sitewisePropertiesData) {
        getTypeSitewise = sitewisePropertiesData?.iotsitewisePropertyTypeCode;
      } else if (utilityMeterData && !sitewisePropertiesData) {
        getTypeSitewise =
          utilityMeterData?.measurementId === pPropertyId
            ? '1'
            : utilityMeterData?.metricId === pPropertyId
              ? '2'
              : '';
      }

      let newData = data?.payload?.values[data?.payload?.values?.length - 1];
      if (newData) {
        newData = newData?.value?.value ? newData?.value : newData;
      }

      const pTimestamp = newData?.timestamp?.timeInSeconds ? newData.timestamp.timeInSeconds : '';
      const pValue =
        newData?.value?.doubleValue !== undefined && newData?.value?.doubleValue !== null
          ? newData.value.doubleValue
          : newData?.value?.integerValue !== undefined && newData?.value?.integerValue !== null
            ? newData.value.integerValue
            : newData?.value?.stringValue !== undefined && newData?.value?.stringValue !== null
              ? newData.value.stringValue
              : newData?.value?.booleanValue !== undefined && newData?.value?.booleanValue !== null
                ? newData.value.booleanValue
                : newData.value;

      if (getTypeSitewise === '3') {
        const dataNew = await this.processData({ pAssetId, pTimestamp, pValue });
        await this.handleMesDxTransform({
          ...dataNew,
          transformName: '',
          transformId: pPropertyId,
        });
      } else if (getTypeSitewise === '2') {
        const dataNew = await this.processData({ pAssetId, pTimestamp, pValue });
        await this.handleMesDxMetrics({
          ...dataNew,
          metricName: '',
          metricId: pPropertyId,
        });
      } else if (getTypeSitewise === '1') {
        const dataNew = await this.processData({ pAssetId, pTimestamp, pValue });
        await this.handleMesDxMeasurement({
          ...dataNew,
          measurementName: '',
          measurementId: pPropertyId,
        });
      }
    } catch (error) {
      console.log(error, 'error');
    }
  }
}
