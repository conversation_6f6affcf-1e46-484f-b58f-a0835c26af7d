import { Body, Controller, Get, Param, Post, Put, Query, UseGuards } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { ProcessAreaActiveStatus } from '~/dto/production-area.dto';
import { ProductionAreaService } from '../../services/system-configuration/production-area.service';
import {
  IProductionAreaFilter,
  IProductionAreaSubmitData,
} from '../../services/system-configuration/production-area.service';
import { Roles } from '~/@core/decorator/roles.decorator';
import { RoleGuard } from '~/@systems/guard/role.guard';

@ApiTags('Production Area')
@Controller()
export class ProductionAreaController {
  constructor(private readonly productionAreaService: ProductionAreaService) {}

  @Get('/production-areas')
  @Roles('/system-configuration/production-area', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: '<PERSON><PERSON><PERSON> danh sách khu vực sản xuất' })
  async getProductionAreaList(@Query() query: IProductionAreaFilter) {
    const result = await this.productionAreaService.getProductionAreaList(query);
    return {
      data: result.items,
      pagination: {
        total: result.total,
        page: result.page,
        limit: result.limit,
        totalPages: result.totalPages,
      },
    };
  }

  @Get('/production-areas/:id')
  @Roles('/system-configuration/production-area', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Lấy thông tin chi tiết khu vực sản xuất' })
  async getProductionAreaInfo(@Param('id') id: string) {
    const result = await this.productionAreaService.getProductionAreaInfo(id);
    return { data: result };
  }

  @Get('/production-areas/:id/details')
  @Roles('/system-configuration/production-area', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Lấy danh sách chi tiết khu vực sản xuất' })
  async getProductionAreaDetails(
    @Param('id') id: string,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
  ) {
    const result = await this.productionAreaService.getProductionAreaDetails(id, page, limit);
    return {
      data: result.items,
      pagination: {
        total: result.total,
        page: result.page,
        limit: result.limit,
        totalPages: result.totalPages,
      },
    };
  }

  @Get('/production-areas-dump/all')
  @Roles('/system-configuration/user', 'View')
  @Roles('/system-configuration/production-area', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Lấy danh sách tất cả khu vực sản xuất' })
  async getAllProductionAreas() {
    const result = await this.productionAreaService.getAllProductionAreas();
    return {
      data: result.items,
    };
  }

  @Get('/production-areas-dump/factories')
  @Roles('/system-configuration/production-area', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Lấy danh sách nhà máy' })
  async getFactoryList() {
    const result = await this.productionAreaService.getFactoryList();
    return { data: result, total: result.length };
  }

  @Get('/production-areas-dump/general-data-details')
  @Roles('/system-configuration/production-area', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Lấy danh sách general data detail' })
  async getAllGeneralDataDetail() {
    const result = await this.productionAreaService.getAllGeneralDataDetail();
    return { data: result, total: result.length };
  }

  @Get('/production-areas-dump/process-areas/:factoryId')
  @Roles('/system-configuration/production-area', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Lấy danh sách khu vực sản xuất theo nhà máy' })
  async getProcessAreaByFactoryId(
    @Param('factoryId') factoryId: string,
    @Query('processAreaCode') processAreaCode?: string,
    @Query('processAreaName') processAreaName?: string,
    @Query('lineId') lineId?: string,
    @Query('activeStatus') activeStatus?: ProcessAreaActiveStatus,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
  ) {
    const result = await this.productionAreaService.getProcessAreaByFactoryId(
      factoryId,
      processAreaCode,
      processAreaName,
      lineId,
      activeStatus,
      page,
      limit,
    );
    return {
      data: result.items,
      pagination: {
        total: result.total,
        page: result.page,
        limit: result.limit,
        totalPages: result.totalPages,
      },
    };
  }

  @Get('/production-areas-dump/lines/:factoryId')
  @Roles('/system-configuration/production-area', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Lấy danh sách line theo nhà máy' })
  async getLinesByFactoryId(@Param('factoryId') factoryId: string) {
    const result = await this.productionAreaService.getLinesByFactoryId(factoryId);
    return { data: result, total: result.length };
  }

  @Post('/production-areas')
  @Roles('/system-configuration/production-area', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Tạo mới khu vực sản xuất' })
  async createProductionArea(@Body() data: IProductionAreaSubmitData) {
    const result = await this.productionAreaService.createProductionArea(data);
    return {
      message: 'success',
      data: result,
    };
  }

  @Put('/production-areas/:id')
  @Roles('/system-configuration/production-area', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Cập nhật khu vực sản xuất' })
  async updateProductionArea(@Param('id') id: string, @Body() data: IProductionAreaSubmitData) {
    const result = await this.productionAreaService.updateProductionArea(id, data);
    return {
      message: 'success',
      data: result,
    };
  }
}
