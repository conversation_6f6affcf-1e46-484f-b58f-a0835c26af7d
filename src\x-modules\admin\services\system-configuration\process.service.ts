import { Injectable } from '@nestjs/common';
import { ILike, In, LessThanOrEqual, Like, MoreThanOrEqual, Not } from 'typeorm';
import { BindRepo, DefTransaction } from '~/@core/decorator';
import { SuccessResponse } from '~/@systems/utils';
import { ProcessReqDto } from '~/dto/process.dto';
import { MachineEntity, ProcessEntity } from '~/entities/primary';
import {
  GeneralDataDetailRepo,
  MachineRepo,
  ProcessMachineRepo,
  ProcessRepo,
} from '~/repositories/primary';
import { OrganizationUnitRepo } from '~/repositories/primary/organization-unit.repo';
import { adminSessionContext } from '~/x-modules/admin/admin-session.context';
import { OrganizationUnitService } from '~/x-modules/admin/services/system-configuration/organization-unit.service';

@Injectable()
export class ProcessService {
  constructor(private readonly organizationUnitService: OrganizationUnitService) {}

  @BindRepo(ProcessMachineRepo)
  private readonly processMachineRepo: ProcessMachineRepo;

  @BindRepo(ProcessRepo)
  private readonly repo: ProcessRepo;

  @BindRepo(MachineRepo)
  private readonly machineRepo: MachineRepo;

  @BindRepo(OrganizationUnitRepo)
  private readonly organizationUnitRepo: OrganizationUnitRepo;

  @BindRepo(GeneralDataDetailRepo)
  private readonly generalDataDetailRepo: GeneralDataDetailRepo;

  async list(params: any) {
    const {
      code,
      name,
      organizationUnitId,
      category,
      isActive,
      createdDateFrom,
      createdDateTo,
      page = 1,
      size = 10,
    } = params;

    const offset = (page - 1) * size;
    const limit = size;
    const whereConditions: any = {};

    if (code) whereConditions.code = ILike(`%${code}%`);
    if (name) whereConditions.name = ILike(`%${name}%`);
    if (organizationUnitId) whereConditions.organization = { id: organizationUnitId };
    if (category) whereConditions.category = category;
    if (isActive !== undefined) whereConditions.isActive = isActive === 'true';
    if (createdDateFrom) whereConditions.createdDate = MoreThanOrEqual(createdDateFrom);
    if (createdDateTo) whereConditions.createdDate = LessThanOrEqual(createdDateTo);

    const processes = await this.repo.find({
      where: whereConditions,
      relations: ['organization'],
      order: { isActive: 'DESC', createdDate: 'DESC' },
      skip: offset,
      take: limit,
    });

    const total = await this.repo.count({ where: whereConditions });

    const processesWithHierarchy = await Promise.all(
      processes.map(async (process) => {
        const [categoryDetail, orgHierarchy] = await Promise.all([
          process.category
            ? this.generalDataDetailRepo.findOne({
                where: { code: process.category },
                select: ['name'],
              })
            : Promise.resolve(null),
          process.organization
            ? (await this.organizationUnitService.getHierarchyFromProcess(process.organization.id))
                .data
            : Promise.resolve({}),
        ]);

        return {
          ...process,
          categoryName: categoryDetail ? categoryDetail.name : null,
          ...orgHierarchy,
        };
      }),
    );

    return {
      data: processesWithHierarchy,
      total,
      page,
      size,
    };
  }

  @DefTransaction()
  async create(data: ProcessReqDto) {
    try {
      const { userId: userId } = adminSessionContext;

      if (!data.code) {
        throw new Error('Mã quy trình không được để trống.');
      }

      // Kiểm tra xem mã quy trình đã tồn tại hay chưa
      const checkExist = await this.repo.findOne({ where: { code: data.code } });
      if (checkExist) {
        throw new Error('Mã quy trình đã tồn tại. Vui lòng kiểm tra lại thông tin!');
      }

      // Tạo mới quy trình
      const entity = new ProcessEntity();
      entity.code = data.code.toUpperCase();
      entity.name = data.name;
      entity.description = data.description;
      entity.ipAddress = data.ipAddress;
      entity.gatewayProtocol = data.gatewayProtocol;
      entity.isActive = data.isActive;
      entity.note = data.note;
      entity.createdBy = userId;
      entity.updatedBy = userId;
      entity.category = data.category;
      entity.organizationUnitId = data.organizationUnitId;
      entity.outputCalculationMethodCode = data.outputCalculationMethodCode;

      const savedProcess = await this.repo.save(entity);

      // Liên kết máy móc vào bảng trung gian process_machine
      const machineIds = data.machineIds || [];
      if (machineIds.length) {
        // Kiểm tra xem có máy nào đã được gán (`assign = true`)
        const assignedMachines = await this.processMachineRepo.find({
          where: { machineId: In(machineIds), assign: true },
        });

        if (assignedMachines.length) {
          throw new Error(
            'Một hoặc nhiều máy đã được gán cho quy trình khác. Vui lòng kiểm tra lại!',
          );
        }

        // Nếu không có máy nào bị gán, tiếp tục thêm mới vào bảng process_machine
        const machines = await this.machineRepo.findByIds(machineIds);
        if (machines.length !== machineIds.length) {
          throw new Error('Một hoặc nhiều máy không tồn tại!');
        }

        const processMachines = machines.map((machine) => {
          return this.processMachineRepo.create({
            processId: savedProcess.id,
            machineId: machine.id,
            assign: true, // Đánh dấu máy đã được gán
          });
        });

        await this.processMachineRepo.save(processMachines);
      }

      return new SuccessResponse();
    } catch (error) {
      throw new Error(error.message || 'Không thể tạo quy trình. Vui lòng thử lại!');
    }
  }

  @DefTransaction()
  async update(id: string, data: ProcessReqDto) {
    try {
      const { userId: userId } = adminSessionContext;

      const existingProcess = await this.repo.findOne({
        where: { id },
        relations: ['processMachines'],
      });

      if (!existingProcess) {
        throw new Error('Quy trình không tồn tại. Vui lòng kiểm tra lại mã quy trình!');
      }

      // Cập nhật thông tin quy trình
      Object.assign(existingProcess, {
        code: data.code ? data.code.toUpperCase() : existingProcess.code,
        name: data.name || existingProcess.name,
        description: data.description || existingProcess.description,
        ipAddress: data.ipAddress || existingProcess.ipAddress,
        gatewayProtocol: data.gatewayProtocol || existingProcess.gatewayProtocol,
        isActive: data.isActive !== undefined ? data.isActive : existingProcess.isActive,
        note: data.note || existingProcess.note,
        category: data.category || existingProcess.category,
        organizationUnitId: data.organizationUnitId || existingProcess.organizationUnitId,
        updatedBy: userId,
        outputCalculationMethodCode:
          data.outputCalculationMethodCode || existingProcess.outputCalculationMethodCode,
      });

      await this.repo.save(existingProcess);

      // Lấy danh sách machineId hiện tại của process
      const currentProcessMachines = await this.processMachineRepo.find({
        where: { processId: id },
      });

      const currentMachineIds = currentProcessMachines.map((pm) => pm.machineId);

      // Lấy danh sách machineId mới từ request
      const newMachineIds = data.machineIds || [];

      // Lọc ra các machineId chưa tồn tại trong process
      const machinesToAdd = newMachineIds.filter(
        (machineId) => !currentMachineIds.includes(machineId),
      );

      console.log(machinesToAdd, newMachineIds, currentMachineIds);

      if (machinesToAdd.length > 0) {
        const assignedMachines = await this.processMachineRepo.find({
          where: { machineId: In(machinesToAdd), assign: true },
        });

        const assignedMachineIds = assignedMachines.map((am) => am.machineId);
        const validMachinesToAdd = machinesToAdd.filter((id) => !assignedMachineIds.includes(id));

        if (validMachinesToAdd.length > 0) {
          const machines = await this.machineRepo.findByIds(validMachinesToAdd);
          if (machines.length !== validMachinesToAdd.length) {
            throw new Error('Một hoặc nhiều máy không tồn tại!');
          }
          const newProcessMachines = machines.map((machine) => {
            return this.processMachineRepo.create({
              processId: id,
              machineId: machine.id,
              assign: true,
            });
          });

          await this.processMachineRepo.save(newProcessMachines);
        }
      }

      return new SuccessResponse();
    } catch (error) {
      throw new Error(error.message || 'Không thể cập nhật quy trình. Vui lòng thử lại!');
    }
  }

  async delete(id: string) {
    try {
      const existingProcess = await this.repo.findOne({ where: { id } });
      if (!existingProcess) {
        throw new Error('Quy trình không tồn tại. Vui lòng kiểm tra lại mã quy trình!');
      }

      await this.repo.delete({ id });
      return new SuccessResponse();
    } catch (error) {
      throw new Error(error.message || 'Không thể xóa quy trình. Vui lòng thử lại!');
    }
  }

  async findById(id: string): Promise<any> {
    try {
      const process = await this.repo.findOne({
        where: { id },
        relations: ['organization', 'processMachines', 'processMachines.machine'],
      });

      if (!process) {
        throw new Error('Quy trình không tồn tại. Vui lòng kiểm tra lại ID!');
      }

      const machines = (await process.processMachines).map((pm) => ({
        ...pm.machine,
        assign: pm.assign,
      }));

      return {
        ...process,
        machines,
      };
    } catch (error) {
      throw new Error(error.message || 'Không thể tìm quy trình. Vui lòng thử lại!');
    }
  }

  async getMachineList() {
    const result = await this.machineRepo.find({
      order: { isActive: 'DESC', createdDate: 'DESC' },
    });
    return result;
  }

  async listMachines(params: any) {
    const { code, name, isActive, processId } = params;

    const whereConditions: any = {};

    if (code) whereConditions.code = ILike(`%${code}%`);
    if (name) whereConditions.name = ILike(`%${name}%`);
    if (isActive !== undefined) whereConditions.isActive = isActive === 'true';

    // Lấy danh sách machineId đã được assign vào bất kỳ process nào
    const assignedMachineIds = (
      await this.processMachineRepo.find({
        where: { assign: true },
        select: ['machineId'],
      })
    ).map((pm) => pm.machineId);

    let excludedMachineIds = assignedMachineIds;

    // Nếu có processId, loại ra những máy đã thuộc processId đó
    if (processId) {
      const machinesInProcess = (
        await this.processMachineRepo.find({
          where: { processId },
          select: ['machineId'],
        })
      ).map((pm) => pm.machineId);

      excludedMachineIds = [...new Set([...assignedMachineIds, ...machinesInProcess])];
    }

    // Lọc danh sách máy chưa được gán vào process hoặc processId được chỉ định
    const machines = await this.machineRepo.find({
      where: [{ ...whereConditions, id: Not(In(excludedMachineIds)) }],
      order: { isActive: 'DESC', createdDate: 'DESC' },
    });

    const total = await this.machineRepo.count({
      where: [{ ...whereConditions, id: Not(In(excludedMachineIds)) }],
    });

    return {
      data: machines,
      total,
    };
  }

  async getForOrganizationUnits() {
    const result = await this.organizationUnitRepo.find({
      order: { isActive: 'DESC', createdDate: 'DESC' },
    });
    return result;
  }

  async updateMachineAssign(processId: string, machineId: string, assign: boolean) {
    try {
      const processMachine = await this.processMachineRepo.findOne({
        where: { processId, machineId },
      });

      if (!processMachine) {
        throw new Error('Máy không tồn tại trong quy trình này.');
      }

      if (assign) {
        const assignedMachine = await this.processMachineRepo.findOne({
          where: { machineId, assign: true, processId: Not(processId) },
        });

        if (assignedMachine) {
          throw new Error('Máy này đã được assign vào một process khác.');
        }
      }

      processMachine.assign = assign;
      await this.processMachineRepo.save(processMachine);

      return { success: true, message: 'Cập nhật trạng thái assign thành công.' };
    } catch (error) {
      throw new Error(error.message || 'Không thể cập nhật assign.');
    }
  }

  async find(data: { organizationUnitId: string }) {
    const whereCon: any = { isActive: true };
    if (data.organizationUnitId) {
      const lstFactory = await this.organizationUnitRepo.find({
        where: { parentId: data.organizationUnitId },
        select: ['id'],
      });
      const lstLine = await this.organizationUnitRepo.find({
        where: { parentId: In(lstFactory.map((u) => u.id)) },
        select: ['id'],
      });
      const lstProcessArea = await this.organizationUnitRepo.find({
        where: { parentId: In(lstLine.map((u) => u.id)) },
        select: ['id'],
      });
      whereCon.organizationUnitId = In(lstProcessArea.map((u) => u.id));
    }
    return await this.repo.find({
      where: whereCon,
      order: { name: 'ASC' },
    });
  }
}
