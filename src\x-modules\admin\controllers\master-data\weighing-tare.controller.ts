import { Body, Param, ParseArrayPipe, ParseUUIDPipe, Query, UseGuards } from '@nestjs/common';
import { ApiBody, ApiOperation, ApiTags } from '@nestjs/swagger';
import { DefController, DefDelete, DefGet, DefPost, DefPut } from '~/@core/decorator';
import { Roles } from '~/@core/decorator/roles.decorator';
import { RoleGuard } from '~/@systems/guard/role.guard';

import { ListWeighingTareReq, WeighingTareReq } from '~/dto/weighing-tare.dto';
import { WeighingTareService } from '~/x-modules/admin/services/master-data/weighing-tare.service';

@DefController('weighing-tare')
export class WeighingTareController {
  constructor(private readonly weighingTareService: WeighingTareService) {}

  @ApiOperation({ summary: 'Tạo máy mới', description: 'Tạo một máy với thông tin đầu vào.' })
  @DefPost('create')
  @Roles('/master-data/weighing-tare', 'View')
  @UseGuards(RoleGuard)
  async create(@Body() body: WeighingTareReq) {
    return this.weighingTareService.create(body);
  }

  @ApiOperation({
    summary: 'Danh sách Weighing Tare',
    description: 'Lấy danh sách các máy theo tiêu chí tìm kiếm.',
  })
  @DefGet('')
  @Roles('/master-data/weighing-tare', 'View')
  @UseGuards(RoleGuard)
  async list(@Query() params: ListWeighingTareReq) {
    return this.weighingTareService.list(params);
  }
  @DefGet('get-code')
  @Roles('/master-data/weighing-tare', 'View')
  @UseGuards(RoleGuard)
  async getCode() {
    return await this.weighingTareService.getCode();
  }

  @DefGet('load-type-items')
  @Roles('/master-data/weighing-tare', 'View')
  @UseGuards(RoleGuard)
  async getSelectBoxTypeItems(): Promise<any> {
    return await this.weighingTareService.getLstTypeItems();
  }

  @DefGet('by-code')
  @Roles('/master-data/weighing-tare', 'View')
  @UseGuards(RoleGuard)
  async getSelectBoxGeneralData(@Query('code') code: string): Promise<any> {
    return await this.weighingTareService.getLstProductionCategory(code);
  }

  @ApiOperation({
    summary: 'Chi tiết máy',
    description: 'Lấy thông tin chi tiết của một máy theo UUID.',
  })
  @DefGet(':id')
  @Roles('/master-data/weighing-tare', 'View')
  @UseGuards(RoleGuard)
  async detail(@Param('id') id: string) {
    return this.weighingTareService.detail(id);
  }

  @ApiOperation({
    summary: 'Cập nhật weighing tare',
    description: 'Cập nhật thông tin của weighing tare.',
  })
  @DefPut('update/:id')
  @Roles('/master-data/weighing-tare', 'View')
  @UseGuards(RoleGuard)
  async update(@Param('id', new ParseUUIDPipe()) id: string, @Body() body: WeighingTareReq) {
    return this.weighingTareService.update(id, body);
  }
}
