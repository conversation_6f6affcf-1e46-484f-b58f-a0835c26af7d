import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import moment from 'moment';
import { In, Like, Raw } from 'typeorm';
import { BindRepo } from '~/@core/decorator';
import { CreateKPIDto, KpiEditDto, KpiEditResponseDto, KpiPaginationDto } from '~/dto/kpi.dto';
import { KpiEntity } from '~/entities/primary/kpi.entity';
import {
  GeneralDataDetailRepo,
  GeneralDataRepo,
  KpiSetDetailRepo,
  MaterialGroupDetailRepo,
  MaterialGroupHeaderRepo,
} from '~/repositories/primary';
import { KpiRepo } from '~/repositories/primary/kpi.repo';
@Injectable()
export class KPIService {
  constructor() {}

  @BindRepo(KpiRepo)
  private repo: KpiRepo;
  @BindRepo(GeneralDataRepo)
  private generalDataRepo: GeneralDataRepo;
  @BindRepo(GeneralDataDetailRepo)
  private generalDataDetailRepo: GeneralDataDetailRepo;
  @BindRepo(KpiSetDetailRepo)
  private kpiSetDetailRepo: KpiSetDetailRepo;
  @BindRepo(MaterialGroupHeaderRepo)
  private materialGroupHeaderRepo: MaterialGroupHeaderRepo;
  @BindRepo(MaterialGroupDetailRepo)
  private materialGroupDetailRepo: MaterialGroupDetailRepo;

  async getLstMaterialGroup(): Promise<any[]> {
    let res = [];
    const lstMaterialGroup = await this.materialGroupHeaderRepo.find({
      where: {
        active: true,
      },
    });
    if (lstMaterialGroup.length > 0) {
      res = lstMaterialGroup;
    }
    return res;
  }

  async getLstProductionCategory(): Promise<any[]> {
    let res = [];
    const lstProdCate = await this.generalDataRepo.find({
      where: {
        code: 'PRODUCTION_CATEGORY',
        isActive: true,
      },
    });
    if (lstProdCate.length > 0) {
      const generalIds = lstProdCate.map((item) => item.id);
      const details = await this.generalDataDetailRepo.find({
        where: {
          generalId: In(generalIds),
          isActive: true,
        },
        order: { createdDate: 'DESC' },
      });
      res = details;
    }
    return res;
  }

  async getLstKpiGroup(): Promise<any[]> {
    let res = [];
    const lstProdCate = await this.generalDataRepo.find({
      where: {
        code: 'KPI_GROUP',
        isActive: true,
      },
    });
    if (lstProdCate.length > 0) {
      const generalIds = lstProdCate.map((item) => item.id);
      const details = await this.generalDataDetailRepo.find({
        where: {
          generalId: In(generalIds),
          isActive: true,
        },
        order: { createdDate: 'DESC' },
      });
      res = details;
    }
    return res;
  }
  async getLstUnit(): Promise<any[]> {
    let res = [];
    const lstProdCate = await this.generalDataRepo.find({
      where: {
        code: 'UOM',
        isActive: true,
      },
    });
    if (lstProdCate.length > 0) {
      const generalIds = lstProdCate.map((item) => item.id);
      const details = await this.generalDataDetailRepo.find({
        where: {
          generalId: In(generalIds),
          isActive: true,
        },
        order: { createdDate: 'DESC' },
      });
      res = details;
    }
    return res;
  }
  async getLstUtilityType(): Promise<any[]> {
    let res = [];
    const lstProdCate = await this.generalDataRepo.find({
      where: {
        code: 'UTILITY_METER_TYPE',
        isActive: true,
      },
    });
    if (lstProdCate.length > 0) {
      const generalIds = lstProdCate.map((item) => item.id);
      const details = await this.generalDataDetailRepo.find({
        where: {
          generalId: In(generalIds),
          isActive: true,
        },
        order: { createdDate: 'DESC' },
      });
      res = details;
    }
    return res;
  }
  async getLstKpiMethod(): Promise<any[]> {
    let res = [];
    const lstProdCate = await this.generalDataRepo.find({
      where: {
        code: 'KPI_METHOD',
        isActive: true,
      },
    });
    if (lstProdCate.length > 0) {
      const generalIds = lstProdCate.map((item) => item.id);
      const details = await this.generalDataDetailRepo.find({
        where: {
          generalId: In(generalIds),
          isActive: true,
        },
        order: { createdDate: 'DESC' },
      });
      res = details;
    }
    return res;
  }
  async getLstCalType(): Promise<any[]> {
    let res = [];
    const lstProdCate = await this.generalDataRepo.find({
      where: {
        code: 'CAL_TYPE',
        isActive: true,
      },
    });
    if (lstProdCate.length > 0) {
      const generalIds = lstProdCate.map((item) => item.id);
      const details = await this.generalDataDetailRepo.find({
        where: {
          generalId: In(generalIds),
          isActive: true,
        },
        order: { createdDate: 'DESC' },
      });
      res = details;
    }
    return res;
  }
  async getLstInputFrequency(): Promise<any[]> {
    let res = [];
    const lstProdCate = await this.generalDataRepo.find({
      where: {
        code: 'INPUT_FREQUENCY',
        isActive: true,
      },
    });
    if (lstProdCate.length > 0) {
      const generalIds = lstProdCate.map((item) => item.id);
      const details = await this.generalDataDetailRepo.find({
        where: {
          generalId: In(generalIds),
          isActive: true,
        },
        order: { createdDate: 'DESC' },
      });
      res = details;
    }
    return res;
  }
  async getLstCalMethod(): Promise<any[]> {
    let res = [];
    const lstProdCate = await this.generalDataRepo.find({
      where: {
        code: 'CAL_METHOD',
        isActive: true,
      },
    });
    if (lstProdCate.length > 0) {
      const generalIds = lstProdCate.map((item) => item.id);
      const details = await this.generalDataDetailRepo.find({
        where: {
          generalId: In(generalIds),
          isActive: true,
        },
        order: { createdDate: 'DESC' },
      });
      res = details;
    }
    return res;
  }
  async getLstKpiFunction(): Promise<any[]> {
    let res = [];
    const lstProdCate = await this.generalDataRepo.find({
      where: {
        code: 'KPI_FUNCTION',
        isActive: true,
      },
    });
    if (lstProdCate.length > 0) {
      const generalIds = lstProdCate.map((item) => item.id);
      const details = await this.generalDataDetailRepo.find({
        where: {
          generalId: In(generalIds),
          isActive: true,
        },
        order: { createdDate: 'DESC' },
      });
      res = details;
    }
    return res;
  }

  async findOne(id: string): Promise<KpiEditResponseDto | undefined> {
    let isDisable = false;
    let data = await this.repo.findOne(id);
    let rs: any = {};

    if (data) {
      // Kiểm tra sự tồn tại của KpiEntity trong kpiSetDetailRepo
      const kpiSetDetail = await this.kpiSetDetailRepo.findOne({ kpiId: data.id });
      if (kpiSetDetail) {
        isDisable = true;
      }

      const lstCode = [
        'PRODUCTION_CATEGORY',
        'KPI_GROUP',
        'UOM',
        'KPI_METHOD',
        'CAL_TYPE',
        'INPUT_FREQUENCY',
        'CAL_METHOD',
        'KPI_FUNCTION',
      ];
      const lstGeneralData = await this.generalDataRepo.find({
        relations: ['details'],
        where: {
          code: In(lstCode),
        },
      });

      const lstCodeCategory =
        (await lstGeneralData.find((item) => item.code === 'PRODUCTION_CATEGORY')?.details) || [];
      const lstCodeKpiGroup =
        (await lstGeneralData.find((item) => item.code === 'KPI_GROUP')?.details) || [];
      const lstCodeUnit = (await lstGeneralData.find((item) => item.code === 'UOM')?.details) || [];
      const lstCodeKpiMethod =
        (await lstGeneralData.find((item) => item.code === 'KPI_METHOD')?.details) || [];
      const lstCodeCalType =
        (await lstGeneralData.find((item) => item.code === 'CAL_TYPE')?.details) || [];
      const lstCodeCalMethod =
        (await lstGeneralData.find((item) => item.code === 'CAL_METHOD')?.details) || [];
      const lstCodeInputFrequency =
        (await lstGeneralData.find((item) => item.code === 'INPUT_FREQUENCY')?.details) || [];
      const lstCodeKpiFunction =
        (await lstGeneralData.find((item) => item.code === 'KPI_FUNCTION')?.details) || [];

      // Map additional properties to the response
      rs.kpiCategoryName = data.kpiCategory
        ? `${data.kpiCategory} - ${lstCodeCategory.find((i) => i.code === data.kpiCategory)?.name}`
        : '';
      rs.kpiGroupName = data.kpiGroup
        ? `${data.kpiGroup} - ${lstCodeKpiGroup.find((i) => i.code === data.kpiGroup)?.name}`
        : '';
      rs.unitName = data.unit
        ? `${data.unit} - ${lstCodeUnit.find((i) => i.code === data.unit)?.name}`
        : '';
      rs.kpiMethodName = data.kpiMethod
        ? `${data.kpiMethod} - ${lstCodeKpiMethod.find((i) => i.code === data.kpiMethod)?.name}`
        : '';
      rs.autoFunctionName = data.autoFunction
        ? `${data.autoFunction} - ${lstCodeKpiFunction.find((i) => i.code === data.autoFunction)?.name}`
        : '';
      rs.calTypeName = data.calType
        ? `${data.calType} - ${lstCodeCalType.find((i) => i.code === data.calType)?.name}`
        : '';
      rs.calMethodName = data.calMethod
        ? `${data.calMethod} - ${lstCodeCalMethod.find((i) => i.code === data.calMethod)?.name}`
        : '';
      rs.inputFrequencyName = data.inputFrequency
        ? `${data.inputFrequency} - ${lstCodeInputFrequency.find((i) => i.code === data.inputFrequency)?.name}`
        : '';
    }

    rs = { ...rs, ...data, isDisable };
    return rs;
  }

  async create(data: CreateKPIDto): Promise<KpiEntity | undefined> {
    const transformedData: any = { ...data };
    const today = new Date();
    // transformedData.createdBy = 'admin';
    transformedData.createdDate = today;
    // transformedData.updatedBy = 'admin';
    delete transformedData.parameters;
    return this.repo.save(transformedData);
  }

  async update(updateData: KpiEditDto): Promise<KpiEntity | undefined> {
    const kpi = await this.repo.findOne(updateData.id);
    const today = new Date();
    if (!kpi) {
      throw new BadRequestException(`Kpi not found.`);
    }
    const transformedData: any = { ...updateData };
    // transformedData.updatedBy = 'admin';
    transformedData.updatedDate = today;
    delete transformedData.parameters;
    await this.repo.update({ id: transformedData.id }, transformedData);
    return transformedData;
  }

  async delete(id: string): Promise<void> {
    const kpi = await this.repo.findOne(id);
    if (!kpi) {
      throw new NotFoundException(`Kpi not found.`);
    }
    if (kpi) {
      // Kiểm tra sự tồn tại của KpiEntity trong kpiSetDetailRepo
      const kpiSetDetail = await this.kpiSetDetailRepo.findOne({ kpiId: kpi.id });
      if (kpiSetDetail) {
        throw new BadRequestException({
          message: "You can't delete this kpi because it use in kpi set",
        });
      }
    }

    await this.repo.remove(kpi);
  }

  async pagination(params: KpiPaginationDto) {
    const { pageIndex, pageSize, where } = params;
    const whereCon: any = {};
    if (where.kpiCategory) whereCon.kpiCategory = Like(`%${where.kpiCategory}%`);
    if (where.kpiGroup) whereCon.kpiGroup = Like(`%${where.kpiGroup}%`);
    if (where.code) whereCon.code = Like(`%${where.code}%`);
    if (where.shortName) whereCon.shortName = Like(`%${where.shortName}%`);
    if (where.createdBy) whereCon.shortName = Like(`%${where.createdBy}%`);
    if (where.updatedBy) whereCon.shortName = Like(`%${where.updatedBy}%`);
    if (where.longName) whereCon.longName = Like(`%${where.longName}%`);
    if (where.unit) whereCon.unit = Like(`%${where.unit}%`);
    if (where.kpiMethod) whereCon.kpiMethod = Like(`%${where.kpiMethod}%`);
    if (where.autoFunction) whereCon.autoFunction = Like(`%${where.autoFunction}%`);
    if (where.parameters) whereCon.parameters = Like(`%${where.parameters}%`);
    if (where.calType) whereCon.calType = Like(`%${where.calType}%`);
    if (where.calMethod) whereCon.calMethod = Like(`%${where.calMethod}%`);
    if (where.inputFrequency) whereCon.inputFrequency = Like(`%${where.inputFrequency}%`);
    if (where?.createdDate) {
      const createdDateStr = moment(where.createdDate, 'YYYY-MM-DD').format('YYYY-MM-DD');
      whereCon.createdDate = Raw((alias) => `to_char(${alias}, 'YYYY-MM-DD') = :createdDate`, {
        createdDate: createdDateStr,
      });
    }
    if (where?.updatedDate) {
      const updatedDateStr = moment(where.updatedDate, 'YYYY-MM-DD').format('YYYY-MM-DD');
      whereCon.updatedDate = Raw((alias) => `to_char(${alias}, 'YYYY-MM-DD') = :updatedDate`, {
        updatedDate: updatedDateStr,
      });
    }
    if (where?.status !== undefined && where.status !== null) {
      whereCon.status = where.status;
    }
    const [data, total]: any = await this.repo.findAndCount({
      where: whereCon,
      order: { createdDate: 'DESC' },
      skip: (pageIndex - 1) * pageSize,
      take: pageSize,
    });
    const lstCode = [
      'PRODUCTION_CATEGORY',
      'KPI_GROUP',
      'UOM',
      'KPI_METHOD',
      'CAL_TYPE',
      'INPUT_FREQUENCY',
      'CAL_METHOD',
      'KPI_FUNCTION',
    ];
    const lstGeneralData = await this.generalDataRepo.find({
      relations: ['details'],
      where: {
        code: In(lstCode),
      },
    });

    const lstCodeCategory: any[] =
      (await lstGeneralData.find((item) => item.code === 'PRODUCTION_CATEGORY').details) || [];
    const lstCodeKpiGroup =
      (await lstGeneralData.find((item) => item.code === 'KPI_GROUP').details) || [];
    const lstCodeUnit = (await lstGeneralData.find((item) => item.code === 'UOM').details) || [];
    const lstCodeKpiMethod =
      (await lstGeneralData.find((item) => item.code === 'KPI_METHOD').details) || [];
    const lstCodeCalType =
      (await lstGeneralData.find((item) => item.code === 'CAL_TYPE').details) || [];
    const lstCodeCalMethod =
      (await lstGeneralData.find((item) => item.code === 'CAL_METHOD').details) || [];
    const lstCodeInputFrequency =
      (await lstGeneralData.find((item) => item.code === 'INPUT_FREQUENCY').details) || [];
    const lstCodeKpiFunction =
      (await lstGeneralData.find((item) => item.code === 'KPI_FUNCTION').details) || [];

    if (lstGeneralData.length > 0) {
      data.forEach((item: any) => {
        item.kpiCategoryName = item.kpiCategory
          ? `${item.kpiCategory} - ${lstCodeCategory.find((i) => i.code == item.kpiCategory)?.name}`
          : '';
        item.kpiGroupName = item.kpiGroup
          ? `${item.kpiGroup} - ${lstCodeKpiGroup.find((i) => i.code == item.kpiGroup)?.name}`
          : '';
        item.unitName = item.unit
          ? `${item.unit} - ${lstCodeUnit.find((i) => i.code == item.unit)?.name}`
          : '';
        item.kpiMethodName = item.kpiMethod
          ? `${item.kpiMethod} - ${lstCodeKpiMethod.find((i) => i.code == item.kpiMethod)?.name}`
          : '';
        item.autoFunctionName = item.autoFunction
          ? `${item.autoFunction} - ${lstCodeKpiFunction.find((i) => i.code == item.autoFunction)?.name}`
          : '';
        item.calTypeName = item.calType
          ? `${item.calType} - ${lstCodeCalType.find((i) => i.code == item.calType)?.name}`
          : '';
        item.calMethodName = item.calMethod
          ? `${item.calMethod} - ${lstCodeCalMethod.find((i) => i.code == item.calMethod)?.name}`
          : '';
        item.inputFrequencyName = item.inputFrequency
          ? `${item.inputFrequency} - ${lstCodeInputFrequency.find((i) => i.code == item.inputFrequency)?.name}`
          : '';
      });
    }
    return {
      data,
      total,
      pageIndex,
      pageSize,
    };
  }

  async findKpiList(params: { kpiGroupCode: string }) {
    const whereCon: any = { status: 1 };
    if (params.kpiGroupCode) whereCon.kpiGroup = params.kpiGroupCode;
    return await this.repo.find({
      where: whereCon,
      select: ['id', 'code', 'shortName', 'longName'],
      order: { longName: 'ASC' },
    });
  }
}
