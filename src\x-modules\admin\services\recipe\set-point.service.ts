import { ConflictException, Injectable, NotFoundException } from '@nestjs/common';
import { ILike, In } from 'typeorm';
import { BindRepo, DefTransaction } from '~/@core/decorator';
import {
  DuplicateByProcessDTO,
  DuplicateByProductDTO,
  DuplicateDTO,
  SetPointCreateByProcessDTO,
  SetPointCreateByProductDTO,
  SetPointCreateDTO,
  SetPointPaginationDTO,
  UpdateSetPointDTO,
} from '~/dto/set-point.dto';
import { SetPointEntity } from '~/entities/primary/set-point.entity';
import {
  ItemRepo,
  MachineParameterRepo,
  MachineRepo,
  OrganizationUnitRepo,
  ProcessMachineRepo,
  ProcessRepo,
  RecipeProcessItemRepo,
  RecipeProcessRepo,
  RecipeRepo,
} from '~/repositories/primary';
import { SetPointRepo } from '~/repositories/primary/set-point.repo';
@Injectable()
export class SetPointService {
  @BindRepo(SetPointRepo)
  private readonly repo: SetPointRepo;
  @BindRepo(RecipeRepo)
  private readonly recipeRepo: RecipeRepo;
  @BindRepo(MachineRepo)
  private readonly machineRepo: MachineRepo;
  @BindRepo(MachineParameterRepo)
  private readonly machineParameterRepo: MachineParameterRepo;
  @BindRepo(ItemRepo)
  private readonly itemRepo: ItemRepo;
  @BindRepo(ProcessRepo)
  private readonly processRepo: ProcessRepo;
  @BindRepo(RecipeProcessRepo)
  private readonly recipeProcessRepo: RecipeProcessRepo;
  @BindRepo(OrganizationUnitRepo)
  private readonly organizationUnitRepo: OrganizationUnitRepo;
  @BindRepo(ProcessMachineRepo)
  private readonly processMachineRepo: ProcessMachineRepo;
  @BindRepo(RecipeProcessItemRepo)
  private readonly recipeProcessItemRepo: RecipeProcessItemRepo;

  // @BindRepo(GeneralDataMappingRepo)
  // private readonly mappingRepo: GeneralDataMappingRepo;

  constructor() {}

  // load danh sách recipe
  async recipeLoadSelectBox() {
    const res = await this.recipeRepo.find();
    return res.map((item) => ({
      label: item.recipeNo + ' - ' + item.recipeVer,
      value: item.id,
    }));
  }

  /// load danh sách process
  async processLoadSelectBox() {
    const res = await this.processRepo.find();
    return res.map((item) => ({
      label: item.code + ' - ' + item.name,
      value: item.id,
    }));
  }

  // load danh sách product
  async productLoadSelectBox() {
    const res = await this.itemRepo.find();
    return res.map((item) => ({
      label: item.code + ' - ' + item.name,
      value: item.id,
    }));
  }

  // load danh sách machine parameter theo recipe
  async machineParameterLoadSelectBox(recipeId: string) {
    const setPointRepo = await this.repo.find({
      where: { isActive: true, recipeId: recipeId },
      select: ['machineId', 'processId', 'machineParameterId'],
    });

    const lstMachineParameterIdExist = setPointRepo.map((item) => item.machineParameterId);

    const recipeProcess: any = await this.recipeProcessRepo.findOne({
      where: {
        recipeId,
      },
      relations: [
        'process',
        'recipe',
        'recipe.organizationUnit',
        'process.processMachines',
        'process.processMachines.machine',
        'process.processMachines.machine.parameters',
      ],
    });

    if (!recipeProcess) {
      throw new NotFoundException('Recipe process not found!');
    }

    const lstMachineParameter = [];

    for (const processMachine of recipeProcess.process.__processMachines__) {
      const machine = processMachine.__machine__;
      const parameters = machine.__parameters__.filter(
        (param) => param.isActive && !lstMachineParameterIdExist.includes(param.id),
      );
      for (const [index, param] of parameters.entries()) {
        // const map: any = await this.mappingRepo.findOne({
        //   where: {
        //     mappedId: param.id,
        //     generalDataCode: NSGeneralData.EGeneralDataCode.UOM,
        //   },
        //   relations: ['generalDataDetail'],
        // });

        const key = recipeProcess.process.id + '-' + machine.id + '-' + param.id;
        const isExist = lstMachineParameter.find((item) => item.key === key);
        if (isExist) {
          continue;
        }
        lstMachineParameter.push({
          id: param.id,
          key: key,
          organizeUnitId: recipeProcess.recipe.organizationUnit.id,
          processCode: recipeProcess.process.code,
          processName: recipeProcess.process.name,
          machineCode: machine.code,
          machineName: machine.name,
          machineDesc: machine.desc,
          parameterCode: param.code,
          parameterName: param.tagName,
          parameterDesc: param.desc,
          processId: recipeProcess.process.id,
          machineId: machine.id,
          parameterId: param.id,
          uom: param.uomCode ?? '',
          // uom: map ? map.__generalDataDetail__.name : '',
        });
      }
    }

    return lstMachineParameter;
  }

  // load danh sách machine parameter theo process
  async machineParameterLoadSelectBoxByProcessId(processId: string) {
    const setPointRepo = await this.repo.find({
      where: { isActive: true, processId: processId },
      select: ['machineId', 'processId', 'machineParameterId'],
    });

    const lstMachineParameterIdExist = setPointRepo.map((item) => item.machineParameterId);

    const process = await this.processRepo.findOne({
      where: { id: processId },
      relations: [
        'processMachines',
        'processMachines.machine',
        'processMachines.machine.parameters',
      ],
    });
    if (!process) {
      throw new NotFoundException('Process not found!');
    }

    const lstProcessMachine = await process.processMachines;

    const lstMachineParameter = [];

    for (const processMachine of lstProcessMachine) {
      const machine = await processMachine.machine;
      const parameters = (await machine.parameters).filter(
        (param) => param.isActive && !lstMachineParameterIdExist.includes(param.id),
      );
      for (const [index, param] of parameters.entries()) {
        const key = process.id + '-' + machine.id + '-' + param.id;
        const isExist = lstMachineParameter.find((item) => item.key === key);
        if (isExist) {
          continue;
        }
        lstMachineParameter.push({
          id: param.id,
          key: key,
          organizeUnitId: process.organizationUnitId,
          processCode: process.code,
          processName: process.name,
          machineCode: machine.code,
          machineName: machine.name,
          machineDesc: machine.desc,
          parameterCode: param.code,
          parameterName: param.tagName,
          parameterDesc: param.desc,
          processId: process.id,
          machineId: machine.id,
          parameterId: param.id,
          uom: param.uomCode ?? '',
        });
      }
    }

    return lstMachineParameter;
  }

  // load danh sách machine parameter theo product
  async machineParameterLoadSelectBoxByProductId(productId: string) {
    const product = await this.itemRepo.findOne({
      where: { id: productId },
      relations: ['recipeProcessItems', 'recipeProcessItems.recipeProcess'],
    });

    if (!product) {
      throw new NotFoundException('Product not found!');
    }

    const lstProcessId = product.recipeProcessItems.map((item) => item?.recipeProcess?.processId);

    const setPointRepo = await this.repo.find({
      where: { isActive: true, processId: In(lstProcessId) },
      select: ['machineId', 'processId', 'machineParameterId'],
    });

    const lstMachineParameterIdExist = setPointRepo.map((item) => item.machineParameterId);

    const process = await this.processRepo.findOne({
      where: { id: In(lstProcessId) },
      relations: [
        'processMachines',
        'processMachines.machine',
        'processMachines.machine.parameters',
      ],
    });
    if (!process) {
      return [];
    }

    const lstProcessMachine = await process.processMachines;

    const lstMachineParameter = [];

    for (const processMachine of lstProcessMachine) {
      const machine = await processMachine.machine;
      const parameters = (await machine.parameters).filter(
        (param) => param.isActive && !lstMachineParameterIdExist.includes(param.id),
      );
      for (const [index, param] of parameters.entries()) {
        const key = process.id + '-' + machine.id + '-' + param.id;
        const isExist = lstMachineParameter.find((item) => item.key === key);
        if (isExist) {
          continue;
        }
        lstMachineParameter.push({
          id: param.id,
          key: key,
          organizeUnitId: process.organizationUnitId,
          processCode: process.code,
          processName: process.name,
          machineCode: machine.code,
          machineName: machine.name,
          machineDesc: machine.desc,
          parameterCode: param.code,
          parameterName: param.tagName,
          parameterDesc: param.desc,
          processId: process.id,
          machineId: machine.id,
          parameterId: param.id,
          uom: param.uomCode ?? '',
        });
      }
    }

    return lstMachineParameter;
  }

  // pagination danh sách set point
  async pagination(params: SetPointPaginationDTO) {
    const { pageSize, pageIndex, where } = params;
    const {
      type,
      organizationUnit,
      organizationCode,
      recipeNo,
      recipeVersion,
      productNo,
      productName,
      processCode,
      processName,
      parameterCode,
      parameterName,
      targetValue,
      machineCode,
      machineName,
      min,
      max,
      recipeDescription,
      machineDescription,
      isActive = null,
    } = where;

    const finalWhere: any = {};
    const relations = ['organizeUnit', 'recipe', 'item', 'process', 'machine', 'machineParameter'];
    const findOptions: any = {
      order: { createdDate: 'DESC' },
      relations,
      skip: (pageIndex - 1) * pageSize,
      take: pageSize,
    };

    const buildWhereClause = async (
      filter: any,
      repo: any,
      field: string,
      whereClause: any,
      isHasWhere: { [key: string]: boolean },
    ) => {
      if (Object.keys(filter).length > 0) {
        const lst = await repo.find({
          where: filter,
          select: ['id'],
        });
        whereClause[field] = In(lst.map((u) => u.id));
        isHasWhere[field.replace('Id', '')] = true;
      }
    };

    const orgWhere: any = {};
    const recipeWhere: any = {};
    const itemWhere: any = {};
    const processWhere: any = {};
    const machineWhere: any = {};
    const machineParameterWhere: any = {};
    const isHasWhere = {
      organizeUnit: false,
      recipe: false,
      item: false,
      process: false,
      machine: false,
      machineParameter: false,
    };

    if (organizationUnit) orgWhere.name = ILike(`%${organizationUnit.trim()}%`);
    if (organizationCode) orgWhere.code = ILike(`%${organizationCode.trim()}%`);
    if (recipeNo) recipeWhere.recipeNo = ILike(`%${recipeNo.trim()}%`);
    if (recipeVersion) recipeWhere.recipeVer = recipeVersion;
    if (recipeDescription) recipeWhere.recipeDescription = ILike(`%${recipeDescription.trim()}%`);
    if (productNo) itemWhere.code = ILike(`%${productNo.trim()}%`);
    if (productName) itemWhere.name = ILike(`%${productName.trim()}%`);
    if (processCode) processWhere.code = ILike(`%${processCode.trim()}%`);
    if (processName) processWhere.name = ILike(`%${processName.trim()}%`);
    if (machineCode) machineWhere.code = ILike(`%${machineCode.trim()}%`);
    if (machineName) machineWhere.name = ILike(`%${machineName.trim()}%`);
    if (machineDescription) machineWhere.desc = ILike(`%${machineDescription.trim()}%`);
    if (parameterCode) machineParameterWhere.code = ILike(`%${parameterCode.trim()}%`);
    if (parameterName) machineParameterWhere.name = ILike(`%${parameterName.trim()}%`);

    if (isActive !== null) finalWhere.isActive = isActive;
    if (targetValue) finalWhere.targetValue = targetValue;
    if (min) finalWhere.min = min;
    if (max) finalWhere.max = max;
    if (type) finalWhere.type = ILike(`%${type.trim()}%`);

    await buildWhereClause(
      orgWhere,
      this.organizationUnitRepo,
      'organizeUnitId',
      finalWhere,
      isHasWhere,
    );
    await buildWhereClause(recipeWhere, this.recipeRepo, 'recipeId', finalWhere, isHasWhere);
    await buildWhereClause(itemWhere, this.itemRepo, 'itemId', finalWhere, isHasWhere);
    await buildWhereClause(processWhere, this.processRepo, 'processId', finalWhere, isHasWhere);
    await buildWhereClause(machineWhere, this.machineRepo, 'machineId', finalWhere, isHasWhere);
    await buildWhereClause(
      machineParameterWhere,
      this.machineParameterRepo,
      'machineParameterId',
      finalWhere,
      isHasWhere,
    );

    findOptions.where = finalWhere;

    const [data, total]: any = await this.repo.findAndCount(findOptions);

    const lstResult = data.map((item) => ({
      id: item.id,
      organizationUnit: item?.__organizeUnit__?.name,
      organizationId: item?.__organizeUnit__?.id,
      organizationCode: item?.__organizeUnit__?.code,
      recipeId: item?.__recipe__?.id,
      recipeNo: item?.__recipe__?.recipeNo,
      recipeVersion: item?.__recipe__?.recipeVer,
      recipeDescription: item?.__recipe__?.recipeDescription,
      productId: item.__item__?.id,
      productNo: item.__item__?.code,
      productName: item.__item__?.name,
      processId: item.__process__.id,
      processCode: item.__process__.code,
      processName: item.__process__.name,
      parameterId: item.__machineParameter__.id,
      parameterCode: item.__machineParameter__.code,
      parameterName: item.__machineParameter__.tagName,
      machineId: item.__machine__.id,
      machineCode: item.__machine__.code,
      machineName: item.__machine__.name,
      machineDescription: item.__machine__.desc,
      targetValue: item.targetValue,
      min: item.min,
      max: item.max,
      isActive: item.isActive,
      type: item.type,
    }));

    return {
      data: lstResult,
      total: total,
    };
  }

  //create set point
  @DefTransaction()
  async create(data: SetPointCreateDTO) {
    const { lstSetPoint, recipeId } = data;
    const recipeExist: any = await this.recipeRepo.findOne({
      where: { id: recipeId },
      relations: ['organizationUnit', 'product'],
    });

    if (!recipeExist) {
      throw new NotFoundException('Recipe not found!');
    }
    const lstTask = [];

    for (const item of lstSetPoint) {
      const org = recipeExist.organizationUnit;

      const newSetPoint = new SetPointEntity();
      newSetPoint.organizeUnitId = org.id;
      newSetPoint.recipeId = recipeId;
      newSetPoint.machineId = item.machineId;
      newSetPoint.processId = item.processId;
      newSetPoint.machineParameterId = item.parameterId;
      newSetPoint.itemId = recipeExist.product.id;
      newSetPoint.targetValue = item.targetValue;
      newSetPoint.min = item.min;
      newSetPoint.max = item.max;
      newSetPoint.isActive = true;
      newSetPoint.type = data.type;
      // newSetPoint.createdBy = '-1';
      newSetPoint.createdDate = new Date();

      lstTask.push(newSetPoint);
    }

    await this.repo.insert(lstTask);
    return { message: 'Create set point success!' };
  }

  @DefTransaction()
  async createByProcess(data: SetPointCreateByProcessDTO) {
    const { lstSetPoint, processId } = data;

    const lstTask = [];

    for (const item of lstSetPoint) {
      const newSetPoint = new SetPointEntity();

      newSetPoint.organizeUnitId = null;
      newSetPoint.recipeId = null;
      newSetPoint.machineId = item.machineId;
      newSetPoint.processId = item.processId;
      newSetPoint.machineParameterId = item.parameterId;
      newSetPoint.targetValue = item.targetValue;
      newSetPoint.min = item.min;
      newSetPoint.max = item.max;
      newSetPoint.isActive = true;
      newSetPoint.type = data.type;
      // newSetPoint.createdBy = '-1';
      newSetPoint.createdDate = new Date();

      lstTask.push(newSetPoint);
    }

    await this.repo.insert(lstTask);
    return { message: 'Create set point success!' };
  }

  @DefTransaction()
  async createByProduct(data: SetPointCreateByProductDTO) {
    const { lstSetPoint, productId } = data;

    const lstTask = [];

    for (const item of lstSetPoint) {
      const newSetPoint = new SetPointEntity();

      newSetPoint.organizeUnitId = null;
      newSetPoint.recipeId = null;
      newSetPoint.machineId = item.machineId;
      newSetPoint.processId = item.processId;
      newSetPoint.machineParameterId = item.parameterId;
      newSetPoint.itemId = productId;
      newSetPoint.targetValue = item.targetValue;
      newSetPoint.min = item.min;
      newSetPoint.max = item.max;
      newSetPoint.isActive = true;
      newSetPoint.type = data.type;
      // newSetPoint.createdBy = '-1';
      newSetPoint.createdDate = new Date();

      lstTask.push(newSetPoint);
    }

    await this.repo.insert(lstTask);
    return { message: 'Create set point success!' };
  }
  // update set point

  async update(data: UpdateSetPointDTO) {
    const { id, targetValue, min, max, isActive } = data;

    const setPoint: any = await this.repo.findOne(id);
    if (!setPoint) {
      throw new Error('Set point not found!');
    }

    setPoint.targetValue = targetValue;
    setPoint.min = min;
    setPoint.max = max;
    setPoint.isActive = isActive;
    // setPoint.updatedBy = '-1';
    setPoint.updatedDate = new Date();

    await this.repo.save(setPoint);

    return {
      message: 'Update set point success!',
    };
  }
  /** duplicate by recipe */
  @DefTransaction()
  async duplicate(data: DuplicateDTO) {
    // lst set point để copy qua
    const lstSetPoint: any = await this.repo.find({
      where: { id: In(data.setPointIds) },
    });

    const lstSetPointExist: any = await this.repo.find({
      where: { recipeId: data.recipeId },
    });

    const recipe: any = await this.recipeRepo.findOne({
      where: { id: data.recipeId },
      relations: ['organizationUnit', 'product'],
    });

    if (!recipe) {
      throw new NotFoundException('Recipe not found!');
    }

    const recipeProcess: any = await this.recipeProcessRepo.findOne({
      where: {
        recipeId: data.recipeId,
      },
      relations: [
        'process',
        'recipe',
        'recipe.organizationUnit',
        'process.processMachines',
        'process.processMachines.machine',
        'process.processMachines.machine.parameters',
      ],
    });

    // kiểm tra và lọc ra parameter chưa tồn tại ở recipe mới
    const lstMachineParameter = [];
    for (const processMachine of recipeProcess.process.__processMachines__) {
      const machine = processMachine.__machine__;
      const lstMachineParameterIdExistNew = lstSetPointExist.map((item) => item.machineParameterId);
      const parameters = machine.__parameters__.filter(
        (param) => param.isActive && !lstMachineParameterIdExistNew.includes(param.id),
      );
      for (const [index, param] of parameters.entries()) {
        lstMachineParameter.push({
          key: index,
          organizeUnitId: recipeProcess.recipe.organizationUnit.id,
          processCode: recipeProcess.process.code,
          processName: recipeProcess.process.name,
          machineCode: machine.code,
          machineName: machine.name,
          machineDesc: machine.desc,
          parameterCode: param.code,
          parameterName: param.name,
          parameterDesc: param.desc,
          processId: recipeProcess.process.id,
          machineId: machine.id,
          parameterId: param.id,
        });
      }
    }

    // data bên cũ chuẩn bị copy qua
    const lstDataCopy = lstSetPoint.map(
      ({ machineId, processId, machineParameterId, targetValue, min, max, isActive }) => ({
        machineId,
        processId,
        machineParameterId,
        targetValue,
        min,
        max,
        isActive,
      }),
    );

    // so sanh nếu parameter owr recipe mới tồn tại không trùng khớp với data ở setpoint cũ thì thông báo lỗi
    for (const item of lstDataCopy) {
      const isExist = lstMachineParameter.find(
        (param) =>
          param.machineId === item.machineId &&
          param.processId === item.processId &&
          param.parameterId === item.machineParameterId,
      );

      if (!isExist) {
        throw new ConflictException(
          'Parameter exist in recipe or can not find parameter in recipe ' +
            [recipe.recipeNo] +
            '!',
        );
      }
    }

    const task = lstDataCopy.map((copy) => ({
      recipeId: data.recipeId,
      organizeUnitId: recipe?.organizationUnit.id,
      itemId: recipe?.product.id,
      ...copy,
      // createdBy: '-1',
      createdDate: new Date(),
    }));

    await this.repo.insert(task);

    return { message: 'Duplicate set point success!' };
  }
  /** duplicate by process */
  @DefTransaction()
  async duplicateByProcess(data: DuplicateByProcessDTO) {
    // lst set point để copy qua
    const lstSetPoint: any = await this.repo.find({
      where: { id: In(data.setPointIds) },
    });

    const lstSetPointExist: any = await this.repo.find({
      where: { processId: data.processId },
    });

    const process: any = await this.processRepo.findOne({
      where: { id: data.processId },
      relations: [
        'processMachines',
        'processMachines.machine',
        'processMachines.machine.parameters',
      ],
    });

    if (!process) {
      throw new NotFoundException('Process not found!');
    }

    const lstProcessMachine = await process.processMachines;
    // kiểm tra và lọc ra parameter chưa tồn tại ở recipe mới
    const lstMachineParameter = [];
    for (const processMachine of lstProcessMachine) {
      const machine = processMachine.__machine__;
      const lstMachineParameterIdExistNew = lstSetPointExist.map((item) => item.machineParameterId);
      const parameters = machine.__parameters__.filter(
        (param) => param.isActive && !lstMachineParameterIdExistNew.includes(param.id),
      );
      for (const [index, param] of parameters.entries()) {
        lstMachineParameter.push({
          key: index,
          processCode: process.code,
          processName: process.name,
          processId: process.id,
          machineCode: machine.code,
          machineName: machine.name,
          machineDesc: machine.desc,
          parameterCode: param.code,
          parameterName: param.name,
          parameterDesc: param.desc,
          machineId: machine.id,
          parameterId: param.id,
        });
      }
    }

    // data bên cũ chuẩn bị copy qua
    const lstDataCopy = lstSetPoint.map(
      ({ machineId, processId, machineParameterId, targetValue, min, max, isActive }) => ({
        machineId,
        processId,
        machineParameterId,
        targetValue,
        min,
        max,
        isActive,
      }),
    );

    // so sanh nếu parameter owr recipe mới tồn tại không trùng khớp với data ở setpoint cũ thì thông báo lỗi
    for (const item of lstDataCopy) {
      const isExist = lstMachineParameter.find(
        (param) =>
          param.machineId === item.machineId &&
          param.processId === item.processId &&
          param.parameterId === item.machineParameterId,
      );

      if (!isExist) {
        throw new ConflictException(
          'Parameter exist in process or can not find parameter in process ' + [process.code] + '!',
        );
      }
    }

    const task = lstDataCopy.map((copy) => ({
      ...copy,
      // createdBy: '-1',
      createdDate: new Date(),
    }));

    await this.repo.insert(task);

    return { message: 'Duplicate set point success!' };
  }
  /** duplicate by product */
  @DefTransaction()
  async duplicateByProduct(data: DuplicateByProductDTO) {
    // lst set point để copy qua
    const lstSetPoint: any = await this.repo.find({
      where: { id: In(data.setPointIds) },
    });

    const recipeProcess = await this.recipeProcessItemRepo.findOne({
      where: { itemId: data.productId },
      relations: ['recipeProcess', 'recipeProcess.process', 'item'],
    });

    if (!recipeProcess) {
      throw new NotFoundException('RecipeProcess not found!');
    }
    const lstSetPointExist: any = await this.repo.find({
      where: { itemId: data.productId },
    });

    const process: any = await this.processRepo.findOne({
      where: { id: recipeProcess.recipeProcess.process.id },
      relations: [
        'processMachines',
        'processMachines.machine',
        'processMachines.machine.parameters',
      ],
    });

    if (!process) {
      throw new NotFoundException('Process not found!');
    }
    const lstProcessMachine = await process.processMachines;

    // kiểm tra và lọc ra parameter chưa tồn tại ở recipe mới
    const lstMachineParameter = [];
    for (const processMachine of lstProcessMachine) {
      1;
      const machine = processMachine.__machine__;
      const lstMachineParameterIdExistNew = lstSetPointExist.map((item) => item.machineParameterId);
      const parameters = machine.__parameters__.filter(
        (param) => param.isActive && !lstMachineParameterIdExistNew.includes(param.id),
      );
      for (const [index, param] of parameters.entries()) {
        lstMachineParameter.push({
          key: index,
          processCode: process.code,
          processName: process.name,
          machineCode: machine.code,
          machineName: machine.name,
          machineDesc: machine.desc,
          parameterCode: param.code,
          parameterName: param.name,
          parameterDesc: param.desc,
          processId: process.id,
          machineId: machine.id,
          parameterId: param.id,
        });
      }
    }

    // data bên cũ chuẩn bị copy qua
    const lstDataCopy = lstSetPoint.map(
      ({ machineId, processId, machineParameterId, targetValue, min, max, isActive }) => ({
        machineId,
        processId,
        machineParameterId,
        targetValue,
        min,
        max,
        isActive,
      }),
    );

    // so sanh nếu parameter owr recipe mới tồn tại không trùng khớp với data ở setpoint cũ thì thông báo lỗi
    for (const item of lstDataCopy) {
      const isExist = lstMachineParameter.find(
        (param) =>
          param.machineId === item.machineId &&
          param.processId === item.processId &&
          param.parameterId === item.machineParameterId,
      );

      if (!isExist) {
        throw new ConflictException(
          'Parameter exist in recipe or can not find parameter in product ' +
            [recipeProcess.item?.code || ''] +
            '!',
        );
      }
    }

    const task = lstDataCopy.map((copy) => ({
      itemId: recipeProcess.itemId,
      ...copy,
      // createdBy: '-1',
      createdDate: new Date(),
    }));

    await this.repo.insert(task);

    return { message: 'Duplicate set point success!' };
  }
}
