import { HttpStatus } from '@nestjs/common';
import { APIGatewayProxyEvent, APIGatewayProxyResult, Context, Handler } from 'aws-lambda';
import { plainToInstance } from 'class-transformer';
import { validate } from 'class-validator';
import { EHttpStatusMessage } from '~/@core/network';
import { ErpApiResponse, ErpBusinessException } from '~/@systems/exceptions/dto/erp-exception';
import { ERPCreateRecipeReq, ERPCreateRecipeResponse } from '~/dto/erp/recipe.dto';
import { ERPRecipeIntegrateService } from '~/x-modules/integration/services';
import withMiddlewares from './common/middleware/base.middleware';
import { ApiTokenMiddleware } from './common/middleware/api-token.middleware';
import { appInstanceProvider } from './common/provider/app-instance.provider';

async function bootstrap() {
  return appInstanceProvider.getAppInstance();
}

/** 🆕 Hàm validate dữ liệu đầu vào
 * @param dtoClass Class DTO
 * @param data Dữ liệu cần validate
 */
async function validateInput(dtoClass: any, data: any) {
  const instance = plainToInstance(dtoClass, data);
  const errors = await validate(instance);
  if (errors.length > 0) {
    throw new ErpBusinessException(
      EHttpStatusMessage[HttpStatus.UNPROCESSABLE_ENTITY],
      errors,
      HttpStatus.UNPROCESSABLE_ENTITY,
    );
  }
}

const formatApiResponse = (body: any, statusCode = HttpStatus.OK): APIGatewayProxyResult => {
  return {
    statusCode,
    body: body?.toString ? body.toString() : JSON.stringify(body),
  };
};

/**
 * Hàm tạo công thức sản xuất
 * @param event APIGatewayProxyEvent
 * @param context Context
 * @returns APIGatewayProxyResult
 */
const createOrUpdateHandler: Handler = async (event: APIGatewayProxyEvent, context: Context) => {
  try {
    const app = await bootstrap();
    const recipeService = app.get(ERPRecipeIntegrateService);
    const body = JSON.parse(event.body);

    await validateInput(ERPCreateRecipeReq, body);

    const result = await recipeService.createOrUpdate(body);

    return formatApiResponse(
      new ErpApiResponse<{
        created_at: string;
        updated_at: string;
        refs: Array<ERPCreateRecipeResponse>;
      }>(EHttpStatusMessage[HttpStatus.OK], HttpStatus.OK, undefined, 'success', {
        created_at: result.createdDate?.toISOString(),
        updated_at: result.updatedDate?.toISOString(),
        refs: [
          {
            RECIPE_NO: result.recipeNo,
            RECIPE_VER: Number(result.recipeVer),
          },
        ],
      }),
      HttpStatus.OK,
    );
  } catch (error) {
    console.error('Error:', error);
    if (error instanceof ErpBusinessException) {
      return formatApiResponse(error, error.code);
    }
    return formatApiResponse(
      new ErpBusinessException(
        EHttpStatusMessage[HttpStatus.INTERNAL_SERVER_ERROR],
        error,
        HttpStatus.INTERNAL_SERVER_ERROR,
      ),
      HttpStatus.INTERNAL_SERVER_ERROR,
    );
  }
};

export const createOrUpdate = withMiddlewares(createOrUpdateHandler, [ApiTokenMiddleware()]);
