import { Body, Inject, Param, Query, UseGuards } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { DefController, DefGet, DefPost } from '~/@core/decorator';
import { ListRecipeReq } from '~/dto/recipe.dto';
import { RecipeService } from '../../services';
import { RoleGuard } from '~/@systems/guard/role.guard';
import { Roles } from '~/@core/decorator/roles.decorator';

@ApiTags('Recipe')
@DefController('recipe')
export class RecipeController {
  constructor(@Inject(RecipeService) private readonly recipeService: RecipeService) {}

  @DefGet('data-select/:itemId')
  @Roles('/recipe/recipes', 'View')
  @UseGuards(RoleGuard)
  async loadDataSelect(@Param('itemId') itemId: string) {
    return this.recipeService.loadDataSelect(itemId);
  }

  @DefPost('find')
  @Roles('/recipe/recipes', 'View')
  @UseGuards(RoleGuard)
  async find(@Body() data: { itemId: string; processAreaId: string }) {
    return this.recipeService.find(data);
  }

  @DefGet('')
  @Roles('/recipe/recipes', 'View')
  @UseGuards(RoleGuard)
  async list(@Query() params: ListRecipeReq) {
    return this.recipeService.findAll(params);
  }

  @DefGet('pagination')
  @Roles('/recipe/recipes', 'View')
  @UseGuards(RoleGuard)
  async pagination(@Query() params: ListRecipeReq) {
    return this.recipeService.pagination(params);
  }

  @DefGet(':id')
  @Roles('/recipe/recipes', 'View')
  @UseGuards(RoleGuard)
  async detail(@Param('id') id: string) {
    return this.recipeService.findById(id);
  }
}
