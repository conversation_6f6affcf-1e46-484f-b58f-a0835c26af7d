import { NestFactory } from '@nestjs/core';
import { Context, Handler } from 'aws-lambda';
import 'reflect-metadata';
import { AppModule } from '~/app.module';
import { HolidayService } from '~/x-modules/admin/services/system-configuration/holiday.service';


// Singleton app instance to avoid recreating for each request
let cachedApp: any = null;

async function bootstrap() {
  if (!cachedApp) {
    const app = await NestFactory.create(AppModule, {
      logger: ['error', 'warn'], // Reduce logging
    });
    cachedApp = app;
  }
  return cachedApp;
}

// Helper function to create response
function createResponse(statusCode: number, body: any) {
  return {
    statusCode,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Credentials': true,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(body),
  };
}

// Get all holidays
export const findAll: Handler = async (event: any, context: Context) => {
  try {
    const app = await bootstrap();
    const holidayService = app.get(HolidayService);
    const result = await holidayService.findAll();
    return createResponse(200, result);
  } catch (error) {
    console.error('Error getting holidays:', error);
    return createResponse(500, { message: error.message || 'Internal server error' });
  }
};

// Search organization units
export const search: Handler = async (event: any, context: Context) => {
  try {
    const term = event.queryStringParameters?.term;
    const app = await bootstrap();
    const holidayService = app.get(HolidayService);
    const result = await holidayService.search(term);
    return createResponse(200, result);
  } catch (error) {
    console.error('Error searching organization units:', error);
    console.error('Error stack:', error.stack);
    return createResponse(500, { message: error.message || 'Internal server error' });
  }
};

// Get organization unit by code
export const findByCode: Handler = async (event: any, context: Context) => {
  try {
    const code = event.pathParameters?.code;
    const app = await bootstrap();
    const holidayService = app.get(HolidayService);
    const result = await holidayService.findByCode(code);

    return createResponse(200, result);
  } catch (error) {
    console.error('Error getting organization unit by code:', error);
    console.error('Error stack:', error.stack);
    return createResponse(500, { message: error.message || 'Internal server error' });
  }
};

// Get organization unit by ID
export const findOne: Handler = async (event: any, context: Context) => {
  try {
    const id = event.pathParameters?.id;
    const app = await bootstrap();
    const holidayService = app.get(HolidayService);
    const result = await holidayService.findOne(id);
    return createResponse(200, result);
  } catch (error) {
    console.error('Error getting holiday by id:', error);
    return createResponse(error.message.includes('not found') ? 404 : 500, {
      message: error.message || 'Internal server error',
    });
  }
};

// Update holiday
export const update: Handler = async (event: any, context: Context) => {
  try {
    const id = event.pathParameters?.id;
    const body = JSON.parse(event.body || '{}');

    const app = await bootstrap();
    const holidayService = app.get(HolidayService);
    
    // Gọi service để xử lý logic (bao gồm các rule kiểm tra)
    const result = await holidayService.update(id, body);

    return createResponse(200, result);
  } catch (error) {
    console.error('Error updating holiday:', error);
    return createResponse(error.message.includes('not found') ? 404 : 500, {
      message: error.message || 'Internal server error',
    });
  }
};

export const create: Handler = async (event: any, context: Context) => {
  try {
    const body = JSON.parse(event.body || '{}');
    const app = await bootstrap();
    const holidayService = app.get(HolidayService);
    const result = await holidayService.create(body);
    return createResponse(201, result);
  } catch (error) {
    const statusCode = error.message.includes('already exists') || error.message.includes('Start time must be earlier than end time') ? 400 : 500;
    return createResponse(statusCode, { message: error.message || 'Internal server error' });
  }
};

// Handle OPTIONS request for CORS
export const options: Handler = async (event: any, context: Context) => {
  return {
    statusCode: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers':
        'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-Amz-User-Agent',
      'Access-Control-Allow-Methods': 'OPTIONS,GET,PUT',
      'Access-Control-Allow-Credentials': true,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({}),
  };
};
