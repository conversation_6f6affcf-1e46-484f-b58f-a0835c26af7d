import {
  Injectable,
  InternalServerErrorException,
  BadRequestException,
  GatewayTimeoutException,
} from '@nestjs/common';
import * as net from 'net';
import dayjs from 'dayjs';
interface TextToZplOptions {
  fontSize?: number;
  fontFamily?: string;
  fontWeight?: string;
  canvasWidth?: number;
  canvasHeight?: number;
  padding?: number;
}
// import { createCanvas } from 'canvas';

@Injectable()
export class PrinterService {
  constructor() {}
  async checkConnectPrinter(
    printerIp: string,
    printerPort: number,
  ): Promise<{ connected: boolean; message: string }> {
    return new Promise((resolve) => {
      let responseSent = false;
      const client = new net.Socket();
      client.setTimeout(3000);
      client.connect(printerPort, printerIp, () => {
        console.log(`Successfully connected to printer at ${printerIp}:${printerPort}`);
        if (!responseSent) {
          responseSent = true;
          client.destroy(); // <PERSON><PERSON><PERSON> kết nối ngay sau khi kiểm tra thành công
          resolve({
            connected: true,
            message: `Successfully connected to printer at ${printerIp}:${printerPort}`,
          });
        }
      });

      client.on('error', (err) => {
        console.error(`Printer connection error: ${err.message}`);
        if (!responseSent) {
          responseSent = true;
          client.destroy();
          resolve({
            connected: false,
            message: `Failed to connect to printer: ${err.message}`,
          });
        }
      });

      client.on('timeout', () => {
        console.error('Printer connection timed out');
        if (!responseSent) {
          responseSent = true;
          client.destroy();
          resolve({
            connected: false,
            message: 'Connection timed out',
          });
        }
      });
    });
  }

  async reconnectPrinter(
    printerIp: string,
    printerPort: number,
  ): Promise<{ connected: boolean; message: string }> {
    try {
      const response = await this.checkConnectPrinter(printerIp, printerPort);
      return response;
    } catch (error) {
      throw new Error(`Failed to reconnect printer: ${error.message}`);
    }
  }

  async print(data: any[], printerIp: string, printerPort: number): Promise<string> {
    if (!Array.isArray(data) || data.length === 0) {
      throw new BadRequestException('Missing printerIp or data array.');
    }
    const zplCommands = data.map(this.generateZPL).join('\n');
    const client = new net.Socket();

    client.setTimeout(3000); // 3s timeout

    return new Promise((resolve, reject) => {
      let responseSent = false;

      client.connect(printerPort, printerIp, () => {
        console.log(`Connected to printer ${printerIp}`);
        client.write(zplCommands, () => {
          client.end(); // đóng socket sau khi gửi
        });
      });

      client.on('error', (err) => {
        console.error('Print error:', err.message);
        if (!responseSent) {
          responseSent = true;
          reject(
            new InternalServerErrorException({
              message: 'Printer connection error',
              error: err.message,
            }),
          );
        }
      });

      client.on('timeout', () => {
        console.error('Printer timeout');
        client.destroy(); // force close
        if (!responseSent) {
          responseSent = true;
          reject(new GatewayTimeoutException('Printer connection timed out'));
        }
      });

      client.on('close', () => {
        console.log('Print job sent and socket closed');
        if (!responseSent) {
          responseSent = true;
          resolve('Print job sent successfully!');
        }
      });
    });
  }

  private generateZPL(data: any): string {
    const { zpl, canvasWidth } = textToZplImage(data.itemName, {
      fontSize: 36,
      canvasHeight: 100,
      fontWeight: 'bold',
    });
    const date = dayjs(data.createdDate).tz('Asia/Ho_Chi_Minh').format('DD/MM/YYYY HH:mm:ss');
    const printWidth = 700;
    const xCentered = Math.floor((printWidth - canvasWidth) / 2);
    return `
^XA
^PW${printWidth}
^LH0,0
^LL500
${zpl}
^FO${xCentered},30^XG TEXT.GRF,1,1^FS
^FO30,90^A0N,28,28^FDItem: ${data.itemCode}^FS
^FO30,120^A0N,28,28^FDLot: ${data.lotNumber}^FS
^FO30,150^A0N,28,28^FDNSX: ${data.productionDate ? dayjs(data.productionDate).format('DD/MM/YYYY') : '-'}^FS
^FO30,180^A0N,28,28^FDHSD: ${data.expiredDate ? dayjs(data.expiredDate).format('DD/MM/YYYY') : '-'}^FS
^FO30,210^A0N,28,28^FDPO: ${data.orderNo}^FS
^FO30,240^A0N,28,28^FDBatch: ${data.batchNo}^FS

^FO30,280^A0N,60,60^FD${data.weighedQty}^FS
^FO160,290^A0N,35,35^FDKgs^FS

^FO30,350^A0N,28,28^FDDateTime: ${date}^FS

^FO490,80^BQN,2,5
^FDLA,${data.qrcode}^FS

^XZ`;
  }
}

function textToZplImage(text: string, options: TextToZplOptions = {}) {
  // const {
  //   fontSize = 24,
  //   fontFamily = 'Helvetica',
  //   canvasHeight = 120,
  //   fontWeight = 'bold',
  //   padding = 10,
  // } = options;
  // // Tạo canvas tạm để đo text
  // let tempCanvas = createCanvas(1, 1);
  // let tempCtx = tempCanvas.getContext('2d');
  // tempCtx.font = `${fontWeight} ${fontSize}px ${fontFamily}`;

  // // Đo chiều rộng text
  // const textWidth = tempCtx.measureText(text).width;

  // // Tính canvasWidth dựa trên textWidth + padding 2 bên
  // const canvasWidth = Math.ceil(textWidth + padding * 2);

  // // Tạo canvas chính với width vừa tính
  // const canvas = createCanvas(canvasWidth, canvasHeight);
  // const ctx = canvas.getContext('2d');

  // // Nền trắng
  // ctx.fillStyle = 'white';
  // ctx.fillRect(0, 0, canvasWidth, canvasHeight);

  // // Vẽ chữ đen
  // ctx.fillStyle = 'black';
  // ctx.font = `${fontWeight} ${fontSize}px ${fontFamily}`;
  // ctx.textBaseline = 'top';

  // // Căn giữa ngang text trong canvas (đã có padding)
  // const x = padding;
  // ctx.fillText(text, x, 0);

  // // Lấy dữ liệu pixel
  // const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
  // const pixels = imageData.data;

  // // Chuyển ảnh màu sang ảnh đen trắng 1-bit
  // const widthBytes = Math.ceil(canvas.width / 8);
  // const totalBytes = widthBytes * canvas.height;
  // const bitmap = new Uint8Array(totalBytes);

  // for (let y = 0; y < canvas.height; y++) {
  //   for (let xByte = 0; xByte < widthBytes; xByte++) {
  //     let byte = 0;
  //     for (let bit = 0; bit < 8; bit++) {
  //       const xPos = xByte * 8 + bit;
  //       if (xPos >= canvas.width) break;

  //       const idx = (y * canvas.width + xPos) * 4;
  //       const r = pixels[idx];
  //       const g = pixels[idx + 1];
  //       const b = pixels[idx + 2];

  //       const gray = 0.299 * r + 0.587 * g + 0.114 * b;
  //       if (gray < 128) {
  //         byte |= 1 << (7 - bit);
  //       }
  //     }
  //     bitmap[y * widthBytes + xByte] = byte;
  //   }
  // }

  // // Chuyển bitmap sang hex
  // const toHex = (byte: number) => ('0' + byte.toString(16)).slice(-2).toUpperCase();
  // let hexData = '';
  // for (let i = 0; i < bitmap.length; i++) {
  //   hexData += toHex(bitmap[i]);
  // }

  // // Tạo lệnh ZPL
  // const zplName = 'R:TEXT.GRF';
  // const zpl = `~DG${zplName},${totalBytes},${widthBytes},${hexData}`;

  // return {
  //   canvasWidth,
  //   zpl,
  // };
  return {
    canvasWidth: 0,
    zpl: '',
  };
}
