import { NestFactory } from '@nestjs/core';
import { APIGatewayProxyEvent, APIGatewayProxyResult, Handler } from 'aws-lambda';
import { AppModule } from '~/app.module';
import { AuthService } from '~/x-modules/admin/services/auth.service';

let cachedApp: any = null;

async function bootstrap() {
  if (!cachedApp) {
    const app = await NestFactory.createApplicationContext(AppModule);
    await app.init();
    cachedApp = app;
  }
  return cachedApp;
}

export const validateToken: Handler = async (
  event: APIGatewayProxyEvent,
): Promise<APIGatewayProxyResult> => {
  try {
    const app = await bootstrap();
    const authService = app.get(AuthService);

    // Lấy token từ request body
    const { token } = JSON.parse(event.body || '{}');

    if (!token) {
      return {
        statusCode: 400,
        body: JSON.stringify({
          isValid: false,
          error: 'Token không được cung cấp',
        }),
      };
    }

    // Xác thực token
    const result = await authService.validateMsToken(token);
    
    return {
      statusCode: result.isValid ? 200 : 401,
      body: JSON.stringify(result),
    };

  } catch (error) {
    return {
      statusCode: 500,
      body: JSON.stringify({
        isValid: false,
        error: error.message,
      }),
    };
  }
}; 