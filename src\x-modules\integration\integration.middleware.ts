import {
  Injectable,
  NestMiddleware,
  UnauthorizedException,
  BadRequestException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { Request, Response } from 'express';
import { SystemValue } from '~/common/constants/SystemValue';

@Injectable()
export class IntegrationMiddleware implements NestMiddleware {
  constructor(private jwtService: JwtService) {}
  async use(req: Request, res: Response, next: Function) {
    // console.log('--------IntegrationMiddleware-----------');

    try {
      const checkObject = (obj: any): boolean => {
        if (!obj || typeof obj !== 'object') return false;

        for (const value of Object.values(obj)) {
          if (typeof value === 'string' && SystemValue.FORBIDDEN_PATTERN.test(value)) {
            return true;
          } else if (typeof value === 'object') {
            if (checkObject(value)) return true; // đệ quy nếu nested
          }
        }

        return false;
      };

      if (checkObject(req.query) || checkObject(req.body)) {
        throw new BadRequestException('SQL Injection detected!');
      }
      next();
    } catch (error) {
      next(new UnauthorizedException('Unauthorized'));
    }
  }

  // private readonly forbiddenPattern =
  //   /(\b(UNION|SELECT|UPDATE|DELETE|INSERT|DROP|ALTER|CREATE|EXEC|TRUNCATE|REPLACE)\b|--|;|\/\*|\*\/|'|"|`)/gi;

  // // Danh sách các giá trị param được phép mặc định, không check
  // private readonly paramValueWhitelist = ['select-one', 'default', 'home', 'index'];

  // private checkObject = (obj: any, checkParamKeys?: string[]): boolean => {
  //   if (!obj || typeof obj !== 'object') return false;

  //   for (const [key, value] of Object.entries(obj)) {
  //     if (checkParamKeys && !checkParamKeys.includes(key)) {
  //       // Nếu có danh sách key param cần check, bỏ qua các key không nằm trong danh sách
  //       continue;
  //     }

  //     if (typeof value === 'string') {
  //       // Nếu nằm trong whitelist thì bỏ qua
  //       if (this.paramValueWhitelist.includes(value.toLowerCase())) continue;

  //       if (this.forbiddenPattern.test(value)) {
  //         return true;
  //       }
  //     } else if (typeof value === 'object') {
  //       if (this.checkObject(value, checkParamKeys)) return true; // đệ quy
  //     }
  //   }

  //   return false;
  // };

  // use(req: Request, res: Response, next: NextFunction): void {
  //   // Nếu muốn check param chỉ với các key này (ví dụ các param dynamic thực sự)
  //   const paramKeysToCheck = Object.keys(req.params); // hoặc gán cố định ['id', 'slug'] ...

  //   if (
  //     this.checkObject(req.query) ||
  //     this.checkObject(req.body) ||
  //     this.checkObject(req.params, paramKeysToCheck)
  //   ) {
  //     throw new BadRequestException('SQL Injection detected!');
  //   }

  //   next();
  // }
}
