import { Injectable } from '@nestjs/common';
import { IsBoolean } from 'class-validator';
import { ILike, In } from 'typeorm';
import { BindRepo, DefTransaction } from '~/@core/decorator';
import { BusinessException } from '~/@systems/exceptions';
import { PageResponse } from '~/@systems/utils';
import { ORGANIZATION_LEVEL } from '~/common/constants';
import { NSGeneralData } from '~/common/enums';
import { PaginationReq } from '~/dto/access.dto';
import { AccessEntity } from '~/entities/primary/access.entity';
import { OrganizationUnitEntity } from '~/entities/primary/organization-unit.entity';
import { UserEntity } from '~/entities/primary/user.entity';
import { AccessRepo } from '~/repositories/primary/access.repo';
import { OrganizationUnitRepo } from '~/repositories/primary/organization-unit.repo';
import { UserRepo } from '~/repositories/primary/user.repo';
import { adminSessionContext } from '~/x-modules/admin/admin-session.context';

@Injectable()
export class AccessService {
  @BindRepo(AccessRepo)
  private accessRepo: AccessRepo;

  @BindRepo(OrganizationUnitRepo)
  private organizationUnitRepo: OrganizationUnitRepo;

  @BindRepo(UserRepo)
  private userRepo: UserRepo;

  async getUserAccessList(employeeID: string): Promise<AccessEntity[]> {
    return this.accessRepo.find({
      where: {
        user: { id: employeeID },
      },
      relations: ['user', 'organizationUnit'],
    });
  }

  async getOrganizationUnitsByIDs(organizationUnitIDs: string[]) {
    const organizationInDB = await this.organizationUnitRepo.find({
      where: {
        id: In(organizationUnitIDs),
      },
    });

    return organizationInDB;
  }

  @DefTransaction()
  async updateOrganizationAccess(user: UserEntity, organizationUnits: OrganizationUnitEntity[]) {
    // Delete all access
    await this.accessRepo.delete({
      user: user,
    });

    // Update new access
    const accesses = organizationUnits.map<Partial<AccessEntity>>((organization) => ({
      user: user,
      organizationUnit: organization,
      status: true,
    }));
    await this.accessRepo.saves(accesses);
  }

  async getOrganizationList(search?: {
    group?: string;
    site?: string;
    factory?: string;
    line?: string;
    process?: string;
    page?: number;
    pageSize?: number;
    limit?: number;
  }) {
    const page = search?.page || 1;
    const pageSize = search?.limit || search?.pageSize || 10;

    // Kiểm tra xem có điều kiện tìm kiếm nào không
    const hasSearchCondition =
      search?.group || search?.site || search?.factory || search?.line || search?.process;
    if (!hasSearchCondition) {
      // Nếu không có điều kiện tìm kiếm, lấy tất cả các bản ghi
      const queryBuilder = this.organizationUnitRepo
        .createQueryBuilder('org')
        .innerJoinAndSelect('org.level', 'level')
        .select([
          'org.id as id',
          'org.code as code',
          'org.name as name',
          'org.parentId as parentId',
          'level.code as "levelCode"',
        ])
        .orderBy('org.parentId', 'ASC')
        .addOrderBy('org.code', 'ASC');

      const organizations = await queryBuilder.getRawMany();
      const hierarchicalResults = this.buildHierarchicalResult(organizations);
      const sortedData = this.sortTreeStructure(hierarchicalResults.data);

      const totalRecords = sortedData.length;
      const totalPages = Math.ceil(totalRecords / pageSize);

      // Đảm bảo page không vượt quá totalPages và không nhỏ hơn 1
      const validPage = Math.max(1, Math.min(page, totalPages));
      const skip = (validPage - 1) * pageSize;
      const paginatedData = sortedData.slice(skip, skip + pageSize);

      return {
        data: paginatedData,
        pagination: {
          page: validPage,
          pageSize,
          total: totalRecords,
          totalPages,
        },
      };
    }

    // Xử lý trường hợp có điều kiện tìm kiếm
    const searchQueryBuilder = this.organizationUnitRepo
      .createQueryBuilder('org')
      .innerJoinAndSelect('org.level', 'level')
      .select(['org.id', 'org.parentId', 'org.code', 'org.name', 'level.code as "levelCode"']);

    const conditions = [];
    const params: any = {};

    // Xác định các level cần tìm kiếm
    const searchLevels = new Set<string>();
    if (search?.process) searchLevels.add(ORGANIZATION_LEVEL.PROCESS);
    if (search?.line) searchLevels.add(ORGANIZATION_LEVEL.LINE);
    if (search?.factory) searchLevels.add(ORGANIZATION_LEVEL.FACTORY);
    if (search?.site) searchLevels.add(ORGANIZATION_LEVEL.SITE);
    if (search?.group) searchLevels.add(ORGANIZATION_LEVEL.GROUP);

    // Tạo điều kiện tìm kiếm cho từng level
    if (search?.group) {
      conditions.push(
        `(UPPER(level.code) = '${ORGANIZATION_LEVEL.GROUP}' AND (UPPER(org.code) LIKE UPPER(:groupSearch) OR UPPER(org.name) LIKE UPPER(:groupSearch)))`,
      );
      params.groupSearch = `%${search.group}%`;
    }

    if (search?.site) {
      conditions.push(
        `(UPPER(level.code) = '${ORGANIZATION_LEVEL.SITE}' AND (UPPER(org.code) LIKE UPPER(:siteSearch) OR UPPER(org.name) LIKE UPPER(:siteSearch)))`,
      );
      params.siteSearch = `%${search.site}%`;
    }

    if (search?.factory) {
      conditions.push(
        `(UPPER(level.code) = '${ORGANIZATION_LEVEL.FACTORY}' AND (UPPER(org.code) LIKE UPPER(:factorySearch) OR UPPER(org.name) LIKE UPPER(:factorySearch)))`,
      );
      params.factorySearch = `%${search.factory}%`;
    }

    if (search?.line) {
      conditions.push(
        `(UPPER(level.code) = '${ORGANIZATION_LEVEL.LINE}' AND (UPPER(org.code) LIKE UPPER(:lineSearch) OR UPPER(org.name) LIKE UPPER(:lineSearch)))`,
      );
      params.lineSearch = `%${search.line}%`;
    }

    if (search?.process) {
      conditions.push(
        `(UPPER(level.code) = '${ORGANIZATION_LEVEL.PROCESS}' AND (UPPER(org.code) LIKE UPPER(:processSearch) OR UPPER(org.name) LIKE UPPER(:processSearch)))`,
      );
      params.processSearch = `%${search.process}%`;
    }

    searchQueryBuilder.andWhere(`(${conditions.join(' OR ')})`, params);

    const matchedOrgs = await searchQueryBuilder.getMany();
    const matchedIds = new Set<string>();
    const childrenIds = new Set<string>();
    const parentChains = new Map<string, Set<string>>();

    // Thu thập ID của các bản ghi thỏa mãn điều kiện tìm kiếm và xây dựng chuỗi parent
    for (const org of matchedOrgs) {
      // Kiểm tra xem bản ghi có thực sự khớp với điều kiện tìm kiếm không và có thuộc level đang tìm không
      const isMatched =
        (search?.group &&
          org.level.code.toUpperCase() === ORGANIZATION_LEVEL.GROUP &&
          (org.code.toUpperCase().includes(search.group.toUpperCase()) ||
            org.name.toUpperCase().includes(search.group.toUpperCase()))) ||
        (search?.site &&
          org.level.code.toUpperCase() === ORGANIZATION_LEVEL.SITE &&
          (org.code.toUpperCase().includes(search.site.toUpperCase()) ||
            org.name.toUpperCase().includes(search.site.toUpperCase()))) ||
        (search?.factory &&
          org.level.code.toUpperCase() === ORGANIZATION_LEVEL.FACTORY &&
          (org.code.toUpperCase().includes(search.factory.toUpperCase()) ||
            org.name.toUpperCase().includes(search.factory.toUpperCase()))) ||
        (search?.line &&
          org.level.code.toUpperCase() === ORGANIZATION_LEVEL.LINE &&
          (org.code.toUpperCase().includes(search.line.toUpperCase()) ||
            org.name.toUpperCase().includes(search.line.toUpperCase()))) ||
        (search?.process &&
          org.level.code.toUpperCase() === ORGANIZATION_LEVEL.PROCESS &&
          (org.code.toUpperCase().includes(search.process.toUpperCase()) ||
            org.name.toUpperCase().includes(search.process.toUpperCase())));

      if (isMatched && searchLevels.has(org.level.code)) {
        matchedIds.add(org.id);
        const parentChain = new Set<string>();

        let currentOrg = await this.organizationUnitRepo.findOne({
          where: { id: org.id },
          select: ['id', 'parentId'],
        });

        while (currentOrg?.parentId) {
          parentChain.add(currentOrg.parentId);
          currentOrg = await this.organizationUnitRepo.findOne({
            where: { id: currentOrg.parentId },
            select: ['id', 'parentId'],
          });
        }

        parentChains.set(org.id, parentChain);

        // Thu thập các cấp con
        if (org.level.code !== ORGANIZATION_LEVEL.PROCESS) {
          const children = await this.findAllChildren(org.id);
          children.forEach((childId) => childrenIds.add(childId));
        }
      }
    }

    // Nếu không có kết quả tìm kiếm, trả về mảng rỗng
    if (matchedIds.size === 0) {
      return {
        data: [],
        pagination: {
          page,
          pageSize,
          total: 0,
          totalPages: 0,
        },
      };
    }

    // Lấy chi tiết của các organization đã tìm thấy và parent của chúng
    const queryBuilder = this.organizationUnitRepo
      .createQueryBuilder('org')
      .innerJoinAndSelect('org.level', 'level')
      .select([
        'org.id as id',
        'org.code as code',
        'org.name as name',
        'org.parentId as parentId',
        'level.code as "levelCode"',
      ]);

    // Tạo tập hợp tất cả các ID cần lấy (bao gồm cả parent và children)
    const allRequiredIds = new Set<string>();
    for (const [matchedId, parentSet] of parentChains.entries()) {
      allRequiredIds.add(matchedId);
      for (const parentId of parentSet) {
        allRequiredIds.add(parentId);
      }
    }
    for (const childId of childrenIds) {
      allRequiredIds.add(childId);
    }

    queryBuilder.andWhere('org.id IN (:...ids)', { ids: Array.from(allRequiredIds) });

    // Sắp xếp theo level và code
    queryBuilder
      .orderBy(
        `CASE 
          WHEN UPPER(level.code) = '${ORGANIZATION_LEVEL.GROUP}' THEN 1
          WHEN UPPER(level.code) = '${ORGANIZATION_LEVEL.SITE}' THEN 2
          WHEN UPPER(level.code) = '${ORGANIZATION_LEVEL.FACTORY}' THEN 3
          WHEN UPPER(level.code) = '${ORGANIZATION_LEVEL.LINE}' THEN 4
          WHEN UPPER(level.code) = '${ORGANIZATION_LEVEL.PROCESS}' THEN 5
          ELSE 6 
        END`,
      )
      .addOrderBy('org.code', 'ASC');

    const organizations = await queryBuilder.getRawMany();

    // Xây dựng kết quả phân cấp cho tất cả các bản ghi
    const hierarchicalResults = this.buildHierarchicalResult(organizations);

    // Sắp xếp lại kết quả theo cấu trúc cây
    const sortedData = this.sortTreeStructure(hierarchicalResults.data);
    const filteredData = sortedData.filter(
      (record) => matchedIds.has(record.id) || childrenIds.has(record.id),
    );

    // Xử lý phân trang cho trường hợp có tìm kiếm
    const totalRecords = filteredData.length;
    const totalPages = Math.ceil(totalRecords / pageSize);

    // Đảm bảo page không vượt quá totalPages và không nhỏ hơn 1
    const validPage = Math.max(1, Math.min(page, totalPages || 1));
    const skip = (validPage - 1) * pageSize;
    const paginatedData = filteredData.slice(skip, skip + pageSize);

    return {
      data: paginatedData,
      pagination: {
        page: validPage,
        pageSize,
        total: totalRecords,
        totalPages,
      },
    };
  }

  private async findAllChildren(parentId: string): Promise<string[]> {
    const children = await this.organizationUnitRepo.find({
      where: { parentId },
      select: ['id'],
    });

    const childrenIds = children.map((child) => child.id);
    const grandChildren = await Promise.all(
      children.map((child) => this.findAllChildren(child.id)),
    );

    return [...childrenIds, ...grandChildren.flat()];
  }

  private buildHierarchicalResult(organizations: any[]) {
    const orgMap = new Map(organizations.map((org) => [org.id, org]));

    const result = organizations.map((org) => {
      const hierarchyInfo: any = {
        id: org.id,
        code: org.code,
        name: org.name,
        levelCode: org.levelCode,
        status: org?.status !== undefined ? org?.status : true,
      };

      const parents = [];
      let currentOrg = org;
      while (currentOrg.parentid) {
        const parent = orgMap.get(currentOrg.parentid);
        if (parent) {
          parents.unshift(parent);
          currentOrg = parent;
        } else {
          break;
        }
      }

      switch (org.levelCode.toUpperCase()) {
        case ORGANIZATION_LEVEL.GROUP: {
          hierarchyInfo.group = {
            id: org.id,
            code: org.code,
            name: org.name,
          };
          break;
        }

        case ORGANIZATION_LEVEL.SITE: {
          const group = parents.find((p) => p.levelCode.toUpperCase() === ORGANIZATION_LEVEL.GROUP);
          if (group) {
            hierarchyInfo.group = {
              id: group.id,
              code: group.code,
              name: group.name,
            };
          }
          hierarchyInfo.site = {
            id: org.id,
            code: org.code,
            name: org.name,
          };
          break;
        }

        case ORGANIZATION_LEVEL.FACTORY: {
          const group = parents.find((p) => p.levelCode.toUpperCase() === ORGANIZATION_LEVEL.GROUP);
          const site = parents.find((p) => p.levelCode.toUpperCase() === ORGANIZATION_LEVEL.SITE);
          if (group) {
            hierarchyInfo.group = {
              id: group.id,
              code: group.code,
              name: group.name,
            };
          }
          if (site) {
            hierarchyInfo.site = {
              id: site.id,
              code: site.code,
              name: site.name,
            };
          }
          hierarchyInfo.factory = {
            id: org.id,
            code: org.code,
            name: org.name,
          };
          break;
        }

        case ORGANIZATION_LEVEL.LINE: {
          const group = parents.find((p) => p.levelCode.toUpperCase() === ORGANIZATION_LEVEL.GROUP);
          const site = parents.find((p) => p.levelCode.toUpperCase() === ORGANIZATION_LEVEL.SITE);
          const factory = parents.find(
            (p) => p.levelCode.toUpperCase() === ORGANIZATION_LEVEL.FACTORY,
          );
          if (group) {
            hierarchyInfo.group = {
              id: group.id,
              code: group.code,
              name: group.name,
            };
          }
          if (site) {
            hierarchyInfo.site = {
              id: site.id,
              code: site.code,
              name: site.name,
            };
          }
          if (factory) {
            hierarchyInfo.factory = {
              id: factory.id,
              code: factory.code,
              name: factory.name,
            };
          }
          hierarchyInfo.line = {
            id: org.id,
            code: org.code,
            name: org.name,
          };
          break;
        }

        case ORGANIZATION_LEVEL.PROCESS: {
          const group = parents.find((p) => p.levelCode.toUpperCase() === ORGANIZATION_LEVEL.GROUP);
          const site = parents.find((p) => p.levelCode.toUpperCase() === ORGANIZATION_LEVEL.SITE);
          const factory = parents.find(
            (p) => p.levelCode.toUpperCase() === ORGANIZATION_LEVEL.FACTORY,
          );
          const line = parents.find((p) => p.levelCode.toUpperCase() === ORGANIZATION_LEVEL.LINE);
          if (group) {
            hierarchyInfo.group = {
              id: group.id,
              code: group.code,
              name: group.name,
            };
          }
          if (site) {
            hierarchyInfo.site = {
              id: site.id,
              code: site.code,
              name: site.name,
            };
          }
          if (factory) {
            hierarchyInfo.factory = {
              id: factory.id,
              code: factory.code,
              name: factory.name,
            };
          }
          if (line) {
            hierarchyInfo.line = {
              id: line.id,
              code: line.code,
              name: line.name,
            };
          }
          hierarchyInfo.processArea = {
            id: org.id,
            code: org.code,
            name: org.name,
          };
          break;
        }
      }

      return hierarchyInfo;
    });

    return { data: result };
  }

  private sortTreeStructure(data: any[]): any[] {
    // Tạo map để lưu trữ node theo id
    const nodeMap = new Map<string, any>();
    data.forEach((node) => nodeMap.set(node.id, { ...node, children: [] }));

    // Xây dựng cấu trúc cây
    const rootNodes: any[] = [];
    data.forEach((node) => {
      const currentNode = nodeMap.get(node.id);

      // Xác định node cha dựa trên cấu trúc phân cấp
      let parentNode = null;
      switch (node.levelCode.toUpperCase()) {
        case ORGANIZATION_LEVEL.PROCESS:
          if (node.line?.id) parentNode = nodeMap.get(node.line.id);
          break;
        case ORGANIZATION_LEVEL.LINE:
          if (node.factory?.id) parentNode = nodeMap.get(node.factory.id);
          break;
        case ORGANIZATION_LEVEL.FACTORY:
          if (node.site?.id) parentNode = nodeMap.get(node.site.id);
          break;
        case ORGANIZATION_LEVEL.SITE:
          if (node.group?.id) parentNode = nodeMap.get(node.group.id);
          break;
      }

      if (parentNode) {
        parentNode.children.push(currentNode);
      } else {
        rootNodes.push(currentNode);
      }
    });

    // Hàm sắp xếp đệ quy
    const sortNodeAndChildren = (node: any) => {
      // Sắp xếp các node con
      node.children.sort((a: any, b: any) => a.code.localeCompare(b.code));
      // Sắp xếp đệ quy cho từng node con
      node.children.forEach(sortNodeAndChildren);
    };

    // Sắp xếp root nodes theo code
    rootNodes.sort((a, b) => a.code.localeCompare(b.code));
    rootNodes.forEach(sortNodeAndChildren);

    // Chuyển đổi cấu trúc cây thành mảng phẳng
    const result: any[] = [];
    const flattenTree = (node: any) => {
      const { children, ...nodeWithoutChildren } = node;
      result.push(nodeWithoutChildren);
      children.forEach(flattenTree);
    };

    rootNodes.forEach(flattenTree);
    return result;
  }

  @DefTransaction()
  async updateAccess(userId: string, addedIds: string[], removedIds: string[]): Promise<void> {
    // Kiểm tra user có tồn tại không
    const user = await this.userRepo.findOne({
      where: { id: userId },
    });

    if (!user) {
      throw new Error('Người dùng không tồn tại!');
    }

    // Kiểm tra các organization units có tồn tại không
    const allIds = [...addedIds, ...removedIds];
    if (allIds.length > 0) {
      const existingOrgs = await this.organizationUnitRepo.find({
        where: { id: In(allIds) },
      });

      if (existingOrgs.length !== allIds.length) {
        throw new Error('Một số Organization Unit không tồn tại!');
      }
    }

    // Xóa các quyền truy cập cũ
    if (removedIds.length > 0) {
      await this.accessRepo.delete({
        user: { id: userId },
        organizationUnit: { id: In(removedIds) },
      });
    }

    // Thêm các quyền truy cập mới
    if (addedIds.length > 0) {
      const newAccesses = addedIds.map((orgId) => ({
        user: { id: userId },
        organizationUnit: { id: orgId },
      }));
      await this.accessRepo.saves(newAccesses);
    }
  }

  async getUserAccess(userId: string) {
    // Kiểm tra user có tồn tại không
    const user = await this.userRepo.findOne({
      where: { id: userId },
    });

    if (!user) {
      throw new Error('Người dùng không tồn tại!');
    }

    // Lấy danh sách access của user
    const accesses = await this.accessRepo.find({
      where: {
        user: { id: userId },
      },
      relations: ['organizationUnit', 'organizationUnit.level'],
    });

    // Lấy danh sách organization units từ accesses
    const organizationUnits = accesses.map((access) => ({
      id: access.organizationUnit.id,
      code: access.organizationUnit.code,
      name: access.organizationUnit.name,
      levelCode: access.organizationUnit.level.code,
      status: access.status,
    }));

    // Xây dựng kết quả phân cấp
    const hierarchicalResults = this.buildHierarchicalResult(organizationUnits);

    // Sắp xếp lại kết quả theo cấu trúc cây
    const sortedData = this.sortTreeStructure(hierarchicalResults.data);

    return {
      data: sortedData,
    };
  }

  @DefTransaction()
  async updateStatus(userId: string, organizationId: string) {
    const existOrganization = await this.organizationUnitRepo.findOne({
      where: { id: organizationId },
    });
    if (!existOrganization) throw new Error('Tổ chức không tồn tại!');
    // Kiểm tra user có tồn tại không
    const user = await this.userRepo.findOne({
      where: { id: userId },
    });

    if (!user) {
      throw new Error('Người dùng không tồn tại!');
    }
    const access = await this.accessRepo.findOne({
      where: { user: { id: userId }, organizationUnit: { id: organizationId } },
    });
    await this.accessRepo.update({ id: access.id }, { status: !access.status });
    return {
      message: 'Update status success',
    };
  }

  async pagination(userId: string, params: PaginationReq) {
    const { pageSize, pageIndex } = params;
    const whereConOrgSite: any = { isActive: true, levelGeneralDataDetailCode: 'SITE' };
    if (params.siteName) whereConOrgSite.name = ILike(`%${params.siteName}%`);
    const lstSite = await this.organizationUnitRepo.find({ where: whereConOrgSite });
    const whereConOrgFac: any = {
      isActive: true,
      levelGeneralDataDetailCode: 'FACTORY',
      parentId: In(lstSite.map((i) => i.id)),
    };
    if (params.factoryName) whereConOrgFac.name = ILike(`%${params.factoryName}%`);
    const lstFactory = await this.organizationUnitRepo.find({ where: whereConOrgFac });
    const whereCon: any = {
      user: { id: userId },
      organizationUnit: { id: In(lstFactory.map((i) => i.id)) },
    };
    if (params.status !== undefined) {
      const statusStr = String(params.status).toLowerCase();
      if (statusStr === 'true') {
        whereCon.status = true;
      } else if (statusStr === 'false') {
        whereCon.status = false;
      }
    }

    if (params.default !== undefined) {
      const defaultStr = String(params.default).toLowerCase();
      if (defaultStr === 'true') {
        whereCon.default = true;
      } else if (defaultStr === 'false') {
        whereCon.default = false;
      }
    }

    const lstFactoryAccess = await this.accessRepo.findPagination(
      {
        where: whereCon,
        relations: ['organizationUnit', 'user'],
      },
      { pageSize, pageIndex },
    );
    const dictFactory: any = {};
    {
      const lstFactory = await this.organizationUnitRepo.find({
        where: {
          id: In(lstFactoryAccess.data.map((i: any) => i.organizationUnit.id)),
          levelGeneralDataDetailCode: 'FACTORY',
        },
      });
      lstFactory.forEach((i) => (dictFactory[i.id] = i));
    }
    const dictSite: any = {};
    {
      const lstSiteId = Object.values(dictFactory).map((i: any) => i.parentId);
      const lstSite = await this.organizationUnitRepo.find({
        where: { id: In(lstSiteId) },
      });
      lstSite.forEach((i) => (dictSite[i.id] = i));
    }
    lstFactoryAccess.data.forEach((i: any) => {
      const factory = dictFactory[i.organizationUnit.id];
      if (!factory) return;
      const site = dictSite[factory.parentId];
      i.factoryId = factory.id;
      i.factoryCode = factory.code;
      i.factoryName = factory.name;
      i.siteId = site.id;
      i.siteCode = site.code;
      i.siteName = site.name;
      delete i.organizationUnit;
      delete i.user;
    });
    return lstFactoryAccess;
  }

  @DefTransaction()
  async createAccess(userId: string, data: any) {
    const existOrganization = await this.organizationUnitRepo.findOne({
      where: { id: data.organizationId },
    });
    if (!existOrganization) throw new BusinessException('Tổ chức không tồn tại!');

    const site = await this.organizationUnitRepo.findOne({
      where: { id: existOrganization.parentId, isActive: true },
    });
    if (!site) throw new BusinessException('Tổ chức không tồn tại!');
    // Kiểm tra user có tồn tại không
    const user = await this.userRepo.findOne({
      where: { id: userId },
    });

    if (!user) {
      throw new BusinessException('Người dùng không tồn tại!');
    }

    const checkAccess = await this.accessRepo.findOne({
      where: { user: { id: userId }, organizationUnit: { id: data.organizationId } },
    });
    if (checkAccess) throw new BusinessException('User này đã tồn tại quyền truy cập hiện tại!');
    await this.accessRepo.save({
      organizationUnit: {
        id: data.organizationId,
      },
      user: {
        id: userId,
      },
      status: data.status,
      default: false,
    });
    return { message: 'Create Successful' };
  }

  async loadSiteData() {
    return await this.organizationUnitRepo.find({
      where: { levelGeneralDataDetailCode: 'SITE', isActive: true },
    });
  }

  async loadFactoryData() {
    return await this.organizationUnitRepo.find({
      where: { levelGeneralDataDetailCode: 'FACTORY', isActive: true },
    });
  }

  async updateDefault(userId: string, accessId) {
    const lstAccess = await this.accessRepo.find({
      where: { user: { id: userId } },
      relations: ['organizationUnit'],
    });
    const defaultAccess = lstAccess.find((i) => i.default === true);
    if (defaultAccess && accessId !== defaultAccess.id)
      throw new BusinessException(
        'User này đã tồn tại default access! Vui lòng tắt default access trước khi chuyển đổi!',
      );
    if (!defaultAccess || (defaultAccess && accessId === defaultAccess.id)) {
      const res = await this.accessRepo.update(
        { user: { id: userId }, id: accessId },
        { default: defaultAccess ? !defaultAccess.default : true },
      );

      return { message: 'Update Default Successful', data: res };
    }
  }

  async getDefaultAccessByUser(userId: string) {
    const defaultAccess = await this.accessRepo.findOne({
      where: { user: { id: userId }, status: true, default: true },
      relations: ['organizationUnit'],
    });
    if (!defaultAccess) return null;
    const factory = await this.organizationUnitRepo.findOne({
      where: { id: defaultAccess.organizationUnit.id },
    });
    const site = await this.organizationUnitRepo.findOne({ where: { id: factory.parentId } });
    return {
      factoryName: factory.name,
      factoryCode: factory.code,
      factoryId: factory.id,
      siteId: site.id,
      siteCode: site.code,
      siteName: site.name,
    };
  }

  async getUserList() {
    return await this.userRepo.find({ where: { isActive: true } });
  }

  async getDefaultAccessForCurrentUser() {
    const { userId } = adminSessionContext;
    if (!userId) return null;
    const defaultAccess = await this.accessRepo.findOne({
      where: { user: { id: userId }, status: true, default: true },
      relations: ['organizationUnit'],
    });
    if (!defaultAccess) return null;
    const factory = await this.organizationUnitRepo.findOne({
      where: { id: defaultAccess.organizationUnit.id },
    });
    const site = await this.organizationUnitRepo.findOne({ where: { id: factory.parentId } });
    return {
      factoryName: factory.name,
      factoryCode: factory.code,
      factoryId: factory.id,
      siteId: site?.id,
      siteCode: site?.code,
      siteName: site?.name,
    };
  }

  async getSiteAccessForCurrentUser(): Promise<PageResponse<OrganizationUnitEntity>> {
    const { userId } = adminSessionContext;
    if (!userId) return { data: [], total: 0 };
    const lstAccess = await this.accessRepo.find({
      where: {
        status: true,
        user: { id: userId },
      },
      relations: ['organizationUnit', 'user'],
    });
    if (lstAccess.length === 0) return { data: [], total: 0 };
    const lstFactoryId = lstAccess.map((item) => item.organizationUnit.id);
    const lstFactory = await this.organizationUnitRepo.find({
      where: { isActive: true, id: In(lstFactoryId) },
    });
    const sites = await this.organizationUnitRepo.find({
      where: {
        isActive: true,
        id: In(lstFactory.map((i) => i.parentId)),
        levelGeneralDataDetailCode: NSGeneralData.EOrgLevel.SITE,
      },
      order: { code: 'ASC', createdDate: 'DESC' },
    });
    return { data: sites, total: sites.length };
  }

  async getFactoryAccessBySiteIdLstForCurrentUser(req: {
    siteIdLst?: string[];
  }): Promise<PageResponse<OrganizationUnitEntity>> {
    const { userId } = adminSessionContext;
    if (!userId) return { data: [], total: 0 };
    const lstAccess = await this.accessRepo.find({
      where: {
        status: true,
        user: { id: userId },
      },
      relations: ['organizationUnit', 'user'],
    });
    if (lstAccess.length === 0) return { data: [] };
    const lstFactory = lstAccess.map((item) => item.organizationUnit.id);
    const factories = await this.organizationUnitRepo.find({
      where: {
        isActive: true,
        id: In(lstFactory),
        levelGeneralDataDetailCode: NSGeneralData.EOrgLevel.FACTORY,
        ...(req?.siteIdLst && { parentId: In(req.siteIdLst) }),
      },
      order: { code: 'ASC', createdDate: 'DESC' },
    });
    return { data: factories, total: factories.length };
  }
}
