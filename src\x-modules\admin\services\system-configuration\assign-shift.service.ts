import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import moment from 'moment';
import { In, Raw } from 'typeorm';
import { BindRepo } from '~/@core/decorator';
import {
  AssignShiftDto,
  AssignShiftEditDto,
  AssignShiftPaginationDto,
} from '~/dto/assign-shift.dto';
import { AssignShiftEntity } from '~/entities/primary/assign-shift.entity';
import { ShiftEntity } from '~/entities/primary/shift.entity';
import { AssignShiftRepository } from '~/repositories/primary/assign-shift.repo';
import { OrganizationUnitRepo } from '~/repositories/primary/organization-unit.repo';
import { ShiftRepo } from '~/repositories/primary/shift.repo';

@Injectable()
export class AssignShiftService {
  constructor() {}

  @BindRepo(AssignShiftRepository)
  private assignShiftRepository: AssignShiftRepository;
  @BindRepo(OrganizationUnitRepo)
  private organizationUnitRepository: OrganizationUnitRepo;
  @BindRepo(ShiftRepo)
  private shiftRepository: ShiftRepo;

  async create(body: AssignShiftDto): Promise<AssignShiftEntity> {
    const existingAssignShift = await this.assignShiftRepository.findOne({
      where: {
        shift: { id: body.shiftId },
        organization: { id: body.organizationId },
      },
    });

    if (existingAssignShift) {
      throw new BadRequestException({
        message: 'Assign shift  and organization already exists.',
      });
    }
    const newAssignShift = this.assignShiftRepository.create(body);
    return await this.assignShiftRepository.save(newAssignShift);
  }

  async findOne(id: number): Promise<AssignShiftEntity | undefined> {
    return await this.assignShiftRepository.findOne(id);
  }

  async findAll(): Promise<AssignShiftEntity[]> {
    return await this.assignShiftRepository.find({
      relations: ['organization', 'shift'],
      order: {
        createdDate: 'DESC',
      },
    });
  }

  async update(updateData: AssignShiftEditDto): Promise<AssignShiftEntity | undefined> {
    const assignShift = await this.assignShiftRepository.findOne(updateData.id);
    if (!assignShift) {
      throw new BadRequestException(`Assign shift  not found.`);
    }

    // Kiểm tra nếu shiftId và organizationId được cập nhật
    if (updateData.shiftId && updateData.organizationId) {
      const existingAssignShift = await this.assignShiftRepository.findOne({
        where: {
          shift: { id: updateData.shiftId },
          organization: { id: updateData.organizationId },
        },
      });

      if (existingAssignShift && existingAssignShift.id !== updateData.id) {
        throw new BadRequestException(`Assign shift  and  already exists.`);
      }
    }
    this.assignShiftRepository.merge(assignShift, updateData);
    return await this.assignShiftRepository.save(assignShift);
  }

  async delete(id: number): Promise<void> {
    const assignShift = await this.assignShiftRepository.findOne(id);
    if (!assignShift) {
      throw new NotFoundException(`Assign shift not found.`);
    }
    await this.assignShiftRepository.remove(assignShift);
  }

  async getSelectBoxOrganizationUnit(): Promise<any[]> {
    // Lấy danh sách tất cả các đơn vị tổ chức
    const lstOrg = await this.organizationUnitRepository.find({
      relations: ['level'],
    });

    // Tạo một map để lưu các node theo id
    const map = new Map<string, any>();
    // Mảng chứa các node gốc (root nodes)
    const tree: any[] = [];

    // Đầu tiên, chuyển đổi mỗi đơn vị thành một node có cấu trúc mong muốn
    lstOrg.forEach((item) => {
      map.set(item.id, {
        id: item.id,
        code: item.code,
        name: item.name,
        description: item.description,
        level: item.level
          ? {
              id: item.level.id,
              code: item.level.code,
            }
          : null,
        children: [],
      });
    });

    // Sau đó, xây dựng cây theo mối quan hệ parent-child
    lstOrg.forEach((item) => {
      const node = map.get(item.id);
      if (item.parentId) {
        // Nếu có parentId, tìm node cha và thêm node hiện tại vào mảng children của node cha
        const parentNode = map.get(item.parentId);
        if (parentNode) {
          parentNode.children.push(node);
        } else {
          // Nếu không tìm thấy node cha, coi node hiện tại là node gốc
          tree.push(node);
        }
      } else {
        // Nếu không có parentId, đây là node gốc
        tree.push(node);
      }
    });

    return tree;
  }

  async getSelectBoxShift(): Promise<ShiftEntity[]> {
    return await this.shiftRepository.find();
  }

  async pagination(params: AssignShiftPaginationDto) {
    const { pageIndex, pageSize, where } = params;
    const whereCon: any = {};
    const whereConShift: any = {};

    // Handle validFrom and validTo
    if (where?.validFrom) {
      const validFromStr = moment(where.validFrom, 'YYYY-MM-DD').format('YYYY-MM-DD');
      whereCon.validFrom = Raw((alias) => `to_char(${alias}, 'YYYY-MM-DD') = :validFrom`, {
        validFrom: validFromStr,
      });
    }
    if (where?.validTo) {
      const validToStr = moment(where.validTo, 'YYYY-MM-DD').format('YYYY-MM-DD');
      whereCon.validTo = Raw((alias) => `to_char(${alias}, 'YYYY-MM-DD') = :validTo`, {
        validTo: validToStr,
      });
    }

    // Filter by Shift Entity
    if (where?.shiftCode) {
      const shiftCode = where.shiftCode.trim().toLowerCase();
      whereConShift.code = Raw((alias) => `LOWER(${alias}) LIKE :shiftCode`, {
        shiftCode: `%${shiftCode}%`,
      });
    }
    if (where?.shiftDescription) {
      const shiftDescription = where.shiftDescription.trim().toLowerCase();
      whereConShift.description = Raw((alias) => `LOWER(${alias}) LIKE :shiftDescription`, {
        shiftDescription: `%${shiftDescription}%`,
      });
    }
    if (where?.nightShift !== null && where?.nightShift !== undefined) {
      whereConShift.nightShift = where.nightShift;
    }
    if (where?.shiftStartTime) {
      whereConShift.startTime = Raw((alias) => `TO_CHAR(${alias}, 'HH24:MI') = :shiftStartTime`, {
        shiftStartTime: where.shiftStartTime,
      });
    }
    if (where?.shiftEndTime) {
      whereConShift.endTime = Raw((alias) => `TO_CHAR(${alias}, 'HH24:MI') = :shiftEndTime`, {
        shiftEndTime: where.shiftEndTime,
      });
    }

    // Filter by Organization-Unit Entity
    let organizationIds: string[] = [];
    if (where?.groupCode) {
      organizationIds = [
        ...organizationIds,
        ...(await this.findOrganizationsByCode(where?.groupCode)),
      ];
    }
    if (where?.siteCode) {
      organizationIds = [
        ...organizationIds,
        ...(await this.findOrganizationsByCode(where.siteCode)),
      ];
    }
    if (where?.factoryCode) {
      organizationIds = [
        ...organizationIds,
        ...(await this.findOrganizationsByCode(where.factoryCode)),
      ];
    }
    if (where?.lineCode) {
      organizationIds = [
        ...organizationIds,
        ...(await this.findOrganizationsByCode(where.lineCode)),
      ];
    }
    if (where?.processCode) {
      organizationIds = [
        ...organizationIds,
        ...(await this.findOrganizationsByCode(where.processCode)),
      ];
    }

    if (where?.status !== undefined) {
      whereCon.status = where.status;
    }

    // Query shifts
    const shifts = await this.shiftRepository.find({ where: whereConShift });
    whereCon.shiftId = shifts.length > 0 ? In(shifts.map((shift) => shift.id)) : In([]);

    if (
      !!where?.siteCode ||
      !!where?.factoryCode ||
      !!where?.lineCode ||
      !!where?.groupCode ||
      !!where?.processCode
    ) {
      whereCon.organizationId = In(organizationIds);
    }

    // Fetch paginated data
    const [data, total] = await this.assignShiftRepository.findAndCount({
      where: whereCon,
      relations: ['organization', 'shift'],
      order: { createdDate: 'DESC' },
      skip: (pageIndex - 1) * pageSize,
      take: pageSize,
    });

    // Format organization
    const formattedData = await Promise.all(
      data.map(async (item) => ({
        ...item,
        organization: await this.formatOrganizationAsObject(item.organization.id),
      })),
    );

    return { data: formattedData, total, pageIndex, pageSize };
  }
  async findOrganizationsByCode(codeOrName: string): Promise<string[]> {
    const lowerCaseCode = codeOrName.trim().toLowerCase();

    // Tìm tổ chức trực tiếp theo code hoặc name
    const orgs = await this.organizationUnitRepository.find({
      where: [
        { code: Raw((alias) => `LOWER(${alias}) LIKE :lowerCaseCode`, { lowerCaseCode }) },
        { name: Raw((alias) => `LOWER(${alias}) LIKE :lowerCaseCode`, { lowerCaseCode }) },
      ],
      relations: ['parent', 'children'],
    });

    let organizationIds = orgs.map((org) => org.id);

    // Tìm tất cả tổ chức cha
    for (const org of orgs) {
      let currentOrg = org;
      while (currentOrg.parent) {
        organizationIds.push(currentOrg.parent.id);
        currentOrg = await this.organizationUnitRepository.findOne({
          where: { id: currentOrg.parent.id },
          relations: ['parent'],
        });
        if (!currentOrg) break;
      }
    }

    // Tìm tất cả tổ chức con
    for (const org of orgs) {
      const findChildren = async (parent: any) => {
        const children = await this.organizationUnitRepository.find({
          where: { parent: { id: parent.id } },
          relations: ['children'],
        });
        for (const child of children) {
          organizationIds.push(child.id);
          await findChildren(child);
        }
      };
      await findChildren(org);
    }

    return Array.from(new Set(organizationIds)); // Loại bỏ trùng lặp ID
  }

  // Hàm format tổ chức theo kiểu object (cây từ con đến cha)
  async formatOrganizationAsObject(orgId: string) {
    let currentOrg = await this.organizationUnitRepository.findOne({
      where: { id: orgId },
      relations: ['parent', 'level'],
    });

    if (!currentOrg) return null;

    const buildHierarchy = async (org: any) => {
      if (!org) return null;

      const parentOrg = org.parent
        ? await this.organizationUnitRepository.findOne({
            where: { id: org.parent.id },
            relations: ['parent', 'level'],
          })
        : null;

      return {
        id: org.id,
        code: org.code,
        name: org.name,
        level: org.level
          ? {
              id: org.level.id,
              code: org.level.code,
            }
          : null,
        parent: await buildHierarchy(parentOrg),
      };
    };

    return buildHierarchy(currentOrg);
  }
}
