import { Column, CreateDateColumn, Entity, Index, PrimaryGeneratedColumn } from 'typeorm';
import { PrimaryBaseEntity } from '../primary-base.entity';
import { ApiProperty } from '@nestjs/swagger';
import { IsUUID } from 'class-validator';

@Entity('mes_dx_metrics')
export class MesDxMetricEntity {
  @ApiProperty()
  @PrimaryGeneratedColumn('uuid')
  @IsUUID(4)
  id?: string;

  @Index()
  @ApiProperty()
  @CreateDateColumn({ type: 'timestamptz' })
  createdDate?: Date;

  @ApiProperty({ description: 'Tên của gateway liên quan đến metric', example: 'Gateway A' })
  @Column()
  gatewayName: string;

  @ApiProperty({ description: 'ID duy nhất của gateway', example: 'GATEWAY_12345' })
  @Column()
  gatewayId: string;

  @ApiProperty({ description: 'Tên của nguồn dữ liệu', example: 'Datasource 1' })
  @Column()
  datasourceName: string;

  @ApiProperty({ description: 'ID của nguồn dữ liệu', example: 'DS_001' })
  @Column()
  datasourceId: string;

  @ApiProperty({ description: 'Tên của mô hình', example: 'Model X' })
  @Column()
  modelName: string;

  @ApiProperty({ description: 'ID của mô hình', example: 'MODEL_9876' })
  @Column()
  modelId: string;

  @ApiProperty({ description: 'Tên của tài sản', example: 'Turbine 1' })
  @Column()
  assetName: string;

  @ApiProperty({ description: 'ID của tài sản', example: 'ASSET_5678' })
  @Column()
  assetId: string;

  @ApiProperty({ description: 'Tên của metric', example: 'Efficiency' })
  @Column()
  metricName: string;

  @Index()
  @ApiProperty({ description: 'ID của metric', example: 'METRIC_7890' })
  @Column()
  metricId: string;

  @ApiProperty({ description: 'Giá trị của metric', example: 88.5 })
  @Column({
    type: 'varchar',
    nullable: true,
  })
  value: string;

  @Index()
  @ApiProperty({ description: 'Thời gian ghi nhận metric', example: '2024-03-06T14:30:00Z' })
  @Column({ type: 'timestamptz', nullable: true })
  datetime?: Date;
}
