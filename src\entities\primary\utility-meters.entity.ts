import { ApiProperty } from '@nestjs/swagger';
import { <PERSON>ti<PERSON>, Column, ManyToOne, Join<PERSON><PERSON><PERSON><PERSON>, OneToMany } from 'typeorm';
import { PrimaryBaseEntity } from '../primary-base.entity';
import { GeneralDataDetailEntity } from './general-data-detail.entity';
import { OrganizationUnitEntity } from './organization-unit.entity';
import { UtilityMeterDetailEntity } from './utility-meter-detail.entity';

@Entity('utility_meter')
export class UtilityMetersEntity extends PrimaryBaseEntity {
  /** Mã đồng hồ tiện ích */
  @ApiProperty({ description: 'Mã đồng hồ tiện ích' })
  @Column({
    nullable: false,
    unique: true,
    // transformer: { to: (value: string) => value?.toUpperCase(), from: (value: string) => value },
  })
  code: string;

  /** Tên đồng hồ tiện ích */
  @ApiProperty({ description: 'Tên đồng hồ tiện ích' })
  @Column({ nullable: false })
  name: string;

  /** Tên tag */
  @ApiProperty({ description: 'Tên tag' })
  @Column({ name: 'tag_name', nullable: true })
  tagName: string;

  /** Địa chỉ tag */
  @ApiProperty({ description: 'Địa chỉ tag' })
  @Column({ name: 'tag_address', nullable: true })
  tagAddress: string;

  /** ID Nhà máy */
  @ApiProperty({ description: 'ID Nhà máy' })
  @Column({ name: 'factory_id', nullable: false })
  factoryId: string;

  /** Mã đơn vị đo */
  @ApiProperty({ description: 'Mã đơn vị đo' })
  @Column({
    name: 'uom_code',
    default: 'UOM',
    nullable: true,
    transformer: {
      to: (value: string) => value || null,
      from: (value: string) => value,
    },
  })
  uomCode: string;

  /** Mã chi tiết đơn vị đo */
  @ApiProperty({ description: 'Mã chi tiết đơn vị đo' })
  @Column({
    name: 'uom_detail_code',
    nullable: true,
    transformer: {
      to: (value: string) => value || null,
      from: (value: string) => value,
    },
  })
  uomDetailCode: string;

  /** Relation với bảng OrganizationUnit */
  @ManyToOne(() => OrganizationUnitEntity, (org) => org.utilityMeters, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'factory_id', referencedColumnName: 'id' })
  factory?: OrganizationUnitEntity;

  /** Relation với bảng OrganizationUnit cho parent */
  @ManyToOne(() => OrganizationUnitEntity, (org) => org.utilityMeters, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'parent_id', referencedColumnName: 'id' })
  parent?: OrganizationUnitEntity;

  /** ID Parent */
  @ApiProperty({ description: 'ID Parent' })
  @Column({ name: 'parent_id', nullable: true })
  parentId: string;

  /** Relation với bảng GeneralDataDetail cho UOM */
  @ManyToOne(() => GeneralDataDetailEntity, { createForeignKeyConstraints: false })
  @JoinColumn({ name: 'uom_detail_code', referencedColumnName: 'code' })
  uomDetail: GeneralDataDetailEntity;

  /** Mã loại đồng hồ tiện ích */
  @ApiProperty({ description: 'Mã loại đồng hồ tiện ích' })
  @Column({ name: 'utility_meter_type_code', default: 'UTILITY_METER_TYPE', nullable: false })
  utilityMeterTypeCode: string;

  /** Mã chi tiết loại đồng hồ tiện ích */
  @ApiProperty({ description: 'Mã chi tiết loại đồng hồ tiện ích' })
  @Column({ name: 'utility_meter_type_detail_code', nullable: false })
  utilityMeterTypeDetailCode: string;

  /** Relation với bảng GeneralDataDetail cho Utility Meter Type */
  @ManyToOne(() => GeneralDataDetailEntity, { createForeignKeyConstraints: false })
  @JoinColumn({ name: 'utility_meter_type_detail_code', referencedColumnName: 'code' })
  utilityMeterType?: GeneralDataDetailEntity;

  /** Mã kiểu dữ liệu */
  @ApiProperty({ description: 'Mã kiểu dữ liệu' })
  @Column({ name: 'data_type_code', default: 'DATA_TYPE', nullable: false })
  dataTypeCode: string;

  /** Mã chi tiết kiểu dữ liệu */
  @ApiProperty({ description: 'Mã chi tiết kiểu dữ liệu' })
  @Column({ name: 'data_type_detail_code', nullable: false })
  dataTypeDetailCode: string;

  /** Relation với bảng GeneralDataDetail cho Data Type */
  @ManyToOne(() => GeneralDataDetailEntity, { createForeignKeyConstraints: false })
  @JoinColumn({ name: 'data_type_detail_code', referencedColumnName: 'code' })
  dataType?: GeneralDataDetailEntity;

  /** Trạng thái hoạt động */
  @ApiProperty({ description: 'Trạng thái hoạt động' })
  @Column({ name: 'is_active', default: true, nullable: false })
  isActive: boolean;

  /** Có khoảng thời gian */
  @ApiProperty({ description: 'Có khoảng thời gian' })
  @Column({ name: 'interval', default: false, nullable: false })
  interval: boolean;

  /** Giá trị thời gian */
  @ApiProperty({ description: 'Giá trị thời gian' })
  @Column({ name: 'time_value', type: 'decimal', precision: 10, scale: 2, nullable: true })
  timeValue: number;

  /** Mã đơn vị thời gian */
  @ApiProperty({ description: 'Mã đơn vị thời gian' })
  @Column({ name: 'datetime_unit_code', default: 'DATETIME_UNIT', nullable: true })
  datetimeUnitCode: string;

  /** Mã chi tiết đơn vị thời gian */
  @ApiProperty({ description: 'Mã chi tiết đơn vị thời gian' })
  @Column({ name: 'datetime_unit_detail_code', nullable: true })
  datetimeUnitDetailCode: string;

  /** Relation với bảng GeneralDataDetail cho DateTime Unit */
  @ManyToOne(() => GeneralDataDetailEntity, { createForeignKeyConstraints: false })
  @JoinColumn({ name: 'datetime_unit_detail_code', referencedColumnName: 'code' })
  datetimeUnit?: GeneralDataDetailEntity;

  /** ID Node */
  @ApiProperty({ description: 'ID Node' })
  @Column({ name: 'node_id', nullable: true })
  nodeId: string;

  /** Ghi chú */
  @ApiProperty({ description: 'Ghi chú' })
  @Column({ name: 'note', nullable: true })
  note: string;

  /** Relation với bảng UtilityMeterDetail */
  @OneToMany(() => UtilityMeterDetailEntity, (detail) => detail.utilityMeter)
  details: UtilityMeterDetailEntity[];

  /** Calculation method */
  @ApiProperty({ description: 'Calculation method' })
  @Column({ name: 'cal_method_code', default: 'CAL_TYPE', nullable: true })
  calMethodCode: string;

  /** Relation với bảng GeneralDataDetail cho Calculation Method */
  @ManyToOne(() => GeneralDataDetailEntity, { createForeignKeyConstraints: false })
  @JoinColumn({ name: 'cal_method_code', referencedColumnName: 'code' })
  calMethod?: GeneralDataDetailEntity;

  /** UOM CAL METHOD CODE */
  @ApiProperty({ description: 'UOM CAL METHOD CODE' })
  @Column({
    name: 'uom_cal_method_code',
    default: 'UOM',
    nullable: true,
    transformer: {
      to: (value: string) => value || null,
      from: (value: string) => value,
    },
  })
  uomCalMethodCode: string;

  /** Relation với bảng GeneralDataDetail cho UOM Cal Method */
  @ManyToOne(() => GeneralDataDetailEntity, { createForeignKeyConstraints: false })
  @JoinColumn({ name: 'uom_cal_method_code', referencedColumnName: 'code' })
  uomCalMethod?: GeneralDataDetailEntity;

  /** RESOURCE CODE */
  @ApiProperty({ description: 'RESOURCE CODE' })
  @Column({
    name: 'resource_code',
    nullable: true,
    transformer: {
      to: (value: string) => value || null,
      from: (value: string) => value,
    },
  })
  resourceCode: string;

  /** ASSET ID */
  @ApiProperty({ description: 'ASSET ID' })
  @Column({
    name: 'asset_id',
    nullable: true,
    transformer: {
      to: (value: string) => value || null,
      from: (value: string) => value,
    },
  })
  assetId: string;

  /** MEASUREMENT ID */
  @ApiProperty({ description: 'MEASUREMENT ID' })
  @Column({
    name: 'measurement_id',
    nullable: true,
    transformer: {
      to: (value: string) => value || null,
      from: (value: string) => value,
    },
  })
  measurementId: string;

  /** METRIC ID */
  @ApiProperty({ description: 'METRIC ID' })
  @Column({
    name: 'metric_id',
    nullable: true,
    transformer: {
      to: (value: string) => value || null,
      from: (value: string) => value,
    },
  })
  metricId: string;
}
