import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { IsUUID } from 'class-validator';
import { PrimaryBaseEntity } from '../primary-base.entity';

@Entity('manhour_transactions')
export class ManhourTransactionsEntity extends PrimaryBaseEntity {
  @ApiProperty()
  @PrimaryGeneratedColumn('uuid')
  @IsUUID(4)
  transactionId?: string;

  @ApiProperty({
    description: 'Theo dòng dữ liệu timesheet_record',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @Column({ type: 'uuid' })
  productionAreaId: string;

  @ApiProperty({
    description: 'work_date của dòng dữ liệu timesheet_record',
    example: '2024-03-06T14:30:00Z',
  })
  @Column({ type: 'date' })
  productionDate: Date;

  @ApiProperty({
    description: 'theo dòng dữ liệu timesheet_record',
    example: '550e8400-e29b-41d4-a716-************',
  })
  @Column({ type: 'uuid' })
  shiftId: string;

  @ApiProperty({
    description: 'bằng salary_time+ot_time+ot_holiday_time+ot_nigh_time+ot_holiday_night_time',
    example: '2024-03-06T14:30:00Z',
  })
  @Column({ type: 'numeric' })
  manhour: number;

  @ApiProperty({ description: 'Note info', example: '1234' })
  @Column({ nullable: true })
  Note: string;

  @ApiProperty({ description: 'Đã xử lý' })
  @Column({ type: 'boolean', nullable: true })
  posted: boolean;

  @ApiProperty({ description: '', example: 'T' })
  @Column({ nullable: true })
  source?: string | null;

  @ApiProperty({ description: '', example: '-1' })
  @Column({ nullable: true })
  lastUpdateBy: number;
}
