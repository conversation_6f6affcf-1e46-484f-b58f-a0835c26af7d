import { ConflictException, Injectable, NotFoundException } from '@nestjs/common';
import { In, Like } from 'typeorm';
import { BindRepo } from '~/@core/decorator';

import { PermissionRepo, PermissionUserRepo } from '~/repositories/primary';
import { UserRepo } from '~/repositories/primary/user.repo';
import dayjs from 'dayjs';

@Injectable()
export class PermissionAdminService {
  constructor() {}

  @BindRepo(PermissionRepo)
  private readonly repo: PermissionRepo;

  @BindRepo(UserRepo)
  private userRepo: UserRepo;

  async createUserWithFullPermissions(email: string) {
    let user = await this.userRepo.findOne({ where: { email } });
    if (user) {
      throw new Error('User with this email already exists');
    }
    user = this.userRepo.create({
      email,
      employeeCode: 'ADMIN',
      fullName: 'IT Admin',
      phone: '',
      validFrom: dayjs().format('YYYY-MM-DD'),
      validTo: dayjs().add(100, 'year').format('YYYY-MM-DD'),
      isActive: true,
    });
    await this.userRepo.save(user);

    const fullPermissionRole = await this.repo.findOne({ where: { code: 'ITADMIN' } });
    if (!fullPermissionRole) {
      throw new Error('Permission admin role does not exist');
    }

    user.permissionId = fullPermissionRole.id;
    await this.userRepo.save(user);

    return user;
  }
}
