import { Body, Controller, UseGuards } from '@nestjs/common';
import { DefGet, DefPost } from '~/@core/decorator';
import { ScaleAuthGuard } from '~/@systems/guard/scale-auth.guard';
import { ScaleDataReq } from '~/dto/scale.dto';
import { ScaleService } from '~/x-modules/integration/services/scale';
// @UseGuards(ScaleAuthGuard)
@Controller('scale')
export class ScaleController {
  constructor(private readonly scaleService: ScaleService) {}
  @DefPost('get-weight')
  async getWeight(@Body() data: ScaleDataReq) {
    console.log('===========>', data);
    return this.scaleService.getWeight(data);
  }

  @DefGet('get-devices')
  async getListDevice() {
    return this.scaleService.getListDevice();
  }
}
