import { ApiProperty } from '@nestjs/swagger';
import { Entity, Column } from 'typeorm';
import { PrimaryBaseEntity } from '../primary-base.entity';

@Entity('utility_transaction')
export class UtilityTransactionEntity extends PrimaryBaseEntity {
  /** <PERSON><PERSON><PERSON> giao dịch */
  @ApiProperty({ description: 'Ngày giao dịch' })
  @Column({
    nullable: true,
    type: 'timestamptz',
  })
  date?: Date;

  /** Mã ca */
  @ApiProperty({ description: 'Mã ca' })
  @Column({
    type: 'uuid',
    nullable: true,
  })
  shiftId?: string;

  /** Mã đồng hồ tiện ích */
  @ApiProperty({ description: 'Mã đồng hồ tiện ích' })
  @Column({
    type: 'uuid',
    nullable: true,
  })
  meterId?: string;

  /** Giá trị phép đo */
  @ApiProperty({ description: '<PERSON><PERSON><PERSON> trị của phép đo', example: 36.5 })
  @Column({
    nullable: true,
    type: 'numeric',
  })
  value: number;

  /** Posted At */
  @ApiProperty({ description: 'Posted At' })
  @Column({
    nullable: true,
    type: 'timestamptz',
  })
  postedAt: Date;

  /** Posted */
  @ApiProperty({ description: 'Posted' })
  @Column({
    nullable: true,
    default: false,
  })
  posted: boolean;
}
