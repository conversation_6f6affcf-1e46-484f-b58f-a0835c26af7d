import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsOptional } from 'class-validator';
import { Expose } from 'class-transformer';

export class CzCodeCheckReq {
  @ApiPropertyOptional({ description: 'Cz Code' })
  @IsOptional()
  czCode?: string;
}

export class CzCodeCheckRepositories {
  @Expose()
  @ApiPropertyOptional({ description: 'id Production_order' })
  orderId?: string;

  @Expose()
  @ApiPropertyOptional({ description: 'id url' })
  urlId?: string;

  @Expose()
  @ApiPropertyOptional({ description: 'fileName url' })
  @IsOptional()
  fileName?: string;
  
  @Expose()
  @ApiPropertyOptional({ description: 'uniqueCode' })
  uniqueCode?: string;

  @Expose()
  @ApiPropertyOptional({ description: 'itemcode' })
  @IsOptional()
  itemcode?: string;
  
  @Expose()
  @ApiPropertyOptional({ description: 'itemname' })
  @IsOptional()
  itemname?: string;
  
  @Expose()
  @ApiPropertyOptional({ description: 'orderNo' })
  @IsOptional()
  orderNo?: string;

  @Expose()
  @ApiPropertyOptional({ description: 'production_order quantity' })
  @IsOptional()
  quantity?: number;
  
  @Expose()
  @ApiPropertyOptional({ description: 'CZ Code actived total' })
  @IsOptional()
  czCodeActiveTotal?: number;

  @Expose()
  @ApiPropertyOptional({ description: 'siteCode' })
  @IsOptional()
  siteCode?: string;
  
  @Expose()
  @ApiPropertyOptional({ description: 'factoryCode' })
  @IsOptional()
  factoryCode?: string;

  @Expose()
  @ApiPropertyOptional({ description: 'lineCode' })
  @IsOptional()
  lineCode?: string;

  @Expose()
  @ApiPropertyOptional({ description: 'lotNumber' })
  @IsOptional()
  lotNumber?: string;

  @Expose()
  @ApiPropertyOptional({ description: 'total actualTrxQty by production_order_material' })
  @IsOptional()
  actualTrxQty?: number;

  @Expose()
  @ApiPropertyOptional({ description: 'status' })
  @IsOptional()
  status?: number;

  @Expose()
  @ApiPropertyOptional({ description: 'activationDate' })
  @IsOptional()
  activationDate?: string;

  @Expose()
  @ApiPropertyOptional({ description: 'productionDate' })
  @IsOptional()
  productionDate?: string;

  @Expose()
  @ApiPropertyOptional({ description: 'expiredDate' })
  @IsOptional()
  expiredDate?: string;

  @Expose()
  @ApiPropertyOptional({ description: 'customerShortName' })
  @IsOptional()
  customerShortName?: string;
  
  @Expose()
  @ApiPropertyOptional({ description: 'customerOrderNo' })
  @IsOptional()
  customerOrderNo?: string;

  @Expose()
  @ApiPropertyOptional({ description: 'customerOrderQty' })
  @IsOptional()
  customerOrderQty?: string;

  @Expose()
  @ApiPropertyOptional({ description: 'customerOrderId' })
  @IsOptional()
  customerOrderId?: string;

  @Expose()
  @ApiPropertyOptional({ description: 'gtin' })
  @IsOptional()
  gtin?: string;

  @Expose()
  @ApiPropertyOptional({ description: 'customerProductCode' })
  @IsOptional()
  customerProductCode?: string;

  @Expose()
  @ApiPropertyOptional({ description: 'customerProductName' })
  @IsOptional()
  customerProductName?: string;
}
export class FilterReq {
  @ApiPropertyOptional({ description: 'ID Production Line' })
  @IsOptional()
  id?: string;
}

export class CzCodeActiveReq {
  @ApiPropertyOptional({ description: 'Cz Code' })
  @IsOptional()
  czCode?: string;

  @ApiPropertyOptional({ description: 'factoryId' })
  @IsOptional()
  factoryId?: string;

  @ApiPropertyOptional({ description: 'processAreaId' })
  @IsOptional()
  processAreaId?: string;

  @ApiPropertyOptional({ description: 'siteId' })
  @IsOptional()
  siteId?: string;

  @ApiPropertyOptional({ description: 'lineId' })
  @IsOptional()
  lineId?: string;

  @ApiPropertyOptional({ description: 'productionOrderId' })
  @IsOptional()
  productionOrderId?: string;
}
