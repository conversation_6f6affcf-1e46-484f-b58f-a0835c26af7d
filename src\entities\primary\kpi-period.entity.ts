import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
  VersionColumn,
} from 'typeorm';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Exclude } from 'class-transformer';
import { PrimaryBaseEntity } from '~/entities/primary-base.entity';

/** Bảng kỳ KPI */
@Entity('kpi_period')
export class KPIPeriodEntity extends PrimaryBaseEntity {
  /** Tên kỳ KPI */
  @Column({ nullable: false, type: 'varchar', length: 15 })
  periodName: string;

  /** <PERSON><PERSON>y bắt đầu của kỳ KPI */
  @Column({ type: 'timestamptz', nullable: true })
  startDate?: Date;

  /** Ngày kết thúc của kỳ KPI */
  @Column({ type: 'timestamptz', nullable: true })
  endDate?: Date;

  /** Trạng thái kỳ KPI */
  @Column({ nullable: false })
  status: number;

  /** Ngư<PERSON><PERSON> thực hiện khóa kỳ KPI */
  @Column({ nullable: true, type: 'varchar', length: 128 })
  lockedBy: string;

  /** Ngày khóa kỳ KPI */
  @Column({ nullable: true, type: 'timestamptz' })
  lockedDate: Date;

  /** Loại kỳ tháng/ quý/ năm */
  @Column({ nullable: false })
  cycleType: string;

  /** Mã site áp dụng kỳ KPI */
  @Column({ nullable: true })
  siteId: number;

  /** Mã rule kỳ KPI */
  @Column({ nullable: false })
  kpiPeriodRuleId: string;
}
