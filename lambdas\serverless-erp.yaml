# ------------------------ RECIPE --------------------------------
createOrUpdateRecipe:
  handler: dist/functions/recipe-lambda.createOrUpdate
  events:
    - http:
        path: integration/erp/recipe
        method: post

# ------------------------ ITEM --------------------------------
batchCreateOrUpdateItem:
  handler: dist/functions/master-data/item-lambda.batchCreateOrUpdate
  events:
    - http:
        path: integration/erp/items/batch
        method: post
# ------------------------ TEST --------------------------------
batchTest:
  handler: dist/functions/test-lambda.test
  events:
    - http:
        path: integration/erp/test
        method: get
