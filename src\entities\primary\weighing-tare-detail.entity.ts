import { Column, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>um<PERSON>, ManyToOne } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { PrimaryBaseEntity } from '~/entities/primary-base.entity';
import { ItemEntity } from '~/entities/primary';
import { WeighingTareEntity } from '~/entities/primary/weighing-tare.entity';

// Weighing Tare Details entity
@Entity('weighing_tare_detail')
export class WeighingTareDetailEntity extends PrimaryBaseEntity {
  @Column({})
  weighingTareId: string;
  @ManyToOne(() => WeighingTareEntity, (p) => p.details)
  @JoinColumn({ name: 'weighingTareId', referencedColumnName: 'id' })
  weighingTare: Promise<WeighingTareEntity>;

  @Column({})
  itemId: string;
  @ManyToOne(() => ItemEntity, (p) => p.weighingTareDetails)
  @JoinColumn({ name: 'itemId', referencedColumnName: 'id' })
  item: Promise<ItemEntity>;

  @ApiProperty({ description: '' })
  @Column({ default: true })
  isAssigned: boolean;
}
