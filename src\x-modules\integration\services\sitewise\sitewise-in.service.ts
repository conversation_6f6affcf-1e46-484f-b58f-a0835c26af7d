import { Injectable } from '@nestjs/common';
import { In } from 'typeorm';
import { BindRepo, DefTransaction } from '~/@core/decorator';
import {
  RecipeRepo,
  ItemRepo,
  UomConventionRepo,
  OrganizationUnitRepo,
  RecipeProcessRepo,
  RecipeProcessItemRepo,
  ProcessRepo,
  SetPointRepo,
  MachineParameterRepo,
  ProductionOrderRepo,
  ProductionBatchRepo,
  InventoryRepository,
  ProductionAreaRepo,
  ProductionAreaDetailRepo,
  ShiftRepo,
} from '~/repositories/primary';
import { scadaApiConnector } from '~/common/connectors/api.connector';
import {
  SyncProductionOrderToScadaDtoReq,
  SyncRecipeToScadaDtoReq,
  SyncRecipeToScadaItemDtoReq,
  SyncInventoryToScadaDtoReq,
} from '~/dto/scada.dto';
import { BusinessException } from '~/@systems/exceptions';
import { configEnv } from '~/@config/env';
import { NSRecipe } from '~/common/enums/NSRecipe';
import { SnsService } from '~/x-modules/sqs/sns.service';
import { NSSQSS } from '~/common/enums/NSSQS';
const { DOCKER_SERVER_IP } = configEnv();
@Injectable()
export class SitewiseInService {
  constructor(private readonly snsService: SnsService) {}

  @BindRepo(RecipeRepo)
  private recipeRepo: RecipeRepo;
  @BindRepo(ItemRepo)
  private itemRepo: ItemRepo;
  @BindRepo(UomConventionRepo)
  private uomConventionRepo: UomConventionRepo;
  @BindRepo(OrganizationUnitRepo)
  private organizationUnitRepo: OrganizationUnitRepo;
  @BindRepo(RecipeProcessRepo)
  private recipeProcessRepo: RecipeProcessRepo;
  @BindRepo(RecipeProcessItemRepo)
  private recipeProcessItemRepo: RecipeProcessItemRepo;
  @BindRepo(ProcessRepo)
  private processRepo: ProcessRepo;
  @BindRepo(SetPointRepo)
  private setPointRepo: SetPointRepo;
  @BindRepo(MachineParameterRepo)
  private machineParameterRepo: MachineParameterRepo;
  @BindRepo(ProductionOrderRepo)
  private productionOrderRepo: ProductionOrderRepo;
  @BindRepo(ProductionBatchRepo)
  private productionBatchRepo: ProductionBatchRepo;
  @BindRepo(InventoryRepository)
  private inventoryRepo: InventoryRepository;
  @BindRepo(ProductionAreaRepo)
  private productionAreaRepo: ProductionAreaRepo;
  @BindRepo(ProductionAreaDetailRepo)
  private productionAreaDetailRepo: ProductionAreaDetailRepo;
  @BindRepo(ShiftRepo)
  private shiftRepo: ShiftRepo;
  // Tas 24 test
  async syncRecipeToScadaFalse() {
    try {
      const listRecipe = await this.recipeRepo.find({
        where: { isSyncScada: false },
      });
      for (const recipe of listRecipe) {
        try {
          // Gửi SQS để đồng bộ data về scada
          this.snsService.sendMessage(
            {
              message: 'Sync recipe to scada',
              data: {
                recipeId: recipe.id,
              },
            },
            NSSQSS.EMessageType.SyncRecipeToScada,
          );
          // await this.syncRecipeToScada(recipe.id);
        } catch (error) {
          console.log(error);
        }
      }
    } catch (error) {
      console.log(error);
    }
  }
  // Task 24
  async syncRecipeToScada(recipeId: string) {
    const recipe = await this.recipeRepo.findOne({
      where: { id: recipeId, isSyncScada: false },
    });
    if (!recipe) {
      throw new Error('Recipe not found');
    }
    // Lấy organizationUnit theo processAreaId
    const organizationUnit = await this.organizationUnitRepo.findOne({
      where: { id: recipe?.processAreaId },
    });
    if (!organizationUnit) {
      throw new Error('OrganizationUnit not found');
    }

    // Lấy orgLine theo parentId: Tức là lấy orgLine
    const orgLine = await this.organizationUnitRepo.findOne({
      where: { id: organizationUnit?.parentId, levelGeneralDataDetailCode: 'LINE' },
    });
    if (!orgLine) {
      throw new Error('OrganizationUnitLine not found');
    }

    // Lấy ipScada từ orgLine
    const ipScada = orgLine?.ipScada;
    if (!ipScada) {
      throw new Error('IpScada not found');
    }
    if (ipScada !== DOCKER_SERVER_IP) {
      // throw new Error('----- Khác IP Server -----');
      return;
    }
    const item = await this.itemRepo.findOne({
      where: { id: recipe?.productId },
    });
    if (!item) {
      throw new Error('Item not found');
    }

    const recipeProcesses = await this.recipeProcessRepo.find({
      where: { recipeId: recipe?.id },
    });
    const recipeProcessIds = recipeProcesses.map((rp) => rp.id);

    const recipeProcessItems = await this.recipeProcessItemRepo.find({
      where: { recipeProcessId: In(recipeProcessIds) },
    });

    // Lọc qua danh sách recipeProcessItems để lấy ra các itemId. Distinct theo itemId
    const itemIds = recipeProcessItems.map((rpi) => rpi.itemId);
    const distinctItemIds = [...new Set(itemIds)];

    // Lấy các item từ itemIds
    const items = await this.itemRepo.find({
      where: { id: In(distinctItemIds) },
    });

    for (const itemProcess of items) {
      const uomConventions = await this.uomConventionRepo.find({
        where: { itemId: itemProcess?.id },
      });
      if (!uomConventions) {
        throw new Error('UomConvention not found');
      }
      const reqItem: SyncRecipeToScadaItemDtoReq = {
        ItemCode: itemProcess?.code,
        ItemName: itemProcess?.name,
        Item_Type: itemProcess?.type,
        Group: itemProcess?.group,
        Category: itemProcess?.category,
        Brand: itemProcess?.brand,
        BaseUnit: itemProcess?.baseUnit,
        InventoryUnit: itemProcess?.inventoryUnit,
        MHUTypes: [],
        Item_Status: itemProcess?.status,
      };
      for (const uomConvention of uomConventions) {
        reqItem.MHUTypes.push({
          FromUnit: uomConvention?.fromUnit,
          ToUnit: uomConvention?.toUnit,
          Conversion: uomConvention?.conversion.toString(),
        });
      }

      try {
        const callApiSyncItemToScada = await scadaApiConnector.post(
          `http://${ipScada}:8088/api/ProductMaster`,
          [reqItem],
        );
        if (callApiSyncItemToScada.message === 'Success') {
          const updateItem = await this.itemRepo.update(itemProcess?.id, {
            isSyncScada: true,
            dateSyncScada: new Date(),
            errorSyncScada: null,
          });
        } else {
          console.log(`Sync Mater Item MES to SCADA Fail`);
          const updateItem = await this.itemRepo.update(itemProcess?.id, {
            dateSyncScada: new Date(),
            errorSyncScada: JSON.stringify(callApiSyncItemToScada),
            isSyncScada: false,
          });
          throw new Error(JSON.stringify(callApiSyncItemToScada));
        }
      } catch (error) {
        console.log(`Sync Mater Item MES to SCADA Fail`);
        const updateItem = await this.itemRepo.update(itemProcess?.id, {
          dateSyncScada: new Date(),
          errorSyncScada: JSON.stringify(error),
          isSyncScada: false,
        });
        throw error;
      }
    }
    // B2: Đồng bộ Recipe sang SCADA
    const statusRecipe =
      recipe?.recipeStatus === NSRecipe.RecipeStatus.ApprovedForGeneralUse ? 'Active' : 'Inactive';
    const reqRecipe: SyncRecipeToScadaDtoReq = {
      ProductCode: item?.code,
      Plant: '',
      Shopfloor: '',
      ProductionLine: orgLine?.code,
      RecipeCode: recipe?.formulaNo,
      RecipeName: recipe?.recipeDescription,
      RecipeStatus: statusRecipe,
      Version: recipe?.recipeVer.toString(),
      Process: [],
    };
    for (const recipeProcess of recipeProcesses) {
      const process = await this.processRepo.findOne({
        where: { id: recipeProcess?.processId },
      });
      const recipeProcessItems = await this.recipeProcessItemRepo.find({
        where: { recipeProcessId: recipeProcess?.id },
      });
      const setPoints = await this.setPointRepo.find({
        where: { recipeId: recipe?.id },
      });
      let ingredients = [];
      let products = [];
      let byProducts = [];
      let parameters = [];
      for (const recipeProcessItem of recipeProcessItems) {
        const item = await this.itemRepo.findOne({
          where: { id: recipeProcessItem?.itemId },
        });
        if (recipeProcessItem?.typeCode === NSRecipe.RecipeProcessItemTypeCode.Ingredient) {
          ingredients.push({
            IngredientCode: item?.code,
            ING_DESCRIPTION: item?.name,
            Quantity: recipeProcessItem?.quantity,
            UnitOfMeasurement: recipeProcessItem?.uom,
          });
        } else if (recipeProcessItem?.typeCode === NSRecipe.RecipeProcessItemTypeCode.Product) {
          products.push({
            ProductCode: item?.code,
            PlanQuantity: recipeProcessItem?.quantity,
            UnitOfMeasurement: recipeProcessItem?.uom,
          });
        } else if (recipeProcessItem?.typeCode === NSRecipe.RecipeProcessItemTypeCode.ByProduct) {
          byProducts.push({
            ByProductCode: item?.code,
            ByProductDescription: item?.name,
            Quantity: recipeProcessItem?.quantity,
            UnitOfMeasurement: recipeProcessItem?.uom,
          });
        }
      }
      for (const setPoint of setPoints) {
        const machineParameter = await this.machineParameterRepo.findOne({
          where: { id: setPoint?.machineParameterId },
        });
        parameters.push({
          Code: machineParameter?.code,
          NameCode: machineParameter?.tagName,
          DataType: machineParameter?.dataTypeCode,
          StartTime: '',
          EndTime: '',
          UnitOfMeasurement: machineParameter?.uomCode,
          Setpoint_Value: setPoint?.targetValue.toString(),
          Min: setPoint?.min.toString(),
          Max: setPoint?.max.toString(),
        });
      }
      reqRecipe.Process.push({
        ProcessCode: process?.code,
        ProcessName: process?.name,
        Duration: '0',
        DurationUoM: '',
        Ingredients: ingredients,
        Products: products,
        ByProducts: byProducts,
        Parameters: parameters,
      });
    }
    try {
      const callApiSyncRecipeToScada = await scadaApiConnector.post(
        `http://${ipScada}:8088/api/RecipeDetails`,
        reqRecipe,
      );
      if (callApiSyncRecipeToScada.message === 'Success') {
        const updateRecipe = await this.recipeRepo.update(recipe?.id, {
          isSyncScada: true,
          dateSyncScada: new Date(),
          errorSyncScada: null,
        });
      } else {
        console.log(`Sync Recipe MES to SCADA Fail`);
        const updateRecipe = await this.recipeRepo.update(recipe?.id, {
          dateSyncScada: new Date(),
          errorSyncScada: JSON.stringify(callApiSyncRecipeToScada),
          isSyncScada: false,
        });
        throw new Error(JSON.stringify(callApiSyncRecipeToScada));
      }
    } catch (error) {
      // console.log(error);
      console.log('Sync Recipe MES to SCADA Fail');
      const updateRecipe = await this.recipeRepo.update(recipe?.id, {
        dateSyncScada: new Date(),
        errorSyncScada: JSON.stringify(error),
        isSyncScada: false,
      });
      throw error;
    }

    return {
      message: 'Sync Recipe to MES and SCADA success',
    };
  }

  async syncProductionOrderToScadaFalse() {
    const listProductionOrder = await this.productionOrderRepo.find({
      where: { isScadaSynced: false },
    });
    if (listProductionOrder.length > 0)
      this.snsService.sendMessage(
        {
          message: 'SyncProductionOrderToScada',
          data: {
            lstId: listProductionOrder.map((p) => p.id),
          },
        },
        NSSQSS.EMessageType.SyncProductionOrderToScada,
      );
    // await this.syncProductionOrderToScada(listProductionOrder.map((p) => p.id));
  }
  // Task 27
  // @DefTransaction()
  async syncProductionOrderToScada(listProductionOrderId: string[]) {
    // Kiểm tra danh sách PO có cột isScadaSynced <> True
    const productionOrders = await this.productionOrderRepo.find({
      where: { isScadaSynced: false, id: In(listProductionOrderId) },
    });
    const listPOId = productionOrders.map((po) => po.id);

    if (productionOrders.length === 0) {
      throw new Error('No production order to sync');
    }

    // Lấy các production_order => production_batch có ""scadaSynced"" <> True
    const productionBatches = await this.productionBatchRepo.find({
      where: { scadaSynced: false, orderId: In(listPOId) },
    });
    let listError = [];
    for (const productionOrder of productionOrders) {
      const line = await this.organizationUnitRepo.findOne({
        where: { id: productionOrder?.lineId },
      });
      if (!line) {
        await this.updateFalseProductionOrder(productionOrder.id, 'Line not found');
        listError.push({ productionOrderId: productionOrder.id, error: 'Line not found' });
        continue;
      }
      // Lấy ipScada từ orgLine
      const ipScada = line?.ipScada;
      if (!ipScada) {
        await this.updateFalseProductionOrder(productionOrder.id, 'IpScada not found');
        listError.push({ productionOrderId: productionOrder.id, error: 'IpScada not found' });
        continue;
      }

      if (ipScada !== DOCKER_SERVER_IP) {
        continue;
      }

      const batches = productionBatches.filter((batch) => batch.orderId === productionOrder?.id);
      if (!batches) {
        await this.updateFalseProductionOrder(productionOrder.id, 'No production batch to sync');
        listError.push({
          productionOrderId: productionOrder.id,
          error: 'No production batch to sync',
        });
        continue;
      }
      const item = await this.itemRepo.findOne({
        where: { id: productionOrder?.itemId },
      });
      if (!item) {
        await this.updateFalseProductionOrder(productionOrder.id, 'Item not found');
        listError.push({ productionOrderId: productionOrder.id, error: 'Item not found' });
        continue;
      }

      const shift = await this.shiftRepo.findOne({
        where: { id: productionOrder?.shiftId },
      });
      if (!shift) {
        await this.updateFalseProductionOrder(productionOrder.id, 'Shift not found');
        listError.push({ productionOrderId: productionOrder.id, error: 'Shift not found' });
        continue;
      }

      const organizationUnit = await this.organizationUnitRepo.findOne({
        where: { id: productionOrder?.processAreaId },
      });
      if (!organizationUnit) {
        await this.updateFalseProductionOrder(productionOrder.id, 'Organization unit not found');
        listError.push({
          productionOrderId: productionOrder.id,
          error: 'Organization unit not found',
        });
        continue;
      }

      let reqProductionOrder: SyncProductionOrderToScadaDtoReq = {
        productionLine: organizationUnit?.code,
        productCode: item?.code,
        productionOrderNumber: productionOrder?.orderNo,
        recipeCode: productionOrder?.recipeCode,
        recipeVersion: productionOrder?.recipeVersion.toString(),
        shift: shift?.description,
        plannedStart: productionOrder?.planStartDate?.toISOString(),
        plannedEnd: productionOrder?.planEndDate?.toISOString(),
        quantity: productionOrder?.quantity,
        unitOfMeasurement: productionOrder?.trxUom,
        lotNumber: productionOrder?.lotNumber,
        batches: batches.map((batch) => ({
          batchNumber: batch?.batchNumber.toString(),
          quantity: batch?.planProdQty,
          unitOfMeasurement: batch?.uom,
        })),
      };
      try {
        const callApiSyncProductionOrderToScada = await scadaApiConnector.post(
          `http://${ipScada}:8088/api/ProductionOrder`,
          reqProductionOrder,
        );
        if (callApiSyncProductionOrderToScada.message === 'Success') {
          // Lưu trạng thái đã send scada
          await this.productionOrderRepo.update(productionOrder.id, {
            isScadaSynced: true,
            dateScadaSynced: new Date(),
            errorSyncScada: null,
          });
          await this.productionBatchRepo.update(
            { id: In(batches.map((b) => b.id)) },
            {
              scadaSynced: true,
              dateScadaSynced: new Date(),
            },
          );
        } else {
          await this.productionOrderRepo.update(productionOrder.id, {
            dateScadaSynced: new Date(),
            errorSyncScada: JSON.stringify(callApiSyncProductionOrderToScada),
            isScadaSynced: false,
          });
          listError.push({
            productionOrderId: productionOrder.id,
            error: JSON.stringify(callApiSyncProductionOrderToScada),
          });
        }
      } catch (error) {
        console.log(`Sync Production Order MES to SCADA Fail`);
        await this.updateFalseProductionOrder(productionOrder.id, error);
        listError.push({ productionOrderId: productionOrder.id, error: JSON.stringify(error) });
      }
    }
    if (listError.length > 0) {
      throw new Error(JSON.stringify(listError));
    }
    return {
      message: 'Sync Production Order MES to SCADA Success',
    };
  }

  async updateFalseProductionOrder(productionOrderId: string, error: any) {
    await this.productionOrderRepo.update(productionOrderId, {
      isScadaSynced: false,
      dateScadaSynced: new Date(),
      errorSyncScada: error,
    });
  }

  async syncInventoryToScadaFalse() {
    const listInventory = await this.inventoryRepo.find({
      where: { isSyncScada: false },
      // take: 2,
      // order: {
      //   createdDate: 'DESC',
      // },
    });
    if (listInventory.length > 0)
      this.snsService.sendMessage(
        {
          message: 'SyncInventoryToScada',
          data: {
            lstId: listInventory.map((i) => i.id),
          },
        },
        NSSQSS.EMessageType.SyncInventoryToScada,
      );

    // await this.syncInventoryToScada(listInventory.map((i) => i.id));
  }

  // Task 29
  async syncInventoryToScada(inventoryIds: Array<string>) {
    try {
      let listError = [];
      let listReq: Array<{
        reqInventory: SyncInventoryToScadaDtoReq;
        ipScada: string;
        inventoryId: string;
      }> = [];
      for (const inventoryId of inventoryIds) {
        const inventory = await this.inventoryRepo.findOne({
          where: { id: inventoryId, isSyncScada: false },
        });
        if (!inventory) {
          listError.push({ inventoryId, error: 'Inventory not found' });
          await this.inventoryRepo.update(inventoryId, {
            errorSyncScada: 'Inventory not found',
          });
          continue;
        }

        // Lấy site theo plantCode lưu ở bảng inventory
        const orgSite = await this.organizationUnitRepo.findOne({
          where: { code: inventory?.plant, levelGeneralDataDetailCode: 'SITE' },
        });
        if (!orgSite) {
          listError.push({ inventoryId, error: 'OrgSite not found' });
          await this.inventoryRepo.update(inventoryId, {
            errorSyncScada: 'OrgSite not found',
          });
          continue;
        }

        // Lấy factory theo site
        const orgFactory = await this.organizationUnitRepo.findOne({
          where: {
            parentId: orgSite?.id,
            code: inventory?.subInventory,
            levelGeneralDataDetailCode: 'FACTORY',
          },
        });
        if (!orgFactory) {
          listError.push({ inventoryId, error: 'OrgFactory not found' });
          await this.inventoryRepo.update(inventoryId, {
            errorSyncScada: 'OrgFactory not found',
          });
          continue;
        }

        // Lấy line theo factory
        const orgLine = await this.organizationUnitRepo.findOne({
          where: { parentId: orgFactory?.id, levelGeneralDataDetailCode: 'LINE' },
        });
        if (!orgLine) {
          listError.push({ inventoryId, error: 'OrgLine not found' });
          await this.inventoryRepo.update(inventoryId, {
            errorSyncScada: 'OrgLine not found',
          });
          continue;
        }

        // Lấy ipScada từ orgLine
        const ipScada = orgLine?.ipScada;
        if (!ipScada) {
          listError.push({ inventoryId, error: 'IpScada not found' });
          await this.inventoryRepo.update(inventoryId, {
            errorSyncScada: 'IpScada not found',
          });
          continue;
        }

        if (ipScada !== DOCKER_SERVER_IP) {
          continue;
        }

        const reqInventoryItem: SyncInventoryToScadaDtoReq = {
          Base_qty: inventory?.baseQty,
          Base_uom: inventory?.baseUom,
          Exprired_Date: inventory?.expiredDate?.toISOString(),
          Manufacture: inventory?.manufacture,
          MaterialCode: inventory?.materialCode,
          MaterialLotNumber: inventory?.materialLotNumber,
        };
        listReq.push({ reqInventory: reqInventoryItem, ipScada, inventoryId });
      }
      const dataFilter = listReq.filter((item) => item.ipScada === DOCKER_SERVER_IP);
      const dataReq = dataFilter.map((item) => item.reqInventory);
      const dataIds = dataFilter.map((item) => item.inventoryId);
      if (dataReq.length > 0)
        try {
          const callApiSyncInventoryToScada = await scadaApiConnector.post(
            `http://${DOCKER_SERVER_IP}:8088/api/InventoryData`,
            dataReq,
          );

          if (callApiSyncInventoryToScada.message === 'Success') {
            await this.inventoryRepo.update(dataIds, {
              isSyncScada: true,
              dateSyncScada: new Date(),
              errorSyncScada: null, // Đảm bảo không có lỗi nếu thành công
            });
          } else {
            await this.inventoryRepo.update(dataIds, {
              errorSyncScada: JSON.stringify(callApiSyncInventoryToScada) || 'Unknown error', // Lưu thông báo lỗi từ API
              dateSyncScada: new Date(),
              isSyncScada: false, // Đảm bảo trạng thái sync là false nếu có lỗi
            });
            throw new Error(JSON.stringify(callApiSyncInventoryToScada));
          }
        } catch (error) {
          console.log(`Sync Inventory MES to SCADA Fail`);
          await this.inventoryRepo.update(dataIds, {
            errorSyncScada: JSON.stringify(error) || 'Unknown error', // Lưu thông báo lỗi hệ thống
            dateSyncScada: new Date(),
            isSyncScada: false, // Đảm bảo trạng thái sync là false nếu có lỗi
          });
          throw error;
        }
    } catch (error) {
      console.log(`Sync Inventory MES to SCADA Fail`);
      throw error;
    }
  }

  // Hàm sleep cho phép tạm dừng trong một khoảng thời gian nhất định
  sleep = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

  async testThrowError() {
    throw new Error('Test throw error');
  }
}
