import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { PrimaryBaseEntity } from '../primary-base.entity';

@Entity('kpi_standard')
export class KpiStandardEntity extends PrimaryBaseEntity {
  /** Hệ thống tự sinh theo số thứ tự tăng dần, mỗi KPI set là 1 ID duy nhất */
  @ApiProperty({
    description: 'Hệ thống tự sinh theo số thứ tự tăng dần, mỗi KPI set là 1 ID duy nhất',
  })
  @PrimaryGeneratedColumn('increment')
  kpiStandardId: number;

  @ApiProperty({
    description: 'Nhóm id của cùng 1 KPI Set',
  })
  @Column({ type: 'uuid', nullable: true })
  groupId: string;

  /** Chọn từ bộ KPI set từ bảng kpi_set_header */
  @ApiProperty({ description: '<PERSON><PERSON><PERSON> từ bộ KPI set từ bảng kpi_set_header' })
  @Column({ type: 'uuid', nullable: true })
  kpiSetId: string;

  /** Valid bộ KPI set từ bảng kpi_set_header, kpi_set_group, kpi_set_detail */
  @ApiProperty({
    description: 'Valid bộ KPI set từ bảng kpi_set_header, kpi_set_group, kpi_set_detail',
  })
  @Column({ type: 'uuid', nullable: true })
  kpiSetGroupId: string;

  /** Valid bộ KPI set từ bảng kpi_set_header, kpi_set_group, kpi_set_detail */
  @ApiProperty({
    description: 'Valid bộ KPI set từ bảng kpi_set_header, kpi_set_group, kpi_set_detail',
  })
  @Column({ type: 'uuid' })
  kpiCodeId: string;

  /** Đơn vị tính KPI, valid theo kpi_code_id trong bảng kpi_list */
  @ApiProperty({ description: 'Đơn vị tính KPI, valid theo kpi_code_id trong bảng kpi_list' })
  @Column({ type: 'varchar', length: 15 })
  unit: string;

  /** Valid bộ KPI set, thông tin weightage_kpi từ bảng kpi_set_detail */
  @ApiProperty({ description: 'Valid bộ KPI set, thông tin weightage_kpi từ bảng kpi_set_detail' })
  @Column({ type: 'numeric' })
  weightageKpi: number;
}
