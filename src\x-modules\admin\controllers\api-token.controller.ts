import { DefController, DefGet, DefPost } from '~/@core/decorator';
import { ApiTokenService } from '../services';
import { ApiTags } from '@nestjs/swagger';
import { Body, Query, UseGuards } from '@nestjs/common';
import { CreateApiTokenReq } from '~/dto/api-token.dto';
import { HttpMethod } from '~/@core/network';
import { Roles } from '~/@core/decorator/roles.decorator';
import { RoleGuard } from '~/@systems/guard/role.guard';

@ApiTags('api-token')
@DefController('api-token')
export class ApiTokenController {
  constructor(private readonly apiTokenService: ApiTokenService) {}

  @DefGet('/', { summary: 'Tạo mới token' })
  @Roles('/system-configuration/api-token', 'View')
  @UseGuards(RoleGuard)
  async pagination(@Query() params: any) {
    return await this.apiTokenService.pagination(params);
  }

  @DefPost('/', { summary: 'Tạo mới token' })
  @Roles('/system-configuration/api-token', 'View')
  @UseGuards(RoleGuard)
  async create(@Body() body: CreateApiTokenReq) {
    return await this.apiTokenService.create(body.paths, body.expiredAt);
  }

  @DefPost('/delete', { summary: 'Xóa token' })
  @Roles('/system-configuration/api-token', 'View')
  @UseGuards(RoleGuard)
  async delete(@Body() data: any) {
    await this.apiTokenService.deleteToken(data.key, data.path, data.method);
  }

  @DefPost('/refresh-cache', { summary: 'Làm mới bộ nhớ cache' })
  @Roles('/system-configuration/api-token', 'View')
  @UseGuards(RoleGuard)
  async refreshCache() {
    await this.apiTokenService.refreshCache();
  }

  @DefPost('/invalidate-cache', { summary: 'Vô hiệu hóa bộ nhớ cache (clear cache)' })
  @Roles('/system-configuration/api-token', 'View')
  @UseGuards(RoleGuard)
  async invalidateCache(path: string, method: HttpMethod, key: string) {
    await this.apiTokenService.invalidateCache(path, method, key);
  }
}
