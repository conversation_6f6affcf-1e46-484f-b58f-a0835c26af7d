import { Body, UseGuards } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { DefController, DefPost } from '~/@core/decorator';
import {
  CreateReasonMasterDto,
  ReasonMasterPaginationDto,
  ReasonMasterResponseDto,
  UpdateReasonMasterDto,
} from '~/dto/reason-master.dto';
import { ReasonMasterService } from '../../services';
import { Roles } from '~/@core/decorator/roles.decorator';
import { RoleGuard } from '~/@systems/guard/role.guard';

@ApiTags('ReasonMaster') // Nhóm API này liên quan đến Quản lý Lý do (Reason Master)
@DefController('reason-master')
export class ReasonMasterController {
  constructor(private readonly service: ReasonMasterService) {}

  @ApiOperation({ summary: 'Lấy dữ liệu Enum cho SelectBox' })
  @DefPost('load-enum')
  @Roles('/master-data/reasons-master', 'View')
  @UseGuards(RoleGuard)
  async loadSelectBoxData() {
    return this.service.loadSelectBoxDataForReasonMaster();
  }

  @ApiOperation({ summary: 'Tạo mới lý do' })
  @DefPost('create')
  @Roles('/master-data/reasons-master', 'View')
  @UseGuards(RoleGuard)
  async create(
    @Body() createReasonMasterDto: CreateReasonMasterDto,
  ): Promise<ReasonMasterResponseDto> {
    return this.service.create(createReasonMasterDto);
  }

  @ApiOperation({ summary: 'Lấy danh sách lý do cha' })
  @DefPost('load-data-parent')
  @Roles('/master-data/reasons-master', 'View')
  @UseGuards(RoleGuard)
  async loadSelectParent() {
    return this.service.loadSelectParent();
  }

  @ApiOperation({ summary: 'Phân trang danh sách lý do' })
  @DefPost('pagination')
  @Roles('/master-data/reasons-master', 'View')
  @UseGuards(RoleGuard)
  async pagination(@Body() params: ReasonMasterPaginationDto) {
    return this.service.pagination(params);
  }

  @ApiOperation({ summary: 'Lấy chi tiết một lý do theo ID' })
  @DefPost('detail')
  @Roles('/master-data/reasons-master', 'View')
  @UseGuards(RoleGuard)
  async findOne(@Body() body: { id: string }): Promise<ReasonMasterResponseDto> {
    return this.service.findOne(body.id);
  }

  @ApiOperation({ summary: 'Cập nhật thông tin lý do' })
  @DefPost('update')
  @Roles('/master-data/reasons-master', 'View')
  @UseGuards(RoleGuard)
  async update(
    @Body() updateReasonMasterDto: UpdateReasonMasterDto,
  ): Promise<ReasonMasterResponseDto> {
    return this.service.update(updateReasonMasterDto);
  }

  @ApiOperation({ summary: 'Xóa một lý do theo ID' })
  @DefPost('delete')
  @Roles('/master-data/reasons-master', 'View')
  @UseGuards(RoleGuard)
  async remove(@Body() body: { id: string }): Promise<ReasonMasterResponseDto> {
    return this.service.remove(body.id);
  }
}
