import {
  Body,
  Controller,
  Param,
  Query,
  UploadedFile,
  UseInterceptors,
  UseGuards,
} from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { DefDelete, DefGet, DefPost, DefPut } from '~/@core/decorator';
import {
  CreateBTPDTO,
  CreateProductionOrderDto,
  LstPOIdDto,
  ProductOrderPageReq,
  SendScadaDto,
  UpdateProductionOrderDto,
} from '~/dto/product-order.dto';
import { ProductionOrderService } from '../../services';
import { S3Service } from '~/x-modules/@global/services';
import { v4 as uuidv4 } from 'uuid';
import { FileInterceptor } from '@nestjs/platform-express';
import { Roles } from '~/@core/decorator/roles.decorator';
import { RoleGuard } from '~/@systems/guard/role.guard';

@ApiTags('Production Orders')
@Controller('production-orders')
export class ProductionOrderController {
  constructor(
    private readonly service: ProductionOrderService,
    private readonly s3Service: S3Service,
  ) {}

  @DefPost('load-recipe-process-item-of-po')
  @Roles('/production-order', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Danh sách bán thành phẩm của po theo po material' })
  @ApiResponse({ status: 201, description: 'Danh sách bán thành phẩm của po theo po material' })
  async loadListPOMaterialOfProductOrder(@Body() body: LstPOIdDto) {
    return this.service.loadListPOMaterialOfProductOrder(body);
  }

  @DefPost()
  @Roles('/production-order', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Tạo mới Production Order' })
  @ApiResponse({ status: 201, description: 'Production Order đã được tạo thành công' })
  create(@Body() createProductionOrderDto: CreateProductionOrderDto) {
    return this.service.create(createProductionOrderDto);
  }
  @DefPost('create-btp-order')
  @Roles('/production-order', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Tạo mới Production Order cho bán thành phẩm' })
  @ApiResponse({ status: 201, description: 'Production Order đã được tạo thành công' })
  createBTPOrder(@Body() data: CreateBTPDTO) {
    return this.service.createBTPOrder(data);
  }

  @DefPut()
  @Roles('/production-order', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Tạo mới Production Order' })
  @ApiResponse({ status: 201, description: 'Production Order đã được tạo thành công' })
  update(@Body() createProductionOrderDto: UpdateProductionOrderDto) {
    return this.service.update(createProductionOrderDto);
  }

  @ApiOperation({ summary: 'Phân trang', description: 'Phân trang kế hoạch kiểm tra' })
  @DefPost('pagination/:userId')
  @Roles('/production-order', 'View')
  @UseGuards(RoleGuard)
  async pagination(@Param('userId') userId: string, @Body() body: ProductOrderPageReq) {
    return this.service.pagination(userId, body);
  }

  @ApiOperation({ summary: 'Scada', description: 'Gửi qua scada' })
  @DefPost('send-to-scada')
  @Roles('/production-order', 'View')
  @UseGuards(RoleGuard)
  async sendToScada(@Body() body: SendScadaDto) {
    return this.service.sendToScada(body);
  }

  @ApiOperation({ summary: 'find', description: 'Tìm kiếm' })
  @DefPost('find')
  @Roles('/production-order', 'View')
  @UseGuards(RoleGuard)
  async find(@Body() body: { lstId: string[]; isScadaSynced: boolean }) {
    return this.service.find(body);
  }

  @DefDelete(':id')
  @Roles('/production-order', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Xóa Production Order' })
  delete(@Param('id') id: string) {
    return this.service.delete(id);
  }

  @ApiOperation({ summary: 'Lấy thông tin selectbox', description: 'Lấy thông tin selectbox' })
  @DefGet('dataselect-for-create/:userId')
  @Roles('/production-order', 'View')
  @UseGuards(RoleGuard)
  async loadDataSelectForCreate(@Param('userId') userId: string) {
    return this.service.loadDataSelectForCreate(userId);
  }

  @DefGet('load-transaction-type')
  @Roles('/production-order', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Lấy danh sách transaction type của một Production Order' })
  loadListTransactionType() {
    return this.service.loadListTransactionType();
  }

  @DefGet(':id')
  @Roles('/production-order', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Lấy thông tin chi tiết của một Production Order' })
  findOne(@Param('id') id: string) {
    return this.service.findOne(id);
  }

  @DefGet('/load-material/:id')
  @Roles('/production-order', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Lấy danh sách material của một Production Order' })
  loadListProductOrderMaterialPO(@Param('id') id: string, @Query() params: any) {
    return this.service.loadListProductOrderMaterialPO(id, params);
  }

  @DefGet('/load-resource/:id')
  @Roles('/production-order', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Lấy danh sách resource của một Production Order' })
  loadListProductOrderResourcePO(@Param('id') id: string, @Query() params: any) {
    return this.service.loadListProductOrderResourcePO(id, params);
  }

  @DefGet('/load-material-transaction/:id')
  @Roles('/production-order', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Lấy danh sách material transaction của một Production Order' })
  loadListProductOrderMaterialTransactionsPO(@Param('id') id: string, @Query() params: any) {
    return this.service.loadListProductOrderMaterialTransactionsPO(id, params);
  }

  @DefGet('/load-batch-process-param/:id')
  @Roles('/production-order', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Lấy danh sách batch và process param của một Production Order' })
  loadListBatchAndProcessParamPO(@Param('id') id: string, @Query() params: any) {
    return this.service.loadListBatchAndProcessParamPO(id, params);
  }

  @DefGet('/load-process-by-po/:id')
  @Roles('/production-order', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Lấy danh sách process của một Production Order' })
  loadListProcessByPoID(@Param('id') id: string) {
    return this.service.loadListProcessByPoID(id);
  }

  @DefGet('/load-uom-by-po/:id')
  @Roles('/production-order', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Lấy danh sách UOM của một Production Order' })
  loadListUOMByPoID(@Param('id') id: string) {
    return this.service.loadListUOMByPoID(id);
  }

  @DefPost('/create-material-transaction/:id')
  @Roles('/production-order', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Lấy danh sách UOM của một Production Order' })
  createMaterialTransaction(@Param('id') id: string, @Body() body: any[]) {
    return this.service.createMaterialTransaction(id, body);
  }

  @DefPut('/update-status-complete-po/:id')
  @Roles('/production-order', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Cập nhật trạng thái complete cho PO' })
  updateStatusPOComplete(@Param('id') id: string) {
    return this.service.updateStatusPOComplete(id);
  }

  @DefPost('/upload-s3')
  @Roles('/production-order', 'View')
  @UseGuards(RoleGuard)
  @UseInterceptors(FileInterceptor('file'))
  @ApiOperation({ summary: 'Upload S3' })
  async uploadS3(@UploadedFile() file: Express.Multer.File) {
    const fileName = uuidv4();

    const uploadResult = await this.s3Service.uploadSingle(file, fileName);

    return {
      originalName: file.originalname, // Tên file gốc từ người dùng
      fileName: uploadResult.fileName, // Tên đã đổi (UUID)
      fileUrl: uploadResult.fileUrl, // URL trên S3
    };
  }

  @DefPost('/create-cz-code')
  @Roles('/production-order', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Tạo PO có mã CZ task 60.1' })
  async createCZCode(@Body() body: any) {
    return await this.service.createCZCode(body);
  }

  @DefPost('/update-cz-code')
  @Roles('/production-order', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Cập nhật PO có mã CZ task 60.1' })
  async updateCZCode(@Body() body: UpdateProductionOrderDto) {
    return await this.service.updateCZCode(body);
  }
}
