import { Body, UseGuards } from '@nestjs/common';
import { ApiOperation } from '@nestjs/swagger';
import { DefController, DefPost } from '~/@core/decorator';
import { Roles } from '~/@core/decorator/roles.decorator';
import { RoleGuard } from '~/@systems/guard/role.guard';
import { CreateReportDto, ListFilterReportDto } from '~/dto';
import { ProductionOrderMaterialReportService } from '~/x-modules/admin/services';

@DefController('production-order-material-reports')
export class ProductionOrderMaterialReportController {
  constructor(private readonly service: ProductionOrderMaterialReportService) {}

  @DefPost('search')
  @Roles('/report/production-order-material-report', 'View')
  @UseGuards(RoleGuard)
  async search(@Body() req: ListFilterReportDto) {
    return this.service.search(req);
  }

  @DefPost('submit-report')
  @Roles('/report/production-order-material-report', 'View')
  @UseGuards(RoleGuard)
  async submitReport(@Body() req: CreateReportDto) {
    return this.service.submitReport(req);
  }

  @DefPost('meta/fetch-lines')
  @Roles('/report/production-order-material-report', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Lấy danh sách nhà máy theo factory' })
  async getMetaLines(@Body() req: { factoryIdLst?: string[] }) {
    return this.service.getMetaLines(req);
  }

  @DefPost('meta/fetch-order-status')
  @Roles('/report/production-order-material-report', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Lấy tất cả trạng thái đơn hàng' })
  async getMetaOrderStatus() {
    return this.service.getMetaOrderStatus();
  }
}
