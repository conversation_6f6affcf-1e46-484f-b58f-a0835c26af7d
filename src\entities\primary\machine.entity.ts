import { Column, Entity, Index, Join<PERSON><PERSON>umn, ManyToOne, OneToMany } from 'typeorm';
import { PrimaryBaseEntity } from '~/entities/primary-base.entity';
import { MachineParameterEntity } from '~/entities/primary/machine-parameter.entity';
import { ProcessMachineEntity } from './process-machine.entity';
import { SetPointEntity } from './set-point.entity';

// Machine entity
/** Máy móc */
@Entity('machine')
export class MachineEntity extends PrimaryBaseEntity {
  /** Mã duy nhất của máy, dùng để định danh */
  @Column({ unique: true, length: 50 })
  @Index()
  code: string;

  /** Tên của máy */
  @Column({ type: 'text' })
  name: string;

  /** Mô tả về máy (có thể null) */
  @Column({ type: 'text', default: '' })
  desc?: string;

  /** <PERSON><PERSON><PERSON> thức kết nối của máy (MQTT, HTTP, v.v.) */
  @Column({ type: 'text' })
  gatewayProtocol: string;

  /** Địa chỉ IP của máy */
  @Column({ type: 'text' })
  ipAddress: string;

  /** Ghi chú thêm về máy */
  @Column({ type: 'text', default: '' })
  note?: string;

  /** Dữ liệu thô về máy */
  @Column({ type: 'text', default: '' })
  rawDataTable: string;

  /** Trạng thái hoạt động của máy, mặc định true  */
  @Column({ default: true })
  isActive: boolean;

  @OneToMany(() => MachineParameterEntity, e => e.machine)
  parameters: Promise<MachineParameterEntity[]>;

  @OneToMany(() => ProcessMachineEntity, pm => pm.machine)
  processMachines: Promise<ProcessMachineEntity[]>;

  @OneToMany(() => SetPointEntity, setPoint => setPoint.machine)
  setPoints: Promise<SetPointEntity[]>;
}
