import { PrimaryRepo } from '~/repositories/primary.repo';
import { UOMEntity } from '~/entities/primary/uom.entity';

export class UOMRepo extends PrimaryRepo<UOMEntity> {
  static getConnectionName(): string {
    return 'primary';
  }

  async findByCode(code: string): Promise<UOMEntity | null> {
    return this.findOne({ where: { code } });
  }

  async findActiveUOMs(): Promise<UOMEntity[]> {
    return this.find({ where: { isActive: true } });
  }

  async findInactiveUOMs(): Promise<UOMEntity[]> {
    return this.find({ where: { isActive: false } });
  }

  async searchByTerm(term: string): Promise<UOMEntity[]> {
    return this.createQueryBuilder('uom')
      .where('uom.name ILIKE :term', { term: `%${term}%` })
      .orWhere('uom.code ILIKE :term', { term: `%${term}%` })
      .orWhere('uom.symbol ILIKE :term', { term: `%${term}%` })
      .getMany();
  }
} 