import { ApiProperty } from '@nestjs/swagger';
import { Column, Entity, JoinColumn, ManyToOne } from 'typeorm';
import { PrimaryBaseEntity } from '../primary-base.entity';
import { UtilityMetersEntity } from './utility-meters.entity';
import { OrganizationUnitEntity } from './organization-unit.entity';

@Entity('utility_meter_detail')
export class UtilityMeterDetailEntity extends PrimaryBaseEntity {
  /** ID Đồng hồ tiện ích */
  @ApiProperty({ description: 'ID Đồng hồ tiện ích' })
  @Column({ name: 'utility_meter_id', nullable: false })
  utilityMeterId: string;

  /** ID Khu vực sản xuất */
  @ApiProperty({ description: 'ID Khu vực sản xuất' })
  @Column({ name: 'process_area_id', nullable: false })
  processAreaId: string;

  /** Trạng thái đượ<PERSON> gán */
  @ApiProperty({ description: 'Trạng thái được gán' })
  @Column({ name: 'is_assigned', default: false, nullable: false })
  isAssigned: boolean;

  /** Relation với bảng UtilityMeters */
  @ManyToOne(() => UtilityMetersEntity, (meter) => meter.details, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'utility_meter_id', referencedColumnName: 'id' })
  utilityMeter?: UtilityMetersEntity;

  /** Relation với bảng OrganizationUnit */
  @ManyToOne(() => OrganizationUnitEntity, (org) => org.utilityMeterDetails, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'process_area_id', referencedColumnName: 'id' })
  organization?: OrganizationUnitEntity;
}
