import { Body, Query, UseGuards } from '@nestjs/common';
import { ApiOperation } from '@nestjs/swagger';
import { DefController, DefGet, DefPost } from '~/@core/decorator';
import { CzCodeCheckReq, FilterReq, CzCodeActiveReq } from '~/dto/cz-code.dto';
import { CzCodeService, CzCodeActiveService, CzCodeRejectService } from '../../services';
import { Roles } from '~/@core/decorator/roles.decorator';
import { RoleGuard } from '~/@systems/guard/role.guard';

@DefController('cz-code')
export class CzCodeController {
  constructor(
    private readonly czCodeService: CzCodeService,
    private readonly czCodeActiveService: CzCodeActiveService,
    private readonly czCodeRejectService: CzCodeRejectService,
  ) {}

  @ApiOperation({ summary: 'L<PERSON>y thông tin cz code' })
  @DefGet('check')
  @Roles('/scan-cz/scan-check', 'View')
  @UseGuards(RoleGuard)
  async handlerCzCodeCheck(@Query() params: CzCodeCheckReq) {
    return await this.czCodeService.czCodeCheckService(params);
  }

  @ApiOperation({ summary: 'Active cz code' })
  @DefPost('active')
  @Roles('/scan-cz/scan-active', 'View')
  @UseGuards(RoleGuard)
  async handlerCzCodeActive(@Body() body: CzCodeActiveReq) {
    return await this.czCodeActiveService.czCodeActiveService(body);
  }

  @ApiOperation({ summary: 'Reject cz code' })
  @DefPost('reject')
  @Roles('/scan-cz/scan-reject', 'View')
  @UseGuards(RoleGuard)
  async handlerCzCodeReject(@Body() body: CzCodeCheckReq) {
    return await this.czCodeRejectService.czCodeRejectService(body);
  }

  @ApiOperation({ summary: 'Lấy danh sách process area' })
  @DefGet('get-process-area')
  @Roles('/scan-cz/scan-active', 'View')
  @UseGuards(RoleGuard)
  async getProcessArea(@Query() params: FilterReq) {
    return await this.czCodeService.getProcessArea(params);
  }

  @ApiOperation({ summary: 'Lấy danh sách production order' })
  @DefGet('get-production-order')
  @Roles('/scan-cz/scan-active', 'View')
  @UseGuards(RoleGuard)
  async getProductionOrder(@Query() params: FilterReq) {
    return await this.czCodeService.getProductionOrder(params);
  }
}
