import { Column, Entity } from 'typeorm';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { PrimaryBaseEntity } from '~/entities/primary-base.entity';

/** Master data thẻ */
// @Entity('card')
export class CardEntity extends PrimaryBaseEntity {
  @ApiProperty({ description: 'Tên loại thẻ' })
  @Column()
  name: string;

  @ApiProperty({ description: 'Mã loại thẻ' })
  @Column({})
  code: string;
}
