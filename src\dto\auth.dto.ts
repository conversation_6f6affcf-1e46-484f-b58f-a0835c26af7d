import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsBoolean, IsNotEmpty, IsOptional, IsString, IsUUID } from 'class-validator';
import * as jwt from 'jsonwebtoken';
import { NSMember } from '~/common/enums';
import { PermissionEntity } from '~/entities/primary';
import { UserEntity } from '~/entities/primary/user.entity';

export class LoginReq {
  @ApiProperty()
  @IsNotEmpty()
  username: string;

  @ApiProperty()
  @IsNotEmpty()
  password: string;
}

export class JwtPayload implements jwt.JwtPayload {
  @ApiPropertyOptional()
  iss?: string | undefined;
  @ApiPropertyOptional()
  sub?: string | undefined;
  @ApiPropertyOptional()
  aud?: string | string[] | undefined;
  @ApiPropertyOptional()
  exp?: number | undefined;
  @ApiPropertyOptional()
  nbf?: number | undefined;
  @ApiPropertyOptional()
  iat?: number | undefined;
  @ApiPropertyOptional()
  jti?: string | undefined;
}

export class ForgotPasswordReq {
  @ApiProperty()
  username: string;
}
export class MemberSessionDto extends JwtPayload {
  accessToken: string;
  refreshToken: string; // Có thể bỏ nếu không dùng
  tokenType: 'Bearer' = 'Bearer';

  // // Microsoft token fields
  // oid: string; // Object ID của người dùng
  // name?: string; // Tên người dùng (full name)
  // preferred_username?: string; // Email đăng nhập
  // tid: string; // Tenant ID
  // aud: any; // App ID (clientId)
  // iat?: number;
  // exp?: number;

  // // Optional: Tùy logic hệ thống có thể mở rộng thêm
  // roles?: string[];
  // groups?: string[];
}

export class ValidateTokenReq {
  @ApiProperty()
  @IsString()
  idToken: string;
}

export class ValidateTokenRes {
  @ApiProperty()
  @IsBoolean()
  isValid: boolean;

  @ApiProperty({
    nullable: true,
  })
  @IsString()
  error?: string;

  @ApiProperty({ type: [PermissionEntity], nullable: true })
  @IsOptional()
  roles?: PermissionEntity[];

  @ApiProperty({ type: UserEntity, nullable: true })
  @IsOptional()
  isUserExist?: UserEntity;
}
