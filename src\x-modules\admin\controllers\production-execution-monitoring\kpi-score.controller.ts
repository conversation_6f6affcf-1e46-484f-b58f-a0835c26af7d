import { Body, UseGuards } from '@nestjs/common';
import { ApiOperation } from '@nestjs/swagger';
import { DefController, DefGet, DefPost } from '~/@core/decorator';
import { Roles } from '~/@core/decorator/roles.decorator';
import { RoleGuard } from '~/@systems/guard/role.guard';
import { KpiScoreReq } from '~/dto/kpi-score.dto';
import { KpiScoreService } from '~/x-modules/admin/services';

@DefController('kpi-scores')
export class KpiScoreController {
  constructor(private readonly kpiScoreService: KpiScoreService) {}

  @DefPost('save')
  @Roles('/production-execution/kpi-score', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Tạo mới điểm KPI' })
  async saveKpiScore(@Body() body: KpiScoreReq) {
    return this.kpiScoreService.save(body);
  }

  @DefPost('list')
  @Roles('/production-execution/kpi-score', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: '<PERSON><PERSON><PERSON> tất cả KPI scrore' })
  async getAll() {
    return this.kpiScoreService.getAll();
  }

  @DefPost('search')
  @Roles('/production-execution/kpi-score', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Tìm các KPI theo tìm kiếm không bao gồm set cần chấm' })
  async searchOnlyScore(@Body() body: any) {
    return this.kpiScoreService.search(body);
  }

  @DefPost('set/check')
  @Roles('/production-execution/kpi-score', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'check input KPI Set' })
  async checkKpiScoreSet(@Body() body: any) {
    return this.kpiScoreService.checkKpiScoreSet(body);
  }

  @DefPost('set/save')
  @Roles('/production-execution/kpi-score', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Tạo mới điểm KPI Set' })
  async saveKpiScoreSet(@Body() body: KpiScoreReq[]) {
    return this.kpiScoreService.saveKpiScoreSet(body);
  }

  @DefPost('set/search')
  @Roles('/production-execution/kpi-score', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Tìm các KPI theo tìm kiếm bao gồm set cần chấm' })
  async setList(@Body() body: any) {
    return this.kpiScoreService.setSearch(body);
  }

  @DefPost('meta/fetch-organization-with-production-areas')
  @Roles('/production-execution/kpi-score', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Lấy dữ liệu cho các dropdown org' })
  async getOrganizationUnitWithProductionAreas() {
    return this.kpiScoreService.getOrganizationUnitWithProductionAreas();
  }

  @DefPost('meta/fetch-sites')
  @Roles('/production-execution/kpi-score', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Lấy danh sách nhà máy theo site' })
  async getSites(@Body() body: any) {
    return this.kpiScoreService.getSites(body);
  }

  @DefPost('meta/fetch-factories')
  @Roles('/production-execution/kpi-score', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Lấy danh sách nhà máy theo site' })
  async getFactories(@Body() body: { siteId?: string; userId?: string }) {
    return this.kpiScoreService.getFactories(body);
  }

  @DefPost('meta/fetch-production-areas')
  @Roles('/production-execution/kpi-score', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Lấy danh sách khu vực sản xuất theo nhà máy' })
  async getProductionAreas(
    @Body() body: { factoryId?: string; kpiSetHeaderId?: string; userId?: string },
  ) {
    return this.kpiScoreService.getProductionAreas(body);
  }

  @ApiOperation({ summary: 'Lấy danh sách kpi-set options' })
  @DefPost('meta/fetch-kpi-set-headers')
  @Roles('/production-execution/kpi-score', 'View')
  @UseGuards(RoleGuard)
  async getKpiSetHeaders(@Body() body: { productionAreaId?: string; userId?: string }) {
    return this.kpiScoreService.getKpiSetHeaders(body);
  }

  @ApiOperation({ summary: 'Lấy danh sách kpi options' })
  @DefPost('meta/fetch-kpis')
  @Roles('/production-execution/kpi-score', 'View')
  @UseGuards(RoleGuard)
  async getKpis() {
    return this.kpiScoreService.getKpis();
  }

  @ApiOperation({ summary: 'Lấy danh sách shift options' })
  @DefPost('meta/fetch-shifts')
  @Roles('/production-execution/kpi-score', 'View')
  @UseGuards(RoleGuard)
  async getShifts(@Body() body: { factoryId?: string }) {
    return this.kpiScoreService.getShifts(body);
  }
}
