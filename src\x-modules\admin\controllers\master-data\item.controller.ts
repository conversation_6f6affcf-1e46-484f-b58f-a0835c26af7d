import { Body, Inject, Param, Query, UseGuards } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { DefController, DefGet, DefPost } from '~/@core/decorator';
import { ListItemReq } from '~/dto/item.dto';
import { ItemService } from '../../services';
import { Roles } from '~/@core/decorator/roles.decorator';
import { RoleGuard } from '~/@systems/guard/role.guard';

@ApiTags('Items')
@DefController('items')
export class ItemController {
  constructor(@Inject(ItemService) private readonly itemService: ItemService) {}

  @DefGet('', { summary: 'Danh sách sản phẩm/thành phần/nguyên liệu' })
  @Roles('/master-data/items', 'View')
  @UseGuards(RoleGuard)
  async list(@Query() params: ListItemReq) {
    return this.itemService.find(params);
  }

  @DefGet(':id', { summary: 'Chi tiết sản phẩm/thành phần/nguyên liệu' })
  @Roles('/master-data/items', 'View')
  @UseGuards(RoleGuard)
  async detail(@Param('id') id: string) {
    return this.itemService.findById(id);
  }

  @DefPost(':id', { summary: 'Cập nhật tolerance sản phẩm/thành phần/nguyên liệu' })
  @Roles('/master-data/items', 'View')
  @UseGuards(RoleGuard)
  async updateTolerance(@Param('id') id: string, @Body() data: { weighingTolerance: number }) {
    return this.itemService.updateTolerance(id, data.weighingTolerance);
  }
}
