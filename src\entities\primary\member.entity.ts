import { BeforeInsert, Column, Entity, Index } from 'typeorm';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { PrimaryBaseEntity } from '~/entities/primary-base.entity';
import { NSMember } from '~/common/enums';

/** Quản lý thông tin nhân viên của cửa hàng */
// @Entity('member')
export class MemberEntity extends PrimaryBaseEntity {
  /** Tên đăng nhập */
  @ApiProperty()
  @Column()
  @Index({ unique: true })
  username: string;

  /** Mật khẩu */
  @ApiProperty()
  @Column()
  password: string;

  @ApiProperty({ enum: NSMember.EMemberType })
  @Column({ default: NSMember.EMemberType.MEMBER })
  memberType?: string;

  /** Email */
  @ApiPropertyOptional()
  @Column({ nullable: true })
  email?: string;

  /** Họ và tên */
  @Column({
    nullable: true,
    type: 'varchar',
    length: 250,
  })
  fullName?: string;

  @ApiPropertyOptional({ description: 'Số điện thoại' })
  @Index()
  @Column({
    nullable: true,
    type: 'varchar',
    length: 50,
  })
  phone?: string;

  @ApiPropertyOptional({ description: 'Bộ phận nếu khách hàng là doanh nghiệp' })
  @Column({
    nullable: true,
    type: 'varchar',
    length: 250,
  })
  dep?: string;

  @Index()
  @Column({ type: 'varchar', default: NSMember.EStatus.ACTIVE })
  status?: NSMember.EStatus;
}
