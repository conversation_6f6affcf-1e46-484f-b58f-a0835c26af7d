import { <PERSON>ti<PERSON>, <PERSON>T<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>um<PERSON>, Column } from 'typeorm';
import { PrimaryBaseEntity } from '~/entities/primary-base.entity';
import { DeviceEntity, StationEntity } from '~/entities/primary';

@Entity('station_detail')
export class StationDetailEntity extends PrimaryBaseEntity {
  /** @RelationId(() => StationEntity */
  @Column({ nullable: true })
  stationId?: string;
  @ManyToOne(() => StationEntity, (e) => e.assigns, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'stationId', referencedColumnName: 'id' })
  station?: Promise<StationEntity>;

  /** @RelationId(() => DeviceEntity */
  @Column({ nullable: true })
  deviceId?: string;
  @ManyToOne(() => DeviceEntity, (e) => e.assigns, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'deviceId', referencedColumnName: 'id' })
  device?: Promise<DeviceEntity>;

  @Column({ type: 'boolean', default: true })
  assign: boolean;
}
