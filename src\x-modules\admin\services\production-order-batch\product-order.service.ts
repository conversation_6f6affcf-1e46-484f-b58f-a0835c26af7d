import { forwardRef, Inject, Injectable } from '@nestjs/common';
import axios from 'axios';
import { isNumber } from 'class-validator';
import moment from 'moment';
import { Between, EntityManager, In, Like, Not } from 'typeorm';
import { Readable } from 'typeorm/platform/PlatformTools';
import { validate as isUUID, v4 as uuidv4 } from 'uuid';
import { BindRepo, DefTransaction } from '~/@core/decorator';
import { BusinessException } from '~/@systems/exceptions';
import { getFirstDayTz, getLastDayTz } from '~/@systems/utils';
import { NSGeneralData, NSMember, NSRecipe, NSSQSS } from '~/common/enums';
import { NSItem } from '~/common/enums/NSItem';
import { dayjs } from '~/common/helpers/date.helper';
import { numberHelper } from '~/common/helpers/number.helper';
import * as readline from 'readline';
import {
  CreateBTPDTO,
  CreateProductionOrderDto,
  LstPOIdDto,
  ProductOrderPageReq,
  SendScadaDto,
  UpdateProductionOrderDto,
} from '~/dto/product-order.dto';
import {
  OrganizationUnitEntity,
  ProductionBatchEntity,
  ProductionOrderEntity,
  ProductionOrderMaterialEntity,
  ProductionOrderResourceEntity,
  RecipeEntity,
  RecipeProcessItemEntity,
  UomConversionEntity,
} from '~/entities/primary';
import {
  GeneralDataDetailRepo,
  GeneralDataRepo,
  ItemRepo,
  MachineParameterRepo,
  MachineRepo,
  MesDxProdBatchParamatersRepo,
  MesDxProdBatchStatusRepo,
  OrganizationUnitRepo,
  ProcessMachineRepo,
  ProcessRepo,
  ProductionBatchRepo,
  ProductionOrderMaterialRepo,
  ProductionOrderMaterialTransactionRepo,
  ProductionOrderRepo,
  ProductionOrderResourceRepo,
  ProductionOrderUniqueCodeDetailRepo,
  RecipeProcessItemRepo,
  RecipeProcessRepo,
  RecipeRepo,
  RecipeResourceRepo,
  UomConventionRepo,
  UrlRepo,
  UserGroupRepo,
} from '~/repositories/primary';
import { AccessRepo } from '~/repositories/primary/access.repo';
import { ShiftRepo } from '~/repositories/primary/shift.repo';
import { UserRepo } from '~/repositories/primary/user.repo';
import { adminSessionContext } from '~/x-modules/admin/admin-session.context';
import { SitewiseInService } from '~/x-modules/integration/services';
import { SnsService } from '~/x-modules/sqs/sns.service';
import { hash } from 'crypto';
import { S3Service } from '~/x-modules/@global/services';
import * as XLSX from 'xlsx';
const dictLineType = {
  ING: -1,
  PRO: 1,
  BY_PRO: 2,
};

@Injectable()
export class ProductionOrderService {
  constructor(
    private readonly snsService: SnsService,
    @Inject(forwardRef(() => SitewiseInService))
    private readonly sitewiseInService: SitewiseInService,
    private readonly entityManager: EntityManager,
    private readonly s3Service: S3Service,
  ) {}

  @BindRepo(ProductionOrderRepo)
  private productionOrderRepo: ProductionOrderRepo;
  @BindRepo(ProductionOrderMaterialRepo)
  private productionOrderMaterialRepo: ProductionOrderMaterialRepo;
  @BindRepo(ProductionOrderMaterialTransactionRepo)
  private productionOrderMaterialTransactionRepo: ProductionOrderMaterialTransactionRepo;
  @BindRepo(ProductionOrderResourceRepo)
  private productionOrderResourceRepo: ProductionOrderResourceRepo;
  @BindRepo(ProductionBatchRepo)
  private productionBatchRepo: ProductionBatchRepo;
  @BindRepo(MesDxProdBatchParamatersRepo)
  private productionBatchParamRepo: MesDxProdBatchParamatersRepo;
  @BindRepo(MesDxProdBatchStatusRepo)
  private productionBatchStatusRepo: MesDxProdBatchStatusRepo;
  @BindRepo(ItemRepo)
  private itemRepo: ItemRepo;
  @BindRepo(ShiftRepo)
  private shiftRepo: ShiftRepo;
  @BindRepo(RecipeRepo)
  private recipeRepo: RecipeRepo;
  @BindRepo(RecipeProcessRepo)
  private recipeProcessRepo: RecipeProcessRepo;
  @BindRepo(RecipeProcessItemRepo)
  private recipeProcessItemRepo: RecipeProcessItemRepo;
  @BindRepo(RecipeResourceRepo)
  private recipeResourceRepo: RecipeResourceRepo;
  @BindRepo(OrganizationUnitRepo)
  private organizationUnitRepo: OrganizationUnitRepo;
  @BindRepo(GeneralDataRepo)
  private generalDataRepo: GeneralDataRepo;
  @BindRepo(ProcessRepo)
  private processRepo: ProcessRepo;
  @BindRepo(MachineRepo)
  private machineRepo: MachineRepo;
  @BindRepo(ProcessMachineRepo)
  private processMachineRepo: ProcessMachineRepo;
  @BindRepo(MachineParameterRepo)
  private machineParamRepo: MachineParameterRepo;
  @BindRepo(UomConventionRepo)
  private uomConventionRepo: UomConventionRepo;

  @BindRepo(GeneralDataDetailRepo)
  private generalDataDetailRepo: GeneralDataDetailRepo;

  @BindRepo(UserGroupRepo)
  private userGroupRepo: UserGroupRepo;

  @BindRepo(AccessRepo)
  private accessRepo: AccessRepo;

  @BindRepo(ProductionOrderUniqueCodeDetailRepo)
  private productionOrderUniqueCodeDetailRepo: ProductionOrderUniqueCodeDetailRepo;

  @BindRepo(UrlRepo)
  private urlRepo: UrlRepo;

  @DefTransaction()
  async create(data: CreateProductionOrderDto) {
    // check validate data
    const checkProduct = await this.itemRepo.findOne({
      where: { id: data.materialId },
      relations: ['uomConversions'],
    });
    if (!checkProduct) throw new BusinessException('Not found item');

    const checkShift = await this.shiftRepo.findOne({ where: { id: data.shiftId } });
    if (!checkShift) throw new BusinessException('Not found shift');

    const checkReceipt = await this.recipeRepo.findOne({ where: { id: data.recipeId } });
    if (!checkReceipt) throw new BusinessException('Not found receipt');

    const checkFactory = await this.organizationUnitRepo.findOne({ where: { id: data.factoryId } });
    if (!checkFactory) throw new BusinessException('Not found factory');

    // let checkUom: any = {};
    // if (isUUID(data.uom)) {
    //   checkUom = await this.generalDataDetailRepo.findOne({
    //     where: {
    //       id: data.uom,
    //     },
    //   });
    //   if (!checkUom) throw new BusinessException('Not found Uom');
    // }

    const checkOrderStatus = await this.generalDataDetailRepo.findOne({
      where: { id: data.orderStatus },
    });

    const checkProcessArea = await this.organizationUnitRepo.findOne({
      where: { id: data.processAreaId },
    });
    if (!checkProcessArea) throw new BusinessException('Not found Process area');

    const checkLine = await this.organizationUnitRepo.findOne({
      where: { id: checkProcessArea.parentId },
    });

    const siteCode = await this.getSiteCode(checkProcessArea);

    // Thêm mới order
    const newOrder = new ProductionOrderEntity();
    newOrder.poParentId = data.poParentId || null;
    newOrder.relatedOrder = data.poParentId || null;
    newOrder.id = uuidv4();
    newOrder.processAreaId = data.processAreaId;
    newOrder.shiftId = data.shiftId;
    newOrder.orderNo = await this.genCodeOrder(
      data,
      newOrder.id,
      checkProduct.code,
      checkShift.code,
      data.planStartDate,
      siteCode,
    );
    newOrder.orderName = data.orderName;
    newOrder.recipeId = data.recipeId;
    newOrder.recipeCode = checkReceipt.recipeNo;
    newOrder.recipeVersion = checkReceipt.recipeVer;
    newOrder.shiftId = data.shiftId;
    newOrder.recipeId = data.recipeId;
    newOrder.planStartDate = data.planStartDate;
    newOrder.planEndDate = data.planEndDate;
    newOrder.operatorUserId = data.operatorUserId;
    if (!data.lot) {
      newOrder.lotNumber = this.genLotNumber(
        checkLine?.code,
        checkShift.code,
        data.planStartDate,
        siteCode,
        checkFactory.code,
      );
    } else {
      newOrder.lotNumber = data.lot;
    }
    newOrder.factoryId = data.factoryId;
    newOrder.processAreaId = data.processAreaId;
    newOrder.siteId = data.siteId;
    newOrder.lineId = data.lineId;
    newOrder.orderStatus = data.orderStatus;
    newOrder.orderStatusCode = checkOrderStatus?.code;
    newOrder.trxUom = data.uom;
    newOrder.trxUomCode = data.uom;
    newOrder.type = NSMember.EProductOrderType.PRODUCT;
    newOrder.itemId = data.materialId;
    newOrder.quantity = +data.quantity || 0;
    newOrder.orderSerialized = 0;
    await this.productionOrderRepo.insert(newOrder);

    // Lấy thông tin công thức sản phẩm từ rcp
    const lstRcpProcess = await this.recipeProcessRepo.find({
      where: { recipeId: newOrder.recipeId },
      select: ['id', 'processId'],
    });
    const mapRcpProcess = new Map(lstRcpProcess.map((rcpPro) => [rcpPro.id, rcpPro]));
    const lstReceiptItem = await this.recipeProcessItemRepo.find({
      where: { recipeProcessId: In(lstRcpProcess.map((rp) => rp.id)) },
    });
    const lstItemId = [...new Set(lstReceiptItem.map((r) => r.itemId))];
    const lstItem = await this.itemRepo.find({ where: { id: In(lstItemId) } });
    const mapItem = new Map(lstItem.map((item) => [item.id, item]));

    const lstUomId = [...new Set(lstReceiptItem.map((r) => r.uom))];
    let lstUom = [];
    if (lstUomId.every((uom) => isUUID(uom))) {
      lstUom = await this.generalDataDetailRepo.find({ where: { id: In(lstUomId) } });
    }
    const mapUom = new Map(lstUom.map((item) => [item.id, item]));

    // Sản phẩm cần sx
    const checkProductRcpItem = lstReceiptItem.find(
      (rcpItem) => rcpItem.itemId === newOrder.itemId,
    );
    // Tính toán tỉ lệ
    const { ratio, quantityOrderByRcp, conversionItemToRcp } = this.computeRatioQuantity(
      checkProduct.uomConversions,
      newOrder,
      checkProductRcpItem,
      checkReceipt,
    );

    // Thêm mới sản phẩm trong order
    const lstOrderItemInsert = [];
    let orderProductId = null;
    if (!checkProductRcpItem) {
      const newOrderItem = new ProductionOrderMaterialEntity();
      newOrderItem.id = uuidv4();
      newOrderItem.poParentId = data.poParentId || null;
      newOrderItem.orderId = newOrder.id;
      newOrderItem.orderNo = newOrder.orderNo;
      newOrderItem.recipeId = newOrder.recipeId;
      newOrderItem.recipeVersion = newOrder.recipeVersion;
      newOrderItem.processId = null;
      newOrderItem.materialId = data.materialId;
      newOrderItem.materialCode = checkProduct.code;
      newOrderItem.materialName = checkProduct.name;
      newOrderItem.trxUom = checkReceipt.batchUom;
      newOrderItem.trxUomCode = checkReceipt.batchUom;
      newOrderItem.lineType = 1;
      newOrderItem.lineTypeCode = NSRecipe.RecipeProcessItemTypeCode.Product;
      newOrderItem.planTrxQty = quantityOrderByRcp;
      newOrderItem.actualTrxQty = 0;
      lstOrderItemInsert.push(newOrderItem);
      orderProductId = newOrderItem.id;
    }

    for (let receiptItem of lstReceiptItem) {
      const curRcpProcess = mapRcpProcess.get(receiptItem.recipeProcessId);
      const curItem = mapItem.get(receiptItem.itemId);
      const newOrderItem = new ProductionOrderMaterialEntity();
      newOrderItem.id = uuidv4();
      newOrderItem.poParentId = data.poParentId || null;
      newOrderItem.orderId = newOrder.id;
      newOrderItem.orderNo = newOrder.orderNo;
      newOrderItem.recipeId = newOrder.recipeId;
      newOrderItem.recipeVersion = newOrder.recipeVersion;
      newOrderItem.processId = curRcpProcess.processId;
      newOrderItem.materialId = receiptItem.itemId;
      newOrderItem.materialCode = curItem?.code;
      newOrderItem.materialName = curItem?.name;
      newOrderItem.trxUom = receiptItem.uom;
      newOrderItem.trxUomCode = mapUom.get(receiptItem.uom)?.code || receiptItem.uom;
      newOrderItem.lineType = dictLineType[receiptItem.typeCode];
      newOrderItem.lineTypeCode = receiptItem.typeCode;
      newOrderItem.planTrxQty = ratio * (+receiptItem.quantity || 0);
      newOrderItem.actualTrxQty = 0;
      lstOrderItemInsert.push(newOrderItem);
      if (receiptItem.itemId === newOrder.itemId) orderProductId = newOrderItem.id;
    }
    await this.productionOrderMaterialRepo.insert(lstOrderItemInsert);

    // Thêm mới nguồn lực cho order
    const lstReceiptResource = await this.recipeResourceRepo.find({
      where: { recipeProcessId: In(lstRcpProcess.map((rp) => rp.id)) },
    });

    const lstOrderResourceInsert = [];
    for (let resource of lstReceiptResource) {
      const newResource = new ProductionOrderResourceEntity();
      newResource.orderId = newOrder.id;
      newResource.orderNo = newOrder.orderNo;
      newResource.processId = mapRcpProcess.get(resource.recipeProcessId)?.processId;
      newResource.recipeId = newOrder.recipeId;
      newResource.recipeVersion = newOrder.recipeVersion;
      newResource.receiptResourceId = resource.id;
      newResource.resourceCode = resource.resource;
      newResource.uom = resource.resourceUsageUom;
      newResource.uomCode = resource.resourceUsageUom;
      newResource.planProdQty = quantityOrderByRcp;
      newResource.actualProdQty = 0;
      newResource.planResourceUsage = ratio * (+resource.resourceUsage || 0);
      newResource.actualResourceUsage = 0;
      newResource.resourceCode = resource.resource;
      lstOrderResourceInsert.push(newResource);
    }
    await this.productionOrderResourceRepo.insert(lstOrderResourceInsert);

    // Tạo mẻ sản xuất
    const insertSize = 500000; // insert 500000 records 1 lần
    let lstOrderBatchInsert = [];
    const batchSizeByPo = conversionItemToRcp
      ? (+checkReceipt.batchSize || 0) / conversionItemToRcp
      : 0;
    if (newOrder.quantity > 0) {
      let planTrxQty = newOrder.quantity;
      let idx = 1;
      do {
        const newProductBatch = new ProductionBatchEntity();
        newProductBatch.orderId = newOrder.id;
        newProductBatch.batchNumber = idx;
        // newProductBatch.orderProductId = orderProductId;
        newProductBatch.productId = newOrder.itemId;
        newProductBatch.planProdQty = Math.min(batchSizeByPo, planTrxQty);
        newProductBatch.actualProdQty = 0;
        newProductBatch.uom = newOrder.trxUom;
        // newProductBatch.status = data.orderStatus;

        planTrxQty = planTrxQty - batchSizeByPo;
        idx++;
        lstOrderBatchInsert.push(newProductBatch);
        if (lstOrderBatchInsert.length === insertSize) {
          await this.productionBatchRepo.insert(lstOrderBatchInsert);
          lstOrderBatchInsert = [];
        }
      } while (planTrxQty > 0);
    }
    await this.productionBatchRepo.insert(lstOrderBatchInsert);
    if (data.isSendScada) {
      await this.sendToScada({ lstId: [newOrder.id] });
    }
    return { message: 'Create Production Order Successfully' };
  }

  async sendToScada(data: SendScadaDto) {
    this.snsService.sendMessage(
      {
        message: 'SyncProductionOrderToScada Manual',
        data: {
          lstId: data.lstId,
        },
      },
      NSSQSS.EMessageType.SyncProductionOrderToScada,
    );
    // const res = await this.sitewiseInService.syncProductionOrderToScada(data.lstId);
    return { message: 'Send to SNS Successfully' };
  }

  /*** hàm tạo order cho bán thành phẩm  */
  @DefTransaction()
  async createBTPOrder(data: CreateBTPDTO) {
    const isHasPoParent = data.lstPo.every((po) => po.poParentId);
    if (!isHasPoParent) throw new BusinessException('Please select parent order!');
    const lstGeneralData: any = await this.generalDataRepo.findOne({
      where: {
        code: NSGeneralData.EGeneralDataCode.ORDER_STATUS,
      },
      relations: ['details'],
    });
    if (lstGeneralData?.__details__?.length <= 0) {
      throw new Error('Order status not found!');
    }
    const status = lstGeneralData?.__details__.find((x) => x.name === 'Pending').id;

    for (let po of data.lstPo) {
      po.orderStatus = status;
      await this.create(po);
    }
    return { message: 'Create Production Order Successfully' };
  }

  @DefTransaction()
  async update(data: UpdateProductionOrderDto) {
    // check validate data
    const foundOrder = await this.productionOrderRepo.findOne({
      where: { id: data.id },
    });
    if (!foundOrder) throw new BusinessException('Not found order');
    if (foundOrder.orderStatusCode !== '1')
      throw new BusinessException('Lệnh sản xuất đã được đẩy xuống Scada, không được phép sửa');

    const checkProduct = await this.itemRepo.findOne({
      where: { id: data.materialId },
      relations: ['uomConversions'],
    });
    if (!checkProduct) throw new BusinessException('Not found item');

    const checkShift = await this.shiftRepo.findOne({ where: { id: data.shiftId } });
    if (!checkShift) throw new BusinessException('Not found shift');

    const checkReceipt = await this.recipeRepo.findOne({ where: { id: data.recipeId } });
    if (!checkReceipt) throw new BusinessException('Not found receipt');

    const checkFactory = await this.organizationUnitRepo.findOne({ where: { id: data.factoryId } });
    if (!checkFactory) throw new BusinessException('Not found factory');

    // const checkUom = await this.generalDataDetailRepo.findOne({ where: { id: data.uom } });
    // if (!checkUom) throw new BusinessException('Not found Uom');

    const checkOrderStatus = await this.generalDataDetailRepo.findOne({
      where: { id: data.orderStatus },
    });

    const checkProcessArea = await this.organizationUnitRepo.findOne({
      where: { id: data.processAreaId },
    });
    if (!checkProcessArea) throw new BusinessException('Not found Process area');

    const checkLine = await this.organizationUnitRepo.findOne({
      where: { id: checkProcessArea.parentId },
    });

    const siteCode = await this.getSiteCode(checkProcessArea);

    // check po có thay đổi code không
    let isChange = await this.isChangeOrderCode(foundOrder, {
      productCode: checkProduct.code,
      shiftCode: checkShift.code,
      planStartDate: data.planStartDate,
      siteCode,
    });

    // Thêm mới order
    foundOrder.processAreaId = data.processAreaId;
    foundOrder.shiftId = data.shiftId;
    if (isChange) {
      foundOrder.orderNo = await this.genCodeOrder(
        data,
        foundOrder.id,
        checkProduct.code,
        checkShift.code,
        data.planStartDate,
        siteCode,
      );
    }
    foundOrder.orderName = data.orderName;
    foundOrder.recipeId = data.recipeId;
    foundOrder.recipeCode = checkReceipt.recipeNo;
    foundOrder.recipeVersion = checkReceipt.recipeVer;
    foundOrder.shiftId = data.shiftId;
    foundOrder.recipeId = data.recipeId;
    foundOrder.planStartDate = data.planStartDate;
    foundOrder.planEndDate = data.planEndDate;
    foundOrder.lotNumber = data.lot;
    foundOrder.operatorUserId = data.operatorUserId;
    if (!data.lot)
      foundOrder.lotNumber = this.genLotNumber(
        checkLine?.code,
        checkShift.code,
        data.planStartDate,
        siteCode,
        checkFactory.code,
      );
    foundOrder.factoryId = data.factoryId;
    foundOrder.processAreaId = data.processAreaId;
    foundOrder.siteId = data.siteId;
    foundOrder.lineId = data.lineId;
    foundOrder.orderStatus = data.orderStatus;
    foundOrder.orderStatusCode = checkOrderStatus?.code;
    foundOrder.itemId = data.materialId;
    foundOrder.quantity = +data.quantity || 0;
    foundOrder.trxUom = data.uom;
    foundOrder.trxUomCode = data.uom;
    foundOrder.type = NSMember.EProductOrderType.PRODUCT;
    await this.productionOrderRepo.update(foundOrder.id, foundOrder);

    await this.productionOrderMaterialRepo.delete({ orderId: foundOrder.id });

    // Lấy thông tin công thức sản phẩm từ rcp
    const lstRcpProcess = await this.recipeProcessRepo.find({
      where: { recipeId: foundOrder.recipeId },
      select: ['id', 'processId'],
    });
    const mapRcpProcess = new Map(lstRcpProcess.map((rcpPro) => [rcpPro.id, rcpPro]));
    const lstReceiptItem = await this.recipeProcessItemRepo.find({
      where: {
        recipeProcessId: In(lstRcpProcess.map((rp) => rp.id)),
      },
    });
    const lstItemId = [...new Set(lstReceiptItem.map((r) => r.itemId))];
    const lstItem = await this.itemRepo.find({ where: { id: In(lstItemId) } });
    const mapItem = new Map(lstItem.map((item) => [item.id, item]));

    const lstUomId = [...new Set(lstReceiptItem.map((r) => r.uom))];
    let lstUom = [];
    if (lstUomId.every((uom) => isUUID(uom))) {
      lstUom = await this.generalDataDetailRepo.find({ where: { id: In(lstUomId) } });
    }
    const mapUom = new Map(lstUom.map((item) => [item.id, item]));

    // Sản phẩm cần sx
    const checkProductRcpItem = lstReceiptItem.find(
      (rcpItem) => rcpItem.itemId === foundOrder.itemId,
    );
    // Tính toán tỉ lệ
    const { ratio, quantityOrderByRcp, conversionItemToRcp } = this.computeRatioQuantity(
      checkProduct.uomConversions,
      foundOrder,
      checkProductRcpItem,
      checkReceipt,
    );

    // Thêm mới sản phẩm trong order
    const lstOrderItemInsert = [];
    let orderProductId = null;
    if (!checkProductRcpItem) {
      const newOrderItem = new ProductionOrderMaterialEntity();
      newOrderItem.id = uuidv4();
      newOrderItem.poParentId = data.poParentId || null;
      newOrderItem.orderId = foundOrder.id;
      newOrderItem.orderNo = foundOrder.orderNo;
      newOrderItem.recipeId = foundOrder.recipeId;
      newOrderItem.recipeVersion = foundOrder.recipeVersion;
      newOrderItem.processId = null;
      newOrderItem.materialId = data.materialId;
      newOrderItem.materialCode = checkProduct.code;
      newOrderItem.materialName = checkProduct.name;
      newOrderItem.trxUom = checkReceipt.batchUom;
      newOrderItem.trxUomCode = checkReceipt.batchUom;
      newOrderItem.lineType = 1;
      newOrderItem.lineTypeCode = NSRecipe.RecipeProcessItemTypeCode.Product;
      newOrderItem.planTrxQty = quantityOrderByRcp;
      newOrderItem.actualTrxQty = 0;
      lstOrderItemInsert.push(newOrderItem);
      orderProductId = newOrderItem.id;
    }

    for (let receiptItem of lstReceiptItem) {
      const curItem = mapItem.get(receiptItem.itemId);
      const curRcpProcess = mapRcpProcess.get(receiptItem.recipeProcessId);
      const newOrderItem = new ProductionOrderMaterialEntity();
      newOrderItem.id = uuidv4();
      newOrderItem.orderId = foundOrder.id;
      newOrderItem.orderNo = foundOrder.orderNo;
      newOrderItem.recipeId = foundOrder.recipeId;
      newOrderItem.recipeVersion = foundOrder.recipeVersion;
      newOrderItem.processId = curRcpProcess.processId;
      newOrderItem.materialId = receiptItem.itemId;
      newOrderItem.materialCode = curItem?.code;
      newOrderItem.materialName = curItem?.name;
      newOrderItem.trxUom = receiptItem.uom;
      newOrderItem.trxUomCode = mapUom.get(receiptItem.uom)?.code || receiptItem.uom;
      newOrderItem.lineType = dictLineType[receiptItem.typeCode];
      newOrderItem.lineTypeCode = receiptItem.typeCode;
      newOrderItem.planTrxQty = ratio * (+receiptItem.quantity || 0);
      newOrderItem.actualTrxQty = 0;
      lstOrderItemInsert.push(newOrderItem);
      if (receiptItem.itemId === foundOrder.itemId) orderProductId = newOrderItem.id;
    }
    await this.productionOrderMaterialRepo.insert(lstOrderItemInsert);

    // Thêm mới nguồn lực cho order
    await this.productionOrderResourceRepo.delete({ orderId: foundOrder.id });
    const lstReceiptResource = await this.recipeResourceRepo.find({
      where: { recipeProcessId: In(lstRcpProcess.map((rp) => rp.id)) },
    });

    const lstOrderResourceInsert = [];
    for (let resource of lstReceiptResource) {
      const newResource = new ProductionOrderResourceEntity();
      newResource.orderId = foundOrder.id;
      newResource.orderNo = foundOrder.orderNo;
      newResource.processId = mapRcpProcess.get(resource.recipeProcessId)?.processId;
      newResource.recipeId = foundOrder.recipeId;
      newResource.recipeVersion = foundOrder.recipeVersion;
      newResource.receiptResourceId = resource.id;
      newResource.resourceCode = resource.resource;
      newResource.uom = resource.resourceUsageUom;
      newResource.uomCode = resource.resourceUsageUom;
      newResource.planProdQty = quantityOrderByRcp;
      newResource.actualProdQty = 0;
      newResource.planResourceUsage = ratio * (+resource.resourceUsage || 0);
      newResource.actualResourceUsage = 0;
      newResource.resourceCode = resource.resource;
      lstOrderResourceInsert.push(newResource);
    }
    await this.productionOrderResourceRepo.insert(lstOrderResourceInsert);

    // Tạo mẻ sản xuất
    await this.productionBatchRepo.delete({ orderId: foundOrder.id });
    const insertSize = 500000; // insert 500000 records 1 lần
    let lstOrderBatchInsert = [];
    const batchSizeByPo = conversionItemToRcp
      ? (+checkReceipt.batchSize || 0) / conversionItemToRcp
      : 0;
    if (foundOrder.quantity > 0) {
      let planTrxQty = foundOrder.quantity;
      let idx = 1;
      do {
        const newProductBatch = new ProductionBatchEntity();
        newProductBatch.orderId = foundOrder.id;
        newProductBatch.batchNumber = idx;
        // newProductBatch.orderProductId = orderProductId;
        newProductBatch.productId = foundOrder.itemId;
        newProductBatch.planProdQty = Math.min(batchSizeByPo, planTrxQty);
        newProductBatch.actualProdQty = 0;
        newProductBatch.uom = foundOrder.trxUom;
        // newProductBatch.status = data.orderStatus;

        planTrxQty = planTrxQty - batchSizeByPo;
        idx++;
        lstOrderBatchInsert.push(newProductBatch);
        if (lstOrderBatchInsert.length === insertSize) {
          await this.productionBatchRepo.insert(lstOrderBatchInsert);
          lstOrderBatchInsert = [];
        }
      } while (planTrxQty > 0);
    }
    await this.productionBatchRepo.insert(lstOrderBatchInsert);
    if (data.isSendScada && !foundOrder.isScadaSynced) {
      await this.sendToScada({ lstId: [foundOrder.id] });
    }
    return { message: 'Update Production Order Successfully' };
  }

  @DefTransaction()
  async delete(id: string) {
    const foundOrder = await this.productionOrderRepo.findOne({ where: { id } });
    if (!foundOrder) throw new BusinessException('Not found order');
    if (foundOrder.orderStatusCode !== '1')
      throw new BusinessException('Lệnh sản xuất đã được đẩy xuống Scada, không được phép sửa');

    await this.productionBatchRepo.delete({ orderId: foundOrder.id });
    await this.productionOrderResourceRepo.delete({ orderId: foundOrder.id });
    await this.productionOrderMaterialRepo.delete({ orderId: foundOrder.id });
    await this.productionOrderRepo.delete({ id: foundOrder.id });
    return { message: 'Delete success' };
  }

  // load danh sách bán thành phẩm của PO đc chọn
  async loadListPOMaterialOfProductOrder(body: LstPOIdDto) {
    const [lstPo, lstPoMaterial] = await Promise.all([
      await this.productionOrderRepo.find({
        where: { id: In(body.poIds) },
        relations: ['item', 'processArea'],
      }),
      await this.productionOrderMaterialRepo.find({
        where: { orderId: In(body.poIds), lineType: -1 },
      }),
    ]);

    const lstItemId = [...new Set(lstPoMaterial.map((po) => po.materialId))];

    const itemWithTypeIP = await this.itemRepo.find({
      where: { id: In(lstItemId), type: 'IP' },
    });

    const lstItemIdWithTypeIP = itemWithTypeIP.map((item) => item.id);
    const lstItem = [];

    for (const po of lstPo) {
      const lstPoMaterial: any = await this.productionOrderMaterialRepo.find({
        where: { orderId: po.id, lineType: -1, materialId: In(lstItemIdWithTypeIP) },
      });
      const curItem: any = {
        orderNo: po.orderNo,
        orderId: po.id,
        ...po.item,
        isParent: true,
        shiftId: po.shiftId,
      };

      if (lstPoMaterial.length !== 0) {
        lstItem.push(curItem);
      }
      const dataSelectOption = await this.loadProcessAreasByPo(po.processArea.parentId);
      for (let itemPoMaterial of lstPoMaterial) {
        // tính toán số lượng đã order
        const lstPoMExist = await this.productionOrderMaterialRepo.find({
          where: {
            poParentId: po.id,
            lineType: 1,
            materialId: itemPoMaterial.materialId,
          },
        });
        let totalQty = 0;
        for (let poMaterial of lstPoMExist) {
          totalQty += +poMaterial.planTrxQty;
        }

        const recipeOptions = await this.recipeRepo.find({
          where: {
            productId: itemPoMaterial.materialId,
            recipeStatus: NSRecipe.RecipeStatus.ApprovedForGeneralUse,
          },
        });
        lstItem.push({
          ...itemPoMaterial,
          poParentId: po.id,
          productQuantity: +itemPoMaterial.planTrxQty,
          orderedQuantity: totalQty,
          orderQuantity: +itemPoMaterial.planTrxQty - totalQty,
          shiftId: po.shiftId,
          isParent: false,
          recipeId: null,
          recipeOptions: recipeOptions,
          startDate: new Date(),
          ...dataSelectOption,
        });
      }
    }

    return {
      data: lstItem,
    };
  }

  // load danh sách data select cho po bán thành phẩm
  private async loadProcessAreasByPo(processAreaParentId: string) {
    const lstGeneralData = await this.generalDataRepo.find({
      where: {
        code: In([NSGeneralData.EGeneralDataCode.ORG_LEVEL]),
      },
    });

    const orgLevel = lstGeneralData.find(
      (gen) => gen.code === NSGeneralData.EGeneralDataCode.ORG_LEVEL,
    );
    const levelProcess = await this.generalDataDetailRepo.findOne({
      where: { code: 'PROCESS', generalId: orgLevel?.id, isActive: true },
    });
    const levelLine = await this.generalDataDetailRepo.findOne({
      where: { code: 'LINE', generalId: orgLevel?.id, isActive: true },
    });
    const levelFactory = await this.generalDataDetailRepo.findOne({
      where: { code: 'FACTORY', generalId: orgLevel?.id, isActive: true },
    });
    const levelSite = await this.generalDataDetailRepo.findOne({
      where: { code: 'SITE', generalId: orgLevel?.id, isActive: true },
    });

    // Lấy thông tin process area: Lấy theo cây Factory -> line -> process area
    const unitProcess: any[] = await this.organizationUnitRepo.find({
      where: {
        levelId: levelProcess?.id,
        parentId: processAreaParentId,
        isActive: true,
      },
      select: ['id', 'code', 'name', 'parentId'],
      relations: ['parent', 'parent.parent', 'parent.parent.parent', 'parent.parent.parent.parent'],
    });

    for (let unit of unitProcess) {
      const factory = unit.parent?.parent;
      const proLine = unit.parent;
      const site = unit.parent?.parent?.parent;
      if (factory && factory.levelId === levelFactory?.id) {
        unit.factoryId = factory.id;
        unit.factoryName = factory.name;
        unit.factoryCode = factory.code;
      }
      if (proLine && proLine.levelId === levelLine?.id) {
        unit.proLineId = proLine.id;
        unit.proLineName = proLine.name;
        unit.proLineCode = proLine.code;
      }
      if (site && site.levelId === levelSite?.id) {
        unit.siteId = site.id;
        unit.siteName = site.name;
        unit.siteCode = site.code;
      }

      delete unit.parent;
    }

    // [ ds ca, ds rcp]
    const shifts = await this.shiftRepo.find({ where: { status: true } });

    return {
      productionAreas: unitProcess,
      shifts,
    };
  }

  async pagination(userId: string, params: ProductOrderPageReq) {
    const lstAccess = await this.accessRepo.find({
      where: { user: { id: userId }, status: true },
      relations: ['user', 'organizationUnit'],
    });
    const whereCon: any = { factoryId: In(lstAccess.map((i) => i.organizationUnit.id)) };

    if (params.planStartDate?.length === 2) {
      const fdTz = getFirstDayTz(params.planStartDate[0]);
      const ldTz = getLastDayTz(params.planStartDate[1]);
      whereCon.planStartDate = Between(fdTz, ldTz);
    }
    if (params.orderNo) whereCon.orderNo = Like(`%${params.orderNo}%`);
    if (params.factoryId) whereCon.factoryId = params.factoryId;
    if (params.processAreaId) whereCon.processAreaId = params.processAreaId;
    if (params.orderStatus) whereCon.orderStatus = params.orderStatus;
    if (params.itemId) whereCon.itemId = params.itemId;
    if (params.lineId) whereCon.lineId = params.lineId;
    if (params.siteId) whereCon.siteId = params.siteId;
    const res: any = await this.productionOrderRepo.findPagination(
      {
        where: whereCon,
        order: { createdDate: 'DESC' },
        relations: ['item', 'processArea'],
      },
      params,
    );

    let lstOriginUnitId = [];
    let lstOrderStatusId = [];
    let lstShiftId = [];
    for (let item of res.data) {
      lstOriginUnitId.push(item.processAreaId);
      lstOriginUnitId.push(item.factoryId);
      lstOriginUnitId.push(item.siteId);
      lstOriginUnitId.push(item.lineId);
      lstOrderStatusId.push(item.orderStatus);
      lstShiftId.push(item.shiftId);
    }
    lstOriginUnitId = [...new Set(lstOriginUnitId)];

    const lstOriginUnit = await this.organizationUnitRepo.find({
      where: { id: In(lstOriginUnitId) },
      select: ['id', 'code', 'name'],
    });
    const mapOriginUnit = new Map(lstOriginUnit.map((u) => [u.id, u]));

    const lstOrderStatus = await this.generalDataDetailRepo.find({
      where: { id: In(lstOrderStatusId) },
    });
    const mapOrderStatus = new Map(lstOrderStatus.map((os) => [os.id, os]));

    const lstShift = await this.shiftRepo.find({
      where: { id: In(lstShiftId) },
      select: ['id', 'code', 'startTime', 'endTime', 'description'],
    });
    const mapShift = new Map(lstShift.map((shift) => [shift.id, shift]));

    let idx = (params.pageIndex - 1) * params.pageSize + 1;
    for (let item of res.data) {
      item.itemCode = item.item?.code;
      item.itemName = item.item?.name;
      item.materialId = item.itemId;
      item.uom = item.trxUom;
      item.no = idx;
      item.productionOrderNo = item.orderNo;
      item.lot = item.lotNumber;
      item.recipeNo = item.recipeCode;
      item.recipeVersion = +item.recipeVersion || 0;
      idx++;

      item.recipeNote = `${item.recipeNo} - ${item.recipeVersion}`;

      const curFactory = mapOriginUnit.get(item.factoryId);
      const curProcessArea = mapOriginUnit.get(item.processAreaId);
      const curSite = mapOriginUnit.get(item.siteId);
      const curLine = mapOriginUnit.get(item.lineId);
      const curStatus = mapOrderStatus.get(item.orderStatus);

      item.processAreaName = curProcessArea?.name;
      item.factoryName = curFactory?.name;
      item.lineName = curLine?.name;
      item.statusName = curStatus?.name;
      item.factoryNote = `${curSite?.code || ''} - ${curFactory?.name} - ${curLine?.name}`;
      item.processAreaNote = `${curProcessArea?.code} - ${curProcessArea?.name}`;

      item.processAreaName = item.processArea?.name;
      item.processAreaCode = item.processArea?.code;

      const curShift = mapShift.get(item.shiftId);
      item.shiftCode = curShift?.code;
      item.shiftNote = curShift?.code + ' - ' + curShift?.description;
      const lstUrl = await this.urlRepo.find({
        where: { orderId: item.id },
      });
      item.lstFile = lstUrl.map((i) => ({
        id: i.id,
        fileUrl: i.url,
        fileName: i.fileName,
      }));
      delete item.item;
      delete item.processArea;
    }
    return res;
  }

  async findOne(id: string) {
    return this.productionOrderRepo.findOne({ where: { id } });
  }

  async find(data: { lstId: string[]; isScadaSynced: boolean }) {
    const res: any[] = await this.productionOrderRepo.find({
      where: { id: In(data.lstId), isScadaSynced: data.isScadaSynced },
      relations: ['item'],
    });
    for (let item of res) {
      item.itemCode = item.item?.code;
      item.itemName = item.item?.name;
      delete item.item;

      item.recipeNote = `${item.recipeCode} - ${item.recipeVersion}`;
    }
    return res;
  }

  async loadDataSelectForCreate(userId: string) {
    const lstAccess = await this.accessRepo.find({
      where: { user: { id: userId }, status: true },
      relations: ['user', 'organizationUnit'],
    });
    const lstFactory = await this.organizationUnitRepo.find({
      where: {
        id: In(lstAccess.map((i) => i.organizationUnit.id)),
        isActive: true,
        levelGeneralDataDetailCode: 'FACTORY',
      },
    });
    const lstSite = await this.organizationUnitRepo.find({
      where: {
        id: In(lstFactory.map((i) => i.parentId)),
        isActive: true,
        levelGeneralDataDetailCode: 'SITE',
      },
    });
    const lstGeneralData = await this.generalDataRepo.find({
      where: {
        code: In([
          NSGeneralData.EGeneralDataCode.ORG_LEVEL,
          NSGeneralData.EGeneralDataCode.UOM,
          NSGeneralData.EGeneralDataCode.ORDER_STATUS,
          NSGeneralData.EGeneralDataCode.USER_GROUP,
        ]),
      },
    });

    const orgLevel = lstGeneralData.find(
      (gen) => gen.code === NSGeneralData.EGeneralDataCode.ORG_LEVEL,
    );
    const lstGeneralDetail = await this.generalDataDetailRepo.find({
      where: {
        code: In(['PROCESS', 'FACTORY', 'LINE', 'SITE']),
        generalId: orgLevel?.id,
        isActive: true,
      },
    });
    const levelProcess = lstGeneralDetail.find((detail) => detail.code === 'PROCESS');
    const levelLine = lstGeneralDetail.find((detail) => detail.code === 'LINE');
    const levelFactory = lstGeneralDetail.find((detail) => detail.code === 'FACTORY');
    const levelSite = lstGeneralDetail.find((detail) => detail.code === 'SITE');

    const lstOriginUnit = await this.organizationUnitRepo.find({
      where: { levelId: In(lstGeneralDetail.map((detail) => detail?.id)), isActive: true },
      select: ['id', 'code', 'name', 'parentId', 'levelId'],
    });

    // Lấy thông tin master data Factory
    const unitFactories = lstFactory;
    const unitLines = lstOriginUnit.filter((ou) => ou.levelId === levelLine.id);
    const unitProcess: any[] = lstOriginUnit.filter((ou) => ou.levelId === levelProcess.id);
    const unitSites = lstSite;
    const mapUnitLine = new Map(unitLines.map((unit) => [unit.id, unit]));

    for (let unit of unitProcess) {
      const curLine = mapUnitLine.get(unit.parentId);
      unit.factoryId = curLine?.parentId;
    }

    // Lấy danh sách đơn vị tính
    const uom = lstGeneralData.find((gen) => gen.code === NSGeneralData.EGeneralDataCode.UOM);
    const uoms = await this.generalDataDetailRepo.find({
      where: { generalId: uom?.id, isActive: true },
    });

    // Lấy ds trạng thái lệnh sản xuất
    const orderStatus = lstGeneralData.find(
      (gen) => gen.code === NSGeneralData.EGeneralDataCode.ORDER_STATUS,
    );
    const orderStatuss = await this.generalDataDetailRepo.find({
      where: { generalId: orderStatus?.id, isActive: true },
    });

    // Lấy user operator
    const userGroupOperator = await this.userGroupRepo.find({
      where: { isActive: true, typeCode: '1' },
      relations: ['users'],
    });
    const setUserId = new Set();
    const newLstUser = [];
    for (let userGr of userGroupOperator) {
      for (let user of userGr.users) {
        if (!setUserId.has(user.id)) {
          newLstUser.push(user);
          setUserId.add(user.id);
        }
      }
    }

    // [Lấy ds sản phẩm, ds ca, ds rcp]
    const [items, shifts, receipts] = await Promise.all([
      this.itemRepo.find({
        where: { status: NSItem.Status.Active, type: In(['FG', 'IP']) },
        select: ['id', 'code', 'name', 'baseUnit'],
      }),
      this.shiftRepo.find({ where: { status: true } }),
      this.recipeRepo.find({ where: {} }),
    ]);
    return {
      items,
      unitProcess,
      shifts,
      receipts,
      uoms,
      orderStatuss,
      unitFactories,
      unitLines,
      unitSites,
      users: newLstUser,
    };
  }

  /** Kiểm tra xem order code có thay đổi k */
  private async isChangeOrderCode(
    order: ProductionOrderEntity,
    data: { productCode: string; shiftCode: string; planStartDate: Date; siteCode: string },
  ) {
    if (!order.orderNo) return true;

    const oldItem = await this.itemRepo.findOne({
      where: { id: order.itemId },
      select: ['id', 'code'],
    });
    if (oldItem.code !== data.productCode) return true;

    const oldShift = await this.shiftRepo.findOne({
      where: { id: order.shiftId },
      select: ['id', 'code'],
    });
    if (oldShift.code !== data.shiftCode) return true;

    const oldSite = await this.organizationUnitRepo.findOne({
      where: { id: order.siteId },
      select: ['id', 'code'],
    });
    if (oldSite.code !== data.siteCode) return true;

    if (
      moment(data.planStartDate).format('DD/MM/YYYY') !==
      moment(order.planStartDate).format('DD/MM/YYYY')
    ) {
      return true;
    }
    return false;
  }

  private async genCodeOrder(
    data: CreateProductionOrderDto,
    id: string,
    productCode: string,
    shiftCode: string,
    planStartDate: Date,
    siteCode: string,
  ) {
    const fdTz = getFirstDayTz(planStartDate);
    const ldTz = getLastDayTz(planStartDate);
    const lstOrder = await this.productionOrderRepo.find({
      where: {
        planStartDate: Between(fdTz, ldTz),
        id: Not(id),
        itemId: data.materialId,
        shiftId: data.shiftId,
        siteId: data.siteId,
      },
    });
    const maxNum = lstOrder.length
      ? Math.max(
          ...lstOrder.map(
            (order) => +order.orderNo?.slice(order.orderNo.length - 2, order.orderNo.length) || 0,
          ),
        )
      : 0;
    const strNum = maxNum < 9 ? `0${maxNum + 1}` : `${maxNum + 1}`;

    const dateFmt = moment(planStartDate).format('DDMMYY');
    return `${productCode}${dateFmt}${shiftCode}${siteCode}${strNum}`;
  }

  private genLotNumber(
    productCode: string,
    shiftCode: string,
    planStartDate: Date,
    siteCode: string,
    factoryCode: string,
  ) {
    const dateFmt = moment(planStartDate).format('DDMMYY');
    return `${dateFmt}${shiftCode}${factoryCode}${productCode}-${siteCode}`;
  }

  private async getSiteCode(checkProcessArea: OrganizationUnitEntity) {
    const organizationUnitLine = await this.organizationUnitRepo.findOne({
      where: { id: checkProcessArea.parentId },
      select: ['id', 'parentId'],
    });
    if (!organizationUnitLine || !organizationUnitLine.parentId) return '';

    const organizationUnitFactory = await this.organizationUnitRepo.findOne({
      where: { id: organizationUnitLine.parentId },
      select: ['id', 'parentId'],
    });
    if (!organizationUnitFactory || !organizationUnitFactory.parentId) return '';

    const organizationUnitSite = await this.organizationUnitRepo.findOne({
      where: { id: organizationUnitFactory.parentId },
      select: ['id', 'parentId', 'code'],
    });
    return organizationUnitSite?.code || '';
  }

  /** Tính toán tỉ lệ sp cần sx / rcp */
  private computeRatioQuantity(
    lstItemUom: UomConversionEntity[],
    productOrder: ProductionOrderEntity,
    checkProductRcpItem: RecipeProcessItemEntity,
    recipe: RecipeEntity,
  ) {
    const checkProductBase = {
      quantity: +recipe.batchSize || 0, // Số lượng sản xuất theo rcp
      uom: recipe.batchUom,
    };

    // Sản phẩm cần sx
    if (checkProductRcpItem) {
      checkProductBase.quantity = checkProductRcpItem.quantity;
      checkProductBase.uom = checkProductRcpItem.uom;
    }
    // Nếu lệnh sx cùng đơn vị với recipe thì chia thẳng ra lấy tỉ lệ
    if (checkProductBase.uom === productOrder.trxUom) {
      return {
        ratio: productOrder.quantity / checkProductBase.quantity,
        quantityOrderByRcp: productOrder.quantity,
        conversionItemToRcp: 1,
      };
    }

    // Nếu khác đơn vị thì cần quy đổi ra cùng đơn vị với recipe
    let conversionItemToRcp = 0;
    {
      let foundConversion = lstItemUom.find(
        (itemUom) =>
          itemUom.fromUnit === productOrder.trxUom && itemUom.toUnit === checkProductBase.uom,
      );
      if (foundConversion) {
        conversionItemToRcp = +foundConversion.conversion || 0;
      }
      if (!foundConversion) {
        foundConversion = lstItemUom.find(
          (itemUom) =>
            itemUom.fromUnit === checkProductBase.uom && itemUom.toUnit === productOrder.trxUom,
        );
        if (foundConversion?.conversion) conversionItemToRcp = 1 / +foundConversion.conversion;
      }
    }
    if (!conversionItemToRcp) return { ratio: 0, quantityOrderByRcp: 0, conversionItemToRcp: 0 };

    // Số lượng lệnh sx tính theo đơn vị recipe
    const quantityOrderByRcp = conversionItemToRcp * productOrder.quantity;

    return {
      ratio: quantityOrderByRcp / checkProductBase.quantity,
      quantityOrderByRcp,
      conversionItemToRcp,
    };
  }

  async loadListProductOrderMaterialPO(id: string, params: any) {
    const whereCon: any = { orderId: id };
    if (params.processCode) whereCon.processCode = params.processCode;
    if (params.materialCode) whereCon.materialCode = params.materialCode;
    if (params.materialName) whereCon.materialName = params.materialName;
    if (params.type) whereCon.type = params.type;
    const res: any = await this.productionOrderMaterialRepo.find({ where: { orderId: id } });
    if (res.length === 0)
      return {
        data: [],
        total: 0,
      };
    const dictProcess: any = {};
    {
      const lstProcess = await this.processRepo.find({
        where: { id: In(res.map((r) => r.processId)) },
      });
      lstProcess.forEach((i) => (dictProcess[i.id] = i.code));
    }
    const dictMaterial: any = {};
    {
      const lstMaterial = await this.itemRepo.find({
        where: { id: In(res.map((r) => r.materialId)) },
      });
      lstMaterial.forEach((i) => (dictMaterial[i.id] = i));
    }
    res.forEach((i) => {
      i.processCode = dictProcess[i.processId];
      i.type = dictMaterial[i.materialId]?.type;
    });

    res.sort((a, b) => {
      if (a.processCode < b.processCode) return -1;
      if (a.processCode > b.processCode) return 1;

      if (a.materialCode < b.materialCode) return -1;
      if (a.materialCode > b.materialCode) return 1;

      return 0; // hoàn toàn giống nhau
    });

    const resFilter = res.filter((item) => {
      return Object.entries(whereCon).every(([key, value]) => {
        // Convert both sides to lowercase strings for case-insensitive match (optional)
        return (item[key]?.toString().toLowerCase() ?? '').includes(
          value?.toString().toLowerCase(),
        );
      });
    });

    // Pagination thủ công
    const pageIndex = params.pageIndex || 1;
    const pageSize = params.pageSize || 10;
    const offset = (pageIndex - 1) * pageSize;

    const paginatedData = resFilter.slice(offset, offset + pageSize);

    return {
      data: paginatedData,
      total: resFilter.length,
    };
  }

  async loadListProductOrderResourcePO(id: string, params: any) {
    const whereCon: any = { orderId: id };
    if (params.processCode) whereCon.processCode = params.processCode;
    if (params.resourceCode) whereCon.resourceCode = params.resourceCode;
    if (params.resourceDesc) whereCon.resourceDesc = params.resourceDesc;
    const res: any = await this.productionOrderResourceRepo.find({ where: { orderId: id } });
    if (res.length === 0)
      return {
        data: [],
        total: 0,
      };
    const dictProcess: any = {};
    {
      const lstProcess = await this.processRepo.find({
        where: { id: In(res.map((r) => r.processId)) },
      });
      lstProcess.forEach((i) => (dictProcess[i.id] = i.code));
    }
    const dictResource: any = {};
    {
      const lstResource = await this.recipeResourceRepo.find({
        where: { resource: In(res.map((i) => i.resourceCode)) },
      });
      lstResource.forEach((i) => (dictResource[i.resource] = i.resourceDesc));
    }
    res.forEach((i) => {
      i.processCode = dictProcess[i.processId];
      i.resourceDesc = dictResource[i.resourceCode];
    });
    res.sort((a, b) => {
      if (a.processCode < b.processCode) return -1;
      if (a.processCode > b.processCode) return 1;

      if (a.resourceCode < b.resourceCode) return -1;
      if (a.resourceCode > b.resourceCode) return 1;

      return 0; // hoàn toàn giống nhau
    });
    const resFilter = res.filter((item) => {
      return Object.entries(whereCon).every(([key, value]) => {
        // Convert both sides to lowercase strings for case-insensitive match (optional)
        return (item[key]?.toString().toLowerCase() ?? '').includes(
          value?.toString().toLowerCase(),
        );
      });
    });

    // Pagination thủ công
    const pageIndex = params.pageIndex || 1;
    const pageSize = params.pageSize || 10;
    const offset = (pageIndex - 1) * pageSize;

    const paginatedData = resFilter.slice(offset, offset + pageSize);

    return {
      data: paginatedData,
      total: resFilter.length,
    };
  }

  async loadListProductOrderMaterialTransactionsPO(id: string, params: any) {
    const whereCon: any = { orderId: id };
    if (params.processCode) whereCon.processCode = params.processCode;
    if (params.transactionType) whereCon.transactionType = params.transactionType;
    if (params.materialCode) whereCon.materialCode = params.materialCode;
    if (params.materialName) whereCon.materialName = params.materialName;
    const res: any = await this.productionOrderMaterialTransactionRepo.find({
      where: { orderId: id },
      order: { createdDate: 'DESC' },
    });

    const dictProcess: any = {};
    {
      const uuidRegex =
        /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;

      const materialIds = res
        .map((i) => i.productionOrderMaterialId)
        .filter((id) => typeof id === 'string' && uuidRegex.test(id)); // Loại bỏ NaN

      const lstMaterial = await this.productionOrderMaterialRepo.find({
        where: { id: In(materialIds) },
      });
      const lstProcess = await this.processRepo.find({
        where: { id: In(lstMaterial.map((i) => i.processId)) },
      });
      lstMaterial.forEach(
        (i) => (dictProcess[i.id] = lstProcess.find((u) => u.id === i.processId)),
      );
    }

    if (res.length === 0)
      return {
        data: [],
        total: 0,
      };
    const dictMaterial: any = {};
    {
      const lstMaterial = await this.itemRepo.find({
        where: { id: In(res.map((r) => r.materialId)) },
      });
      lstMaterial.forEach((i) => (dictMaterial[i.id] = i));
    }

    const dictBatch: any = {};
    {
      const lstBatch = await this.productionBatchRepo.find({
        where: { id: In(res.map((r) => r.batchId)) },
      });
      lstBatch.forEach((i) => (dictBatch[i.id] = i));
    }
    res.forEach((i) => {
      i.batchNumber = dictBatch[i.batchId]?.batchNumber;
      i.materialCode = dictMaterial[i.materialId]?.code;
      i.materialName = dictMaterial[i.materialId]?.name;
      i.processCode = dictProcess[i.productionOrderMaterialId]?.code;
      i.type = dictMaterial[i.materialId]?.type;
      i.oeeCal = i.oeeCal === 1 ? true : false;
    });
    res.sort((a, b) => {
      if (a.batchNumber < b.batchNumber) return -1;
      if (a.batchNumber > b.batchNumber) return 1;

      if (a.materialCode < b.materialCode) return -1;
      if (a.materialCode > b.materialCode) return 1;

      return 0; // hoàn toàn giống nhau
    });
    const resFilter = res.filter((item) => {
      return Object.entries(whereCon).every(([key, value]) => {
        // Convert both sides to lowercase strings for case-insensitive match (optional)
        return (item[key]?.toString().toLowerCase() ?? '').includes(
          value?.toString().toLowerCase(),
        );
      });
    });

    // Pagination thủ công
    const pageIndex = params.pageIndex || 1;
    const pageSize = params.pageSize || 10;
    const offset = (pageIndex - 1) * pageSize;

    const paginatedData = resFilter.slice(offset, offset + pageSize);

    return {
      data: paginatedData,
      total: resFilter.length,
    };
  }

  async loadListBatchAndProcessParamPO(id: string, params: any) {
    const whereCon: any = { orderId: id };
    const order = await this.productionOrderRepo.findOne({ where: { id: id } });
    if (!order) throw new Error('Not found order');

    let baseSql = `
    WITH raw_data AS (
      SELECT DISTINCT ON (
        pb."batchNumber",
        pbs."machineId",
        mp."code",
        pbp."dateTime"
      )
        pbs."batchId",
        pb."batchNumber",
        gdd."name" as status,
        p.code AS process_code,
        p."name" AS process_name,
        pbs."machineId",
        m.code AS machine_code,
        m."name" AS machine_name,
        pbs."startTime",
        pbs."endTime",
        pbp."measurementId",
        mp.code AS parameter_code,
        mp."desc" AS parameter_desc,
        pbp.value,
        pbp."dateTime"
      FROM production_batch_status pbs
      LEFT JOIN production_batch_paramaters pbp 
        ON pbs."batchId" = pbp."batchId" and pbs."machineId" = pbp."machineId"
      LEFT JOIN machine m 
        ON m.id = CAST(pbs."machineId" AS uuid)
      LEFT JOIN process_machine pm 
        ON m.id = pm."machineId" AND pm.assign = true
      LEFT JOIN process p 
        ON p.id = pm."processId"
      LEFT JOIN production_batch pb 
        ON pb.id = CAST(pbs."batchId" AS uuid)
      LEFT JOIN machine_parameter mp 
        ON mp."iotsitewisePropertyId" = pbp."measurementId"
      LEFT JOIN production_order po 
        ON po.id = pb."orderId"
      LEFT JOIN general_data_details gdd
        ON gdd.code = pbs."status" AND gdd."generalId" IN (SELECT id FROM general_data WHERE code = 'ORDER_STATUS')
      WHERE po."orderNo" = $1
    
  `;
    const values: any[] = [order.orderNo];
    let paramIndex = 2;
    if (params.batchNumber) {
      baseSql += `AND pb."batchNumber" = $${paramIndex}`;

      values.push(`${params.batchNumber}`);
      paramIndex++;
    }
    if (params.process_code) {
      baseSql += ` AND p."code" ILIKE $${paramIndex}`;
      values.push(`%${params.process_code}%`);
      paramIndex++;
    }
    if (params.machine_code) {
      baseSql += ` AND m."code" ILIKE $${paramIndex}`;
      values.push(`%${params.machine_code}%`);
      paramIndex++;
    }
    if (params.machine_name) {
      baseSql += ` AND m."name" ILIKE $${paramIndex}`;
      values.push(`%${params.machine_name}%`);
      paramIndex++;
    }
    if (params.parameter_code) {
      baseSql += ` AND CAST(mp."code" AS TEXT) ILIKE $${paramIndex}`;
      values.push(`%${params.parameter_code}%`);
      paramIndex++;
    }
    if (params.parameter_desc) {
      baseSql += ` AND mp."desc" ILIKE $${paramIndex}`;
      values.push(`%${params.parameter_desc}%`);
      paramIndex++;
    }
    if (params.status) {
      baseSql += ` AND  gdd."name" ILIKE $${paramIndex}`;
      values.push(`%${params.status}%`);
      paramIndex++;
    }
    baseSql += `)`;
    const dataSql = `
    ${baseSql}
    SELECT * FROM raw_data
    ORDER BY "batchNumber", "machine_code", "parameter_code", "dateTime"
    LIMIT $${paramIndex} OFFSET $${paramIndex + 1}
  `;

    const totalSql = `
    ${baseSql}
    SELECT COUNT(*) FROM raw_data
  `;

    const [data, totalResult] = await Promise.all([
      this.entityManager.query(dataSql, [
        ...values,
        params.pageSize,
        (params.pageIndex - 1) * params.pageSize,
      ]),
      this.entityManager.query(totalSql, [...values]),
    ]);

    data.forEach((i) => {
      if (isNaN(Number(i.value))) {
        const jsonData = JSON.parse(i.value);
        if (jsonData.dataType && jsonData.dataType === 'DateTime') {
          const formatted = dayjs(jsonData.value)
            .tz('Asia/Ho_Chi_Minh')
            .format('DD/MM/YYYY HH:mm:ss');
          i.value = formatted;
        }
      } else if (isNumber(Number(i.value))) {
        const num = this.formatNumber(i.value);
        i.value = num;
      }
    });

    return {
      data,
      total: parseInt(totalResult[0].count, 10),
    };
  }

  formatNumber = (value: string | number) => {
    const num = Number(value);
    if (isNaN(num)) return value;
    return num.toLocaleString('en-US', {
      minimumFractionDigits: 0,
      maximumFractionDigits: 5,
    });
  };

  async loadListProcessByPoID(id: string) {
    const lstProductionOrderMaterial: any = await this.productionOrderMaterialRepo.find({
      where: { orderId: id },
    });
    if (lstProductionOrderMaterial.length === 0) return lstProductionOrderMaterial;
    return await this.processRepo.find({
      where: { id: In(lstProductionOrderMaterial.map((r) => r.processId)) },
    });
  }

  async loadListTransactionType() {
    const lstGeneralData = await this.generalDataRepo.find({
      where: { code: 'TRANSACTION_TYPE', isActive: true },
    });
    const res = await this.generalDataDetailRepo.find({
      where: { generalId: In(lstGeneralData.map((i) => i.id)), isActive: true },
    });
    res.forEach((i: any) => {
      switch (i.code) {
        case 'WIP_ISSUE':
        case 'WIP_ISSUE_RETURN':
          i.lineTypeCode = 'ING';
          break;
        case 'WIP_COMPLETION':
        case 'WIP_COMPLETION_RETURN':
          i.lineTypeCode = 'PRO';
          break;
        case 'BYPRODUCT_COMPLETION':
        case 'BYPRODUCT_COMPLETION_RETURN':
          i.lineTypeCode = 'BY_PRO';
          break;
      }
    });
    return res;
  }

  async loadListUOMByPoID(id: string) {
    const lstProductionOrderMaterial: any = await this.productionOrderMaterialRepo.find({
      where: { orderId: id },
      select: ['materialId'],
    });
    if (lstProductionOrderMaterial.length === 0) return lstProductionOrderMaterial;
    const lstMaterialId = lstProductionOrderMaterial.map((i) => i.materialId);
    const lstItem = await this.itemRepo.find({
      where: { id: In(lstMaterialId), status: 'ACTIVE' },
    });
    const lstItemId = lstItem.map((i) => i.id);
    const lstUomCon = await this.uomConventionRepo.find({ where: { itemId: In(lstItemId) } });
    let data = [];
    for (const item of lstItem) {
      data.push({
        itemId: { ...item }.id,
        uom: { ...item }.baseUnit,
      });
      data.push({
        itemId: { ...item }.id,
        uom: { ...item }.inventoryUnit,
      });
      for (const uom of lstUomCon.filter((i) => i.itemId === item.id)) {
        data.push({
          itemId: { ...item }.id,
          uom: { ...uom }.toUnit,
        });
      }
    }
    // Dùng Set để loại bỏ trùng lặp, chuyển đối tượng thành chuỗi
    const uniqueData = Array.from(new Set(data.map((item) => JSON.stringify(item)))).map((item) =>
      JSON.parse(item),
    ); // Chuyển lại chuỗi thành đối tượng

    return uniqueData;
  }

  async createMaterialTransaction(id: string, body: any[]) {
    const { userId: userId } = adminSessionContext;
    const lstMaterialId = body.map((i) => i.materialId);
    const productOrder = await this.productionOrderRepo.findOne({ where: { id: id } });

    if (!productOrder) throw new BusinessException('Not found Product Order');

    const dictMaterialTransaction: any = {};
    {
      const lstMaterialTransaction = await this.productionOrderMaterialTransactionRepo.find({
        where: { orderId: id },
      });
      lstMaterialTransaction.forEach((i) => {
        if (!dictMaterialTransaction[i.productionOrderMaterialId]) {
          dictMaterialTransaction[i.productionOrderMaterialId] = [];
        }
        dictMaterialTransaction[i.productionOrderMaterialId].push(i);
      });
    }
    const plusTypes = ['WIP_ISSUE', 'WIP_COMPLETION', 'BY_PRODUCT_COMPLETION'];
    const minusTypes = ['WIP_ISSUE_RETURN', 'WIP_COMPLETION_RETURN', 'BYPRODUCT COMPLETION_RETURN'];
    const dictMaterial: any = {};
    {
      const lstProductionOrderMaterial = await this.productionOrderMaterialRepo.find({
        where: {
          processId: In(body.map((i) => i.processId)),
          materialId: In(lstMaterialId),
          orderId: id,
        },
      });

      for (const item of body) {
        const material = lstProductionOrderMaterial.find(
          (i) =>
            i.processId === item.processId && i.materialId === item.materialId && i.orderId === id,
        );
        dictMaterial[material.processId + material.materialId] = material;
      }
    }
    const lstSite = await this.organizationUnitRepo.find({
      where: { levelGeneralDataDetailCode: 'SITE', isActive: true },
      select: ['code', 'id'],
    });

    const regex = /^(\d{2})(\d{2})(\d{2})-(\w{3})/;
    const today = new Date();
    const dictUomConverse: any = {};
    {
      const itemIds = Object.values(dictMaterial).map((item: any) => item.materialId);
      const lstUomConverse = await this.uomConventionRepo.find({ where: { itemId: In(itemIds) } });
      const lstItem = Array.from(
        new Set(
          JSON.parse(
            JSON.stringify(Object.values(dictMaterial).map((i: any) => ({ itemId: i.materialId }))),
          ),
        ),
      );
      lstItem.forEach(
        (i: any) =>
          (dictUomConverse[i.itemId] = [
            ...lstUomConverse.filter((u) => u.itemId === i.itemId).map((i) => i),
          ]),
      );
    }

    for (const item of body) {
      const material = dictMaterial[item.processId + item.materialId];
      item.productionOrderMaterialId = material?.id;
      if (!item.productionOrderMaterialId) throw new BusinessException('Can not found Material');

      if (!item.lotNumber) throw new BusinessException('Lot can not undefined!');
      // const match = item.lotNumber.match(regex);
      // if (!match) {
      //   throw new BusinessException('Lot must be in the format DDMMYY-XXX.');
      // }
      // // Kiểm tra ngày tháng (DDMMYY)
      // const [_, day, month, year] = match;
      // // Kiểm tra ngày tháng hợp lệ
      // const date = new Date(`20${year}-${month}-${day}`);
      // if (
      //   date.getDate() !== parseInt(day) ||
      //   date.getMonth() + 1 !== parseInt(month) ||
      //   date.getFullYear().toString().slice(-2) !== year
      // ) {
      //   throw new BusinessException('Invalid date in Lot.');
      // }

      // const siteCodeFromLot = match[4];
      // if (
      //   !lstSite.find((i) => i.code === siteCodeFromLot) ||
      //   lstSite.find((i) => i.code === siteCodeFromLot).id !== productOrder.siteId
      // ) {
      //   throw new BusinessException('Invalid site in Lot.');
      // }
      item.batchId = null;
      item.machineId = null;
      item.metricId = null;
      item.createdDate = today;
      item.createdBy = userId;

      item.orderId = id;
      item.oeeCal = item.oeeCal === true ? 1 : 0;

      // item.lastUpdateBy = userId;
    }

    const updates = new Map();
    const materialMap = new Map();
    for (const item of body) {
      const material = dictMaterial[item.processId + item.materialId];
      item.productionOrderMaterialId = material?.id;
      if (item.transactionUom !== material.trxUomCode) {
        if (
          !dictUomConverse[material.materialId] ||
          dictUomConverse[material.materialId].length === 0
        )
          throw new BusinessException('Lỗi không tìm thấy đơn vị cần chuyển đổi');
      }

      if (dictUomConverse[material.materialId]) {
        const lstUomConverse = dictUomConverse[material.materialId];
        let qty = 0;

        let value;
        if (item.transactionUom === material.trxUomCode) value = item.transactionQty;
        else {
          value = this.calculateActualTrxQTY(lstUomConverse, material, item);
        }
        if (plusTypes.includes(item.transactionType)) {
          qty += Number(value);
        } else if (minusTypes.includes(item.transactionType)) {
          qty -= Number(value);
        }
        const oldQty = updates.get(item.productionOrderMaterialId);
        if (oldQty) updates.set(item.productionOrderMaterialId, qty + oldQty);
        else updates.set(item.productionOrderMaterialId, qty);
        materialMap.set(item.productionOrderMaterialId, material);
      }
    }

    for (let [id, actualTrxQty] of updates) {
      const lstMaterialTransaction = dictMaterialTransaction[id];
      const material = materialMap.get(id);
      const lstUomConverse = dictUomConverse[material.materialId];
      let num = 0;

      if (lstMaterialTransaction && lstMaterialTransaction.length > 0) {
        for (const materialTransaction of lstMaterialTransaction) {
          if (materialTransaction.transactionUom === material.trxUomCode)
            num = Number(materialTransaction.transactionQty);
          else {
            num = this.calculateActualTrxQTY(lstUomConverse, material, materialTransaction);
          }

          if (plusTypes.includes(materialTransaction.transactionType)) {
            actualTrxQty += num;
          } else if (minusTypes.includes(materialTransaction.transactionType)) {
            actualTrxQty -= num;
          }
        }
      }

      await this.productionOrderMaterialRepo.update({ id }, { actualTrxQty });
    }
    await this.productionOrderMaterialTransactionRepo.insert(body);
    return { message: 'Create Successfully' };
  }

  calculateActualTrxQTY(lstUomConverse, material, materialTransaction) {
    if (material.trxUomCode === materialTransaction.transactionUom)
      return Number(materialTransaction.transactionQty);
    let unitRatio;
    let inventoryUnitRatio = lstUomConverse.find(
      (i) =>
        i.fromUnit === material.trxUomCode &&
        i.toUnit === materialTransaction.transactionUom &&
        i.itemId === material.materialId,
    );
    if (inventoryUnitRatio) {
      unitRatio = numberHelper.checkNaN(Number(inventoryUnitRatio?.conversion));
      return Number(materialTransaction.transactionQty) / unitRatio;
    } else if (!inventoryUnitRatio) {
      inventoryUnitRatio = lstUomConverse.find(
        (i) =>
          i.fromUnit === materialTransaction.transactionUom &&
          i.toUnit === material.trxUomCode &&
          i.itemId === material.materialId,
      );
      if (inventoryUnitRatio) {
        unitRatio = numberHelper.checkNaN(Number(inventoryUnitRatio?.conversion));
        return Number(materialTransaction.transactionQty) * unitRatio;
      }
    }
    let uomBetween = this.findSingleIntermediate(
      materialTransaction.transactionUom,
      material.trxUomCode,
      lstUomConverse,
    );
    if (!uomBetween) throw new BusinessException('Lỗi không tìm thấy đơn vị cần chuyển đổi');

    return Number(materialTransaction.transactionQty) * uomBetween.totalRate;
  }

  findSingleIntermediate1(fromUom, toUom, lstUomConverse) {
    const unitMap = {};
    const conversionMap = {};

    // Tạo map các đơn vị kết nối lẫn nhau
    lstUomConverse.forEach(({ fromUnit, toUnit, conversion }) => {
      if (!unitMap[fromUnit]) unitMap[fromUnit] = new Set();
      if (!unitMap[toUnit]) unitMap[toUnit] = new Set();

      unitMap[fromUnit].add(toUnit);
      unitMap[toUnit].add(fromUnit); // 2 chiều
      // Lưu tỉ lệ quy đổi theo cả 2 chiều
      conversionMap[`${fromUnit}->${toUnit}`] = conversion;
      conversionMap[`${toUnit}->${fromUnit}`] = 1 / conversion;
    });

    const fromNeighbors = unitMap[fromUom] || new Set();
    const toNeighbors = unitMap[toUom] || new Set();

    // Tìm giao nhau → các đơn vị trung gian kết nối cả hai
    const intermediates = [...fromNeighbors].filter((uom) => toNeighbors.has(uom));
    if (intermediates.length === 0) return null;

    const intermediate = intermediates[0];
    const rate1 = conversionMap[`${fromUom}->${intermediate}`];
    const rate2 = conversionMap[`${intermediate}->${toUom}`];

    return {
      intermediate,
      totalRate: rate1 * rate2,
      detail: {
        [`${fromUom}->${intermediate}`]: rate1,
        [`${intermediate}->${toUom}`]: rate2,
      },
    };
  }

  findSingleIntermediate(fromUom, toUom, lstUomConverse) {
    const unitMap = {};
    const conversionMap = {};

    // Xây dựng graph và map chuyển đổi
    lstUomConverse.forEach(({ fromUnit, toUnit, conversion }) => {
      if (!unitMap[fromUnit]) unitMap[fromUnit] = [];
      if (!unitMap[toUnit]) unitMap[toUnit] = [];

      unitMap[fromUnit].push(toUnit);
      unitMap[toUnit].push(fromUnit);

      conversionMap[`${fromUnit}->${toUnit}`] = conversion;
      conversionMap[`${toUnit}->${fromUnit}`] = 1 / conversion;
    });

    // BFS
    const queue = [[fromUom]];
    const visited = new Set();

    while (queue.length > 0) {
      const path = queue.shift();
      const last = path[path.length - 1];

      if (last === toUom) {
        // Tính tổng conversion
        let totalRate = 1;
        const detail = {};

        for (let i = 0; i < path.length - 1; i++) {
          const key = `${path[i]}->${path[i + 1]}`;
          const rate = conversionMap[key];
          totalRate *= rate;
          detail[key] = rate;
        }

        return {
          path,
          totalRate,
          detail,
        };
      }

      if (visited.has(last)) continue;
      visited.add(last);

      for (const neighbor of unitMap[last] || []) {
        if (!visited.has(neighbor)) {
          queue.push([...path, neighbor]);
        }
      }
    }

    return null; // Không tìm được đường chuyển đổi
  }

  async updateStatusPOComplete(id: string) {
    const generalData = await this.generalDataRepo.findOne({ where: { code: 'ORDER_STATUS' } });
    const statusComplete = await this.generalDataDetailRepo.findOne({
      where: { code: '3', generalId: generalData },
    });
    return await this.productionOrderRepo.update(
      { id: id },
      { orderStatus: statusComplete.id, orderStatusCode: statusComplete.code },
    );
  }

  @DefTransaction()
  async createCZCode(data: any) {
    const checkProduct = await this.itemRepo.findOne({
      where: { id: data.materialId },
      relations: ['uomConversions'],
    });
    if (!checkProduct) throw new BusinessException('Not found item');

    const checkShift = await this.shiftRepo.findOne({ where: { id: data.shiftId } });
    if (!checkShift) throw new BusinessException('Not found shift');

    const checkReceipt = await this.recipeRepo.findOne({ where: { id: data.recipeId } });
    if (!checkReceipt) throw new BusinessException('Not found receipt');

    const checkFactory = await this.organizationUnitRepo.findOne({ where: { id: data.factoryId } });
    if (!checkFactory) throw new BusinessException('Not found factory');

    const checkOrderStatus = await this.generalDataDetailRepo.findOne({
      where: { id: data.orderStatus },
    });

    const checkProcessArea = await this.organizationUnitRepo.findOne({
      where: { id: data.processAreaId },
    });
    if (!checkProcessArea) throw new BusinessException('Not found Process area');

    const checkLine = await this.organizationUnitRepo.findOne({
      where: { id: checkProcessArea.parentId },
    });

    const siteCode = await this.getSiteCode(checkProcessArea);

    let lstFileData: any = [];
    for (const file of data.lstFile.map((i) => ({ fileUrl: i.fileUrl, fileName: i.fileName }))) {
      const response = await axios.get(file.fileUrl, {
        responseType: 'stream',
      });

      const rows = await this.parseLines(response.data);
      lstFileData.push({
        link: file.fileUrl,
        data: rows,
        fileName: file.fileName,
      });
    }

    const seenStrings = new Set<string>();
    let lstData = [];
    for (const fileData of lstFileData) {
      for (const row of fileData.data) {
        lstData.push(row);
        if (seenStrings.has(row)) {
          throw new BusinessException(`Duplicate row found: ${row}`);
        }
        seenStrings.add(row);
      }
    }
    if (lstData.length === 0)
      throw new BusinessException('Lỗi không tìm thấy dữ liệu trong file csv');
    // await this.productionOrderUniqueCodeDetailRepo.delete({ uniqueCode: In(lstData) });
    const checkExist = await this.productionOrderUniqueCodeDetailRepo.findOne({
      where: { uniqueCode: In(lstData) },
    });

    if (checkExist)
      throw new BusinessException(
        `Dữ liệu dòng ${checkExist.uniqueCode} bị trùng! Vui Lòng kiểm tra lại file`,
      );

    const lstUom = await this.uomConventionRepo.find({ itemId: data.materialId });
    if (lstUom.length === 0) throw new BusinessException('Không tìm thấy đơn vị cần chuyển đổi');
    if (!lstUom.some((i) => i.fromUnit === 'CTN' || i.toUnit === 'CTN'))
      throw new BusinessException('Không tìm thấy đơn vị CONT cần tính cho PO này');
    const item = checkProduct;
    if (!item) throw new BusinessException('Không tìm thấy sản phẩm');
    const ratioFromPOToBaseUnit = this.findSingleIntermediate(data.uom, item.baseUnit, lstUom);
    const qtyConverse = ratioFromPOToBaseUnit.totalRate * data.customerOrderQty;
    let totalData = 0;
    for (const fileData of lstFileData) {
      totalData += fileData.data.length;
    }
    if (qtyConverse > totalData)
      throw new BusinessException(
        'Số lượng dữ liệu file csv không đủ so với quantity được khai báo. Vui lòng kiểm tra lại',
      );
    const ratioFromBaseUnitToCONT = this.findSingleIntermediate(item.baseUnit, 'CTN', lstUom);
    const qtyConverseCont = ratioFromBaseUnitToCONT.totalRate * qtyConverse;
    const poLength = Math.ceil(qtyConverseCont);
    const checkQty = poLength > 1 ? true : false;
    let lstPO = [];
    const totalRow = lstFileData.reduce((acc, file) => acc + file.data.length, 0);

    for (let i = 0; i < poLength; i++) {
      // Thêm mới order
      const lstProduct = [];
      const newOrder = new ProductionOrderEntity();
      newOrder.poParentId = data.poParentId || null;
      newOrder.relatedOrder = data.poParentId || null;
      newOrder.id = uuidv4();
      newOrder.processAreaId = data.processAreaId;
      newOrder.shiftId = data.shiftId;
      const orderNo = await this.genCodeOrder(
        data,
        newOrder.id,
        checkProduct.code,
        checkShift.code,
        data.planStartDate,
        siteCode,
      );
      newOrder.orderNo = orderNo;
      newOrder.orderName = data.orderName;
      newOrder.recipeId = data.recipeId;
      newOrder.recipeCode = checkReceipt.recipeNo;
      newOrder.recipeVersion = checkReceipt.recipeVer;
      newOrder.shiftId = data.shiftId;
      newOrder.recipeId = data.recipeId;
      newOrder.planStartDate = data.planStartDate;
      newOrder.planEndDate = data.planEndDate;
      newOrder.operatorUserId = data.operatorUserId;
      if (!data.lot) {
        newOrder.lotNumber = this.genLotNumber(
          checkLine?.code,
          checkShift.code,
          data.planStartDate,
          siteCode,
          checkFactory.code,
        );
      } else {
        newOrder.lotNumber = data.lot;
      }
      if (checkQty) {
        newOrder.lotNumber =
          newOrder.lotNumber.slice(0, 7) + (i + 1).toString() + newOrder.lotNumber.slice(7);
      }
      newOrder.factoryId = data.factoryId;
      newOrder.processAreaId = data.processAreaId;
      newOrder.siteId = data.siteId;
      newOrder.lineId = data.lineId;
      newOrder.orderStatus = data.orderStatus;
      newOrder.orderStatusCode = checkOrderStatus?.code;
      newOrder.trxUom = data.uom;
      newOrder.trxUomCode = data.uom;
      newOrder.type = NSMember.EProductOrderType.PRODUCT;
      newOrder.itemId = data.materialId;
      newOrder.quantity = +data.quantity || 0;
      newOrder.orderSerialized = data.orderSerialized;

      if (data.orderSerialized === 1) {
        newOrder.customerOrderId = null;
        newOrder.customerOrderNo = data.customerOrderNo;
        newOrder.customerProductCode = data.customerProductCode;
        newOrder.customerProductName = data.customerProductName;
        newOrder.customerShortName = data.customerShortName;
        newOrder.gtin = data.gtin;
        newOrder.customerOrderQty = +data.quantity;

        const unitSize = 1 / Number(ratioFromBaseUnitToCONT.totalRate);
        let mod = Number(unitSize);

        if (i === poLength - 1 && qtyConverse % unitSize !== 0) {
          mod = qtyConverse % unitSize;
        }

        const quantityConverse = mod;
        if (poLength > 1) {
          newOrder.quantity = Math.floor(quantityConverse / ratioFromPOToBaseUnit.totalRate);
          newOrder.customerOrderQty = newOrder.quantity;
        }
      }

      lstProduct.push(newOrder);
      lstPO.push(newOrder); // Lưu lại để xử lí sau
      await this.productionOrderRepo.insert(newOrder);

      // Lấy thông tin công thức sản phẩm từ rcp
      const lstRcpProcess = await this.recipeProcessRepo.find({
        where: { recipeId: newOrder.recipeId },
        select: ['id', 'processId'],
      });
      const mapRcpProcess = new Map(lstRcpProcess.map((rcpPro) => [rcpPro.id, rcpPro]));
      const lstReceiptItem = await this.recipeProcessItemRepo.find({
        where: { recipeProcessId: In(lstRcpProcess.map((rp) => rp.id)) },
      });
      const lstItemId = [...new Set(lstReceiptItem.map((r) => r.itemId))];
      const lstItem = await this.itemRepo.find({ where: { id: In(lstItemId) } });
      const mapItem = new Map(lstItem.map((item) => [item.id, item]));

      const lstUomId = [...new Set(lstReceiptItem.map((r) => r.uom))];
      let lstUom = [];
      if (lstUomId.every((uom) => isUUID(uom))) {
        lstUom = await this.generalDataDetailRepo.find({ where: { id: In(lstUomId) } });
      }
      const mapUom = new Map(lstUom.map((item) => [item.id, item]));

      // Sản phẩm cần sx
      const checkProductRcpItem = lstReceiptItem.find(
        (rcpItem) => rcpItem.itemId === newOrder.itemId,
      );
      // Tính toán tỉ lệ
      const { ratio, quantityOrderByRcp, conversionItemToRcp } = this.computeRatioQuantity(
        checkProduct.uomConversions,
        newOrder,
        checkProductRcpItem,
        checkReceipt,
      );

      // Thêm mới sản phẩm trong order

      let orderProductId = null;
      const lstOrderItemInsert = [];
      if (!checkProductRcpItem) {
        const newOrderItem = new ProductionOrderMaterialEntity();
        newOrderItem.id = uuidv4();
        newOrderItem.poParentId = data.poParentId || null;
        newOrderItem.orderId = newOrder.id;
        newOrderItem.orderNo = newOrder.orderNo;
        newOrderItem.recipeId = newOrder.recipeId;
        newOrderItem.recipeVersion = newOrder.recipeVersion;
        newOrderItem.processId = null;
        newOrderItem.materialId = data.materialId;
        newOrderItem.materialCode = checkProduct.code;
        newOrderItem.materialName = checkProduct.name;
        newOrderItem.trxUom = checkReceipt.batchUom;
        newOrderItem.trxUomCode = checkReceipt.batchUom;
        newOrderItem.lineType = 1;
        newOrderItem.lineTypeCode = NSRecipe.RecipeProcessItemTypeCode.Product;
        newOrderItem.planTrxQty = quantityOrderByRcp;
        newOrderItem.actualTrxQty = 0;
        lstOrderItemInsert.push(newOrderItem);
        orderProductId = newOrderItem.id;
      }

      for (let receiptItem of lstReceiptItem) {
        const curRcpProcess = mapRcpProcess.get(receiptItem.recipeProcessId);
        const curItem = mapItem.get(receiptItem.itemId);
        const newOrderItem = new ProductionOrderMaterialEntity();
        newOrderItem.id = uuidv4();
        newOrderItem.poParentId = data.poParentId || null;
        newOrderItem.orderId = newOrder.id;
        newOrderItem.orderNo = newOrder.orderNo;
        newOrderItem.recipeId = newOrder.recipeId;
        newOrderItem.recipeVersion = newOrder.recipeVersion;
        newOrderItem.processId = curRcpProcess.processId;
        newOrderItem.materialId = receiptItem.itemId;
        newOrderItem.materialCode = curItem?.code;
        newOrderItem.materialName = curItem?.name;
        newOrderItem.trxUom = receiptItem.uom;
        newOrderItem.trxUomCode = mapUom.get(receiptItem.uom)?.code || receiptItem.uom;
        newOrderItem.lineType = dictLineType[receiptItem.typeCode];
        newOrderItem.lineTypeCode = receiptItem.typeCode;
        newOrderItem.planTrxQty = ratio * (+receiptItem.quantity || 0);
        newOrderItem.actualTrxQty = 0;
        lstOrderItemInsert.push(newOrderItem);
        if (receiptItem.itemId === newOrder.itemId) orderProductId = newOrderItem.id;
      }
      await this.productionOrderMaterialRepo.insert(lstOrderItemInsert);

      // Thêm mới nguồn lực cho order
      const lstReceiptResource = await this.recipeResourceRepo.find({
        where: { recipeProcessId: In(lstRcpProcess.map((rp) => rp.id)) },
      });

      const lstOrderResourceInsert = [];
      for (let resource of lstReceiptResource) {
        const newResource = new ProductionOrderResourceEntity();
        newResource.orderId = newOrder.id;
        newResource.orderNo = newOrder.orderNo;
        newResource.processId = mapRcpProcess.get(resource.recipeProcessId)?.processId;
        newResource.recipeId = newOrder.recipeId;
        newResource.recipeVersion = newOrder.recipeVersion;
        newResource.receiptResourceId = resource.id;
        newResource.resourceCode = resource.resource;
        newResource.uom = resource.resourceUsageUom;
        newResource.uomCode = resource.resourceUsageUom;
        newResource.planProdQty = quantityOrderByRcp;
        newResource.actualProdQty = 0;
        newResource.planResourceUsage = ratio * (+resource.resourceUsage || 0);
        newResource.actualResourceUsage = 0;
        newResource.resourceCode = resource.resource;
        lstOrderResourceInsert.push(newResource);
      }
      await this.productionOrderResourceRepo.insert(lstOrderResourceInsert);

      // Tạo mẻ sản xuất
      const insertSize = 500000; // insert 500000 records 1 lần
      let lstOrderBatchInsert = [];
      const batchSizeByPo = conversionItemToRcp
        ? (+checkReceipt.batchSize || 0) / conversionItemToRcp
        : 0;
      if (newOrder.quantity > 0) {
        let planTrxQty = newOrder.quantity;
        let idx = 1;
        do {
          const newProductBatch = new ProductionBatchEntity();
          newProductBatch.orderId = newOrder.id;
          newProductBatch.batchNumber = idx;
          // newProductBatch.orderProductId = orderProductId;
          newProductBatch.productId = newOrder.itemId;
          newProductBatch.planProdQty = Math.min(batchSizeByPo, planTrxQty);
          newProductBatch.actualProdQty = 0;
          newProductBatch.uom = newOrder.trxUom;
          // newProductBatch.status = data.orderStatus;

          planTrxQty = planTrxQty - batchSizeByPo;
          idx++;
          lstOrderBatchInsert.push(newProductBatch);
          if (lstOrderBatchInsert.length === insertSize) {
            await this.productionBatchRepo.insert(lstOrderBatchInsert);
            lstOrderBatchInsert = [];
          }
        } while (planTrxQty > 0);
      }
      await this.productionBatchRepo.insert(lstOrderBatchInsert);
      if (data.isSendScada) {
        await this.sendToScada({ lstId: [newOrder.id] });
      }
    }

    let totalAllocatedQty = 0;
    lstPO = lstPO.map((i: any, index, arr) => {
      const unitSize = 1 / Number(ratioFromBaseUnitToCONT.totalRate);
      let mod;
      mod = Number(unitSize);
      if (arr.length === 1) mod = qtyConverse;

      // Nếu là phần tử cuối và dư !== 0, thì lấy unitSize;
      if (index === arr.length - 1 && qtyConverse % unitSize !== 0) {
        mod = qtyConverse % unitSize;
      }

      let allocated = Math.floor((mod * totalRow) / qtyConverse);
      if (index === arr.length - 1) {
        allocated = totalRow - totalAllocatedQty;
      }
      totalAllocatedQty += allocated;
      return {
        ...i,
        qtyConverse: allocated,
      };
    });

    const splittedData: any[] = [];
    let currentIndex = 0;
    lstPO.forEach((po) => {
      const count = Math.round(po.qtyConverse); // làm tròn nếu cần
      const chunk = lstData.slice(currentIndex, currentIndex + count);
      splittedData.push(chunk);
      currentIndex += count;
    });
    lstFileData = [];
    const results = await this.splitAndUploadCsv(splittedData, lstPO);
    // Gộp lại thành lstFileData cấu trúc như trên sau đó thực hiện như cũ
    for (const item of results) {
      lstFileData.push({
        link: item.fileUrl,
        fileName: item.fileName,
        data: splittedData[results.indexOf(item)],
      });
    }
    let currentPoIndex = 0;
    let remainingPoQty = lstPO[currentPoIndex].qtyConverse;
    const lstUrl = [];
    const dictUrl: any = {};
    for (const file of lstFileData) {
      let remainingInFile = file.data.length;

      while (remainingInFile > 0 && currentPoIndex < lstPO.length) {
        const used = Math.min(remainingInFile, remainingPoQty);

        lstUrl.push({
          id: uuidv4(),
          orderId: lstPO[currentPoIndex].id,
          fileName: file.fileName,
          url: file.link,
          originalFileName: data.lstFile.map((i) => i.originalName).join('-'),
        });

        remainingInFile -= used;
        remainingPoQty -= used;

        if (remainingPoQty === 0) {
          currentPoIndex++;
          if (currentPoIndex < lstPO.length) {
            remainingPoQty = lstPO[currentPoIndex].qtyConverse;
          }
        }
      }
    }
    lstUrl.forEach((i) => (dictUrl[i.url + i.orderId] = i));
    await this.urlRepo.insert(lstUrl);
    let lstPoUniqueCodeDetail = [];
    let currentFileIndex = 0;
    let remainingFileQty = lstFileData[currentFileIndex].data.length;
    for (const po of lstPO) {
      let remainingPO = po.qtyConverse;

      while (remainingPO > 0 && currentFileIndex < lstFileData.length) {
        const used = Math.min(remainingPO, remainingFileQty);
        const lstData = lstFileData[currentFileIndex].data;
        const url = dictUrl[lstFileData[currentFileIndex]?.link + po.id];
        for (const row of lstData) {
          if (url) {
            lstPoUniqueCodeDetail.push({
              id: uuidv4(),
              orderId: po.id,
              urlId: url.id,
              fileName: url.fileName,
              uniqueCode: row,
              createdDate: dayjs(),
              updatedDate: dayjs(),
            });
          }
        }
        remainingPO -= used;
        remainingFileQty -= used;

        if (remainingFileQty === 0) {
          currentFileIndex++;
          if (currentFileIndex < lstFileData.length) {
            remainingFileQty = lstFileData[currentFileIndex].data.length;
          }
        }
      }
    }
    lstFileData = null;
    let lstInsertPO: any = [];
    let chunkSize = 10000;
    for (const item of lstPoUniqueCodeDetail) {
      lstInsertPO.push(item);
      if (lstInsertPO.length === chunkSize) {
        await this.productionOrderUniqueCodeDetailRepo.insert(lstInsertPO);
        lstInsertPO = [];
      }
    }
    await this.productionOrderUniqueCodeDetailRepo.insert(lstInsertPO);
    return { message: 'Create Production Order Successfully' };
  }

  async parseLines(stream: Readable): Promise<string[]> {
    const rl = readline.createInterface({ input: stream });
    const lines: string[] = [];

    for await (const line of rl) {
      if (line.trim()) {
        lines.push(line.trim());
      }
    }

    return lines;
  }

  async splitAndUploadCsv(splittedData: any[], lstPo: any[]): Promise<any[]> {
    const results: any[] = [];

    for (let i = 0; i < splittedData.length; i++) {
      const chunk = splittedData[i];
      const csvWithBom = '\uFEFF' + chunk.join('\r\n');

      const buffer = Buffer.from(csvWithBom, 'utf8');

      const fakeFile: Express.Multer.File = {
        fieldname: 'file',
        originalname: `${lstPo[i]?.orderNo}.csv`,
        encoding: '7bit',
        mimetype: 'text/csv',
        buffer,
        size: buffer.length,
        stream: undefined,
        destination: '',
        filename: `${lstPo[i]?.orderNo}`,
        path: '',
      };

      const { fileUrl, fileName } = await this.s3Service.uploadSingle(
        fakeFile,
        `${lstPo[i]?.orderNo}`,
      );
      results.push({ fileUrl: fileUrl, fileName: fileName });
    }

    return results;
  }

  async uploadCsv(data: any[], po: any): Promise<any> {
    const csvWithBom = '\uFEFF' + data.join('\r\n');

    const buffer = Buffer.from(csvWithBom, 'utf8');

    const fakeFile: Express.Multer.File = {
      fieldname: 'file',
      originalname: `${po?.orderNo}.csv`,
      encoding: '7bit',
      mimetype: 'text/csv',
      buffer,
      size: buffer.length,
      stream: undefined,
      destination: '',
      filename: `${po?.orderNo}`,
      path: '',
    };

    const { fileUrl, fileName } = await this.s3Service.uploadSingle(fakeFile, `${po?.orderNo}`);
    return { fileUrl, fileName };
  }

  convertToCsvBuffer(data: Record<string, any>[]): Buffer {
    const worksheet = XLSX.utils.json_to_sheet(data);
    const csv = XLSX.utils.sheet_to_csv(worksheet); // xuất ra chuỗi CSV
    return Buffer.from(csv, 'utf8');
  }

  @DefTransaction()
  async updateCZCode(data: UpdateProductionOrderDto) {
    // check validate data
    const foundOrder = await this.productionOrderRepo.findOne({
      where: { id: data.id },
    });
    if (!foundOrder) throw new BusinessException('Not found order');
    if (foundOrder.orderStatusCode !== '1')
      throw new BusinessException('Lệnh sản xuất đã được đẩy xuống Scada, không được phép sửa');

    const checkProduct = await this.itemRepo.findOne({
      where: { id: data.materialId },
      relations: ['uomConversions'],
    });
    if (!checkProduct) throw new BusinessException('Not found item');

    const checkShift = await this.shiftRepo.findOne({ where: { id: data.shiftId } });
    if (!checkShift) throw new BusinessException('Not found shift');

    const checkReceipt = await this.recipeRepo.findOne({ where: { id: data.recipeId } });
    if (!checkReceipt) throw new BusinessException('Not found receipt');

    const checkFactory = await this.organizationUnitRepo.findOne({ where: { id: data.factoryId } });
    if (!checkFactory) throw new BusinessException('Not found factory');

    const checkOrderStatus = await this.generalDataDetailRepo.findOne({
      where: { id: data.orderStatus },
    });

    const checkProcessArea = await this.organizationUnitRepo.findOne({
      where: { id: data.processAreaId },
    });
    if (!checkProcessArea) throw new BusinessException('Not found Process area');

    const checkLine = await this.organizationUnitRepo.findOne({
      where: { id: checkProcessArea.parentId },
    });

    const siteCode = await this.getSiteCode(checkProcessArea);

    let lstFileData: any = [];
    let lstData = [];
    let checkIsUpdate = false;
    let originalFileName = '';
    let fileUrl = '';
    if (data.orderSerialized === 1) {
      // Xử lý file data - chỉ xử lý 1 lần
      for (const file of data.lstFile.map((i) => ({
        fileUrl: i.fileUrl,
        fileName: i.fileName,
        originalName: i.originalName,
      }))) {
        try {
          const response = await axios.get(file.fileUrl, {
            responseType: 'stream',
          });

          const rows = await this.parseLines(response.data);
          lstFileData.push({
            link: file.fileUrl,
            data: rows,
            fileName: file.fileName,
            originalName: file.originalName,
          });
        } catch (error) {
          throw new BusinessException(
            `Không thể đọc file: ${file.fileName}. Lỗi: ${error.message}`,
          );
        }
      }

      // Kiểm tra file đã tồn tại
      const lstFileUrl = lstFileData.map((i: any) => i.link);
      if (lstFileUrl && lstFileUrl.length > 0) {
        const check = await this.urlRepo.findOne({ where: { url: In(lstFileUrl) } });
        if (check) {
          checkIsUpdate = true;
          originalFileName = check.originalFileName;
          fileUrl = check.url;
        }
      }

      // Kiểm tra dữ liệu trùng lặp
      const seenStrings = new Set<string>();
      for (const fileData of lstFileData) {
        for (const row of fileData.data) {
          lstData.push(row);
          if (seenStrings.has(row)) {
            throw new BusinessException(`Duplicate row found: ${row}`);
          }
          seenStrings.add(row);
        }
      }
      if (lstData.length === 0)
        throw new BusinessException('Lỗi không tìm thấy dữ liệu trong file csv');

      await this.productionOrderUniqueCodeDetailRepo.delete({ orderId: data.id });
      const checkExist = await this.productionOrderUniqueCodeDetailRepo.findOne({
        where: { uniqueCode: In(lstData) },
      });

      if (checkExist)
        throw new BusinessException(
          `Dữ liệu dòng ${checkExist.uniqueCode} bị trùng! Vui Lòng kiểm tra lại file`,
        );
    }

    // check po có thay đổi code không
    let isChange = await this.isChangeOrderCode(foundOrder, {
      productCode: checkProduct.code,
      shiftCode: checkShift.code,
      planStartDate: data.planStartDate,
      siteCode,
    });

    const lstUomData = await this.uomConventionRepo.find({ itemId: data.materialId });
    if (lstUomData.length === 0)
      throw new BusinessException('Không tìm thấy đơn vị cần chuyển đổi');
    if (!lstUomData.some((i) => i.fromUnit === 'CTN' || i.toUnit === 'CTN'))
      throw new BusinessException('Không tìm thấy đơn vị CONT cần tính cho PO này');

    const item = checkProduct;
    if (!item) throw new BusinessException('Không tìm thấy sản phẩm');

    const ratioFromPOToBaseUnit = this.findSingleIntermediate(data.uom, item.baseUnit, lstUomData);
    if (!ratioFromPOToBaseUnit) {
      throw new BusinessException('Không thể tính toán tỉ lệ chuyển đổi từ UOM sang Base Unit');
    }

    const qtyConverse = ratioFromPOToBaseUnit.totalRate * data.customerOrderQty;
    if (qtyConverse > lstData.length) {
      throw new BusinessException(
        'Số lượng dữ liệu file csv không đủ so với quantity được khai báo. Vui lòng kiểm tra lại',
      );
    }

    // Thêm mới order
    foundOrder.processAreaId = data.processAreaId;
    foundOrder.shiftId = data.shiftId;
    if (isChange) {
      foundOrder.orderNo = await this.genCodeOrder(
        data,
        foundOrder.id,
        checkProduct.code,
        checkShift.code,
        data.planStartDate,
        siteCode,
      );
    }
    foundOrder.orderName = data.orderName;
    foundOrder.recipeId = data.recipeId;
    foundOrder.recipeCode = checkReceipt.recipeNo;
    foundOrder.recipeVersion = checkReceipt.recipeVer;
    foundOrder.shiftId = data.shiftId;
    foundOrder.recipeId = data.recipeId;
    foundOrder.planStartDate = data.planStartDate;
    foundOrder.planEndDate = data.planEndDate;
    foundOrder.lotNumber = data.lot;
    foundOrder.operatorUserId = data.operatorUserId;
    if (!data.lot)
      foundOrder.lotNumber = this.genLotNumber(
        checkLine?.code,
        checkShift.code,
        data.planStartDate,
        siteCode,
        checkFactory.code,
      );
    foundOrder.factoryId = data.factoryId;
    foundOrder.processAreaId = data.processAreaId;
    foundOrder.siteId = data.siteId;
    foundOrder.lineId = data.lineId;
    foundOrder.orderStatus = data.orderStatus;
    foundOrder.orderStatusCode = checkOrderStatus?.code;
    foundOrder.itemId = data.materialId;
    foundOrder.quantity = +data.quantity || 0;
    foundOrder.trxUom = data.uom;
    foundOrder.trxUomCode = data.uom;
    foundOrder.type = NSMember.EProductOrderType.PRODUCT;
    if (data.orderSerialized === 1) {
      foundOrder.customerOrderId = null;
      foundOrder.customerOrderNo = data.customerOrderNo;
      foundOrder.customerProductCode = data.customerProductCode;
      foundOrder.customerProductName = data.customerProductName;
      foundOrder.customerShortName = data.customerShortName;
      foundOrder.gtin = data.gtin;
      foundOrder.customerOrderQty = data.customerOrderQty;
    }
    await this.productionOrderRepo.update(foundOrder.id, foundOrder);

    await this.productionOrderMaterialRepo.delete({ orderId: foundOrder.id });

    // Lấy thông tin công thức sản phẩm từ rcp
    const lstRcpProcess = await this.recipeProcessRepo.find({
      where: { recipeId: foundOrder.recipeId },
      select: ['id', 'processId'],
    });
    const mapRcpProcess = new Map(lstRcpProcess.map((rcpPro) => [rcpPro.id, rcpPro]));
    const lstReceiptItem = await this.recipeProcessItemRepo.find({
      where: {
        recipeProcessId: In(lstRcpProcess.map((rp) => rp.id)),
      },
    });
    const lstItemId = [...new Set(lstReceiptItem.map((r) => r.itemId))];
    const lstItem = await this.itemRepo.find({ where: { id: In(lstItemId) } });
    const mapItem = new Map(lstItem.map((item) => [item.id, item]));

    const lstUomId = [...new Set(lstReceiptItem.map((r) => r.uom))];
    let lstUom = [];
    if (lstUomId.every((uom) => isUUID(uom))) {
      lstUom = await this.generalDataDetailRepo.find({ where: { id: In(lstUomId) } });
    }
    const mapUom = new Map(lstUom.map((item) => [item.id, item]));

    // Sản phẩm cần sx
    const checkProductRcpItem = lstReceiptItem.find(
      (rcpItem) => rcpItem.itemId === foundOrder.itemId,
    );
    // Tính toán tỉ lệ
    const { ratio, quantityOrderByRcp, conversionItemToRcp } = this.computeRatioQuantity(
      checkProduct.uomConversions,
      foundOrder,
      checkProductRcpItem,
      checkReceipt,
    );

    // Thêm mới sản phẩm trong order
    const lstOrderItemInsert = [];
    let orderProductId = null;
    if (!checkProductRcpItem) {
      const newOrderItem = new ProductionOrderMaterialEntity();
      newOrderItem.id = uuidv4();
      newOrderItem.poParentId = data.poParentId || null;
      newOrderItem.orderId = foundOrder.id;
      newOrderItem.orderNo = foundOrder.orderNo;
      newOrderItem.recipeId = foundOrder.recipeId;
      newOrderItem.recipeVersion = foundOrder.recipeVersion;
      newOrderItem.processId = null;
      newOrderItem.materialId = data.materialId;
      newOrderItem.materialCode = checkProduct.code;
      newOrderItem.materialName = checkProduct.name;
      newOrderItem.trxUom = checkReceipt.batchUom;
      newOrderItem.trxUomCode = checkReceipt.batchUom;
      newOrderItem.lineType = 1;
      newOrderItem.lineTypeCode = NSRecipe.RecipeProcessItemTypeCode.Product;
      newOrderItem.planTrxQty = quantityOrderByRcp;
      newOrderItem.actualTrxQty = 0;
      lstOrderItemInsert.push(newOrderItem);
      orderProductId = newOrderItem.id;
    }

    for (let receiptItem of lstReceiptItem) {
      const curItem = mapItem.get(receiptItem.itemId);
      const curRcpProcess = mapRcpProcess.get(receiptItem.recipeProcessId);
      const newOrderItem = new ProductionOrderMaterialEntity();
      newOrderItem.id = uuidv4();
      newOrderItem.orderId = foundOrder.id;
      newOrderItem.orderNo = foundOrder.orderNo;
      newOrderItem.recipeId = foundOrder.recipeId;
      newOrderItem.recipeVersion = foundOrder.recipeVersion;
      newOrderItem.processId = curRcpProcess.processId;
      newOrderItem.materialId = receiptItem.itemId;
      newOrderItem.materialCode = curItem?.code;
      newOrderItem.materialName = curItem?.name;
      newOrderItem.trxUom = receiptItem.uom;
      newOrderItem.trxUomCode = mapUom.get(receiptItem.uom)?.code || receiptItem.uom;
      newOrderItem.lineType = dictLineType[receiptItem.typeCode];
      newOrderItem.lineTypeCode = receiptItem.typeCode;
      newOrderItem.planTrxQty = ratio * (+receiptItem.quantity || 0);
      newOrderItem.actualTrxQty = 0;
      lstOrderItemInsert.push(newOrderItem);
      if (receiptItem.itemId === foundOrder.itemId) orderProductId = newOrderItem.id;
    }
    await this.productionOrderMaterialRepo.insert(lstOrderItemInsert);

    // Thêm mới nguồn lực cho order
    await this.productionOrderResourceRepo.delete({ orderId: foundOrder.id });
    const lstReceiptResource = await this.recipeResourceRepo.find({
      where: { recipeProcessId: In(lstRcpProcess.map((rp) => rp.id)) },
    });

    const lstOrderResourceInsert = [];
    for (let resource of lstReceiptResource) {
      const newResource = new ProductionOrderResourceEntity();
      newResource.orderId = foundOrder.id;
      newResource.orderNo = foundOrder.orderNo;
      newResource.processId = mapRcpProcess.get(resource.recipeProcessId)?.processId;
      newResource.recipeId = foundOrder.recipeId;
      newResource.recipeVersion = foundOrder.recipeVersion;
      newResource.receiptResourceId = resource.id;
      newResource.resourceCode = resource.resource;
      newResource.uom = resource.resourceUsageUom;
      newResource.uomCode = resource.resourceUsageUom;
      newResource.planProdQty = quantityOrderByRcp;
      newResource.actualProdQty = 0;
      newResource.planResourceUsage = ratio * (+resource.resourceUsage || 0);
      newResource.actualResourceUsage = 0;
      newResource.resourceCode = resource.resource;
      lstOrderResourceInsert.push(newResource);
    }
    await this.productionOrderResourceRepo.insert(lstOrderResourceInsert);

    // Tạo mẻ sản xuất
    await this.productionBatchRepo.delete({ orderId: foundOrder.id });
    const insertSize = 500000; // insert 500000 records 1 lần
    let lstOrderBatchInsert = [];
    const batchSizeByPo = conversionItemToRcp
      ? (+checkReceipt.batchSize || 0) / conversionItemToRcp
      : 0;
    if (foundOrder.quantity > 0) {
      let planTrxQty = foundOrder.quantity;
      let idx = 1;
      do {
        const newProductBatch = new ProductionBatchEntity();
        newProductBatch.orderId = foundOrder.id;
        newProductBatch.batchNumber = idx;
        // newProductBatch.orderProductId = orderProductId;
        newProductBatch.productId = foundOrder.itemId;
        newProductBatch.planProdQty = Math.min(batchSizeByPo, planTrxQty);
        newProductBatch.actualProdQty = 0;
        newProductBatch.uom = foundOrder.trxUom;
        // newProductBatch.status = data.orderStatus;

        planTrxQty = planTrxQty - batchSizeByPo;
        idx++;
        lstOrderBatchInsert.push(newProductBatch);
        if (lstOrderBatchInsert.length === insertSize) {
          await this.productionBatchRepo.insert(lstOrderBatchInsert);
          lstOrderBatchInsert = [];
        }
      } while (planTrxQty > 0);
    }
    await this.productionBatchRepo.insert(lstOrderBatchInsert);
    if (data.isSendScada && !foundOrder.isScadaSynced) {
      await this.sendToScada({ lstId: [foundOrder.id] });
    }

    // Xử lý dữ liệu unique code
    let urlEntity;
    if (lstFileData.length > 0) {
      if (checkIsUpdate) {
        if (lstFileData.length > 1) {
          const updateOriginalFileName = [
            ...lstFileData.map((i: any) =>
              fileUrl !== i.link ? i.originalName : originalFileName,
            ),
          ].join('-');

          const results = await this.uploadCsv(lstData, foundOrder);
          urlEntity = {
            orderId: foundOrder.id,
            url: results.fileUrl,
            fileName: results.fileName,
            originalFileName: updateOriginalFileName,
          };
        } else {
          const results = await this.uploadCsv(lstData, foundOrder);
          urlEntity = {
            orderId: foundOrder.id,
            url: results.fileUrl,
            fileName: results.fileName,
            originalFileName: originalFileName,
          };
        }
      } else {
        const results = await this.uploadCsv(lstData, foundOrder);
        urlEntity = {
          orderId: foundOrder.id,
          url: results.fileUrl,
          fileName: results.fileName,
          originalFileName: lstFileData.map((i: any) => i.fileName).join('-'),
        };
      }

      await this.urlRepo.delete({ orderId: foundOrder.id });
      await this.urlRepo.save(urlEntity);

      const lstPoUniqueCodeDetail = [];
      for (const row of lstData) {
        lstPoUniqueCodeDetail.push({
          id: uuidv4(),
          orderId: foundOrder.id,
          urlId: urlEntity.id,
          fileName: urlEntity.fileName,
          uniqueCode: row,
          createdDate: dayjs(),
          updatedDate: dayjs(),
        });
      }
      await this.productionOrderUniqueCodeDetailRepo.insert(lstPoUniqueCodeDetail);
    }

    return { message: 'Update Production Order Successfully' };
  }
}
