import { Column, Entity, Index, ManyToOne, OneToMany } from 'typeorm';
import { PrimaryBaseEntity } from '../primary-base.entity';
import { ApiProperty } from '@nestjs/swagger';
import { ProcessEntity } from './process.entity';
import { RecipeEntity } from './recipe.entity';
import { RecipeResourceEntity } from './recipe-resource.entity';
import { RecipeProcessItemEntity } from './recipe-process-item.entity';

/** Bảng liện kết giữa công thức và công đoạn */
@Entity('recipe_process')
@Index(['recipeId', 'processId'], { unique: true })
export class RecipeProcessEntity extends PrimaryBaseEntity {
  /** ID công thức */
  @ApiProperty({ description: 'ID công thức' })
  @Column({ nullable: false, type: 'uuid' })
  recipeId: string;

  /** ID công đoạn */
  @ApiProperty({ description: 'ID công đoạn' })
  @Column({ nullable: false, type: 'uuid' })
  processId: string;

  @ManyToOne(() => RecipeEntity, recipe => recipe.recipeProcesses, { nullable: false })
  recipe: RecipeEntity;

  /** Công đoạn */
  @ManyToOne(() => ProcessEntity, p => p.recipeProcesses, { nullable: false })
  process: ProcessEntity;

  /** Mã thực thi */
  @ApiProperty({ description: 'Mã thực thi' })
  @Column({ length: 50, nullable: false })
  operationCode: string;

  /** Tài nguyên */
  @OneToMany(() => RecipeResourceEntity, rr => rr.recipeProcess, { cascade: true })
  recipeResources: Array<RecipeResourceEntity>;

  /** Nguyên liệu */
  @OneToMany(() => RecipeProcessItemEntity, rpi => rpi.recipeProcess, { cascade: true })
  recipeProcessItems: Array<RecipeProcessItemEntity>;
}
