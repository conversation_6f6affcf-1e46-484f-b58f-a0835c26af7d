import { Injectable } from '@nestjs/common';
import { delay } from 'rxjs';
import { Between, ILike } from 'typeorm';
import { BindRepo } from '~/@core/decorator';
import { NSSQSS } from '~/common/enums';
import { LogJobRepo } from '~/repositories/primary/log-job.repo';

@Injectable()
export class LogJobService {
  constructor() {}
  @BindRepo(LogJobRepo)
  private readonly logJobRepo: LogJobRepo;

  async checkJobRunning(type: string) {
    const job = await this.logJobRepo.findOne({
      where: { type: type, status: NSSQSS.EStatus.Running },
    });
    return job;
  }

  async createLogJob(data: {
    message: string;
    status: string;
    type: string;
    messageSNSId: string;
    metadata?: any;
  }) {
    return await this.logJobRepo.save(data);
  }

  async updateLogJobSuccess(messageSNSId: string) {
    try {
      return await this.logJobRepo.update(
        { messageSNSId: messageSNSId },
        { status: NSSQSS.EStatus.Success },
      );
    } catch (error) {
      console.log('Error updating log job success', error);
    }
  }

  async updateLogJobError(messageSNSId: string, error: any) {
    try {
      setTimeout(async () => {
        return await this.logJobRepo.update(
          { messageSNSId: messageSNSId },
          {
            status: NSSQSS.EStatus.Error,
            description: error?.message || error || 'Unknown error',
            stacktrace: error?.stack || JSON.stringify(error, null, 2),
          },
        );
      }, 1000);
    } catch (error) {
      console.log('Error updating log job error', error);
    }
  }

  async pagination(params: any) {
    const whereCon: any = {};
    if (params.type) whereCon.type = ILike(`%${params.type}%`);
    if (params.status) whereCon.status = ILike(`%${params.status}%`);
    if (params.startTime && params.endTime)
      whereCon.createdDate = Between(params.startTime, params.endTime);
    const res = await this.logJobRepo.findPagination(
      { where: whereCon, order: { createdDate: 'DESC' } },
      { pageSize: params.pageSize, pageIndex: params.pageIndex },
    );
    res.data.forEach((i: any) => (i.metadata = JSON.stringify(i.metadata)));
    return res;
  }
}
