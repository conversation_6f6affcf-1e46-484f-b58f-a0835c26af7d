import { ApiProperty } from '@nestjs/swagger';
import { IsBoolean, IsISO8601, IsNotEmpty, IsString } from 'class-validator';
import { PageRequest } from '~/@systems/utils';

export class AssignShiftDto {
  @ApiProperty({ description: 'ID của tổ chức' })
  @IsNotEmpty()
  organizationId: string;

  @ApiProperty({ description: 'ID của ca làm' })
  @IsNotEmpty()
  shiftId: string;

  @ApiProperty({ description: '<PERSON><PERSON>y bắt đầu hiệu lực', example: '2025-03-07T17:05:53.286Z' })
  @IsNotEmpty()
  @IsISO8601()
  validFrom: string;

  @ApiProperty({ description: '<PERSON><PERSON><PERSON> kết thúc hiệu lực', example: '2025-03-30T17:06:01.286Z' })
  @IsNotEmpty()
  @IsISO8601()
  validTo: string;

  @ApiProperty({
    description:
      'Trạng thái ca làm của tổ chức (true = áp dụng ca làm cho tổ chức, false = không áp dụng ca làm cho tổ chức)',
  })
  @IsNotEmpty()
  @IsBoolean()
  status: boolean;
}
export class AssignShiftEditDto extends AssignShiftDto {
  @ApiProperty({ description: 'ID ca làm của tổ chức' })
  @IsNotEmpty()
  @IsString()
  id: string;
}

export class AssignShiftPaginationDto extends PageRequest {
  @ApiProperty({
    type: Object,
  })
  where: any;
}
