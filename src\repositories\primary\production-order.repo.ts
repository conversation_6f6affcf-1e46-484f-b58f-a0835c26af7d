import { EntityRepository } from 'typeorm';
import { PrimaryRepo } from '../primary.repo';
import {
  ProductionBatchEntity,
  ProductionOrderEntity,
  ProductionOrderMaterialEntity,
  ProductionOrderResourceEntity,
} from '~/entities/primary';

@EntityRepository(ProductionBatchEntity)
export class ProductionBatchRepo extends PrimaryRepo<ProductionBatchEntity> {}

@EntityRepository(ProductionOrderEntity)
export class ProductionOrderRepo extends PrimaryRepo<ProductionOrderEntity> {}

@EntityRepository(ProductionOrderMaterialEntity)
export class ProductionOrderMaterialRepo extends PrimaryRepo<ProductionOrderMaterialEntity> {}

@EntityRepository(ProductionOrderResourceEntity)
export class ProductionOrderResourceRepo extends PrimaryRepo<ProductionOrderResourceEntity> {}
