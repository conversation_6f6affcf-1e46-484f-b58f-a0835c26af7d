import { ApiProperty } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsOptional, IsString, IsUUID } from 'class-validator';
import { PageRequest } from '~/@systems/utils';

export class InspectionPlanCreateDto {
  @ApiProperty({ description: 'ID Đơn vị tổ chức' })
  @IsOptional()
  @IsString()
  organizationUnitId: string;

  @ApiProperty({ description: 'ID tiến trình' })
  @IsOptional()
  @IsString()
  processId: string;

  @ApiProperty({ description: 'ID máy móc' })
  @IsOptional()
  @IsString()
  machineId: string;

  @ApiProperty({ description: 'ID thông số máy móc' })
  @IsOptional()
  @IsString()
  machineParameterId: string;

  @ApiProperty({ description: 'ID nguyên vật liệu' })
  @IsNotEmpty()
  @IsString()
  itemId: string;

  @ApiProperty({ description: 'Trạng thái hoạt động' })
  @IsOptional()
  isActive: boolean;

  @ApiProperty({ description: 'Kế hoạch kiểm tra theo' })
  @IsNotEmpty()
  @IsString()
  frequency: string;

  @ApiProperty({ description: 'Loại kế hoạch' })
  @IsNotEmpty()
  @IsString()
  dataType: string;

  @ApiProperty({ description: 'Số lượng sản phẩm đếm được để kích hoạt Inspection Plan tạo data' })
  @IsOptional()
  outputQty: number;

  @ApiProperty({ description: 'Lấy từ UOM trong item' })
  @IsOptional()
  uom: string;

  @ApiProperty({ description: 'Số thời gian đếm để kích hoạt Inspection Plan tạo data' })
  @IsOptional()
  timeValue: number;

  @ApiProperty({ description: 'Lấy từ master data code: DATETIME_UNIT' })
  @IsOptional()
  timeUnit: string;

  @ApiProperty({ description: 'Lấy từ master data code: QA_SAMPLE_TYPE' })
  @IsOptional()
  sampleType: string;

  @ApiProperty({ description: 'Lấy từ master data code: QA_DEPARTMENT' })
  @IsOptional()
  costCenter: string;

  @ApiProperty({ description: 'Lấy từ master data code: ORG' })
  @IsOptional()
  receivingSite: string;

  @ApiProperty({ description: 'Lấy từ master data code: QA_DEPARTMENT' })
  @IsOptional()
  receivingDepartment: string;

  @ApiProperty({ description: 'Lấy từ master data code: QA_CATEGORY' })
  @IsOptional()
  category: string;

  @ApiProperty({ description: 'Thông tin mã Spec' })
  @IsOptional()
  specCode: string;

  @ApiProperty({ description: 'version mã Spec' })
  @IsOptional()
  specVersion: string;

  @ApiProperty({ description: 'Họ tên người tạo data' })
  @IsOptional()
  qcFullname: string;

  @ApiProperty({ description: '' })
  @IsOptional()
  aqlCode: string;

  @ApiProperty({ description: 'Lấy từ master data code: QA_INSPECTION_LEVEL' })
  @IsOptional()
  inspectionLevel: string;

  @ApiProperty({ description: 'Lấy từ master data code: QA_SEVERITY' })
  @IsOptional()
  aqlSeverity: string;
}

export class InspectionPlanUpdateDto extends InspectionPlanCreateDto {
  @ApiProperty({ description: 'ID kế hoạch kiểm tra' })
  @IsNotEmpty()
  @IsString()
  id: string;
}

export class InspectionPageReq extends PageRequest {
  @ApiProperty({ description: 'Mã' })
  @IsOptional()
  code: string;

  @ApiProperty({ description: 'Đơn vị tổ chức' })
  @IsOptional()
  oraganizationCode: string;

  @ApiProperty({ description: 'nguyên vật liệu' })
  @IsOptional()
  itemCode: string;

  @ApiProperty({ description: 'nguyên vật liệu' })
  @IsOptional()
  itemName: string;

  @ApiProperty({ description: 'Loại kế hoạch' })
  @IsOptional()
  dataType: string;

  @ApiProperty({ description: 'Ngày cập nhật' })
  @IsOptional()
  updatedDate: Date[];

  @ApiProperty({ description: 'Trạng thái hoạt động' })
  @IsOptional()
  isActive: boolean;
}
