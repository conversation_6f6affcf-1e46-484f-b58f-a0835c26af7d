import { Column, Entity } from 'typeorm';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { PrimaryBaseEntity } from '~/entities/primary-base.entity';
@Entity('log_schedule')
export class LogScheduleEntity extends PrimaryBaseEntity {
  @ApiProperty({ description: 'Message', nullable: true })
  @Column({ nullable: true })
  message?: string;

  @ApiProperty({ description: 'Status', nullable: true })
  @Column({ nullable: true })
  status?: string;

  @ApiProperty({ description: 'Type' })
  @Column({})
  type: string;

  @ApiProperty({ description: 'Date Time' })
  @Column({ type: 'timestamptz', nullable: true })
  dateTime?: Date;
}
