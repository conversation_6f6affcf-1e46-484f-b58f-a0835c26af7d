export class ScadaDataExchangeReq {
  downtimeId: string;
  machineCode: string;
  startTime: string;
  endTime: string;
  reasonCode: string;
  reasonDesc: string;
}

export class AcceptMaterialConsumptionReq {
  ProductionOrderNumber: string;
  BatchCode: string;
  Consumptions: [
    {
      IngredientCode: string;
      Lot: string;
      Quantity: string;
      UnitOfMeasurment: string;
      Datetime: string;
      Operator_ID: string;
    },
    {
      IngredientCode: string;
      Lot: string;
      Quantity: string;
      UnitOfMeasurment: string;
      Datetime: string;
      Operator_ID: string;
    },
  ];
}

export class SyncRecipeToScadaItemDtoReq {
  ItemCode: string;
  ItemName: string;
  Item_Type: string;
  Group: string;
  Category: string;
  Brand: string;
  BaseUnit: string;
  InventoryUnit: string;
  MHUTypes: Array<{
    FromUnit: string;
    ToUnit: string;
    Conversion: string;
  }>;
  Item_Status: string;
}

export class SyncRecipeToScadaDtoReq {
  ProductCode: string;
  Plant: string;
  Shopfloor: string;
  ProductionLine: string;
  RecipeCode: string;
  RecipeName: string;
  RecipeStatus: string;
  Version: string;
  Process: Array<{
    ProcessCode: string;
    ProcessName: string;
    Duration: string;
    DurationUoM: string;
    Ingredients: Array<{
      IngredientCode: string;
      ING_DESCRIPTION: string;
      Quantity: number;
      UnitOfMeasurement: string;
    }>;
    Products: Array<{
      ProductCode: string;
      PlanQuantity: number;
      UnitOfMeasurement: string;
    }>;
    ByProducts: Array<{
      ByProductCode: string;
      PlanQuantity: number;
      UnitOfMeasurement: string;
    }>;
    Parameters: Array<{
      Code: string;
      DataType: string;
      StartTime: string;
      EndTime: string;
      UnitOfMeasurement: string;
      Setpoint_Value: string;
      Min: string;
      Max: string;
    }>;
  }>;
}

export class SyncProductionOrderToScadaDtoReq {
  productionLine: string;
  productCode: string;
  productionOrderNumber: string;
  recipeCode: string;
  recipeVersion: string;
  shift: string;
  plannedStart: string;
  plannedEnd: string;
  quantity: number;
  unitOfMeasurement: string;
  lotNumber: string;
  batches: Array<{
    batchNumber: string;
    quantity: number;
    unitOfMeasurement: string;
  }>;
}

export class SyncInventoryToScadaDtoReq {
  Base_qty: number;
  Base_uom: string;
  Exprired_Date: string;
  Manufacture: string;
  MaterialCode: string;
  MaterialLotNumber: string;
}
