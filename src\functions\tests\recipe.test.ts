describe('Recipe Lambda Integration Tests', () => {
  const API_BASE_URL = 'http://localhost:3000/dev';

  // Sample valid recipe data based on your curl example
  const validRecipeData = {
    RECIPE_NO: '99PH00092-MGP',
    RECIPE_DESC: 'Gia vị mì Kokomi Tôm (Xu<PERSON>t khẩu) ( <PERSON><PERSON><PERSON><PERSON> chỉnh công thức, line 3, bột mì xích lô)',
    RECIPE_VER: 1,
    RECIPE_STATUS: 'Approved for General Use',
    ORGANIZATION: 'MGP',
    PRODUCT: '99PH00092',
    FORMULA_NO: '99PH00092-MGP',
    FORMULA_VERSION: 1,
    ROUTING_NO: 'MGPWP3L0202',
    ROUTING_VERS: 1,
    BATCH_SIZE: 1272,
    BATCH_SIZE_UOM: 'Kgs',
    PROCESS: [
      {
        PROCESS: 'MGPWP3L0202',
        PROCESS_FORMULA_VERSION: 1,
        Ingredient: [
          {
            ING_LINE_NO: 1,
            ING_NO: 'ABNC001',
            ING_DESCRIPTION: '<PERSON><PERSON>ớ<PERSON> chế biến thực phẩm',
            ING_QUANTITY: 1200,
            ING_UOM_CODE: 'Kgs',
          },
        ],
        Product: [
          {
            Product_LINE_NO: 1,
            Product_NO: '99PH00092',
            Product_DESCRIPTION: 'Gia vị mì Kokomi Tôm (Xuất khẩu)',
            Product_QUANTITY: 1272,
            Product_UOM_CODE: 'Kgs',
          },
        ],
        By_Product: [
          {
            BY_PRODUCT_LINE_NO: 1,
            BY_PRODUCT_NO: '99PH00092',
            BY_PRODUCT_DESCRIPTION: 'Gia vị mì Kokomi Tôm (Xuất khẩu)',
            BY_PRODUCT_QUANTITY: 1272,
            BY_PRODUCT_UOM_CODE: 'Kgs',
          },
        ],
        Resource: [
          {
            RESOURCES: 'LB033',
            RESOURCE_DESC: 'Nhân công trực tiếp',
            RESOURCE_USAGE: 1,
            RESOURCE_USAGE_UOM: 'Kgs',
          },
        ],
      },
    ],
  };

  // Helper function to create a recipe via API
  async function createRecipe(data) {
    const response = await fetch(`${API_BASE_URL}/integration/erp/recipe`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        Accept: '*/*',
      },
      body: JSON.stringify(data),
    });

    return {
      status: response.status,
      ok: response.ok,
      data: await response.json(),
    };
  }

  describe('Create Recipe API', () => {
    // Test successful recipe creation
    it('should create a recipe successfully', async () => {
      const response = await createRecipe(validRecipeData);
      console.log(`------------------------------------`);
      console.log(JSON.stringify(response, null, 2));
      console.log(`------------------------------------`);

      // Check response status
      expect(response.status).toBe(200);
      expect(response.ok).toBe(true);

      // Check response data structure
      expect(response.data).toHaveProperty('status', 'OK');
      expect(response.data).toHaveProperty('data');
      expect(response.data.data).toHaveProperty('refs');
      expect(response.data.data.refs).toBeInstanceOf(Array);
      expect(response.data.data.refs.length).toBeGreaterThan(0);

      // Check that the refs contain the created recipe
      const createdRecipe = response.data.data.refs[0];
      expect(createdRecipe).toHaveProperty('RECIPE_NO', validRecipeData.RECIPE_NO);
      expect(createdRecipe).toHaveProperty('RECIPE_VER', validRecipeData.RECIPE_VER);
    }, 10000); // Increase timeout for API call

    // Test invalid recipe data
    it('should reject recipe with missing required fields', async () => {
      // Create invalid recipe (missing RECIPE_NO)
      const invalidRecipeData = { ...validRecipeData };
      delete invalidRecipeData.RECIPE_NO;

      const response = await createRecipe(invalidRecipeData);
      console.log(`------------------------------------`);
      console.log(JSON.stringify(response, null, 2));
      console.log(`------------------------------------`);

      // Check response status
      expect(response.status).toBe(422); // Unprocessable Entity
      expect(response.ok).toBe(false);

      // Check error response structure
      expect(response.data).toHaveProperty('status', 'UNPROCESSABLE_ENTITY');
      expect(response.data).toHaveProperty('errors');
    });

    // Test recipe with invalid version format
    it('should reject recipe with invalid version format', async () => {
      const invalidVersionData = {
        ...validRecipeData,
        RECIPE_VER: 'not-a-number', // Invalid version format
      };

      const response = await createRecipe(invalidVersionData);
      console.log(`------------------------------------`);
      console.log(JSON.stringify(response, null, 2));
      console.log(`------------------------------------`);

      // Check response status
      expect(response.status).toBe(422);
      expect(response.ok).toBe(false);

      // Check error details
      expect(response.data).toHaveProperty('errors');
    });

    // Test duplicate recipe creation
    it('should handle duplicate recipe creation correctly', async () => {
      // First create a recipe
      await createRecipe(validRecipeData);

      // Try to create the same recipe again
      const duplicateResponse = await createRecipe(validRecipeData);

      // The behavior depends on your implementation:
      // 1. If you update existing recipes: expect status 200
      // 2. If you reject duplicates: expect status 409 Conflict

      // Check either case
      if (duplicateResponse.status === 200) {
        // If your API updates existing recipes
        expect(duplicateResponse.data).toHaveProperty('status', 'OK');
        expect(duplicateResponse.data.data.refs[0].RECIPE_NO).toBe(validRecipeData.RECIPE_NO);
      } else {
        // If your API rejects duplicates
        expect(duplicateResponse.status).toBe(409);
        expect(duplicateResponse.data).toHaveProperty('status', 'CONFLICT');
      }
    });

    // Test with malformed JSON
    it('should reject requests with malformed JSON', async () => {
      const response = await fetch(`${API_BASE_URL}/integration/erp/recipe`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          Accept: '*/*',
        },
        body: '{ this is not valid JSON }',
      });
      console.log(`------------------------------------`);
      console.log(JSON.stringify(response, null, 2));
      console.log(`------------------------------------`);

      expect(response.status).toBe(400);
      const responseData = await response.json();
      expect(responseData).toHaveProperty('status', 'BAD_REQUEST');
    });
  });

  // Additional test suite for error handling scenarios
  describe('Recipe API Error Handling', () => {
    it('should handle server errors gracefully', async () => {
      // Create recipe data that would cause a server error
      // For example, extremely large payload or values that would cause DB constraint violations
      const problematicData = {
        ...validRecipeData,
        PROCESS: Array(1000).fill(validRecipeData.PROCESS[0]), // Very large payload
      };

      try {
        const response = await createRecipe(problematicData);
        console.log(`------------------------------------`);
        console.log(JSON.stringify(response, null, 2));
        console.log(`------------------------------------`);
        // If it doesn't throw, it should still return an error status
        expect(response.status).toBeGreaterThanOrEqual(500);
      } catch (error) {
        // If fetch throws, it's acceptable as long as we catch it
        expect(error).toBeDefined();
      }
    });
  });
});
