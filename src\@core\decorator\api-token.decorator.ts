import { CanActivate, ExecutionContext, Injectable, UnauthorizedException } from '@nestjs/common';
import { ApiTokenService } from '~/x-modules/admin/services';

@Injectable()
export class ApiTokenGuard implements CanActivate {
  constructor(private readonly apiTokenService: ApiTokenService) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const token = request.headers['x-header'] || request.headers['X-Header'];

    if (!token) {
      throw new UnauthorizedException('Token is missing');
    }

    const path = request.route?.path || request.url;
    const method = request.method;

    const isValid = await this.apiTokenService.check(path, method, token);

    if (!isValid) {
      throw new UnauthorizedException('Invalid API token');
    }

    return true;
  }
}
