import { Column, <PERSON>tity, OneToMany } from 'typeorm';
import { PrimaryBaseEntity } from '../primary-base.entity';

@Entity('uoms')
export class UOMEntity extends PrimaryBaseEntity {
  @Column({ unique: true })
  code: string;

  @Column()
  name: string;

  @Column()
  symbol: string;

  @Column({ nullable: true })
  description: string;

  @Column({ name: 'isactive', default: true })
  isActive: boolean;
}
