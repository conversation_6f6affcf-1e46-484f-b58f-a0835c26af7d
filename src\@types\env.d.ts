/// <reference types="node" />

declare namespace NodeJS {
  interface ProcessEnv {
    PORT: number;
    REQUEST_TIMEOUT: number;
    // Swagger Config
    SWAGGER_TITLE: string;
    SWAGGER_DESCRIPTION: string;
    SWAGGER_VERSION: string;
    // DB Config
    DB_PRIMARY_HOST: string;
    DB_PRIMARY_PORT: number;
    DB_PRIMARY_USERNAME: string;
    DB_PRIMARY_PASSWORD: string;
    DB_PRIMARY_DATABASE: string;
    DB_PRIMARY_SYNCHRONIZE: boolean;
    DB_PRIMARY_SSL: boolean;
    DB_PRIMARY_SSL_REJECT_UNAUTHORIZED: boolean;
    // JWT HS256 config
    JWT_SECRET: string;
    JWT_EXPIRY: string;
    API_TOKEN_CACHE_INTERVAL: number;
    API_TOKEN_EXPIRY: string;
    API_TOKEN_SECRET: string;
    AWS_ACCESS_KEY_ID: string;
    AWS_SECRET_ACCESS_KEY: string;
    AWS_REGION: string;
    DOCKER_SERVER_IP: string;
    TOKEN_ERP_URL: string;
    ERP_INVENTORY: string;
    EBS_MES_USERNAME: string;
    EBS_MES_PASSWORD: string;
    IS_DOCKER_SERVER: boolean;
    IS_DISABLE_SQS: boolean;
    IS_EVENT_HUB: boolean;
    INVENTORY_ERP_URL: string;
    AWS_SQS_URL: string;
    AWS_SQS_URL_CUSTOM: string;
    AWS_SQS_REGION: string;
    AWS_SQS_ACCESS_KEY_ID: string;
    AWS_SQS_SECRET_ACCESS_KEY: string;
    AWS_API_VERSION: string;
    AWS_SNS_ARN: string;
    REDIS_URL: string;
    ACCEPT_PUBLIC_IP: string;

    // AWS S3
    LINK_UPLOAD_S3: string;
    AWS_S3_BUCKET_NAME: string;
    AWS_S3_ACCESS_KEY_ID: string;
    AWS_S3_SECRET_ACCESS_KEY: string;
    AWS_S3_REGION: string;
    AWS_S3_UPLOAD_FOLDER: string;

    QLONE_URL: string;
    QLONE_SAMPLE_URL: string;
    QLONE_CREATE_SAMPLE_URL: string;
    QLONE_VISUAL_INSPECTION_URL: string;

    // Azure Event Hub
    AZURE_EVENT_HUB_CONNECTION_STRING: string;
    AZURE_EVENT_HUB_NAME: string;
  }
}
