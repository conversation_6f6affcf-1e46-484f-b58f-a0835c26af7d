import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { Between, <PERSON>ike, LessThanOrEqual, Like, MoreThanOrEqual } from 'typeorm';
import { BindRepo } from '~/@core/decorator';
import {
  GeneralMasterDataCreateDto,
  GeneralMasterDataUpdateDto,
  ListGeneralReq,
} from '~/dto/general-data.dto';
import { GeneralDataDetailRepo, GeneralDataRepo } from '~/repositories/primary/general-data.repo';

@Injectable()
export class GeneralDataService {
  constructor() {}

  @BindRepo(GeneralDataRepo)
  private repo: GeneralDataRepo;

  @BindRepo(GeneralDataDetailRepo)
  private generalDataDetailRepo: GeneralDataDetailRepo;

  async create(body: GeneralMasterDataCreateDto) {
    const newGeneralMasterData = this.repo.create(body);
    // newGeneralMasterData.createdBy = 'admin';
    newGeneralMasterData.createdDate = new Date();
    newGeneralMasterData.updatedDate = new Date();
    await this.repo.insert(newGeneralMasterData);
    return { message: 'create success', data: newGeneralMasterData };
  }

  async findAll(filter: any = {}) {
    const whereCon: any = {};

    if (filter.code) whereCon.code = filter.code;
    if (filter.name) whereCon.name = Like(`%${filter.name}%`);
    if (filter.description) whereCon.description = Like(`%${filter.description}%`);
    if (filter.note) whereCon.note = Like(`%${filter.note}%`);
    if (filter.isActive !== undefined) whereCon.isActive = filter.isActive;

    const data = await this.repo.find({
      where: whereCon,
      order: { createdDate: 'DESC' },
    });

    return {
      message: 'Success',
      data,
    };
  }

  async findOne(id: string) {
    const generalData = await this.repo.findOne({ where: { id } });
    if (!generalData) {
      throw new NotFoundException(`General data with ID ${id} not found`);
    }
    return {
      message: 'Success',
      data: generalData,
    };
  }

  async list(params: ListGeneralReq) {
    const whereCon: any = {};

    if (params.code) whereCon.code = ILike(`%${params.code}%`);
    if (params.name) whereCon.name = ILike(`%${params.name}%`);
    if (params.description) whereCon.description = Like(`%${params.description}%`);
    if (params.note) whereCon.note = Like(`%${params.note}%`);
    if (params.isActive !== undefined) whereCon.isActive = params.isActive;

    // Add filtering by Created Date range
    if (params.createdDateFrom && params.createdDateTo) {
      whereCon.createdDate = Between(
        new Date(params.createdDateFrom),
        new Date(params.createdDateTo),
      );
    } else if (params.createdDateFrom) {
      whereCon.createdDate = MoreThanOrEqual(new Date(params.createdDateFrom));
    } else if (params.createdDateTo) {
      whereCon.createdDate = LessThanOrEqual(new Date(params.createdDateTo));
    }

    // Add filtering by Updated Date range
    if (params.updatedDateFrom && params.updatedDateTo) {
      whereCon.updatedDate = Between(
        new Date(params.updatedDateFrom),
        new Date(params.updatedDateTo),
      );
    } else if (params.updatedDateFrom) {
      whereCon.updatedDate = MoreThanOrEqual(new Date(params.updatedDateFrom));
    } else if (params.updatedDateTo) {
      whereCon.updatedDate = LessThanOrEqual(new Date(params.updatedDateTo));
    }

    return await this.repo.findPagination(
      {
        where: whereCon,
        order: { createdDate: 'DESC' },
      },
      params,
    );
  }

  async listDetails(generalId: string, filter: any = {}) {
    const generalData = await this.repo.findOne({
      where: { id: generalId },
    });

    if (!generalData) {
      throw new NotFoundException(`General Master Data not found`);
    }

    const whereCon: any = { generalId };

    if (filter.code) whereCon.code = filter.code;
    if (filter.name) whereCon.name = Like(`%${filter.name}%`);
    if (filter.description) whereCon.description = Like(`%${filter.description}%`);
    if (filter.note) whereCon.note = Like(`%${filter.note}%`);
    if (filter.isActive !== undefined) whereCon.isActive = filter.isActive;

    const details = await this.generalDataDetailRepo.find({
      where: whereCon,
      order: { createdDate: 'DESC' },
    });

    return {
      message: 'Success',
      total: details.length,
      data: details,
    };
  }

  async findDetailById(detailId: string) {
    const detail = await this.generalDataDetailRepo.findOne({
      where: { id: detailId },
    });

    if (!detail) {
      throw new NotFoundException(`General data detail with ID ${detailId} not found`);
    }

    return {
      message: 'Success',
      data: detail,
    };
  }

  async update(body: GeneralMasterDataUpdateDto) {
    const foundGeneralMasterData = await this.repo.findOne({ where: { id: body.id } });
    if (!foundGeneralMasterData) throw new NotFoundException('General Master Data not found');

    // Update data
    try {
      const newGeneralMasterData = this.repo.create({
        ...foundGeneralMasterData,
        ...body,
        // updatedBy: 'admin',
        updatedDate: new Date(),
      });

      await this.repo.update(newGeneralMasterData.id, newGeneralMasterData);
      return { message: 'update success', data: newGeneralMasterData };
    } catch (error) {
      throw new BadRequestException(`Failed to update general data: ${error.message}`);
    }
  }

  async remove(id: string) {
    const generalData = await this.repo.findOne({ where: { id } });
    if (!generalData) {
      throw new NotFoundException(`General data with ID ${id} not found`);
    }
    // Check if there are any details associated with this general data
    const details = await this.generalDataDetailRepo.find({ where: { generalId: id } });
    if (details.length > 0) {
      throw new BadRequestException(
        `Cannot delete general data with associated details. Please delete the details first.`,
      );
    }
    await this.repo.remove(generalData);
    return { message: 'delete success' };
  }

  async createDetails(generalId: string, data: GeneralMasterDataCreateDto) {
    const generalData = await this.repo.findOne({ where: { id: generalId } });
    if (!generalData) throw new NotFoundException('General Master Data not found');

    // Kiểm tra nếu là ORG_LEVEL thì không được trùng code
    if (generalData.code === 'ORG_LEVEL') {
      const existingDetail = await this.generalDataDetailRepo.findOne({
        where: { code: data.code, generalId },
      });

      if (existingDetail) {
        throw new BadRequestException(`Code ${data.code} already exists in ORG_LEVEL`);
      }
    }

    const newGeneralMasterDataDetail = this.generalDataDetailRepo.create({
      ...data,
      generalId,
      // createdBy: 'admin',
      createdDate: new Date(),
      updatedDate: new Date(),
    });
    await this.generalDataDetailRepo.insert(newGeneralMasterDataDetail);
    return { message: 'create success', data: newGeneralMasterDataDetail };
  }

  async updateDetails(detailId: string, data: GeneralMasterDataCreateDto) {
    const existingDetail = await this.generalDataDetailRepo.findOne({
      where: { id: detailId },
    });

    if (!existingDetail) {
      throw new NotFoundException(`Detail with ID ${detailId} not found`);
    }

    // Update data
    const updatedDetail = {
      ...existingDetail,
      ...data,
      // updatedBy: 'admin',
      updatedDate: new Date(),
    };

    try {
      await this.generalDataDetailRepo.save(updatedDetail);
      return { message: 'Update success', data: updatedDetail };
    } catch (error) {
      throw new BadRequestException(`Failed to update detail: ${error.message}`);
    }
  }

  async removeDetail(detailId: string) {
    const detail = await this.generalDataDetailRepo.findOne({
      where: { id: detailId },
    });

    if (!detail) {
      throw new NotFoundException(`General data detail with ID ${detailId} not found`);
    }

    await this.generalDataDetailRepo.remove(detail);
    return { message: 'delete success' };
  }

  async findByCode(code: string) {
    const generalData = await this.repo.findOne({ where: { code } });
    if (!generalData) {
      throw new NotFoundException(`General data with code ${code} not found`);
    }

    // Lấy chi tiết của general data
    const details = await this.generalDataDetailRepo.find({
      where: { generalId: generalData.id },
      order: { createdDate: 'DESC' },
    });

    return {
      message: 'Success',
      data: {
        ...generalData,
        details,
      },
    };
  }
}
