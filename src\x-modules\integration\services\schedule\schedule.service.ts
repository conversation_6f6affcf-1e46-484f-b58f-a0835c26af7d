import { forwardRef, Inject, Injectable, UseGuards } from '@nestjs/common';
import axios from 'axios';
import dayjs from 'dayjs';
import { EntityManager, In, LessThanOrEqual, MoreThanOrEqual } from 'typeorm';
import { BindRepo } from '~/@core/decorator';
import { BusinessException } from '~/@systems/exceptions';
import { NSSQSS } from '~/common/enums';
import { NSSetting } from '~/common/enums/NSSetting.enum';
import { QualityNotificationEntity } from '~/entities/primary';
import {
  InspectionPlanRepo,
  MachineParameterRepo,
  OrganizationUnitRepo,
  ProcessRepo,
  ProductionAreaDetailRepo,
  ProductionBatchRepo,
  ProductionOrderRepo,
  SettingStringRepo,
  ShiftRepo,
} from '~/repositories/primary';
import { LogScheduleRepo } from '~/repositories/primary/log-schedule.repo';
import { KPIPeriodService, RejectionReasonService } from '~/x-modules/admin/services';
import { QualityNotificationService } from '~/x-modules/integration/services/quality-notification';

@Injectable()
export class ScheduleService {
  constructor(
    @Inject(forwardRef(() => KPIPeriodService))
    private readonly kpiPeriodservice: KPIPeriodService,
    @Inject(forwardRef(() => RejectionReasonService))
    private rejectionReasonService: RejectionReasonService,
    private qualityNotificationService: QualityNotificationService,
  ) {}

  @BindRepo(LogScheduleRepo)
  private readonly logScheduleRepo: LogScheduleRepo;

  async runYearlyUpdate() {
    console.log(`===== Running yearly update on 25/12 =====`);
    const lstRule = await this.kpiPeriodservice.findRule();
    for (const rule of lstRule) {
      const { id, status, ...rest }: any = rule;

      rest.status = status === 1 ? true : false;
      const nextYear = new Date().getFullYear();
      await this.kpiPeriodservice.updateRule(id, rest, nextYear);
    }
    console.log(`===== Yearly update completed! =====`);
  }

  async handleMidnightCron() {
    try {
      await this.rejectionReasonService.refresh();
    } catch (error) {}
  }

  async createLogSchedule(data: { message: string; type: string }) {
    // console.log(`===== Creating log schedule =====`);
    return await this.logScheduleRepo.save({
      type: data.type,
      dateTime: new Date(),
      message: data.message,
      status: NSSQSS.EScheduleStatus.Success,
    });
  }

  async createSampleQLONE() {
    try {
      return this.qualityNotificationService.createSampleQLONE();
    } catch (err) {}
  }
}
