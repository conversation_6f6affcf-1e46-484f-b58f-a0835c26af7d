import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsBoolean, IsOptional } from 'class-validator';

export class PaginationReq {
  @ApiPropertyOptional({ description: 'Site name' })
  @IsOptional()
  siteName?: any;

  @ApiPropertyOptional({ description: 'Factory name' })
  @IsOptional()
  factoryName?: any;

  @ApiPropertyOptional({ description: 'Status' })
  @Type(() => Boolean)
  @IsOptional()
  status?: boolean;

  @ApiPropertyOptional({ description: 'Default ?' })
  @IsOptional()
  default?: any;

  @ApiProperty({
    example: 10,
  })
  pageSize: number = 50;
  @ApiProperty({
    example: 1,
  })
  pageIndex: number = 1;
}
