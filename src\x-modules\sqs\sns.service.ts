// sns.service.ts
import { Injectable } from '@nestjs/common';
import { SNSClient, PublishCommand } from '@aws-sdk/client-sns';
import { configEnv } from '~/@config/env';
import * as aws from 'aws-sdk';
import { BindRepo } from '~/@core/decorator';
import { LogJobEntity } from '~/entities/primary';
import { LogJobRepo } from '~/repositories/primary/log-job.repo';
import { NSSQSS } from '~/common/enums';
import { LogJobService } from './log-job.service';
import { delay } from 'rxjs';
const {
  AWS_SQS_REGION,
  AWS_SQS_ACCESS_KEY_ID,
  AWS_SQS_SECRET_ACCESS_KEY,
  AWS_API_VERSION,
  AWS_SNS_ARN,
} = configEnv();
@Injectable()
export class SnsService {
  private readonly snsClient: SNSClient;
  constructor(private readonly logJobService: LogJobService) {
    const sns = new SNSClient({
      apiVersion: AWS_API_VERSION || '2023-03-09',
      region: AWS_SQS_REGION || 'ap-southeast-1',
      credentials: {
        accessKeyId: AWS_SQS_ACCESS_KEY_ID || '',
        secretAccessKey: AWS_SQS_SECRET_ACCESS_KEY || '',
      },
    });
    this.snsClient = sns;
  }

  async sendMessage(
    message: {
      message: string;
      data: any;
    },
    type?: string,
  ): Promise<aws.SNS.PublishResponse> {
    const params = {
      Message: JSON.stringify(message), // Nội dung message (thường là JSON)
      TopicArn: AWS_SNS_ARN || '',
      // Subject: subject, // Tiêu đề tùy chọn cho message (tùy thuộc vào subscriber)
      MessageAttributes: {
        type: {
          DataType: 'String',
          StringValue: type,
        },
      },
    };

    try {
      const result = await this.snsClient.send(new PublishCommand(params));
      await this.logJobService.createLogJob({
        message: message.message,
        status: NSSQSS.EStatus.Running,
        type: type,
        messageSNSId: result.MessageId,
        metadata: message?.data,
      });
      return result;
    } catch (error) {
      console.error('Error sending message to SNS:', error);
      // throw error; // Re-throw lỗi để controller hoặc các lớp khác có thể xử lý
    }
  }
}
