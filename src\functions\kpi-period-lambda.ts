import { NestFactory } from '@nestjs/core';
import { Context, Handler } from 'aws-lambda';
import 'reflect-metadata';
import { AppModule } from '~/app.module';
import { KPIPeriodService } from '~/x-modules/admin/services';


// Singleton app instance to avoid recreating for each request
let cachedApp: any = null;

async function bootstrap() {
  if (!cachedApp) {
    const app = await NestFactory.create(AppModule, {
      logger: ['error', 'warn'], // Reduce logging
    });
    cachedApp = app;
  }
  return cachedApp;
}

// Helper function to create response
function createResponse(statusCode: number, body: any) {
  return {
    statusCode,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Credentials': true,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(body),
  };
}

// List kpi period
export const list: Handler = async (event: any, context: Context) => {
  try {
    const app = await bootstrap();
    const kpiPeriodService = app.get(KPIPeriodService);
    const result = await kpiPeriodService.list(event.queryStringParameters || {});
    return createResponse(200, result);
  } catch (error) {
    console.error('Error getting kpi period:', error);
    return createResponse(500, { message: error.message || 'Internal server error' });
  }
};

// Detail KPI Period
export const detail: Handler = async (event: any, context: Context) => {
  try {
    const id = event.pathParameters?.id;
    const app = await bootstrap();
    const kpiPeriodService = app.get(KPIPeriodService);
    const result = await kpiPeriodService.detail(id);
    return createResponse(200, result);
  } catch (error) {
    console.error('Error searching organization units:', error);
    console.error('Error stack:', error.stack);
    return createResponse(500, { message: error.message || 'Internal server error' });
  }
};

// Find KPI Period Rule
export const findRule: Handler = async (event: any, context: Context) => {
  try {
    const app = await bootstrap();
    const kpiPeriodService = app.get(KPIPeriodService);
    const result = await kpiPeriodService.findRule();

    return createResponse(200, result);
  } catch (error) {
    console.error('Error getting organization unit by code:', error);
    console.error('Error stack:', error.stack);
    return createResponse(500, { message: error.message || 'Internal server error' });
  }
};

// Get organization unit by ID
export const findOne: Handler = async (event: any, context: Context) => {
  try {
    const id = event.pathParameters?.id;
    const app = await bootstrap();
    const kpiPeriodService = app.get(KPIPeriodService);
    const result = await kpiPeriodService.findOne(id);
    return createResponse(200, result);
  } catch (error) {
    console.error('Error getting holiday by id:', error);
    return createResponse(error.message.includes('not found') ? 404 : 500, {
      message: error.message || 'Internal server error',
    });
  }
};

// Detail KPI Period Rule
export const detailRule: Handler = async (event: any, context: Context) => {
  try {
    const id = event.pathParameters?.id;

    const app = await bootstrap();
    const kpiPeriodService = app.get(KPIPeriodService);
    
    // Gọi service để xử lý logic (bao gồm các rule kiểm tra)
    const result = await kpiPeriodService.detailRule(id);

    return createResponse(200, result);
  } catch (error) {
    console.error('Error getting detail KPI Period Rule:', error);
    return createResponse(error.message.includes('not found') ? 404 : 500, {
      message: error.message || 'Internal server error',
    });
  }
};

export const createRule: Handler = async (event: any, context: Context) => {
  try {
    const body = JSON.parse(event.body || '{}');
    const app = await bootstrap();
    const kpiPeriodService = app.get(KPIPeriodService);
    const result = await kpiPeriodService.createRule(body);
    return createResponse(201, result);
  } catch (error) {
    const statusCode = error.message.includes('already exists');
    return createResponse(statusCode, { message: error.message || 'Internal server error' });
  }
};

export const updateRule: Handler = async (event: any, context: Context) => {
  try {
    const body = JSON.parse(event.body || '{}');
    const app = await bootstrap();
    const kpiPeriodService = app.get(KPIPeriodService);
    const result = await kpiPeriodService.createRule(body);
    return createResponse(201, result);
  } catch (error) {
    const statusCode = error.message.includes('not found data');
    return createResponse(statusCode, { message: error.message || 'Internal server error' });
  }
};

// Handle OPTIONS request for CORS
export const options: Handler = async (event: any, context: Context) => {
  return {
    statusCode: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers':
        'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-Amz-User-Agent',
      'Access-Control-Allow-Methods': 'OPTIONS,GET,PUT',
      'Access-Control-Allow-Credentials': true,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({}),
  };
};
