import { ApiProperty } from '@nestjs/swagger';
import { IsOptional } from 'class-validator';
import { PageRequest } from '~/@systems/utils';

export class RejectionReasonPageReq extends PageRequest {
  @ApiProperty({ description: 'Mã' })
  @IsOptional()
  code: string;

  @ApiProperty({ description: 'Tên' })
  @IsOptional()
  name: string;

  @ApiProperty({ description: '<PERSON><PERSON>y cập nhật' })
  @IsOptional()
  updatedDate: Date[];

  @ApiProperty({ description: 'Trạng thái hoạt động' })
  @IsOptional()
  status: number;
}
