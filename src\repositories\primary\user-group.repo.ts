import { EntityRepository } from 'typeorm';
import { UserGroupEntity } from '~/entities/primary/user-group.entity';
import { UserEntity } from '~/entities/primary/user.entity';
import { PrimaryRepo } from '../primary.repo';

@EntityRepository(UserGroupEntity)
export class UserGroupRepo extends PrimaryRepo<UserGroupEntity> {
  async findGroupWithUsers(id: string): Promise<UserGroupEntity | undefined> {
    return this.createQueryBuilder('userGroup')
      .leftJoinAndSelect('userGroup.users', 'users')
      .where('userGroup.id = :id', { id })
      .getOne();
  }

  async addUsersToGroup(groupId: string, userIds: string[]): Promise<UserGroupEntity | undefined> {
    // Load group with existing users
    const group = await this.findOne(groupId, { relations: ['users'] });
    if (!group) return undefined;

    // Find all users to be added
    const usersToAdd = await this.manager.findByIds(UserEntity, userIds);
    if (!usersToAdd.length) return group;

    // Initialize users array if it doesn't exist
    if (!group.users) {
      group.users = [];
    }

    // Add new users
    group.users = [...group.users, ...usersToAdd];

    // Save and return the updated group with users
    const savedGroup = await this.save(group);

    // Reload the group to ensure all relations are properly loaded
    return this.findOne(savedGroup.id, { relations: ['users'] });
  }

  async removeUsersFromGroup(
    groupId: string,
    userIds: string[],
  ): Promise<UserGroupEntity | undefined> {
    const group = await this.findOne(groupId, { relations: ['users'] });
    if (!group) return undefined;

    group.users = group.users.filter((user) => !userIds.includes(user.id));
    return this.save(group);
  }
}
