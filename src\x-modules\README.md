# 📦 <PERSON><PERSON><PERSON> mục `x-modules` – Business Modules

<PERSON><PERSON><PERSON> mục `src/x-modules/` chứa các module nghiệp vụ mở rộng cho hệ thống MES. Mỗi module đảm nhận một nhóm chức năng riêng biệt, phụ<PERSON> vụ cho các nghiệp vụ quản trị, t<PERSON><PERSON> hợ<PERSON>, xử lý dữ liệu, và các tác vụ nền.

## Cấu trúc các module

- **@global/**: Các service, helper, và middleware dùng chung toàn hệ thống.
- **@schedule/**: <PERSON><PERSON><PERSON> nghĩa các tác vụ định kỳ (cron jobs, scheduled tasks).
- **admin/**: <PERSON><PERSON><PERSON> chức năng quản trị (master data, user, permission, system configuration, ...).
- **integration/**: Tích hợp với hệ thống bên ngoài (ví dụ: IoT, AWS, SCADA, ...).
- **publics/**: Các API public hoặc các module mở rộng cho bên ngoài truy cập.
- **scada/**: <PERSON><PERSON><PERSON> hợp và xử lý dữ liệu từ hệ thống SCADA.
- **sqs/**: Xử lý message queue với Amazon SQS.

## Mục đích

- Tách biệt rõ ràng các nghiệp vụ, dễ dàng mở rộng và bảo trì.
- Hỗ trợ phát triển theo hướng module hóa, mỗi module có thể phát triển, test, và deploy độc lập.

## Ví dụ sử dụng

Bạn có thể import các service/module từ `x-modules` như sau:

```typescript
import { UserService } from './x-modules/admin/services/system-configuration/user.service';
import { S3Service } from './x-modules/@global/services/s3.service';
```

## Đóng góp

- Đảm bảo mỗi module có README riêng nếu module phức tạp.
- Đặt tên module rõ ràng, nhất quán.
- Viết code theo chuẩn TypeScript và NestJS.

---

> Thư mục này được re-export trong [`index.ts`](index.ts) để thuận tiện cho việc import ở các nơi khác trong hệ thống.
