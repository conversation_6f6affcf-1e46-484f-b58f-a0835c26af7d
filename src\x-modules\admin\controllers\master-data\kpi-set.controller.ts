import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { DefController, DefD<PERSON>te, DefGet, DefPost, DefPut } from '~/@core/decorator';
import { KPISetService } from '../../services';
import { Body, Param, ParseUUIDPipe, UseGuards } from '@nestjs/common';
import { CreateKpiSetDto, KpiSetPageReq, UpdateKpiSetDto } from '~/dto/kpi-set.dto';
import { Roles } from '~/@core/decorator/roles.decorator';
import { RoleGuard } from '~/@systems/guard/role.guard';

@ApiTags('KPISet')
@DefController('kpi-set')
export class KPISetController {
  constructor(private service: KPISetService) {}

  @ApiOperation({ summary: 'Tạo mới', description: 'Tạo mới kế hoạch kiểm tra' })
  @DefPost('')
  @Roles('/master-data/kpi-set', 'View')
  @UseGuards(RoleGuard)
  async create(@Body() body: CreateKpiSetDto) {
    return this.service.create(body);
  }

  @ApiOperation({ summary: 'Cập nhật', description: 'Cập nhật kế hoạch kiểm tra' })
  @DefPut('')
  @Roles('/master-data/kpi-set', 'View')
  @UseGuards(RoleGuard)
  async update(@Body() body: UpdateKpiSetDto) {
    return this.service.update(body);
  }

  @ApiOperation({ summary: 'Phân trang', description: 'Phân trang kế hoạch kiểm tra' })
  @DefPost('pagination')
  @Roles('/master-data/kpi-set', 'View')
  @UseGuards(RoleGuard)
  async pagination(@Body() body: KpiSetPageReq) {
    return this.service.pagination(body);
  }

  @DefDelete('delete/:id')
  @Roles('/master-data/kpi-set', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Xóa' })
  delete(@Param('id') id: string) {
    return this.service.delete(id);
  }

  @DefDelete('delete-not-confirm/:id')
  @Roles('/master-data/kpi-set', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Xóa' })
  deleteNotConfirm(@Param('id') id: string) {
    return this.service.deleteNotConfirm(id);
  }

  @ApiOperation({ summary: 'Lấy thông tin selectbox', description: 'Lấy thông tin selectbox' })
  @DefGet('/dataselect-for-create/:userId')
  @Roles('/master-data/kpi-set', 'View')
  @UseGuards(RoleGuard)
  async loadDataSelectForCreate(@Param('userId') userId?: string) {
    return this.service.loadDataSelectForCreate(userId);
  }

  @ApiOperation({ summary: 'Lấy thông tin chi tiết', description: 'Lấy thông tin chi tiết' })
  @DefGet('detail/:id')
  @Roles('/master-data/kpi-set', 'View')
  @UseGuards(RoleGuard)
  async findDetail(@Param('id') id: string) {
    return this.service.findDetail(id);
  }

  // @ApiOperation({ summary: 'Lấy thông tin selectbox', description: 'Lấy thông tin selectbox' })
  // @DefGet('dataselect-for-create')
  // async loadDataSelectForCreate() {
  //   return this.service.loadDataSelectForCreate();
  // }

  // @ApiOperation({ summary: 'Chi tiết', description: 'Lấy thông tin chi tiết' })
  // @DefGet(':id')
  // async detail(@Param('id', new ParseUUIDPipe()) id: string) {
  //   return this.service.detail(id);
  // }
}
