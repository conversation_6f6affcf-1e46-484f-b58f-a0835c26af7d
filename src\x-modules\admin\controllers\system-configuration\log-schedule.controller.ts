import { Body, Param, Query, UseGuards } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { DefController, DefGet, DefPost, DefPut } from '~/@core/decorator';
import { Roles } from '~/@core/decorator/roles.decorator';
import { RoleGuard } from '~/@systems/guard/role.guard';
import { LogScheduleService } from '~/x-modules/sqs/log-schedule.service';

@ApiTags('Log Schedule')
@DefController('log-schedule')
export class LogScheduleController {
  constructor(private readonly service: LogScheduleService) {}

  @ApiOperation({ summary: 'Danh sách log schedule' })
  @DefGet('')
  @Roles('/system-configuration/log-scheduling', 'View')
  @UseGuards(RoleGuard)
  async list(@Query() params: any) {
    return this.service.pagination(params);
  }
}
