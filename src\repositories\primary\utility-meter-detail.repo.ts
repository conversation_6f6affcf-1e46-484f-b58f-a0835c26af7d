// src/repositories/primary/utility-meter-detail.repo.ts
import { EntityRepository } from 'typeorm';
import { PrimaryRepo } from '../primary.repo';
import { UtilityMeterDetailEntity } from '../../entities/primary/utility-meter-detail.entity';

@EntityRepository(UtilityMeterDetailEntity)
export class UtilityMeterDetailRepo extends PrimaryRepo<UtilityMeterDetailEntity> {
  async findByMeterId(meterId: string) {
    return this.find({
      where: { utilityMeterId: meterId },
      relations: ['utilityMeter', 'organization']
    });
  }

  async findByProcessAreaId(processAreaId: string) {
    return this.find({
      where: { processAreaId },
      relations: ['utilityMeter', 'organization']
    });
  }

  async findAssigned() {
    return this.find({
      where: { isAssigned: true },
      relations: ['utilityMeter', 'organization']
    });
  }

  async findUnassigned() {
    return this.find({
      where: { isAssigned: false },
      relations: ['utilityMeter', 'organization']
    });
  }
}