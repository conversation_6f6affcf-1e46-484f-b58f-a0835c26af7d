import { Entity, Column, CreateDateColumn, Index, PrimaryGeneratedColumn } from 'typeorm';
import { PrimaryBaseEntity } from '../primary-base.entity';
import { ApiProperty } from '@nestjs/swagger';
import { IsUUID } from 'class-validator';
@Index('IDX_measurementId_datetime', ['measurementId', 'datetime'])
@Entity('mes_dx_measurements')
export class MesDxMeasurementEntity {
  @ApiProperty()
  @PrimaryGeneratedColumn('uuid')
  @IsUUID(4)
  id?: string;

  @Index()
  @ApiProperty()
  @CreateDateColumn({ type: 'timestamptz' })
  createdDate?: Date;

  @ApiProperty({ description: 'Tên của gateway thực hiện phép đo', example: 'Gateway A' })
  @Column()
  gatewayName: string;

  @ApiProperty({ description: 'ID duy nhất của gateway', example: 'GATEWAY_12345' })
  @Column()
  gatewayId: string;

  @ApiProperty({
    description: 'Tên của nguồn dữ liệu liên quan đến phép đo',
    example: 'Datasource 1',
  })
  @Column()
  datasourceName: string;

  @ApiProperty({ description: 'ID của nguồn dữ liệu', example: 'DS_001' })
  @Column()
  datasourceId: string;

  @ApiProperty({ description: 'Tên của mô hình liên quan đến phép đo', example: 'Model X' })
  @Column()
  modelName: string;

  @ApiProperty({ description: 'ID của mô hình', example: 'MODEL_9876' })
  @Column()
  modelId: string;

  @ApiProperty({ description: 'Tên của tài sản liên quan đến phép đo', example: 'Turbine 1' })
  @Column()
  assetName: string;

  @ApiProperty({ description: 'ID của tài sản', example: 'ASSET_5678' })
  @Column()
  assetId: string;

  @ApiProperty({ description: 'Tên phép đo', example: 'Temperature' })
  @Column()
  measurementName: string;

  @Index()
  @ApiProperty({ description: 'ID của phép đo', example: 'MEAS_2345' })
  @Column()
  measurementId: string;

  @ApiProperty({ description: 'Giá trị của phép đo', example: 36.5 })
  @Column({
    type: 'varchar',
    nullable: true,
  })
  value: string;

  @Index()
  @ApiProperty({ description: 'Thời gian thực hiện phép đo', example: '2024-03-06T14:30:00Z' })
  @Column({ type: 'timestamptz', nullable: true })
  datetime?: Date;
}
