import { NestFactory } from '@nestjs/core';
import { Context, Handler } from 'aws-lambda';
import { ReasonMasterService } from '~/x-modules/admin/services';
import { AppModule } from '../../app.module';

let cachedApp: any = null;

async function bootstrap() {
  if (!cachedApp) {
    const app = await NestFactory.createApplicationContext(AppModule);
    await app.init();
    cachedApp = app;
  }
  return cachedApp;
}

/**
 * Create a new reason master
 */
export const createReasonMaster: Handler = async (event: any, context: Context) => {
  const app = await bootstrap();
  const service = app.get(ReasonMasterService);
  const body = JSON.parse(event.body);

  try {
    const result = await service.create(body);
    return {
      statusCode: 201,
      body: JSON.stringify(result),
      headers: {
        'Content-Type': 'application/json',
      },
      message: 'Create reason master successfully!',
    };
  } catch (error) {
    return {
      statusCode: error.status || 500,
      body: JSON.stringify({
        message: error.message || 'Internal server error',
        error: error.response?.error || error.name,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    };
  }
};
/**
 * Load enum of general data
 */
export const loadEnumForReasonMaster: Handler = async (event: any, context: Context) => {
  const app = await bootstrap();
  const service = app.get(ReasonMasterService);

  try {
    const result = await service.loadSelectBoxDataForReasonMaster();
    return {
      statusCode: 200,
      body: JSON.stringify(result),
      headers: {
        'Content-Type': 'application/json',
      },
      message: 'Load enum data success!',
    };
  } catch (error) {
    return {
      statusCode: error.status || 500,
      body: JSON.stringify({
        message: error.message || 'Internal server error',
        error: error.response?.error || error.name,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    };
  }
};

/**
 * Load list parent to select
 */
export const loadSelectParent: Handler = async (event: any, context: Context) => {
  const app = await bootstrap();
  const service = app.get(ReasonMasterService);

  try {
    const result = await service.loadSelectParent();
    return {
      statusCode: 200,
      body: JSON.stringify(result),
      headers: {
        'Content-Type': 'application/json',
      },
      message: 'Get reason master successfully!',
    };
  } catch (error) {
    return {
      statusCode: error.status || 500,
      body: JSON.stringify({
        message: error.message || 'Internal server error',
        error: error.response?.error || error.name,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    };
  }
};
/**
 * Get paginated list of reason masters
 */
export const paginationReasonMaster: Handler = async (event: any, context: Context) => {
  const app = await bootstrap();
  const service = app.get(ReasonMasterService);

  try {
    const body = JSON.parse(event.body);
    const result = await service.pagination(body);
    return {
      statusCode: 200,
      body: JSON.stringify(result),
      headers: {
        'Content-Type': 'application/json',
      },
      message: 'Get paginated reason masters successfully!',
    };
  } catch (error) {
    return {
      statusCode: error.status || 500,
      body: JSON.stringify({
        message: error.message || 'Internal server error',
        error: error.response?.error || error.name,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    };
  }
};

/**
 * Get a reason master by ID
 */
export const findReasonMasterById: Handler = async (event: any, context: Context) => {
  const app = await bootstrap();
  const service = app.get(ReasonMasterService);

  try {
    const id = event.pathParameters?.id;
    if (!id) {
      return {
        statusCode: 400,
        body: JSON.stringify({
          message: 'Missing ID parameter',
        }),
        headers: {
          'Content-Type': 'application/json',
        },
      };
    }

    const result = await service.findOne(id);
    return {
      statusCode: 200,
      body: JSON.stringify(result),
      headers: {
        'Content-Type': 'application/json',
      },
      message: 'Get reason master successfully!',
    };
  } catch (error) {
    return {
      statusCode: error.status || 500,
      body: JSON.stringify({
        message: error.message || 'Internal server error',
        error: error.response?.error || error.name,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    };
  }
};

/**
 * Update a reason master
 */
export const updateReasonMaster: Handler = async (event: any, context: Context) => {
  const app = await bootstrap();
  const service = app.get(ReasonMasterService);

  try {
    const body = JSON.parse(event.body);
    const result = await service.update(body);
    return {
      statusCode: 200,
      body: JSON.stringify(result),
      headers: {
        'Content-Type': 'application/json',
      },
      message: 'Update reason master successfully!',
    };
  } catch (error) {
    return {
      statusCode: error.status || 500,
      body: JSON.stringify({
        message: error.message || 'Internal server error',
        error: error.response?.error || error.name,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    };
  }
};

/**
 * Delete a reason master
 */
export const deleteReasonMaster: Handler = async (event: any, context: Context) => {
  const app = await bootstrap();
  const service = app.get(ReasonMasterService);

  try {
    const body = JSON.parse(event.body);

    if (!body.id) {
      return {
        statusCode: 400,
        body: JSON.stringify({
          message: 'Missing ID parameter',
        }),
        headers: {
          'Content-Type': 'application/json',
        },
      };
    }

    const result = await service.remove(body.id);
    return {
      statusCode: 200,
      body: JSON.stringify(result),
      headers: {
        'Content-Type': 'application/json',
      },
      message: 'Delete reason master successfully!',
    };
  } catch (error) {
    return {
      statusCode: error.status || 500,
      body: JSON.stringify({
        message: error.message || 'Internal server error',
        error: error.response?.error || error.name,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    };
  }
};
