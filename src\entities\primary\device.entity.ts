import { ApiProperty } from '@nestjs/swagger';
import { Column, Entity, JoinColumn, ManyToOne, OneToMany } from 'typeorm';
import { PrimaryBaseEntity } from '../primary-base.entity';
import { StationEntity } from '~/entities/primary';
import { StationDetailEntity } from '~/entities/primary/station-detail.entity';

@Entity('device')
export class DeviceEntity extends PrimaryBaseEntity {
  @ApiProperty({ description: 'Mã thiết bị' })
  @Column({ type: 'int', default: 0 })
  code: number;

  @ApiProperty({ description: 'Mô tả' })
  @Column({ type: 'text', nullable: true })
  description?: string;

  @ApiProperty({ description: 'Mã kiểu thiết bị' })
  @Column({ nullable: false })
  deviceTypeCode: string;

  @ApiProperty({ description: 'Mã kiểu chi tiết thiết bị' })
  @Column({ nullable: false })
  deviceTypeDetailCode: string;

  @ApiProperty({ description: 'Số serial' })
  @Column({ nullable: true })
  serialNumber: string;

  @ApiProperty({ description: 'Địa chỉ IP' })
  @Column({ nullable: true })
  ipAddress: string;

  @ApiProperty({ description: 'Port thiết bị' })
  @Column({ type: 'decimal', nullable: true })
  devicePort: number;

  @ApiProperty({ description: 'Trạng thái thiết bị' })
  @Column({ type: 'boolean', default: true })
  isActive: boolean;

  @ApiProperty({ description: 'Mô tả' })
  @Column({ type: 'text', nullable: true })
  note: string;

  @OneToMany(() => StationDetailEntity, (e) => e.device)
  assigns?: Promise<StationDetailEntity[]>;
}
