import { Injectable } from '@nestjs/common';
import dayjs from 'dayjs';
import { BindRepo } from '~/@core/decorator';
import { CreateHolidayDto, UpdateHolidayDto } from '~/dto/holiday.dto';
import { HolidayEntity } from '~/entities/primary/holiday.entity';
import { HolidayDetailEntity } from '~/entities/primary/holiday-detail.entity';
import { HolidayRepo } from '~/repositories/primary/holiday.repo';
import { HolidayDetailRepo } from '~/repositories/primary/holiday-detail.repo';

@Injectable()
export class HolidayService {
  @BindRepo(HolidayRepo)
  private HolidayRepo: HolidayRepo;

  @BindRepo(HolidayDetailRepo)
  private HolidayDetailRepo: HolidayDetailRepo;

  async create(createHolidayDto: CreateHolidayDto): Promise<HolidayEntity> {
    // Kiểm tra startDate phải nhỏ hơn endDate
    if (new Date(createHolidayDto.startDate) > new Date(createHolidayDto.endDate)) {
      throw new Error('Start date must be earlier than end date');
    }

    const currentDateTime = dayjs().format('YYYY-MM-DD HH:mm:ss');

    // Tạo danh sách ngày giữa khoảng startDate và endDate
    const startDate = dayjs(createHolidayDto.startDate);
    const endDate = dayjs(createHolidayDto.endDate);
    const dateDetail: string[] = [];

    for (
      let date = startDate;
      date.isBefore(endDate) || date.isSame(endDate, 'day');
      date = date.add(1, 'day')
    ) {
      dateDetail.push(date.format('YYYY-MM-DD'));
    }

    // ✅ Kiểm tra xem có ngày nào trong khoảng thời gian đã tồn tại hay chưa
    const existingDate = await this.HolidayDetailRepo.createQueryBuilder('holiday_detail')
      .where('holiday_detail.dateDetail IN (:...dates)', { dates: dateDetail })
      .andWhere('holiday_detail.status = :status', { status: true })
      .getOne();

    if (existingDate) {
      throw new Error(
        `Already exists : ${dayjs(existingDate.dateDetail).format('YYYY-MM-DD')} (${existingDate.description})`,
      );
    }

    // Tạo holiday
    const holiday = this.HolidayRepo.create({
      ...createHolidayDto,
      dateDetail: dateDetail,
      status: createHolidayDto.status ?? true, // Mặc định status = true nếu không truyền
      // createdBy: null, // Cần cập nhật bằng userId nếu có user authentication
      createdDate: currentDateTime,
      // updatedBy: null,
      updatedDate: currentDateTime,
    });

    const savedHoliday = await this.HolidayRepo.save(holiday);

    // Tạo danh sách holiday_detail và insert vào DB
    const holidayDetails = dateDetail.map((date) => ({
      holidayId: holiday.id,
      description: createHolidayDto.description,
      dateDetail: date,
      yearDetail: dayjs(date).format('YYYY'),
      status: createHolidayDto.status ?? true,
    }));

    await this.HolidayDetailRepo.save(holidayDetails);

    return savedHoliday;
  }

  async findAll(): Promise<HolidayEntity[]> {
    return this.HolidayRepo.find({
      order: {
        updatedDate: 'DESC', // Sắp xếp giảm dần theo lastModifiedDate
      },
    });
  }

  async findOne(id: string): Promise<HolidayEntity> {
    return this.HolidayRepo.findOneOrFail({ where: { id } });
  }

  async update(id: string, updateHolidayDto: UpdateHolidayDto): Promise<HolidayEntity> {
    const holiday = await this.HolidayRepo.findOneOrFail({ where: { id } });

    // Kiểm tra startDate và endDate nếu có thay đổi
    const newStartDate = updateHolidayDto.startDate
      ? new Date(updateHolidayDto.startDate)
      : holiday.startDate;
    const newEndDate = updateHolidayDto.endDate
      ? new Date(updateHolidayDto.endDate)
      : holiday.endDate;

    if (newStartDate > newEndDate) {
      throw new Error('Start date must be earlier than end date');
    }

    const currentDateTime = dayjs().format('YYYY-MM-DD HH:mm:ss');

    // Nếu `status` không được truyền, giữ nguyên giá trị cũ
    const status = updateHolidayDto.status ?? holiday.status;

    // Tạo danh sách ngày mới từ startDate đến endDate
    const startDate = dayjs(newStartDate);
    const endDate = dayjs(newEndDate);
    const dateDetail: string[] = [];

    for (
      let date = startDate;
      date.isBefore(endDate) || date.isSame(endDate, 'day');
      date = date.add(1, 'day')
    ) {
      dateDetail.push(date.format('YYYY-MM-DD'));
    }

    // Kiểm tra xem có ngày nào đã tồn tại trong `holiday_detail` (ngoại trừ holiday hiện tại)
    const existingDate = await this.HolidayDetailRepo.createQueryBuilder('holiday_detail')
      .where('holiday_detail.dateDetail IN (:...dates)', { dates: dateDetail })
      .andWhere('holiday_detail.holidayId != :holidayId', { holidayId: id }) // Loại trừ holiday hiện tại
      .andWhere('holiday_detail.status = :status', { status: true })
      .getOne();

    if (existingDate) {
      throw new Error(
        `Already exists :  ${dayjs(existingDate.dateDetail).format('YYYY-MM-DD')} (${existingDate.description})`,
      );
    }

    // Cập nhật holiday
    delete updateHolidayDto.updatedDate;
    delete updateHolidayDto?.createdDate;
    await this.HolidayRepo.update(id, {
      ...updateHolidayDto,
      status, // Đảm bảo `status` luôn có giá trị
      dateDetail: dateDetail, // Định dạng đúng cho PostgreSQL
      // updatedBy: null,
      updatedDate: currentDateTime,
    });

    // Xóa dữ liệu cũ trong `holiday_detail` dựa trên `holidayId`
    await this.HolidayDetailRepo.delete({ holidayId: id });

    // Chạy vòng lặp insert dữ liệu mới vào `holiday_detail`
    for (const date of dateDetail) {
      await this.HolidayDetailRepo.save({
        holidayId: holiday.id,
        description: updateHolidayDto.description ?? holiday.description,
        dateDetail: date, // Lưu đúng kiểu DATE
        yearDetail: dayjs(date).format('YYYY'), // Lấy năm từ ngày
        status,
      });
    }

    return this.HolidayRepo.findOneOrFail({ where: { id } });
  }

  async remove(id: string): Promise<void> {
    await this.HolidayRepo.delete(id);
  }
}
