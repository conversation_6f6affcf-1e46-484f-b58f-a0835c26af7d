import { Body, Delete, Inject, Param, Query, UseGuards } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { DefController, DefGet, DefPost, DefPut } from '~/@core/decorator';

import { PermissionService } from '../../services';
import {
  GetRoleDto,
  PermissionCreateDto,
  PermissionGetUserRoleDto,
  PermissionPageReq,
  PermissionUpdateDto,
  PermissionUpdateRoleDto,
  PermissionUpdateUserDto,
} from '~/dto/permission.dto';
import { Roles } from '~/@core/decorator/roles.decorator';
import { RoleGuard } from '~/@systems/guard/role.guard';

@ApiTags('Permission')
@DefController('permission')
export class PermissionController {
  constructor(@Inject(PermissionService) private readonly permissionService: PermissionService) {}

  /* hàm load select box */
  @DefGet('find')
  @Roles('/system-configuration/permission', 'View')
  @UseGuards(RoleGuard)
  async find() {
    return await this.permissionService.find();
  }

  @ApiOperation({ summary: 'Lấy ds nhóm quyền phân trang' })
  @DefPost('pagination')
  @Roles('/system-configuration/permission', 'View')
  @UseGuards(RoleGuard)
  public async pagination(@Body() params: PermissionPageReq) {
    return await this.permissionService.pagination(params);
  }

  /* hàm tạo mới dữ liệu */
  @DefPost('create_data')
  @Roles('/system-configuration/permission', 'View')
  @UseGuards(RoleGuard)
  async createData(@Body() data: PermissionCreateDto) {
    return await this.permissionService.createData(data);
  }

  @DefGet('load-data-permission-first/:permissionId')
  @Roles('/system-configuration/permission', 'View')
  @UseGuards(RoleGuard)
  async findOne(@Param('permissionId') permissionId: string) {
    return await this.permissionService.loadDataPermissionFirst(permissionId);
  }

  /* hàm update dữ liệu */
  @DefPost('update_data')
  @Roles('/system-configuration/permission', 'View')
  @UseGuards(RoleGuard)
  async updateData(@Body() data: PermissionUpdateDto) {
    return await this.permissionService.updateData(data);
  }

  @DefGet('find-by-id/:id')
  @Roles('/system-configuration/permission', 'View')
  @UseGuards(RoleGuard)
  async detail(@Param('id') id: string) {
    return this.permissionService.findById(id);
  }

  /* hàm load ra danh sách quyền của permission */
  @DefPost('get_role')
  @Roles('/system-configuration/permission', 'View')
  @UseGuards(RoleGuard)
  async getRole(@Body() data: GetRoleDto) {
    return await this.permissionService.loadPermissionGroup(data.id);
  }

  /* hàm set quyền cho permission */
  @DefPost('set_rule')
  @Roles('/system-configuration/permission', 'View')
  @UseGuards(RoleGuard)
  async getRule(@Body() data: PermissionUpdateRoleDto) {
    return await this.permissionService.updatePermissionGroupRole(data);
  }

  /* hàm load data permission group */
  @ApiOperation({ summary: 'Load data select' })
  @DefPost('load_data_select')
  @Roles('/system-configuration/permission', 'View')
  @UseGuards(RoleGuard)
  async loadDataSelect() {
    return await this.permissionService.loadDataSelect();
  }

  @DefGet()
  @Roles('/system-configuration/permission', 'View')
  @UseGuards(RoleGuard)
  loadEnumRole() {
    return this.permissionService.getRolePermissions();
  }

  @Delete('delete/:id')
  @Roles('/system-configuration/permission', 'View')
  @UseGuards(RoleGuard)
  async delete(@Param('id') id: string) {
    return this.permissionService.delete(id);
  }

  @ApiOperation({ summary: 'Cập nhật trạng thái nhóm nguyên vật liệu' })
  @DefPut('active-permission/:id')
  @Roles('/system-configuration/permission', 'View')
  @UseGuards(RoleGuard)
  async updateActivePermission(@Param('id') id: string) {
    return this.permissionService.updateActivePermission(id);
  }

  @DefGet('all')
  @Roles('/system-configuration/permission', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Lấy danh sách tất cả quyền' })
  async getAllPermissions() {
    const result = await this.permissionService.getAllPermissions();
    return {
      data: result.items,
    };
  }

  @ApiOperation({ summary: 'Danh sách quyền user' })
  @DefPost('get-user-role')
  @Roles('/system-configuration/permission', 'View')
  @UseGuards(RoleGuard)
  async getUserRole(@Body('userId') userId: string) {
    return this.permissionService.getUserRoles(userId);
  }
}
