import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsBoolean, IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';
import { PageRequest } from '~/@systems/utils';

export class RejectionMonitoringReq {
  @ApiPropertyOptional({ description: 'Site ID' })
  @IsOptional()
  siteId?: string;

  @ApiPropertyOptional({ description: 'Factory ID' })
  @IsOptional()
  factoryId?: string;

  @ApiProperty({ description: 'Production Line ID' })
  @IsOptional()
  lineId?: string;

  @ApiProperty({ description: 'Start Date' })
  @IsOptional()
  startDate?: Date;

  @ApiProperty({ description: 'End Date' })
  @IsOptional()
  endDate?: Date;
}

export class ProductionRejectionReq {
  @ApiProperty({ description: 'Line ID' })
  @IsNotEmpty()
  productionLineId: string;

  @ApiProperty({ description: 'Process Area ID' })
  @IsNotEmpty()
  processAreaId: string;

  @ApiProperty({ description: 'Production Order ID' })
  @IsOptional()
  productionOrderId?: string;

  @ApiProperty({ description: 'Item ID' })
  @IsNotEmpty()
  itemId: string;

  @ApiProperty({ description: 'Transaction Date' })
  @IsNotEmpty()
  transactionDate: Date;

  @ApiProperty({ description: 'Shift ID' })
  @IsNotEmpty()
  shiftId: string;

  @ApiProperty({ description: 'Transaction Type ID' })
  @IsNotEmpty()
  transactionTypeId: string;

  @ApiProperty({ description: 'Quantity' })
  @IsNotEmpty()
  quantity: number;

  @ApiProperty({ description: 'UOM' })
  @IsNotEmpty()
  uomCode: string;

  @ApiProperty({ description: 'Note' })
  @IsOptional()
  note?: string;

  @ApiProperty({ description: 'OEE' })
  @IsOptional()
  oeeCal?: number;

  @ApiProperty({ description: 'Description' })
  @IsOptional()
  description?: string;

  @ApiProperty({ description: 'Rejection reason Id' })
  @IsOptional()
  rejectionReasonId?: string;
}

export class ListProductionRejection extends PageRequest {
  @ApiProperty({ description: 'Site ID' })
  @IsOptional()
  siteId?: string;

  @ApiProperty({ description: 'Factory ID' })
  @IsOptional()
  factoryId?: string;

  @ApiProperty({ description: 'Production Line ID' })
  @IsOptional()
  lineId?: string;

  @ApiProperty({ description: 'Start Date' })
  @IsOptional()
  startDate?: Date;

  @ApiProperty({ description: 'End Date' })
  @IsOptional()
  endDate?: Date;
}
