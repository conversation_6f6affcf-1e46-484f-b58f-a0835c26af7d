import {
  EventSubscriber,
  EntitySubscriberInterface,
  InsertEvent,
  UpdateEvent,
  RemoveEvent,
} from 'typeorm';
// import { configEnv } from '~/@config/env';
// import { EventHubService } from '~/x-modules/@global/services/event-hub.service';
// const { IS_DOCKER_SERVER, IS_DISABLE_SQS } = configEnv();
@EventSubscriber()
export class EverythingSubscriber implements EntitySubscriberInterface {
  // private eventHubService: EventHubService;

  constructor() {
    // Tạo instance thủ công service, chú ý service này có thể phụ thuộc khác sẽ không được inject
    // this.eventHubService = new EventHubService(/* nếu cần truyền tham số */);
    // console.log('EverythingSubscriber constructor');
  }
  /**
   * Called before entity insertion.
   */
  beforeInsert(event: InsertEvent<any>) {
    // const tableName = event.metadata.tableName;
    // const msg = this.eventHubService.mappingData({
    //   table: tableName,
    //   data: [event.entity],
    //   timestamp: new Date(),
    // });
    // // console.log('msg', msg);
    // this.eventHubService.sendEvents([msg]);
  }

  /**
   * Called before entity insertion.
   */
  beforeUpdate(event: UpdateEvent<any>) {
    // const tableName = event.metadata.tableName;
    // const msg = this.eventHubService.mappingData({
    //   table: tableName,
    //   data: [event.entity],
    //   timestamp: new Date(),
    // });
    // this.eventHubService.sendEvents([msg]);
  }

  /**
   * Called before entity insertion.
   */
  beforeRemove(event: RemoveEvent<any>) {
    // console.log(`BEFORE ENTITY WITH ID ${event.entityId} REMOVED: `, event.entity);
  }

  /**
   * Called after entity insertion.
   */
  afterInsert(event: InsertEvent<any>) {
    // console.log(`AFTER ENTITY INSERTED: `, event.entity);
  }

  /**
   * Called after entity insertion.
   */
  afterUpdate(event: UpdateEvent<any>) {
    // console.log(`AFTER ENTITY UPDATED: `, event.entity);
  }

  /**
   * Called after entity insertion.
   */
  afterRemove(event: RemoveEvent<any>) {
    // console.log(`AFTER ENTITY WITH ID ${event.entityId} REMOVED: `, event.entity);
  }

  /**
   * Called after entity is loaded.
   */
  afterLoad(entity: any) {
    // console.log(`AFTER ENTITY LOADED: `, entity);
  }
}
