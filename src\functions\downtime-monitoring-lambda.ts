import { NestFactory } from '@nestjs/core';
import { Handler, Context } from 'aws-lambda';
import { AppModule } from '~/app.module';
import { ListProductionDowntime, ProductionDowntimeReq } from '~/dto/production-downtime.dto'; // <PERSON>i<PERSON><PERSON> chỉnh theo đường dẫn DTO của bạn
import { DowntimeMonitoringService } from '~/x-modules/admin/services';

let cachedApp: any = null;

async function bootstrap() {
  if (!cachedApp) {
    const app = await NestFactory.createApplicationContext(AppModule);
    await app.init();
    cachedApp = app;
  }
  return cachedApp;
}

// Lambda handler cho việc phân trang danh sách Production Downtime
export const listProductionDowntime: Handler = async (event: any, context: Context) => {
  const app = await bootstrap(); // Khởi tạo NestJS app
  const service = app.get(DowntimeMonitoringService);

  const params: ListProductionDowntime = event.queryStringParameters; // <PERSON><PERSON><PERSON> tham số từ query string

  try {
    const result = await service.pagination(params); // G<PERSON>i phương thức phân trang
    return {
      statusCode: 200,
      body: JSON.stringify(result),
      headers: {
        'Content-Type': 'application/json',
      },
    };
  } catch (error) {
    return {
      statusCode: error.status || 500,
      body: JSON.stringify({
        message: error.message || 'Internal server error',
        error: error.response?.error || error.name,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    };
  }
};

// Lambda handler cho việc tạo mới Production Downtime
export const createProductionDowntime: Handler = async (event: any, context: Context) => {
  const app = await bootstrap(); // Khởi tạo NestJS app
  const service = app.get(DowntimeMonitoringService);

  const body: ProductionDowntimeReq = JSON.parse(event.body); // Lấy dữ liệu từ body

  try {
    const result = await service.create(body); // Gọi phương thức tạo mới Production Downtime
    return {
      statusCode: 201,
      body: JSON.stringify(result),
      headers: {
        'Content-Type': 'application/json',
      },
    };
  } catch (error) {
    return {
      statusCode: error.status || 500,
      body: JSON.stringify({
        message: error.message || 'Internal server error',
        error: error.response?.error || error.name,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    };
  }
};

// Lambda handler cho việc cập nhật Production Downtime
export const updateProductionDowntime: Handler = async (event: any, context: Context) => {
  const app = await bootstrap(); // Khởi tạo NestJS app
  const service = app.get(DowntimeMonitoringService);

  const id: string = event.pathParameters.id; // Lấy id từ path parameters
  const body: ProductionDowntimeReq = JSON.parse(event.body); // Lấy dữ liệu từ body

  try {
    const result = await service.update(id, body); // Gọi phương thức cập nhật Production Downtime
    return {
      statusCode: 200,
      body: JSON.stringify(result),
      headers: {
        'Content-Type': 'application/json',
      },
    };
  } catch (error) {
    return {
      statusCode: error.status || 500,
      body: JSON.stringify({
        message: error.message || 'Internal server error',
        error: error.response?.error || error.name,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    };
  }
};

// Lambda handler cho việc lấy danh sách Process Area theo factoryId
export const getProcessArea: Handler = async (event: any, context: Context) => {
  const app = await bootstrap(); // Khởi tạo NestJS app
  const service = app.get(DowntimeMonitoringService);

  const factoryId: string = event.queryStringParameters.factoryId; // Lấy factoryId từ query string

  try {
    const result = await service.getProcessArea(factoryId); // Gọi phương thức lấy danh sách Process Area
    return {
      statusCode: 200,
      body: JSON.stringify(result),
      headers: {
        'Content-Type': 'application/json',
      },
    };
  } catch (error) {
    return {
      statusCode: error.status || 500,
      body: JSON.stringify({
        message: error.message || 'Internal server error',
        error: error.response?.error || error.name,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    };
  }
};

// Lambda handler cho việc lấy danh sách Process theo factoryId
export const getProcess: Handler = async (event: any, context: Context) => {
  const app = await bootstrap(); // Khởi tạo NestJS app
  const service = app.get(DowntimeMonitoringService);

  const factoryId: string = event.queryStringParameters.factoryId; // Lấy factoryId từ query string

  try {
    const result = await service.getProcess(factoryId); // Gọi phương thức lấy danh sách Process
    return {
      statusCode: 200,
      body: JSON.stringify(result),
      headers: {
        'Content-Type': 'application/json',
      },
    };
  } catch (error) {
    return {
      statusCode: error.status || 500,
      body: JSON.stringify({
        message: error.message || 'Internal server error',
        error: error.response?.error || error.name,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    };
  }
};

// Lambda handler cho việc lấy danh sách Machine theo factoryId
export const getMachine: Handler = async (event: any, context: Context) => {
  const app = await bootstrap(); // Khởi tạo NestJS app
  const service = app.get(DowntimeMonitoringService);

  const factoryId: string = event.queryStringParameters.factoryId; // Lấy factoryId từ query string

  try {
    const result = await service.getMachine(factoryId); // Gọi phương thức lấy danh sách Machine
    return {
      statusCode: 200,
      body: JSON.stringify(result),
      headers: {
        'Content-Type': 'application/json',
      },
    };
  } catch (error) {
    return {
      statusCode: error.status || 500,
      body: JSON.stringify({
        message: error.message || 'Internal server error',
        error: error.response?.error || error.name,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    };
  }
};

// Lambda handler cho việc lấy danh sách Reason
export const getReason: Handler = async (event: any, context: Context) => {
  const app = await bootstrap(); // Khởi tạo NestJS app
  const service = app.get(DowntimeMonitoringService);

  try {
    const result = await service.getReason(); // Gọi phương thức lấy danh sách Reason
    return {
      statusCode: 200,
      body: JSON.stringify(result),
      headers: {
        'Content-Type': 'application/json',
      },
    };
  } catch (error) {
    return {
      statusCode: error.status || 500,
      body: JSON.stringify({
        message: error.message || 'Internal server error',
        error: error.response?.error || error.name,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    };
  }
};
