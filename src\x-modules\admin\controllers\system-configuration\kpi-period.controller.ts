import { Body, Param, Query, UseGuards } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { DefController, DefDelete, DefGet, DefPost, DefPut } from '~/@core/decorator';
import {
  KPIPeriodRuleReq,
  KPIPeriodUpdateStatusDTO,
  ListKPIPeriodMonthDTO,
  ListKPIPeriodReq,
  ListKPIPeriodRuleReq,
} from '~/dto/kpi-period.dto';
import { KPIPeriodService } from '../../services';
import { Roles } from '~/@core/decorator/roles.decorator';
import { RoleGuard } from '~/@systems/guard/role.guard';

@ApiTags('KPI Period')
@DefController('kpi-period')
export class KPIPeriodController {
  constructor(private readonly service: KPIPeriodService) {}

  @ApiOperation({
    summary: 'Danh sách rule kỳ KPI',
    description: '<PERSON><PERSON><PERSON> danh sách rule kỳ KPI',
  })
  @DefGet('rule/find')
  @Roles('/system-configuration/kpi-period', 'View')
  @UseGuards(RoleGuard)
  async findRule() {
    return this.service.findRule();
  }

  @ApiOperation({
    summary: 'Danh sách phân trang rule kỳ KPI',
    description: 'Lấy danh sách phân trang rule kỳ KPI',
  })
  @DefGet('rule')
  @Roles('/system-configuration/kpi-period', 'View')
  @UseGuards(RoleGuard)
  async listRule(@Query() params: ListKPIPeriodRuleReq) {
    return this.service.listRule(params);
  }

  @ApiOperation({
    summary: 'Chi tiết rule kỳ KPI',
    description: 'Lấy thông tin chi tiết rule kỳ KPI.',
  })
  @DefGet('rule/:id')
  @Roles('/system-configuration/kpi-period', 'View')
  @UseGuards(RoleGuard)
  async detailRule(@Param('id') id: string) {
    return this.service.detailRule(id);
  }

  @ApiOperation({ summary: 'Tạo Rule Kỳ KPI', description: 'Tạo Rule Kỳ KPI' })
  @DefPost('rule')
  @Roles('/system-configuration/kpi-period', 'View')
  @UseGuards(RoleGuard)
  async createRule(@Body() body: KPIPeriodRuleReq) {
    return this.service.createRule(body);
  }

  @ApiOperation({ summary: 'Cập nhật rule kỳ KPI', description: 'Cập nhật thông tin rule kỳ KPI.' })
  @DefPut('rule/:id')
  @Roles('/system-configuration/kpi-period', 'View')
  @UseGuards(RoleGuard)
  async updateRule(@Param('id') id: string, @Body() body: KPIPeriodRuleReq) {
    return this.service.updateRule(id, body);
  }

  @ApiOperation({ summary: 'Xóa rule kỳ KPI', description: 'Xóa rule kỳ KPI theo id.' })
  @DefDelete('rule/:id')
  @Roles('/system-configuration/kpi-period', 'View')
  @UseGuards(RoleGuard)
  async deleteRule(@Param('id') id: string) {
    return this.service.deleteRule(id);
  }

  @ApiOperation({
    summary: 'Danh sách kỳ KPI',
    description: 'Lấy danh sách kỳ KPI',
  })
  @DefGet('find')
  @Roles('/system-configuration/kpi-period', 'View')
  @UseGuards(RoleGuard)
  async find() {
    return this.service.find();
  }

  @ApiOperation({
    summary: 'Danh sách phân trang kỳ KPI',
    description: 'Lấy danh sách phân trang kỳ KPI',
  })
  @DefGet('')
  @Roles('/system-configuration/kpi-period', 'View')
  @UseGuards(RoleGuard)
  async list(@Query() params: ListKPIPeriodReq) {
    return this.service.list(params);
  }

  @ApiOperation({
    summary: 'Danh sách phân trang rule kỳ KPI',
    description: 'Lấy danh sách phân trang rule kỳ KPI',
  })
  @DefGet('list-kpi-period-group-by-site')
  @Roles('/system-configuration/kpi-period', 'View')
  @UseGuards(RoleGuard)
  async listKPIPeriodMonthGroupBySite(@Query() params: ListKPIPeriodMonthDTO) {
    return this.service.listKPIPeriodMonthGroupBySite(params);
  }

  @ApiOperation({
    summary: 'Lấy danh sách ca làm',
    description: 'Lấy danh sách ca làm',
  })
  @DefGet('load-data-shift-options')
  @Roles('/system-configuration/kpi-period', 'View')
  @UseGuards(RoleGuard)
  async listShiftOptions() {
    return this.service.listShiftOptions();
  }

  @ApiOperation({
    summary: 'Chi tiết kỳ KPI',
    description: 'Lấy thông tin chi tiết kỳ KPI.',
  })
  @DefGet(':id')
  @Roles('/system-configuration/kpi-period', 'View')
  @UseGuards(RoleGuard)
  async detail(@Param('id') id: string) {
    return this.service.detail(id);
  }

  @ApiOperation({ summary: 'Cập nhật status kỳ KPI', description: 'Cập nhật status kỳ KPI.' })
  @DefPut('status')
  @Roles('/system-configuration/kpi-period', 'View')
  @UseGuards(RoleGuard)
  async updateStatus(@Body() body: KPIPeriodUpdateStatusDTO) {
    return this.service.updateStatus(body);
  }

  // @ApiOperation({ summary: 'Cập nhật máy', description: 'Cập nhật thông tin của một máy hiện có.' })
  // @DefPut(':id')
  // async update(@Param('id', new ParseUUIDPipe()) id: string, @Body() body: KPIPeriodReq) {
  //   return this.service.update(id, body);
  // }

  // @ApiOperation({ summary: 'Xóa máy', description: 'Xóa một máy theo UUID.' })
  // @DefDelete(':id')
  // async delete(@Param('id', new ParseUUIDPipe()) id: string) {
  //   return this.service.delete(id);
  // }
}
