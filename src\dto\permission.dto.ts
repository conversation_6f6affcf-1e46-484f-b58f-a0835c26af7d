import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsArray, IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { PageRequest } from '~/@systems/utils';

export class PermissionCreateDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  name: string;

  @IsString()
  code: string;

  @ApiProperty()
  isActive: boolean;

  @IsString()
  description: string;

  @IsOptional()
  @IsArray()
  listUserId: string[];
}

export class PermissionUpdateDto extends PermissionCreateDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  id: string;
}

export class UserPermissionDto {
  @IsString()
  id: string;

  @IsString()
  validFrom: Date;

  @IsString()
  validTo: Date;

  @ApiProperty({ description: 'Trạng thái hoạt động' })
  @IsOptional()
  isActive: boolean;
}

export class PermissionUpdateUserDto {
  @IsString()
  id: string;

  @Type(() => UserPermissionDto)
  listUser: UserPermissionDto[];
}

export class GetRoleDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  id: string;
}

export class PermissionUpdateRoleDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  id: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  listPermission: string;
}

export class PermissionGetUserRoleDto {
  @ApiProperty()
  @IsNotEmpty()
  @IsString()
  userId: string;
}
export class PermissionFilter {
  @ApiProperty({ description: 'Tên Nhóm Quyền' })
  @IsOptional()
  name?: string;

  @ApiProperty({ description: 'Code nhóm quyền' })
  @IsOptional()
  code?: string;

  @ApiProperty({ description: 'Trạng thái hoạt động' })
  @IsOptional()
  isActive?: boolean;
}

export class PermissionPageReq extends PageRequest {
  @ApiProperty({ description: 'Trạng thái hoạt động' })
  @IsOptional()
  where?: PermissionFilter;
}
export class PaginationDto {
  @ApiProperty()
  where: any;
  @ApiProperty()
  skip: number;
  @ApiProperty()
  take: number;
}
