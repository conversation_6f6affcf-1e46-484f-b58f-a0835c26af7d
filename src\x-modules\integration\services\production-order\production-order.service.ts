import { HttpStatus, Injectable } from '@nestjs/common';
import { Between, In, LessThanOrEqual, MoreThanOrEqual } from 'typeorm';
import { BindRepo } from '~/@core/decorator';
import { EHttpStatusMessage } from '~/@core/network';
import { ErpBusinessException } from '~/@systems/exceptions/dto/erp-exception';
import { ORGANIZATION_LEVEL } from '~/common/constants';
import {
  GeneralDataDetailRepo,
  ItemRepo,
  OrganizationUnitRepo,
  ProductionOrderRepo,
  ShiftRepo,
} from '~/repositories/primary';

@Injectable()
export class ERPProductionOrderIntegrateService {
  constructor() {}
  @BindRepo(OrganizationUnitRepo)
  private readonly organizationUnitRepo: OrganizationUnitRepo;
  @BindRepo(ItemRepo)
  private readonly itemRepo: ItemRepo;
  @BindRepo(ShiftRepo)
  private readonly shiftRepo: ShiftRepo;
  @BindRepo(ProductionOrderRepo)
  private readonly productionOrderRepo: ProductionOrderRepo;
  @BindRepo(GeneralDataDetailRepo)
  private readonly generalDataDetailRepo: GeneralDataDetailRepo;

  async list(data: any) {
    const whereCon: any = {
      planStartDate: Between(data.fromProductionDate, data.toProductionDate),
    };
    const err = [];
    if (!data.fromProductionDate || isNaN(new Date(data.fromProductionDate).getTime())) {
      err.push({
        field: 'From Production Date',
        message: 'Invalid',
      });
    }
    if (!data.toProductionDate || isNaN(new Date(data.toProductionDate).getTime())) {
      err.push({
        field: 'To Production Date',
        message: 'Invalid',
      });
    }
    if (
      data.fromUpdatedDate &&
      data?.fromUpdatedDate?.length > 0 &&
      isNaN(new Date(data.fromUpdatedDate).getTime())
    ) {
      err.push({
        field: 'From Updated Date',
        message: 'Invalid',
      });
    }
    if (
      data.toUpdatedDate &&
      data?.toUpdatedDate?.length > 0 &&
      isNaN(new Date(data.toUpdatedDate).getTime())
    ) {
      err.push({
        field: 'To Updated Date',
        message: 'Invalid',
      });
    }
    if (err.length > 0) {
      throw new ErpBusinessException(
        EHttpStatusMessage[HttpStatus.BAD_REQUEST],
        err,
        HttpStatus.BAD_REQUEST,
      );
    }
    let site;
    if (data.site) {
      site = await this.organizationUnitRepo.find({
        where: { code: data.site, levelGeneralDataDetailCode: ORGANIZATION_LEVEL.SITE },
      });
    }
    let factory;
    if (data.factory) {
      factory = await this.organizationUnitRepo.find({
        where: { code: data.factory, levelGeneralDataDetailCode: ORGANIZATION_LEVEL.FACTORY },
      });
    }
    let productionLine;
    if (data.productionLine) {
      productionLine = await this.organizationUnitRepo.find({
        where: { code: data.productionLine, levelGeneralDataDetailCode: ORGANIZATION_LEVEL.LINE },
      });
    }
    let shift;
    if (data.shift) {
      shift = await this.shiftRepo.findOne({ where: { code: data.shift } });
    }
    if (data.site && data.site?.length > 0 && site?.length === 0)
      err.push({ field: 'Site', message: 'Invalid' });
    if (data.factory && data.factory?.length > 0 && factory?.length === 0)
      err.push({ field: 'Factory', message: 'Invalid' });
    if (data.productionLine && data.productionLine?.length > 0 && productionLine?.length === 0)
      err.push({ field: 'Production Line', message: 'Invalid' });
    if (data.shift && data.shift?.length > 0 && !shift)
      err.push({ field: 'Shift', message: 'Invalid' });
    if (err.length > 0) {
      throw new ErpBusinessException(
        EHttpStatusMessage[HttpStatus.UNPROCESSABLE_ENTITY],
        err,
        HttpStatus.UNPROCESSABLE_ENTITY,
      );
    }

    if (site && site?.length > 0) whereCon.siteId = In(site.map((i) => i.id));
    if (factory && factory?.length > 0) whereCon.factoryId = In(factory.map((i) => i.id));
    if (productionLine && productionLine?.length > 0)
      whereCon.lineId = In(productionLine.map((i) => i.id));
    if (shift) whereCon.shiftId = shift.id;
    if (data.fromUpdatedDate && data.fromUpdatedDate.length > 0)
      whereCon.updatedDate = MoreThanOrEqual(data.fromUpdatedDate);
    if (data.toUpdatedDate && data.toUpdatedDate.length > 0)
      whereCon.updatedDate = LessThanOrEqual(data.toUpdatedDate);
    if (data.orderNo && data.orderNo.length > 0) whereCon.orderNo = data.orderNo;
    const res = await this.productionOrderRepo.find({
      where: whereCon,
      select: [
        'id',
        'orderNo',
        'quantity',
        'trxUom',
        'recipeCode',
        'recipeVersion',
        'siteId',
        'factoryId',
        'lineId',
        'planStartDate',
        'createdDate',
        'updatedDate',
        'orderStatus',
        'itemId',
        'shiftId',
      ],
    });
    const dictItemCode: any = {};
    {
      const lstItem = await this.itemRepo.find({
        where: { id: In(res.map((i) => i.itemId)) },
        select: ['code', 'id'],
      });
      lstItem.forEach((i) => (dictItemCode[i.id] = i.code));
    }

    const dictShiftCode: any = {};
    {
      const lstShift = await this.shiftRepo.find({
        where: { id: In(res.map((i) => i.shiftId)) },
        select: ['code', 'id'],
      });
      lstShift.forEach((i) => (dictShiftCode[i.id] = i.code));
    }

    const dictSiteCode: any = {};
    {
      const lstSite = await this.organizationUnitRepo.find({
        where: { levelGeneralDataDetailCode: ORGANIZATION_LEVEL.SITE },
      });
      lstSite.forEach((i) => (dictSiteCode[i.id] = i.code));
    }

    const dictFactoryCode: any = {};
    {
      const lstFactory = await this.organizationUnitRepo.find({
        where: { levelGeneralDataDetailCode: ORGANIZATION_LEVEL.FACTORY },
      });
      lstFactory.forEach((i) => (dictFactoryCode[i.id] = i.code));
    }

    const dictLineCode: any = {};
    {
      const lstLine = await this.organizationUnitRepo.find({
        where: { levelGeneralDataDetailCode: ORGANIZATION_LEVEL.LINE },
      });
      lstLine.forEach((i) => (dictLineCode[i.id] = i.code));
    }

    const dictStatus: any = {};
    {
      const lstStatus = await this.generalDataDetailRepo.find({
        where: { id: In(res.map((i) => i.orderStatus)) },
      });
      lstStatus.forEach((i) => (dictStatus[i.id] = i.code));
    }

    const mapRes = res.map((i) => ({
      id: i.id,
      status: dictStatus[i.orderStatus],
      orderNo: i.orderNo,
      itemCode: dictItemCode[i.itemId],
      quantity: i.quantity,
      uom: i.trxUom,
      recipeCode: i.recipeCode,
      recipeVersion: i.recipeVersion,
      site: dictSiteCode[i.siteId],
      factory: dictFactoryCode[i.factoryId],
      shift: dictShiftCode[i.shiftId],
      productionLine: dictLineCode[i.lineId],
      productionDate: i.planStartDate,
      createdDate: i.createdDate,
      updatedDate: i.updatedDate,
    }));

    // const grouped: any = {};
    // mapRes.forEach((ref) => {
    //   const created = ref.createdDate;
    //   const updated = ref.updatedDate;
    //   const key = `${created}_${updated}`;
    //   if (!grouped[key]) {
    //     grouped[key] = {
    //       created_at: created,
    //       updated_at: updated,
    //       refs: [],
    //     };
    //   }
    //   grouped[key].refs.push(ref);
    // });
    return {
      code: '200',
      status: 'success',
      message: 'successfully',
      data: {
        created_at: new Date(),
        updated_at: new Date(),
        refs: mapRes,
      },
    };
  }
}
