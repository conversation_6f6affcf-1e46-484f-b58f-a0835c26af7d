import { forwardRef, Inject, Injectable } from '@nestjs/common';
import { isNumber } from 'class-validator';
import moment from 'moment';
import { Connection, MoreThan } from 'typeorm';
import { validate as isUUID, v4 as uuidv4 } from 'uuid';
import { BindRepo, DefTransaction } from '~/@core/decorator';
import { plainToInstance } from 'class-transformer';
import { BusinessException } from '~/@systems/exceptions';
import { CzCodeCheckReq, FilterReq, CzCodeCheckRepositories  } from '~/dto/cz-code.dto';
import {
  UrlRepo,
  UomConventionRepo,
  UniqueCodeActivationRepo,
  ProductionOrderMaterialRepo,
  ProductionOrderUniqueCodeDetailRepo,
  OrganizationUnitRepo,
  ProductionOrderRepo,
} from '~/repositories/primary';
@Injectable()
export class CzCodeService {
  constructor( private readonly connection: Connection ) {}

  @BindRepo(UrlRepo)
  private urlRepo: UrlRepo;

  @BindRepo(UomConventionRepo)
  private uomConventionRepo: UomConventionRepo;

  @BindRepo(UniqueCodeActivationRepo)
  private uniqueCodeActivationRepo: UniqueCodeActivationRepo;

  @BindRepo(ProductionOrderUniqueCodeDetailRepo)
  private productionOrderUniqueCodeDetailRepo: ProductionOrderUniqueCodeDetailRepo;

  @BindRepo(OrganizationUnitRepo)
  private OrganUnit: OrganizationUnitRepo;
  @BindRepo(ProductionOrderRepo)
  private productionOrderRepo: ProductionOrderRepo;

  
  @BindRepo(ProductionOrderMaterialRepo)
  private productionOrderMaterialRepo: ProductionOrderMaterialRepo;

  async findCzCodeByCode(code: string) {
    if(!code || code === '') return undefined;

    const query = `
      select 
        poucd.*,
        i."code" as itemCode,
        i."name" as itemName,
        po."orderNo",
        po."customerOrderId",
        po."customerOrderNo",
        po."customerOrderQty",
        po."gtin",
        po."customerProductCode",
        po."customerProductName",
        po."customerShortName",
        CAST(po.quantity AS INTEGER) as quantity,
        (
          SELECT CAST(COUNT(*) AS INTEGER)
          FROM unique_code_activation uca
          WHERE uca."orderId" = po.id AND uca."status" = 1
        ) AS "czCodeActiveTotal",
        ousi.code as "siteCode",
        ouf.code as "factoryCode",
        ouline.code as "lineCode",
        po."lotNumber",
        i."baseUnit",
        i.id as "itemId",
        i."shelfLifeDay"
      from production_order_unique_code_detail poucd 
      INNER join production_order po on po.id = poucd."orderId" 
      INNER join item i on i.id = po."itemId" 
      inner join organization_units ousi on ousi.id = po."siteId" 
      inner join organization_units ouf on ouf.id = po."factoryId" 
      inner join organization_units ouline on ouline.id = po."lineId" 
      where poucd."uniqueCode" = $1
    `
    const result = await this.connection.query(query,[code]);
    return result?.length > 0 ? result[0] : undefined
  }

  // Hàm quy đổi UOM 
  findSingleIntermediate(fromUom: any, toUom: any, lstUomConverse: any) {
    const unitMap = {};
    const conversionMap = {};
  
    lstUomConverse.forEach(({ fromUnit, toUnit, conversion }) => {
      if (!unitMap[fromUnit]) unitMap[fromUnit] = new Set();
      if (!unitMap[toUnit]) unitMap[toUnit] = new Set();
  
      unitMap[fromUnit].add(toUnit);
      unitMap[toUnit].add(fromUnit);
      conversionMap[`${fromUnit}->${toUnit}`] = conversion;
      conversionMap[`${toUnit}->${fromUnit}`] = 1 / conversion;
    });
  
    const visited = new Set();
    const queue = [{ unit: fromUom, rate: 1 }];
  
    while (queue.length > 0) {
      const { unit, rate } = queue.shift();
      if (unit === toUom) { return rate }
      visited.add(unit);
  
      const neighbors = unitMap[unit] || new Set();
      for (const neighbor of neighbors) {
        if (!visited.has(neighbor)) {
          const conversionRate = conversionMap[`${unit}->${neighbor}`];
          queue.push({ unit: neighbor, rate: rate * conversionRate });
        }
      }
    }
  
    return null;
  }

  // Hàm tính actualTrxQty by production_order_material
  async handleActualTrxQty(itemId: string, baseUnit: string, orderId: string) {
    const uomConventionData = await this.uomConventionRepo.find({
      where: { itemId: itemId }
    })
    const prodOrderMaterialData = await this.productionOrderMaterialRepo.find({
      where: { orderId: orderId, lineType: 1}
    })

    if(!prodOrderMaterialData || prodOrderMaterialData?.length === 0) return 0;

    let result = 0;
    prodOrderMaterialData.forEach(prodOrderMaterial => {
      const UOMConversionData = this.findSingleIntermediate(prodOrderMaterial?.trxUomCode, baseUnit, uomConventionData) || 1  
      const actualTrxQtyValue = !isNaN(Number(prodOrderMaterial?.actualTrxQty)) ? Number(prodOrderMaterial?.actualTrxQty) * UOMConversionData : Number(prodOrderMaterial?.actualTrxQty)
      result += actualTrxQtyValue
    })

    return result;
  }

  handleSuccessRep(code: Number, body: CzCodeCheckRepositories) {
    const handleBody = plainToInstance(CzCodeCheckRepositories, body, {excludeExtraneousValues: true})
    return {
        statusCode: code,
        message: 'Query CzCode successfull !',
        body: handleBody
    }
  }

  async czCodeCheckService(data: CzCodeCheckReq) {
    try {
      const czCodeData = await this.findCzCodeByCode(data?.czCode)
      if(!czCodeData) return this.handleSuccessRep(404, {})

      // Handle actualTrxQty by production_order_material
      const actualTrxQty = await this.handleActualTrxQty(czCodeData?.itemId, czCodeData?.baseUnit, czCodeData?.orderId)
      Object.assign(czCodeData, {actualTrxQty})

      // Handle tiếp thông tin cz code active
      const czCodeActiveData = await this.uniqueCodeActivationRepo.findOne({
        where: { uniqueCode : czCodeData?.uniqueCode }
      })
      const czCodeActiveObj = {
        status: czCodeActiveData?.status ? czCodeActiveData.status : 0,
        activationDate: czCodeActiveData?.activateDatetime ? moment(czCodeActiveData.activateDatetime, "YYYY-MM-DD HH:mm:ss.SSS Z").format('DD/MM/YYYY  HH:mm:ss') : '',
        productionDate: czCodeActiveData?.productionDate ? moment(czCodeActiveData.productionDate, "YYYY-MM-DD HH:mm:ss.SSS Z").format('DD/MM/YYYY') : '',
        expiredDate: czCodeActiveData?.expiredDate ? moment(czCodeActiveData.expiredDate, "YYYY-MM-DD HH:mm:ss.SSS Z").format('DD/MM/YYYY') : '',
      }
      Object.assign(czCodeData, czCodeActiveObj)

      return this.handleSuccessRep(200, czCodeData)
    } catch (error) {
      return {
        statusCode: 404,
        message: error.message || 'Failed to find CzCode',
        body: {},
      };
    }
  }

  // viết api lấy danh sách org unit theo production line được chọn dựa vào parentId = với id được truyền xuống
  async getProcessArea(params: FilterReq) {
    try {
      const result = await this.OrganUnit.find({
        where: {
          parentId: params.id,
        },
      });
      return result.map((item) => ({
        value: item.id,
        label: item.code + ' - ' + item.name,
        name: item.name,
        code: item.code,
      }));
    } catch (error) {
      throw new BusinessException(error.message);
    }
  }

  async getProductionOrder(params: FilterReq) {
    try {
      const query = `
        select po.*, i."name" as "itemName"
        from production_order po 
        left join item i on i.id = po."itemId" 
        where "processAreaId" = $1
        AND "planStartDate" >= CURRENT_DATE - INTERVAL '1 day'
      `
      const result = await this.connection.query(query,[params.id]);
      return result;
    } catch (error) {
      throw new BusinessException(error.message);
    }
  }
}
