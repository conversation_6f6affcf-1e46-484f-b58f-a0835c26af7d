import {
  BadRequestException,
  ConflictException,
  Injectable,
  NotFoundException,
} from '@nestjs/common';
import { ILike, In, Like } from 'typeorm';
import { BindRepo, DefTransaction } from '~/@core/decorator';
import {
  KPIPeriodRuleReq,
  KPIPeriodUpdateStatusDTO,
  ListKPIPeriodMonthDTO,
  ListKPIPeriodReq,
  ListKPIPeriodRuleReq,
} from '~/dto/kpi-period.dto';
import {
  GeneralDataDetailRepo,
  GeneralDataRepo,
  KPIPeriodRepo,
  KPIPeriodRuleRepo,
} from '~/repositories/primary';
import { adminSessionContext } from '../../admin-session.context';
import { KPIPeriodEntity } from '~/entities/primary';
import { ShiftRepo } from '~/repositories/primary/shift.repo';
import dayjs from 'dayjs';
import timezone from 'dayjs/plugin/timezone';
dayjs.extend(timezone);
@Injectable()
export class KPIPeriodService {
  constructor() {}

  @BindRepo(KPIPeriodRepo)
  private repo: KPIPeriodRepo;

  @BindRepo(KPIPeriodRuleRepo)
  private ruleRepo: KPIPeriodRuleRepo;

  @BindRepo(GeneralDataDetailRepo)
  private generalDataDetailRepo: GeneralDataDetailRepo;

  @BindRepo(GeneralDataRepo)
  private generalDataRepo: GeneralDataRepo;

  @BindRepo(ShiftRepo)
  private shiftRepo: ShiftRepo;

  async find() {
    return this.repo.find({ order: { status: 'DESC', createdDate: 'DESC' } });
  }

  async list(params: ListKPIPeriodReq) {
    const whereCycle: any = {};
    if (params.cycleTypeName) whereCycle.name = ILike(`%${params.cycleTypeName}%`);
    const lstCycleType = await this.generalDataDetailRepo.find({ where: whereCycle });
    const whereCon: any = {};
    if (lstCycleType.length > 0) whereCon.cycleType = In(lstCycleType.map((item) => item.id));
    if (params.cycleType) whereCon.cycleType = params.cycleType;
    if (params.periodName) whereCon.periodName = ILike(`%${params.periodName}%`);
    if (params.startDate) whereCon.startDate = params.startDate;
    if (params.endDate) whereCon.endDate = params.endDate;
    if (params.siteId) whereCon.siteId = params.siteId;
    if (params.status) whereCon.status = params.status;
    const result = await this.repo.findPagination(
      {
        where: whereCon,
        order: { cycleType: 'ASC', periodName: 'ASC', status: 'DESC', createdDate: 'DESC' },
      },
      params,
    );
    const lstCycle = await this.generalDataDetailRepo.find({
      where: { id: In(result.data.map((item) => item.cycleType)) },
    });
    const lstRule = await this.ruleRepo.find({
      where: { id: In(result.data.map((item) => item.kpiPeriodRuleId)) },
    });
    result.data.forEach((element: any) => {
      element.cycleTypeName = lstCycle.find((item) => item.id === element.cycleType)?.name || '';
      element.periodRuleName =
        lstRule.find((item) => item.id === element.kpiPeriodRuleId)?.periodRuleName || '';
    });
    return result;
  }

  async detail(id: string) {
    if (!id) throw new BadRequestException('ID don’t exist');
    return this.repo.findOneOrFail({ where: { id } });
  }

  async findRule() {
    return this.ruleRepo.find({ order: { createdDate: 'DESC', status: 1 } });
  }

  async listRule(params: ListKPIPeriodRuleReq) {
    const whereCycle: any = {};

    const whereCon: any = {};
    if (params.periodRuleName) whereCon.periodRuleName = ILike(`%${params.periodRuleName}%`);
    if (params.startDate) whereCon.startDate = params.startDate;
    if (params.endDate) whereCon.endDate = params.endDate;
    if (params.startMonth) whereCon.startMonth = params.startMonth;
    if (params.startShift) whereCon.startShift = params.startShift;
    if (params.endShift) whereCon.endShift = params.endShift;
    if (params.cycleType) whereCon.cycleType = params.cycleType;
    if (params.status) whereCon.status = params.status;
    const result = await this.ruleRepo.findPagination(
      { where: whereCon, order: { createdDate: 'DESC' } },
      params,
    );
    const lstShiftId = [
      ...result.data.map((item) => item.startShift),
      ...result.data.map((item) => item.endShift),
    ];
    const lstShift = await this.shiftRepo.find({ where: { id: In(lstShiftId) } });
    const lstCycleType = await this.generalDataDetailRepo.find({
      where: { id: In(result.data.map((i) => i.cycleType)) },
    });
    result.data.forEach((item: any) => {
      const startShift = lstShift.find((shift) => shift.id === item.startShift);
      const endShift = lstShift.find((shift) => shift.id === item.endShift);
      item.startShiftCode = startShift.code + ' - ' + startShift.description;
      item.endShiftCode = endShift.code + ' - ' + endShift.description;
      const cycleType = lstCycleType.find((i) => i.id === item.cycleType);
      item.cycleTypeName = cycleType.code + ' - ' + cycleType.name;
      item.status = item.status === 1 ? true : false;
    });
    return result;
  }

  async detailRule(id: string) {
    if (!id) throw new BadRequestException('ID don’t exist');
    const data: any = await this.ruleRepo.findOneOrFail({ where: { id: id } });
    if (data) data.status = data.status === 1 ? true : false;
    return data;
  }

  @DefTransaction()
  async createRule(body: KPIPeriodRuleReq) {
    const { userId: userId } = adminSessionContext;
    const today = new Date();
    const filterBody = Object.fromEntries(
      Object.entries(body).map(([key, value]) => [key, value === undefined ? null : value]),
    );
    delete filterBody.status;
    const existRule = await this.ruleRepo.findOne({
      where: filterBody,
    });
    if (existRule) throw new ConflictException(`KPI Period Rule already exists!`);
    const cycle = await this.generalDataDetailRepo.findOne({ where: { id: filterBody.cycleType } });
    const shift = await this.shiftRepo.find({
      where: { id: In([body.startShift, body.endShift]) },
    });
    const startShift = shift.find((i) => i.id === body.startShift);
    const endShift = shift.find((i) => i.id === body.endShift);
    const [startShiftHours, startShiftMinutes, startShiftSeconds] = startShift.startTime
      .split(':')
      .map(Number);
    const [endShiftHours, endShiftMinutes, endShiftSeconds] = endShift.endTime
      .split(':')
      .map(Number);
    const rule = await this.ruleRepo.save({
      ...body,
      cycleCode: cycle?.code || '',
      cycleName: cycle?.name || '',
      startShiftCode: shift.find((i) => i.id === body.startShift)?.code || '',
      endShiftCode: shift.find((i) => i.id === body.endShift)?.code || '',
      status: body.status ? 1 : 0,
      // createdBy: 'admin',
      // updatedBy: 'admin',
      createdDate: today,
    });
    if (rule && rule.status === 1) {
      const year = new Date().getFullYear() - 1;
      const nextYear = year + 1;
      const cycleType = await this.generalDataDetailRepo.findOne({ where: { id: rule.cycleType } });

      switch (Number.parseInt(cycleType.code)) {
        // Case monthly
        case 1: {
          const dictKPIPeriod: any = {};
          {
            const lstKPIPeriod = await this.repo.find();
            lstKPIPeriod.forEach(
              (item) => dictKPIPeriod[item.periodName + item.startDate + item.endDate],
            );
          }
          const lstKPIPeriodEntity = [];
          for (let i = 1; i <= 12; i++) {
            const startMonth = (i - 2 + 12) % 12;
            const startYear = startMonth === 11 ? year : nextYear;
            const startDate = dayjs(
              new Date(
                startYear,
                startMonth, // nhớ: tháng 0-based
                rule.startDate,
                startShiftHours,
                startShiftMinutes,
                startShiftSeconds,
              ),
            )
              .tz('Asia/Ho_Chi_Minh', true)
              .toDate();
            const endMonth = (startMonth + 1) % 12;
            const endYear = endMonth === 0 ? startYear + 1 : startYear;
            const endDate = dayjs(
              new Date(
                endYear,
                endMonth, // nhớ: tháng 0-based
                rule.endDate,
                endShiftHours,
                endShiftMinutes,
                endShiftSeconds,
              ),
            )
              .tz('Asia/Ho_Chi_Minh', true)
              .toDate();
            const periodName = `${(endMonth + 1).toString().padStart(2, '0')}/${endYear}`;
            if (!dictKPIPeriod[periodName + startDate + endDate]) {
              const kpiPeriodEntity = new KPIPeriodEntity();
              kpiPeriodEntity.periodName = periodName;
              kpiPeriodEntity.startDate = startDate;
              kpiPeriodEntity.endDate = endDate;
              kpiPeriodEntity.cycleType = rule.cycleType;
              kpiPeriodEntity.status = 0;
              kpiPeriodEntity.kpiPeriodRuleId = rule.id;
              // kpiPeriodEntity.createdBy = 'admin';
              // kpiPeriodEntity.updatedBy = 'admin';
              kpiPeriodEntity.createdDate = today;
              lstKPIPeriodEntity.push(kpiPeriodEntity);
            }
          }
          await this.repo.insert(lstKPIPeriodEntity);
          break;
        }
        // Case Quarter
        case 2: {
          const dictKPIPeriod: any = {};
          {
            const lstKPIPeriod = await this.repo.find();
            lstKPIPeriod.forEach(
              (item) => dictKPIPeriod[item.periodName + item.startDate + item.endDate],
            );
          }
          const lstKPIPeriodEntity = [];
          let startMonth, startYear, endMonth, endYear, quarter;
          switch (rule.startMonth) {
            case 12:
              quarter = 1;
              startMonth = 11; // Tháng 12 của năm trước
              startYear = year;
              endMonth = 2; // Tháng 3 của năm hiện tại
              endYear = nextYear;
              break;
            case 3:
              quarter = 2;
              startMonth = 2; // Tháng 3
              startYear = nextYear;
              endMonth = 5; // Tháng 6
              endYear = nextYear;
              break;
            case 6:
              quarter = 3;
              startMonth = 5; // Tháng 6
              startYear = nextYear;
              endMonth = 8; // Tháng 9
              endYear = nextYear;
              break;
            case 9:
              quarter = 4;
              startMonth = 8; // Tháng 9
              startYear = nextYear;
              endMonth = 11; // Tháng 12
              endYear = nextYear;
              break;
          }
          const startDate = dayjs(
            new Date(
              startYear,
              startMonth, // nhớ: tháng 0-based
              rule.startDate,
              startShiftHours,
              startShiftMinutes,
              startShiftSeconds,
            ),
          )
            .tz('Asia/Ho_Chi_Minh', true)
            .toDate();
          const endDate = dayjs(
            new Date(
              endYear,
              endMonth, // nhớ: tháng 0-based
              rule.endDate,
              endShiftHours,
              endShiftMinutes,
              endShiftSeconds,
            ),
          )
            .tz('Asia/Ho_Chi_Minh', true)
            .toDate();
          const periodName = `Q${quarter}/${nextYear}`;
          if (!dictKPIPeriod[periodName + startDate + endDate]) {
            const kpiPeriodEntity = new KPIPeriodEntity();
            kpiPeriodEntity.periodName = periodName;
            kpiPeriodEntity.startDate = startDate;
            kpiPeriodEntity.endDate = endDate;
            kpiPeriodEntity.cycleType = rule.cycleType;
            kpiPeriodEntity.status = 0;
            kpiPeriodEntity.kpiPeriodRuleId = rule.id;
            // kpiPeriodEntity.createdBy = 'admin';
            // kpiPeriodEntity.updatedBy = 'admin';
            kpiPeriodEntity.createdDate = today;
            lstKPIPeriodEntity.push(kpiPeriodEntity);
          }

          await this.repo.insert(lstKPIPeriodEntity);
          break;
        }
        // Case Yearly
        case 3: {
          const month = rule.startMonth - 1;
          const startDate = dayjs(
            new Date(
              year,
              month, // nhớ: tháng 0-based
              rule.startDate,
              startShiftHours,
              startShiftMinutes,
              startShiftSeconds,
            ),
          )
            .tz('Asia/Ho_Chi_Minh', true)
            .toDate();
          const endDate = dayjs(
            new Date(
              nextYear,
              month, // nhớ: tháng 0-based
              rule.endDate,
              endShiftHours,
              endShiftMinutes,
              endShiftSeconds,
            ),
          )
            .tz('Asia/Ho_Chi_Minh', true)
            .toDate();
          const kpiPeriod = await this.repo.findOne({
            where: { periodName: Like(nextYear), startDate: startDate, endDate: endDate },
          });
          if (!kpiPeriod) {
            const kpiPeriodEntity = new KPIPeriodEntity();
            kpiPeriodEntity.periodName = nextYear.toString();
            kpiPeriodEntity.startDate = startDate;
            kpiPeriodEntity.endDate = endDate;
            kpiPeriodEntity.cycleType = rule.cycleType;
            kpiPeriodEntity.status = 0;
            kpiPeriodEntity.kpiPeriodRuleId = rule.id;
            // kpiPeriodEntity.createdBy = 'admin';
            // kpiPeriodEntity.updatedBy = 'admin';
            kpiPeriodEntity.createdDate = today;
            await this.repo.save(kpiPeriodEntity);
          }
          break;
        }
        default:
          break;
      }
    }
    return { message: 'Create successfully', data: rule };
  }

  @DefTransaction()
  async updateRule(id: string, body: KPIPeriodRuleReq, yearIn?: number) {
    const { userId: userId } = adminSessionContext;
    const today = new Date();
    if (!id) throw new BadRequestException('ID don’t exist');
    const existRule = await this.ruleRepo.findOne({
      where: {
        startDate: body.startDate,
        endDate: body.endDate,
        startShift: body.startShift,
        endShift: body.endShift,
        startMonth: body.startMonth,
        cycleType: body.cycleType,
      },
    });
    if (existRule && existRule.id !== id)
      throw new ConflictException(`KPI Period Rule already exists!`);
    const cycle = await this.generalDataDetailRepo.findOne({ where: { id: body.cycleType } });
    const shift = await this.shiftRepo.find({
      where: { id: In([body.startShift, body.endShift]) },
    });
    const startShift = shift.find((i) => i.id === body.startShift);
    const endShift = shift.find((i) => i.id === body.endShift);
    const [startShiftHours, startShiftMinutes, startShiftSeconds] = startShift.startTime
      .split(':')
      .map(Number);
    const [endShiftHours, endShiftMinutes, endShiftSeconds] = endShift.endTime
      .split(':')
      .map(Number);
    const { affected } = await this.ruleRepo.update(
      { id },
      {
        ...body,
        cycleCode: cycle?.code || '',
        cycleName: cycle?.name || '',
        startShiftCode: shift.find((i) => i.id === body.startShift)?.code || '',
        endShiftCode: shift.find((i) => i.id === body.endShift)?.code || '',
        status: body.status === true ? 1 : 0,
        // updatedBy: 'admin',
        updatedDate: today,
      },
    );
    if (!affected) throw new NotFoundException(`KPI Period Rule with ID: ${id} not found`);
    const rule = await this.ruleRepo.findOne({ where: { id: id } });
    if (rule.status === 0) return { message: 'Update successfully', data: affected };
    const year = yearIn || new Date().getFullYear() - 1;
    const nextYear = year + 1;
    const cycleType = await this.generalDataDetailRepo.findOne({ where: { id: rule.cycleType } });
    switch (Number.parseInt(cycleType.code)) {
      // Case monthly
      case 1: {
        const dictKPIPeriod: any = {};
        {
          const lstKPIPeriod = await this.repo.find();
          lstKPIPeriod.forEach(
            (item) => (dictKPIPeriod[item.periodName + item.startDate + item.endDate] = item),
          );
        }
        const lstKPIPeriodEntity = [];
        for (let i = 1; i <= 12; i++) {
          const startMonth = (i - 2 + 12) % 12;
          const startYear = startMonth === 11 ? year : nextYear;
          const startDate = dayjs(
            new Date(
              startYear,
              startMonth,
              rule.startDate,
              startShiftHours,
              startShiftMinutes,
              startShiftSeconds,
            ),
          )
            .tz('Asia/Ho_Chi_Minh', true)
            .toDate();
          const endMonth = (startMonth + 1) % 12;
          const endYear = endMonth === 0 ? startYear + 1 : startYear;
          const endDate = dayjs(
            new Date(
              endYear,
              endMonth,
              rule.endDate,
              endShiftHours,
              endShiftMinutes,
              endShiftSeconds,
            ),
          )
            .tz('Asia/Ho_Chi_Minh', true)
            .toDate();
          const periodName = `${(endMonth + 1).toString().padStart(2, '0')}/${endYear}`;
          if (!dictKPIPeriod[periodName + startDate + endDate]) {
            const kpiPeriodEntity = new KPIPeriodEntity();
            kpiPeriodEntity.periodName = periodName;
            kpiPeriodEntity.startDate = startDate;
            kpiPeriodEntity.endDate = endDate;
            kpiPeriodEntity.cycleType = rule.cycleType;
            kpiPeriodEntity.status = 0;
            kpiPeriodEntity.kpiPeriodRuleId = rule.id;
            // kpiPeriodEntity.createdBy = 'admin';
            // kpiPeriodEntity.updatedBy = 'admin';
            kpiPeriodEntity.createdDate = today;
            lstKPIPeriodEntity.push(kpiPeriodEntity);
          }
        }
        await this.repo.insert(lstKPIPeriodEntity);
        break;
      }
      // Case Quarter
      case 2: {
        const dictKPIPeriod: any = {};
        {
          const lstKPIPeriod = await this.repo.find();
          lstKPIPeriod.forEach(
            (item) => (dictKPIPeriod[item.periodName + item.startDate + item.endDate] = item),
          );
        }
        const lstKPIPeriodEntity = [];

        let startMonth, startYear, endMonth, endYear, quarter;
        switch (rule.startMonth) {
          case 12:
            quarter = 1;
            startMonth = 11; // Tháng 12 của năm trước
            startYear = year;
            endMonth = 2; // Tháng 3 của năm hiện tại
            endYear = nextYear;
            break;
          case 3:
            quarter = 2;
            startMonth = 2; // Tháng 3
            startYear = nextYear;
            endMonth = 5; // Tháng 6
            endYear = nextYear;
            break;
          case 6:
            quarter = 3;
            startMonth = 5; // Tháng 6
            startYear = nextYear;
            endMonth = 8; // Tháng 9
            endYear = nextYear;
            break;
          case 9:
            quarter = 4;
            startMonth = 8; // Tháng 9
            startYear = nextYear;
            endMonth = 11; // Tháng 12
            endYear = nextYear;
            break;
        }
        const startDate = dayjs(
          new Date(
            startYear,
            startMonth,
            rule.startDate,
            startShiftHours,
            startShiftMinutes,
            startShiftSeconds,
          ),
        )
          .tz('Asia/Ho_Chi_Minh', true)
          .toDate();
        const endDate = dayjs(
          new Date(
            endYear,
            endMonth,
            rule.endDate,
            endShiftHours,
            endShiftMinutes,
            endShiftSeconds,
          ),
        )
          .tz('Asia/Ho_Chi_Minh', true)
          .toDate();
        const periodName = `Q${quarter}/${nextYear}`;
        if (!dictKPIPeriod[periodName + startDate + endDate]) {
          const kpiPeriodEntity = new KPIPeriodEntity();
          kpiPeriodEntity.periodName = periodName;
          kpiPeriodEntity.startDate = startDate;
          kpiPeriodEntity.endDate = endDate;
          kpiPeriodEntity.cycleType = rule.cycleType;
          kpiPeriodEntity.status = 0;
          kpiPeriodEntity.kpiPeriodRuleId = rule.id;
          // kpiPeriodEntity.createdBy = 'admin';
          // kpiPeriodEntity.updatedBy = 'admin';
          kpiPeriodEntity.createdDate = today;
          lstKPIPeriodEntity.push(kpiPeriodEntity);
        }

        await this.repo.insert(lstKPIPeriodEntity);
        break;
      }
      // Case Yearly
      case 3: {
        const month = rule.startMonth - 1;
        const startDate = dayjs(
          new Date(
            year,
            month,
            rule.startDate,
            startShiftHours,
            startShiftMinutes,
            startShiftSeconds,
          ),
        )
          .tz('Asia/Ho_Chi_Minh', true)
          .toDate();
        const endDate = dayjs(
          new Date(nextYear, month, rule.endDate, endShiftHours, endShiftMinutes, endShiftSeconds),
        )
          .tz('Asia/Ho_Chi_Minh', true)
          .toDate();
        const kpiPeriod = await this.repo.findOne({
          where: { periodName: Like(nextYear), startDate: startDate, endDate: endDate },
        });
        if (!kpiPeriod) {
          const kpiPeriodEntity = new KPIPeriodEntity();
          kpiPeriodEntity.periodName = nextYear.toString();
          kpiPeriodEntity.startDate = startDate;
          kpiPeriodEntity.endDate = endDate;
          kpiPeriodEntity.cycleType = rule.cycleType;
          kpiPeriodEntity.status = 0;
          kpiPeriodEntity.kpiPeriodRuleId = rule.id;
          // kpiPeriodEntity.createdBy = 'admin';
          // kpiPeriodEntity.updatedBy = 'admin';
          kpiPeriodEntity.createdDate = today;
          await this.repo.save(kpiPeriodEntity);
        }
        break;
      }
      default:
        break;
    }
    return { message: 'Update successfully', data: affected };
  }

  @DefTransaction()
  async deleteRule(id: string) {
    if (!id) throw new BadRequestException('ID don’t exist');
    const machine = await this.ruleRepo.findOne({ where: { id } });
    if (!machine) throw new NotFoundException('Not found machine');
    await this.ruleRepo.delete({ id: id });
    return { message: 'Delete successfully' };
  }

  @DefTransaction()
  async updateStatus(body: KPIPeriodUpdateStatusDTO) {
    const { userId: userId } = adminSessionContext;
    const today = new Date();

    const whereCon: any = { periodName: body.periodName };
    if (body.isAll === false) {
      if (body.lstSiteId.length === 0) {
        whereCon.siteId = null;
      } else {
        whereCon.siteId = In(body.lstSiteId);
      }
    }
    const kpiPeriods = await this.repo.find({ where: whereCon });
    for (const item of kpiPeriods) {
      item.status = body.status;
      if (item.status === 2) {
        item.lockedBy = 'admin';
        item.lockedDate = today;
      }
      // item.updatedBy = 'admin';
      item.updatedDate = today;
      await this.repo.update({ id: item.id }, item);
    }
    return { message: 'Update successfully' };
  }

  async listKPIPeriodMonthGroupBySite(params: ListKPIPeriodMonthDTO) {
    const generalData = await this.generalDataRepo.findOne({ where: { code: 'KPI_PERIOD_CYCLE' } });
    const cycleType = await this.generalDataDetailRepo.findOne({
      where: { code: '1', generalId: generalData.id },
    });
    const result: any = await this.repo.find({
      where: { periodName: Like(`%${params.year}%`), cycleType: cycleType.id },
      order: { periodName: 'ASC' },
    });
    const today = new Date();
    const lstData = [];
    const lstSite = Array.from(new Set(result.map((item) => item.siteId)));
    for (const site of lstSite) {
      const data: any = {};
      if (site === null) {
        data.siteName = 'ALL';
        data.siteId = null;
      } else {
        data.siteId = site;
      }
      result.forEach((item) => {
        if (item.siteId === site)
          data[item.periodName] = {
            value: item.status,
            isFuture: today < item.startDate ? true : false,
          };
      });
      lstData.push(data);
    }

    return lstData;
  }

  async listShiftOptions() {
    return await this.shiftRepo.find({ where: { status: true } });
  }
}
