export namespace NSSQSS {
  export enum EMessageType {
    SyncRecipeToScada = 'syncRecipeToScada',
    SyncProductionOrderToScada = 'syncProductionOrderToScada',
    SyncInventoryToScada = 'syncInventoryToScada',
    TestSNS = 'testSNS',
    OnProdBatchParamaters = 'OnProdBatchParamaters', // Task 36.1
    OnProductOutputByRejection = 'OnProductOutputByRejection', // Task 36.3
    OnProductionOrderMaterialTransaction = 'OnProductionOrderMaterialTransaction', // Task 36.2
    OnCalculateUtilityUsage = 'OnCalculateUtilityUsage', // Task 36.13
    OnOeeCalculation = 'OnOeeCalculation', // Task 36.14
    OnKansuiAndSeasoningCalculation = 'OnKansuiAndSeasoningCalculation', // Task 36.15
    OnTransactionProductOutput = 'onTransactionProductOutput', // Task 36.5
    OnUtilityTransactionAllocation = 'OnUtilityTransactionAllocation', // Task 36.11
    OnManhourTransactionAllocation = 'OnManhourTransactionAllocation', // Task 36.12
    OnCreateManhourTransactions = 'OnCreateManhourTransactions', // Task 36.15
    OnCreateUtilityConsumptionKpi = 'OnCreateUtilityConsumptionKpi', // Task 41
    OnCreateManhourKpi = 'OnCreateManhourKpi', // Task 43
    RejectReasonMidNight = 'RejectReasonMidNight',
    OnCreateOeeKpi = 'OnCreateOeeKpi', //Task 46
    OnDockerEventHandling = 'OnDockerEventHandling', // Nhận sự kiện thay đổi db ở docker msi, mhg
    OnRejectionKpi = 'OnRejectionKpi', //Task 47
    OnLossPowderKpi = 'OnLossPowderKpi', //Tash 40.2
  }
  export enum EStatus {
    Success = 'success',
    Running = 'running',
    Error = 'error',
  }

  export enum EScheduleStatus {
    Success = 'success',
    Running = 'running',
    Error = 'error',
  }
}
