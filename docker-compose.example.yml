version: '0.1'
services:
  mes-api:
    build:
      context: .
      dockerfile: Dockerfile.scada
    container_name: mes-api
    environment:
      - PORT=9000
      - NODE_ENV=production
      - TZ=Asia/Ho_Chi_Minh
      - REQUEST_TIMEOUT=300000
      # Swagger Config
      - SWAGGER_TITLE=MES API
      - SWAGGER_DESCRIPTION=The MES API
      - SWAGGER_VERSION=1.0
      # MS SSO

      - MS_CLIENT_ID=
      - MS_CLIENT_SECRET=
      - MS_TENANT_ID=
      # Primary Database
      - DB_PRIMARY_HOST=
      - DB_PRIMARY_PORT=5432
      - DB_PRIMARY_USERNAME=
      - DB_PRIMARY_PASSWORD=
      - DB_PRIMARY_DATABASE=
      - DB_PRIMARY_SYNCHRONIZE=false
      - DB_PRIMARY_SSL=true
      - DB_PRIMARY_SSL_REJECT_UNAUTHORIZED=false
      # Scada Database
      - DB_SCADA_HOST=
      - DB_SCADA_PORT=5432
      - DB_SCADA_USERNAME=
      - DB_SCADA_PASSWORD=
      - DB_SCADA_DATABASE=
      - DB_SCADA_SYNCHRONIZE=false
      - DB_SCADA_SSL=true
      - DB_SCADA_SSL_REJECT_UNAUTHORIZED=false
      - JWT_SECRET=
      - JWT_EXPIRY=10h
      - API_TOKEN_CACHE_INTERVAL=1
      - API_TOKEN_SECRET=
      # Key chạy job schedule
      - API_SCHEDULE_SECRET=
      - API_SCADA_SECRET=
      - API_SITEWISE_SECRET=
      - API_SCALE_SECRET=
      - AWS_ACCESS_KEY_ID=
      - AWS_SECRET_ACCESS_KEY=
      - AWS_REGION=
      # Cấu hình Cognito
      - USER_POOL_ID=
      - CLIENT_ID=
      - REGION=
      # Thông tin đăng nhập của người dùng
      - USERNAME_COGNITO=
      - PASSWORD_COGNITO=
      # Phải thay đổi khi đổi server
      - DOCKER_SERVER_IP=
      # Thông tin đồng bộ inventory erp mes
      - TOKEN_ERP_URL=
      - ERP_INVENTORY=
      - EBS_MES_USERNAME=
      - EBS_MES_PASSWORD=
      - INVENTORY_ERP_URL=
      - IS_DOCKER_SERVER=true
      - IS_DISABLE_SQS=true
      - IS_EVENT_HUB=false
      - AWS_SQS_URL=
      # Phải thay đổi khi đổi server
      - AWS_SQS_URL_CUSTOM=
      - AWS_SQS_REGION=ap-southeast-1
      - AWS_SQS_ACCESS_KEY_ID=
      - AWS_SQS_SECRET_ACCESS_KEY=
      - AWS_API_VERSION=
      - AWS_SNS_ARN=
      - REDIS_URL=

      # AWS S3
      - AWS_S3_UPLOAD_FOLDER=
      - AWS_S3_BUCKET_NAME=
      - AWS_S3_ACCESS_KEY_ID=
      - AWS_S3_SECRET_ACCESS_KEY=

      - QLONE_URL=
      - AZURE_EVENT_HUB_CONNECTION_STRING=
      - AZURE_EVENT_HUB_NAME=
    restart: always # <== Tự động restart nếu container die
    ports:
      - '9001:9000'
