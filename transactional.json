[{"table_schema": "public", "table_name": "access", "table_type": "BASE TABLE", "columns": [{"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "created<PERSON>y", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "updatedBy", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "version", "data_type": "integer", "is_nullable": "NO", "column_default": "0"}, {"column_name": "userId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "organizationUnitId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "updatedDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "status", "data_type": "boolean", "is_nullable": "YES", "column_default": "true"}, {"column_name": "default", "data_type": "boolean", "is_nullable": "YES", "column_default": "false"}]}, {"table_schema": "public", "table_name": "assign_shift", "table_type": "BASE TABLE", "columns": [{"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "created<PERSON>y", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "updatedBy", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "version", "data_type": "integer", "is_nullable": "NO", "column_default": "0"}, {"column_name": "validFrom", "data_type": "timestamp with time zone", "is_nullable": "NO", "column_default": null}, {"column_name": "validTo", "data_type": "timestamp with time zone", "is_nullable": "NO", "column_default": null}, {"column_name": "shiftId", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"column_name": "organizationId", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"column_name": "status", "data_type": "boolean", "is_nullable": "NO", "column_default": "true"}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "updatedDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}]}, {"table_schema": "public", "table_name": "authorization-face", "table_type": "BASE TABLE", "columns": [{"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "updatedDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "created<PERSON>y", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "updatedBy", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "version", "data_type": "integer", "is_nullable": "NO", "column_default": "0"}, {"column_name": "keycode", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "date", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "placeId", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "deviceId", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "placeName", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "deviceName", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "aliasId", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "detectedImageUrl", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "personTitle", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "temp", "data_type": "double precision", "is_nullable": "YES", "column_default": null}, {"column_name": "data", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "actionType", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "personName", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "dataType", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "personId", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "time", "data_type": "bigint", "is_nullable": "YES", "column_default": null}, {"column_name": "personType", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "hash", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "mask", "data_type": "character varying", "is_nullable": "YES", "column_default": null}]}, {"table_schema": "public", "table_name": "card", "table_type": "BASE TABLE", "columns": [{"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "created<PERSON>y", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "updatedBy", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "version", "data_type": "integer", "is_nullable": "NO", "column_default": "0"}, {"column_name": "name", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "code", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "updatedDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}]}, {"table_schema": "public", "table_name": "device", "table_type": "BASE TABLE", "columns": [{"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "updatedDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "created<PERSON>y", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "updatedBy", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "version", "data_type": "integer", "is_nullable": "NO", "column_default": "0"}, {"column_name": "code", "data_type": "integer", "is_nullable": "NO", "column_default": "0"}, {"column_name": "description", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"column_name": "deviceTypeCode", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "deviceTypeDetailCode", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "ip<PERSON><PERSON><PERSON>", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "devicePort", "data_type": "numeric", "is_nullable": "YES", "column_default": null}, {"column_name": "isActive", "data_type": "boolean", "is_nullable": "NO", "column_default": "true"}, {"column_name": "note", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"column_name": "serialNumber", "data_type": "character varying", "is_nullable": "YES", "column_default": null}]}, {"table_schema": "public", "table_name": "general_data", "table_type": "BASE TABLE", "columns": [{"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "created<PERSON>y", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "updatedBy", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "version", "data_type": "integer", "is_nullable": "NO", "column_default": "0"}, {"column_name": "code", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "name", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "description", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"column_name": "note", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"column_name": "isActive", "data_type": "boolean", "is_nullable": "YES", "column_default": "true"}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "updatedDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}]}, {"table_schema": "public", "table_name": "general_data_details", "table_type": "BASE TABLE", "columns": [{"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "created<PERSON>y", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "updatedBy", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "version", "data_type": "integer", "is_nullable": "NO", "column_default": "0"}, {"column_name": "code", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "name", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "description", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"column_name": "note", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"column_name": "isActive", "data_type": "boolean", "is_nullable": "NO", "column_default": "true"}, {"column_name": "generalId", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "updatedDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}]}, {"table_schema": "public", "table_name": "holiday", "table_type": "BASE TABLE", "columns": [{"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "description", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "status", "data_type": "boolean", "is_nullable": "NO", "column_default": "false"}, {"column_name": "startDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"column_name": "endDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"column_name": "dateDetail", "data_type": "ARRAY", "is_nullable": "YES", "column_default": null}, {"column_name": "created<PERSON>y", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"column_name": "lastModifiedBy", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "lastModifiedDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}]}, {"table_schema": "public", "table_name": "holiday_detail", "table_type": "BASE TABLE", "columns": [{"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "description", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "status", "data_type": "boolean", "is_nullable": "NO", "column_default": "false"}, {"column_name": "dateDetail", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"column_name": "yearDetail", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "holidayId", "data_type": "character varying", "is_nullable": "NO", "column_default": "false"}]}, {"table_schema": "public", "table_name": "inspection_plan", "table_type": "BASE TABLE", "columns": [{"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "created<PERSON>y", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "updatedBy", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "version", "data_type": "integer", "is_nullable": "NO", "column_default": "0"}, {"column_name": "itemId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "isActive", "data_type": "boolean", "is_nullable": "NO", "column_default": "true"}, {"column_name": "frequency", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "dataType", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "uom", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "specCode", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "specVersion", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "qcFullname", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "organizationUnitId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "aqlCode", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "outputQty", "data_type": "numeric", "is_nullable": "YES", "column_default": null}, {"column_name": "timeValue", "data_type": "numeric", "is_nullable": "YES", "column_default": null}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "updatedDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "processId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "machineId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "machineParameterId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "minValue", "data_type": "numeric", "is_nullable": "YES", "column_default": null}, {"column_name": "maxValue", "data_type": "numeric", "is_nullable": "YES", "column_default": null}, {"column_name": "description", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "code", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "timeUnit", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "sampleType", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "costCenter", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "receivingSite", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "receivingDepartment", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "category", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "inspectionLevel", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "aqlSeverity", "data_type": "uuid", "is_nullable": "YES", "column_default": null}]}, {"table_schema": "public", "table_name": "inventory", "table_type": "BASE TABLE", "columns": [{"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "updatedDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "created<PERSON>y", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "updatedBy", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "version", "data_type": "integer", "is_nullable": "NO", "column_default": "0"}, {"column_name": "materialCode", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"column_name": "plant", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"column_name": "subInventory", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"column_name": "locator", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"column_name": "lotStatus", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"column_name": "receiptDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"column_name": "expiredDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"column_name": "productionDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"column_name": "baseQty", "data_type": "double precision", "is_nullable": "NO", "column_default": null}, {"column_name": "inventoryQty", "data_type": "double precision", "is_nullable": "NO", "column_default": null}, {"column_name": "baseUom", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"column_name": "inventoryUom", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"column_name": "palletQty", "data_type": "double precision", "is_nullable": "NO", "column_default": null}, {"column_name": "supplier", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"column_name": "manufacture", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"column_name": "materialLotNumber", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"column_name": "isSyncScada", "data_type": "boolean", "is_nullable": "NO", "column_default": "false"}, {"column_name": "dateSyncScada", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"column_name": "materialName", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"column_name": "errorSyncScada", "data_type": "text", "is_nullable": "YES", "column_default": null}]}, {"table_schema": "public", "table_name": "item", "table_type": "BASE TABLE", "columns": [{"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "created<PERSON>y", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "updatedBy", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "version", "data_type": "integer", "is_nullable": "NO", "column_default": "0"}, {"column_name": "code", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "name", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "baseUnit", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "type", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "group", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "category", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "brand", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "inventoryUnit", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "status", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "updatedDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "isSyncScada", "data_type": "boolean", "is_nullable": "YES", "column_default": "false"}, {"column_name": "dateSyncScada", "data_type": "timestamp without time zone", "is_nullable": "YES", "column_default": null}, {"column_name": "errorSyncScada", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"column_name": "weighingTolerance", "data_type": "numeric", "is_nullable": "NO", "column_default": "'0'::numeric"}]}, {"table_schema": "public", "table_name": "kpi", "table_type": "BASE TABLE", "columns": [{"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "created<PERSON>y", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "updatedBy", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "version", "data_type": "integer", "is_nullable": "NO", "column_default": "0"}, {"column_name": "kpiCategory", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "kpiGroup", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "code", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "shortName", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "<PERSON><PERSON><PERSON>", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "unit", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "kpiMethod", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "autoFunction", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "calType", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "inputFrequency", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "calMethod", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "parameter3", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "parameter4", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "parameter5", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "parameter6", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "status", "data_type": "integer", "is_nullable": "YES", "column_default": "1"}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "updatedDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "materialGroup", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "utilityType", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "kpiCategoryId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "kpiGroupId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "unitId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "kpiMethodId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "autoFunctionId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "calTypeId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "inputFrequencyId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "calMethodId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "materialGroupId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "utilityTypeId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}]}, {"table_schema": "public", "table_name": "kpi_period", "table_type": "BASE TABLE", "columns": [{"column_name": "id", "data_type": "integer", "is_nullable": "NO", "column_default": "nextval('kpi_period_id_seq'::regclass)"}, {"column_name": "periodName", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "status", "data_type": "integer", "is_nullable": "NO", "column_default": null}, {"column_name": "locked<PERSON>y", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "siteId", "data_type": "integer", "is_nullable": "YES", "column_default": null}, {"column_name": "created<PERSON>y", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "updatedBy", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "version", "data_type": "integer", "is_nullable": "NO", "column_default": "0"}, {"column_name": "kpiPeriodRuleId", "data_type": "integer", "is_nullable": "NO", "column_default": null}, {"column_name": "cycleType", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "startDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"column_name": "endDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"column_name": "lockedDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "NO", "column_default": "now()"}, {"column_name": "updatedDate", "data_type": "timestamp with time zone", "is_nullable": "NO", "column_default": "now()"}]}, {"table_schema": "public", "table_name": "kpi_period_rule", "table_type": "BASE TABLE", "columns": [{"column_name": "id", "data_type": "integer", "is_nullable": "NO", "column_default": "nextval('kpi_period_rule_id_seq'::regclass)"}, {"column_name": "startDate", "data_type": "integer", "is_nullable": "NO", "column_default": null}, {"column_name": "endDate", "data_type": "integer", "is_nullable": "NO", "column_default": null}, {"column_name": "created<PERSON>y", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "updatedBy", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "version", "data_type": "integer", "is_nullable": "NO", "column_default": "0"}, {"column_name": "startMonth", "data_type": "integer", "is_nullable": "YES", "column_default": null}, {"column_name": "periodRuleName", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "status", "data_type": "integer", "is_nullable": "YES", "column_default": null}, {"column_name": "startShift", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "endShift", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "cycleType", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "cycleCode", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "cycleName", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "startShiftCode", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "endShiftCode", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "NO", "column_default": "now()"}, {"column_name": "updatedDate", "data_type": "timestamp with time zone", "is_nullable": "NO", "column_default": "now()"}]}, {"table_schema": "public", "table_name": "kpi_score", "table_type": "BASE TABLE", "columns": [{"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "updatedDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "created<PERSON>y", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "updatedBy", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "version", "data_type": "integer", "is_nullable": "NO", "column_default": "0"}, {"column_name": "siteId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "factoryId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "productionAreaId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "kpiSetHeaderId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "kpiSetGroupId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "kpiId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "shiftId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "actualScore", "data_type": "double precision", "is_nullable": "YES", "column_default": "'-1'::double precision"}, {"column_name": "lineId", "data_type": "integer", "is_nullable": "NO", "column_default": "nextval('\"kpi_score_lineId_seq\"'::regclass)"}, {"column_name": "kpiSetDetailId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "kpiPeriodId", "data_type": "integer", "is_nullable": "YES", "column_default": null}, {"column_name": "scoreDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}]}, {"table_schema": "public", "table_name": "kpi_score_test", "table_type": "BASE TABLE", "columns": [{"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "updatedDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "created<PERSON>y", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "updatedBy", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "version", "data_type": "integer", "is_nullable": "NO", "column_default": "0"}, {"column_name": "lineId", "data_type": "integer", "is_nullable": "NO", "column_default": "nextval('\"kpi_score_test_lineId_seq\"'::regclass)"}, {"column_name": "kpiSetDetailId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "kpiId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "kpiSetGroupId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "kpiSetHeaderId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "productionAreaId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "factoryId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "siteId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "kpiPeriodId", "data_type": "integer", "is_nullable": "YES", "column_default": null}, {"column_name": "shiftId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "scoreDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"column_name": "actualScore", "data_type": "double precision", "is_nullable": "YES", "column_default": "'-1'::double precision"}]}, {"table_schema": "public", "table_name": "kpi_set_detail", "table_type": "BASE TABLE", "columns": [{"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "created<PERSON>y", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "updatedBy", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "version", "data_type": "integer", "is_nullable": "NO", "column_default": "0"}, {"column_name": "kpiSetGroupId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "kpiId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "weighTarget", "data_type": "numeric", "is_nullable": "YES", "column_default": null}, {"column_name": "budget", "data_type": "numeric", "is_nullable": "YES", "column_default": null}, {"column_name": "kpiSetDetailId", "data_type": "integer", "is_nullable": "NO", "column_default": "nextval('\"kpi_set_detail_kpiSetDetailId_seq\"'::regclass)"}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "updatedDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}]}, {"table_schema": "public", "table_name": "kpi_set_group", "table_type": "BASE TABLE", "columns": [{"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "created<PERSON>y", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "updatedBy", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "version", "data_type": "integer", "is_nullable": "NO", "column_default": "0"}, {"column_name": "kpiSetHeaderId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "kpiCategoryId", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "kpiCategoryCode", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "kpiCategoryName", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "kpiGroupCode", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "kpiGroupName", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "weighTarget", "data_type": "numeric", "is_nullable": "YES", "column_default": "'0'::numeric"}, {"column_name": "budget", "data_type": "numeric", "is_nullable": "YES", "column_default": "'0'::numeric"}, {"column_name": "kpiSetGroupId", "data_type": "integer", "is_nullable": "NO", "column_default": "nextval('\"kpi_set_group_kpiSetGroupId_seq\"'::regclass)"}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "updatedDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "kpiGroupId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}]}, {"table_schema": "public", "table_name": "kpi_set_header", "table_type": "BASE TABLE", "columns": [{"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "created<PERSON>y", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "updatedBy", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "version", "data_type": "integer", "is_nullable": "NO", "column_default": "0"}, {"column_name": "code", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "shortName", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "<PERSON><PERSON><PERSON>", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "budget", "data_type": "numeric", "is_nullable": "YES", "column_default": null}, {"column_name": "enableFlag", "data_type": "integer", "is_nullable": "YES", "column_default": null}, {"column_name": "status", "data_type": "integer", "is_nullable": "YES", "column_default": null}, {"column_name": "kpiSetId", "data_type": "integer", "is_nullable": "NO", "column_default": "nextval('\"kpi_set_header_kpiSetId_seq\"'::regclass)"}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "updatedDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}]}, {"table_schema": "public", "table_name": "kpi_set_production_area", "table_type": "BASE TABLE", "columns": [{"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "created<PERSON>y", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "updatedBy", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "version", "data_type": "integer", "is_nullable": "NO", "column_default": "0"}, {"column_name": "factoryId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "productionAreaId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "kpiSetHeaderId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "status", "data_type": "integer", "is_nullable": "YES", "column_default": "1"}, {"column_name": "siteId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "updatedDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}]}, {"table_schema": "public", "table_name": "kpi_standard", "table_type": "BASE TABLE", "columns": [{"column_name": "unit", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "weightageKpi", "data_type": "numeric", "is_nullable": "NO", "column_default": null}, {"column_name": "kpiStandardId", "data_type": "integer", "is_nullable": "NO", "column_default": "nextval('\"kpi_standard_kpiStandardId_seq\"'::regclass)"}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "NO", "column_default": "now()"}, {"column_name": "updatedDate", "data_type": "timestamp with time zone", "is_nullable": "NO", "column_default": "now()"}, {"column_name": "created<PERSON>y", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "updatedBy", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "groupId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "kpiSetId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "kpiSetGroupId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "kpiCodeId", "data_type": "uuid", "is_nullable": "NO", "column_default": null}]}, {"table_schema": "public", "table_name": "kpi_standard_score", "table_type": "BASE TABLE", "columns": [{"column_name": "levelScore", "data_type": "numeric", "is_nullable": "NO", "column_default": null}, {"column_name": "level", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "rewardPercentage", "data_type": "numeric", "is_nullable": "NO", "column_default": null}, {"column_name": "kpiScoreLineId", "data_type": "integer", "is_nullable": "NO", "column_default": "nextval('\"kpi_standard_score_kpiScoreLineId_seq\"'::regclass)"}, {"column_name": "groupId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "effectiveDateFrom", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"column_name": "effectiveDateTo", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"column_name": "kpiSetId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "kpiCodeId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}]}, {"table_schema": "public", "table_name": "log_job", "table_type": "BASE TABLE", "columns": [{"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "updatedDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "created<PERSON>y", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "updatedBy", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "version", "data_type": "integer", "is_nullable": "NO", "column_default": "0"}, {"column_name": "message", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "status", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "type", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "messageSNSId", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "stacktrace", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"column_name": "metadata", "data_type": "jsonb", "is_nullable": "YES", "column_default": null}, {"column_name": "description", "data_type": "text", "is_nullable": "YES", "column_default": null}]}, {"table_schema": "public", "table_name": "log_schedule", "table_type": "BASE TABLE", "columns": [{"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "updatedDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "created<PERSON>y", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "updatedBy", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "version", "data_type": "integer", "is_nullable": "NO", "column_default": "0"}, {"column_name": "message", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "status", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "type", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "dateTime", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}]}, {"table_schema": "public", "table_name": "machine", "table_type": "BASE TABLE", "columns": [{"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "created<PERSON>y", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "updatedBy", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "version", "data_type": "integer", "is_nullable": "NO", "column_default": "0"}, {"column_name": "code", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "name", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"column_name": "desc", "data_type": "text", "is_nullable": "NO", "column_default": "''::text"}, {"column_name": "gatewayProtocol", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"column_name": "ip<PERSON><PERSON><PERSON>", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"column_name": "note", "data_type": "text", "is_nullable": "NO", "column_default": "''::text"}, {"column_name": "rawDataTable", "data_type": "text", "is_nullable": "NO", "column_default": "''::text"}, {"column_name": "isActive", "data_type": "boolean", "is_nullable": "NO", "column_default": "true"}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "updatedDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}]}, {"table_schema": "public", "table_name": "machine_parameter", "table_type": "BASE TABLE", "columns": [{"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "created<PERSON>y", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "updatedBy", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "version", "data_type": "integer", "is_nullable": "NO", "column_default": "0"}, {"column_name": "code", "data_type": "integer", "is_nullable": "NO", "column_default": null}, {"column_name": "desc", "data_type": "text", "is_nullable": "NO", "column_default": "''::text"}, {"column_name": "ip<PERSON><PERSON><PERSON>", "data_type": "text", "is_nullable": "NO", "column_default": "''::text"}, {"column_name": "tagName", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "tag<PERSON><PERSON><PERSON>", "data_type": "text", "is_nullable": "NO", "column_default": "''::text"}, {"column_name": "interval", "data_type": "boolean", "is_nullable": "NO", "column_default": "false"}, {"column_name": "timeValue", "data_type": "numeric", "is_nullable": "YES", "column_default": null}, {"column_name": "nodeId", "data_type": "text", "is_nullable": "NO", "column_default": "''::text"}, {"column_name": "note", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"column_name": "batchStatusControl", "data_type": "smallint", "is_nullable": "NO", "column_default": "'-1'::smallint"}, {"column_name": "showOnDashboard", "data_type": "boolean", "is_nullable": "NO", "column_default": "false"}, {"column_name": "isActive", "data_type": "boolean", "is_nullable": "NO", "column_default": "true"}, {"column_name": "machineId", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"column_name": "showOnRejectionMonitoring", "data_type": "boolean", "is_nullable": "NO", "column_default": "false"}, {"column_name": "typeCode", "data_type": "character varying", "is_nullable": "NO", "column_default": "''::character varying"}, {"column_name": "typeId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "dataTypeCode", "data_type": "character varying", "is_nullable": "NO", "column_default": "''::character varying"}, {"column_name": "dataTypeId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "uomCode", "data_type": "character varying", "is_nullable": "NO", "column_default": "''::character varying"}, {"column_name": "uomId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "datetimeUnitCode", "data_type": "character varying", "is_nullable": "NO", "column_default": "''::character varying"}, {"column_name": "datetimeUnitId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "typeGeneralCode", "data_type": "character varying", "is_nullable": "NO", "column_default": "''::character varying"}, {"column_name": "dataTypeGeneralCode", "data_type": "character varying", "is_nullable": "NO", "column_default": "''::character varying"}, {"column_name": "uomGeneralCode", "data_type": "character varying", "is_nullable": "NO", "column_default": "''::character varying"}, {"column_name": "datetimeUnitGeneralCode", "data_type": "character varying", "is_nullable": "NO", "column_default": "''::character varying"}, {"column_name": "iotsitewiseAssetId", "data_type": "text", "is_nullable": "NO", "column_default": "''::text"}, {"column_name": "oeeCal", "data_type": "smallint", "is_nullable": "NO", "column_default": "'0'::smallint"}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "updatedDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "iotsitewisePropertyId", "data_type": "character varying", "is_nullable": "NO", "column_default": "''::character varying"}, {"column_name": "iotsitewisePropertyTypeGeneralCode", "data_type": "character varying", "is_nullable": "NO", "column_default": "''::character varying"}, {"column_name": "iotsitewisePropertyTypeCode", "data_type": "character varying", "is_nullable": "NO", "column_default": "''::character varying"}, {"column_name": "iotsitewisePropertyTypeId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}]}, {"table_schema": "public", "table_name": "manhour_transaction_allocation", "table_type": "BASE TABLE", "columns": [{"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "updatedDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "created<PERSON>y", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "updatedBy", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "version", "data_type": "integer", "is_nullable": "NO", "column_default": "0"}, {"column_name": "date", "data_type": "date", "is_nullable": "YES", "column_default": null}, {"column_name": "shiftId", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "resourceCode", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "processAreaId", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"column_name": "value", "data_type": "numeric", "is_nullable": "NO", "column_default": null}, {"column_name": "planResourceUsage", "data_type": "numeric", "is_nullable": "NO", "column_default": null}, {"column_name": "oldActualResourceUsage", "data_type": "numeric", "is_nullable": "NO", "column_default": null}, {"column_name": "actualTrxQty", "data_type": "numeric", "is_nullable": "NO", "column_default": null}, {"column_name": "planProdQty", "data_type": "numeric", "is_nullable": "NO", "column_default": null}, {"column_name": "poId", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "contribution", "data_type": "numeric", "is_nullable": "NO", "column_default": null}, {"column_name": "contrPercent", "data_type": "numeric", "is_nullable": "NO", "column_default": null}, {"column_name": "allocatedValue", "data_type": "numeric", "is_nullable": "NO", "column_default": null}, {"column_name": "status", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "lineId", "data_type": "double precision", "is_nullable": "YES", "column_default": null}, {"column_name": "productionAreaId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}]}, {"table_schema": "public", "table_name": "manhour_transactions", "table_type": "BASE TABLE", "columns": [{"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "updatedDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "created<PERSON>y", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "updatedBy", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "version", "data_type": "integer", "is_nullable": "NO", "column_default": "0"}, {"column_name": "transactionId", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "creationDate", "data_type": "timestamp with time zone", "is_nullable": "NO", "column_default": "now()"}, {"column_name": "productionDate", "data_type": "date", "is_nullable": "NO", "column_default": null}, {"column_name": "manhour", "data_type": "numeric", "is_nullable": "NO", "column_default": null}, {"column_name": "Note", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "posted", "data_type": "boolean", "is_nullable": "YES", "column_default": null}, {"column_name": "lastUpdate", "data_type": "timestamp with time zone", "is_nullable": "NO", "column_default": "now()"}, {"column_name": "lastUpdateBy", "data_type": "integer", "is_nullable": "NO", "column_default": null}, {"column_name": "productionAreaId", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"column_name": "shiftId", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"column_name": "source", "data_type": "character varying", "is_nullable": "YES", "column_default": null}]}, {"table_schema": "public", "table_name": "material_consumption_scada", "table_type": "BASE TABLE", "columns": [{"column_name": "orderNumber", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"column_name": "orderId", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"column_name": "batchCode", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"column_name": "batchId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "ingredientCode", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"column_name": "ingredientId", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"column_name": "lotNumber", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"column_name": "qty", "data_type": "numeric", "is_nullable": "NO", "column_default": null}, {"column_name": "uom", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"column_name": "operatorId", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"column_name": "transactionType", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"column_name": "isProcessed", "data_type": "boolean", "is_nullable": "NO", "column_default": "false"}, {"column_name": "datetime", "data_type": "timestamp with time zone", "is_nullable": "NO", "column_default": null}, {"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "updatedDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "created<PERSON>y", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "updatedBy", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "version", "data_type": "integer", "is_nullable": "NO", "column_default": "0"}]}, {"table_schema": "public", "table_name": "material_group_detail", "table_type": "BASE TABLE", "columns": [{"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "updatedDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "created<PERSON>y", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "updatedBy", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "version", "data_type": "integer", "is_nullable": "NO", "column_default": "0"}, {"column_name": "headerId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "itemId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "active", "data_type": "boolean", "is_nullable": "NO", "column_default": "true"}]}, {"table_schema": "public", "table_name": "material_group_header", "table_type": "BASE TABLE", "columns": [{"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "updatedDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "created<PERSON>y", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "updatedBy", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "version", "data_type": "integer", "is_nullable": "NO", "column_default": "0"}, {"column_name": "materialGroupCode", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "materialGroupDescription", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "active", "data_type": "boolean", "is_nullable": "NO", "column_default": "true"}]}, {"table_schema": "public", "table_name": "monitor_manhour", "table_type": "BASE TABLE", "columns": [{"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "updatedDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "created<PERSON>y", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "updatedBy", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "version", "data_type": "integer", "is_nullable": "NO", "column_default": "0"}, {"column_name": "productionDate", "data_type": "timestamp without time zone", "is_nullable": "NO", "column_default": null}, {"column_name": "productionAreaId", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "shiftId", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "manhour", "data_type": "integer", "is_nullable": "YES", "column_default": null}, {"column_name": "note", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "posted", "data_type": "integer", "is_nullable": "YES", "column_default": null}]}, {"table_schema": "public", "table_name": "organization_units", "table_type": "BASE TABLE", "columns": [{"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "created<PERSON>y", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "updatedBy", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "version", "data_type": "integer", "is_nullable": "NO", "column_default": "0"}, {"column_name": "code", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "name", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "description", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "capacity", "data_type": "numeric", "is_nullable": "YES", "column_default": null}, {"column_name": "weightingDistribution", "data_type": "numeric", "is_nullable": "YES", "column_default": null}, {"column_name": "ingredientLocator", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "productsLocator", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "parentId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "isActive", "data_type": "boolean", "is_nullable": "NO", "column_default": "true"}, {"column_name": "note", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"column_name": "levelId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "categoryId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "storageLocation", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"column_name": "levelGeneralDataParentCode", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "levelGeneralDataDetailCode", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "categoryGeneralDataParentCode", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "categoryGeneralDataDetailCode", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "orderNo", "data_type": "integer", "is_nullable": "YES", "column_default": "0"}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "updatedDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "ipScada", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "materialHandleType", "data_type": "text", "is_nullable": "YES", "column_default": null}]}, {"table_schema": "public", "table_name": "permission", "table_type": "BASE TABLE", "columns": [{"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "created<PERSON>y", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "updatedBy", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "version", "data_type": "integer", "is_nullable": "NO", "column_default": "0"}, {"column_name": "code", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "name", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "description", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "isActive", "data_type": "boolean", "is_nullable": "NO", "column_default": "true"}, {"column_name": "roleStringify", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "updatedDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}]}, {"table_schema": "public", "table_name": "permission-user", "table_type": "BASE TABLE", "columns": [{"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "created<PERSON>y", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "updatedBy", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "version", "data_type": "integer", "is_nullable": "NO", "column_default": "0"}, {"column_name": "permissionId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "userId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "updatedDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "isActive", "data_type": "boolean", "is_nullable": "NO", "column_default": "false"}, {"column_name": "validFrom", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"column_name": "validTo", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}]}, {"table_schema": "public", "table_name": "process", "table_type": "BASE TABLE", "columns": [{"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "created<PERSON>y", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "updatedBy", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "version", "data_type": "integer", "is_nullable": "NO", "column_default": "0"}, {"column_name": "code", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "organizationUnitId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "name", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "description", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"column_name": "ip<PERSON><PERSON><PERSON>", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "gatewayProtocol", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "category", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "isActive", "data_type": "boolean", "is_nullable": "NO", "column_default": "true"}, {"column_name": "note", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"column_name": "outputCalculationMethodCode", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "updatedDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}]}, {"table_schema": "public", "table_name": "process_machine", "table_type": "BASE TABLE", "columns": [{"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "machineId", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"column_name": "processId", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"column_name": "assign", "data_type": "boolean", "is_nullable": "NO", "column_default": "true"}]}, {"table_schema": "public", "table_name": "production_area", "table_type": "BASE TABLE", "columns": [{"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "created<PERSON>y", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "updatedBy", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "version", "data_type": "integer", "is_nullable": "NO", "column_default": "0"}, {"column_name": "name", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "description", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "isActive", "data_type": "boolean", "is_nullable": "NO", "column_default": "true"}, {"column_name": "note", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "code", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "organizationUnitId", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"column_name": "uomID", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "tolerance", "data_type": "numeric", "is_nullable": "YES", "column_default": null}, {"column_name": "uomGeneralCode", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "uomGeneralDetailCode", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "uomGeneralDetailName", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "locationX", "data_type": "numeric", "is_nullable": "YES", "column_default": null}, {"column_name": "locationY", "data_type": "numeric", "is_nullable": "YES", "column_default": null}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "updatedDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "manHourResourceCode", "data_type": "text", "is_nullable": "YES", "column_default": null}]}, {"table_schema": "public", "table_name": "production_area_detail", "table_type": "BASE TABLE", "columns": [{"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "created<PERSON>y", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "updatedBy", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "version", "data_type": "integer", "is_nullable": "NO", "column_default": "0"}, {"column_name": "productionAreaId", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"column_name": "organizationUnitId", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"column_name": "isActive", "data_type": "boolean", "is_nullable": "NO", "column_default": "true"}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "updatedDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}]}, {"table_schema": "public", "table_name": "production_batch", "table_type": "BASE TABLE", "columns": [{"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "created<PERSON>y", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "updatedBy", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "version", "data_type": "integer", "is_nullable": "NO", "column_default": "0"}, {"column_name": "batchNumber", "data_type": "numeric", "is_nullable": "YES", "column_default": null}, {"column_name": "planProdQty", "data_type": "numeric", "is_nullable": "YES", "column_default": null}, {"column_name": "actualProdQty", "data_type": "numeric", "is_nullable": "YES", "column_default": null}, {"column_name": "status", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "scadaSynced", "data_type": "boolean", "is_nullable": "NO", "column_default": "false"}, {"column_name": "uom", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "updatedDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "planStartTime", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"column_name": "planEndTime", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"column_name": "actualStartTime", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"column_name": "actualEndTime", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"column_name": "orderId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "productId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "dateScadaSynced", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"column_name": "weighStatus", "data_type": "integer", "is_nullable": "YES", "column_default": "0"}]}, {"table_schema": "public", "table_name": "production_batch_paramaters", "table_type": "BASE TABLE", "columns": [{"column_name": "bacthProcessParameterId", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "machineId", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "batchId", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "measurementId", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "value", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "lastUpdateBy", "data_type": "integer", "is_nullable": "YES", "column_default": null}, {"column_name": "created<PERSON>y", "data_type": "integer", "is_nullable": "NO", "column_default": null}, {"column_name": "creationDate", "data_type": "timestamp with time zone", "is_nullable": "NO", "column_default": "now()"}, {"column_name": "dateTime", "data_type": "timestamp with time zone", "is_nullable": "NO", "column_default": "now()"}, {"column_name": "lastUpdate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}]}, {"table_schema": "public", "table_name": "production_batch_status", "table_type": "BASE TABLE", "columns": [{"column_name": "status", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "productionBatchStatusId", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "machineId", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "batchId", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "lastUpdateBy", "data_type": "integer", "is_nullable": "NO", "column_default": null}, {"column_name": "creationDate", "data_type": "timestamp with time zone", "is_nullable": "NO", "column_default": "now()"}, {"column_name": "lastUpdate", "data_type": "timestamp with time zone", "is_nullable": "NO", "column_default": "now()"}, {"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "updatedDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "updatedBy", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "version", "data_type": "integer", "is_nullable": "NO", "column_default": "0"}, {"column_name": "created<PERSON>y", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "startTime", "data_type": "timestamp with time zone", "is_nullable": "NO", "column_default": null}, {"column_name": "endTime", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}]}, {"table_schema": "public", "table_name": "production_downtime", "table_type": "BASE TABLE", "columns": [{"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "created<PERSON>y", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "updatedBy", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "version", "data_type": "integer", "is_nullable": "NO", "column_default": "0"}, {"column_name": "scadaDowntimeId", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"column_name": "machineCode", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"column_name": "reasonCode", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "reasonDescription", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"column_name": "cancelledFlag", "data_type": "integer", "is_nullable": "NO", "column_default": "0"}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "updatedDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "startTime", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"column_name": "endTime", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"column_name": "description", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"column_name": "isProcessed", "data_type": "boolean", "is_nullable": "NO", "column_default": "false"}, {"column_name": "note", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"column_name": "machineId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "processAreaId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}]}, {"table_schema": "public", "table_name": "production_oee", "table_type": "BASE TABLE", "columns": [{"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "updatedDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "created<PERSON>y", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "updatedBy", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "version", "data_type": "integer", "is_nullable": "NO", "column_default": "0"}, {"column_name": "productionOeeId", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "creationDate", "data_type": "timestamp without time zone", "is_nullable": "NO", "column_default": "now()"}, {"column_name": "productionDate", "data_type": "timestamp with time zone", "is_nullable": "NO", "column_default": null}, {"column_name": "productionLineId", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "posted", "data_type": "integer", "is_nullable": "NO", "column_default": "0"}, {"column_name": "lastUpdate", "data_type": "timestamp with time zone", "is_nullable": "NO", "column_default": "now()"}, {"column_name": "lastUpdateBy", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "loadingTime", "data_type": "numeric", "is_nullable": "YES", "column_default": null}, {"column_name": "operationTime", "data_type": "numeric", "is_nullable": "YES", "column_default": null}, {"column_name": "shutdownLossTime", "data_type": "numeric", "is_nullable": "YES", "column_default": null}, {"column_name": "downtime", "data_type": "numeric", "is_nullable": "YES", "column_default": null}, {"column_name": "utilisationTime", "data_type": "numeric", "is_nullable": "YES", "column_default": null}, {"column_name": "availability", "data_type": "numeric", "is_nullable": "YES", "column_default": null}, {"column_name": "expectedProductQty", "data_type": "numeric", "is_nullable": "YES", "column_default": null}, {"column_name": "actualProductQty", "data_type": "numeric", "is_nullable": "YES", "column_default": null}, {"column_name": "performance", "data_type": "numeric", "is_nullable": "YES", "column_default": null}, {"column_name": "defectiveProducts", "data_type": "numeric", "is_nullable": "YES", "column_default": null}, {"column_name": "quality", "data_type": "numeric", "is_nullable": "YES", "column_default": null}, {"column_name": "oee", "data_type": "numeric", "is_nullable": "YES", "column_default": null}, {"column_name": "shiftId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}]}, {"table_schema": "public", "table_name": "production_oee_test", "table_type": "BASE TABLE", "columns": [{"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "updatedDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "created<PERSON>y", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "updatedBy", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "version", "data_type": "integer", "is_nullable": "NO", "column_default": "0"}, {"column_name": "productionOeeId", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "creationDate", "data_type": "timestamp without time zone", "is_nullable": "NO", "column_default": "now()"}, {"column_name": "loadingTime", "data_type": "numeric", "is_nullable": "YES", "column_default": null}, {"column_name": "operationTime", "data_type": "numeric", "is_nullable": "YES", "column_default": null}, {"column_name": "shutdownLossTime", "data_type": "numeric", "is_nullable": "YES", "column_default": null}, {"column_name": "downtime", "data_type": "numeric", "is_nullable": "YES", "column_default": null}, {"column_name": "utilisationTime", "data_type": "numeric", "is_nullable": "YES", "column_default": null}, {"column_name": "availability", "data_type": "numeric", "is_nullable": "YES", "column_default": null}, {"column_name": "expectedProductQty", "data_type": "numeric", "is_nullable": "YES", "column_default": null}, {"column_name": "actualProductQty", "data_type": "numeric", "is_nullable": "YES", "column_default": null}, {"column_name": "performance", "data_type": "numeric", "is_nullable": "YES", "column_default": null}, {"column_name": "defectiveProducts", "data_type": "numeric", "is_nullable": "YES", "column_default": null}, {"column_name": "quality", "data_type": "numeric", "is_nullable": "YES", "column_default": null}, {"column_name": "oee", "data_type": "numeric", "is_nullable": "YES", "column_default": null}, {"column_name": "shiftId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "productionDate", "data_type": "timestamp with time zone", "is_nullable": "NO", "column_default": null}, {"column_name": "productionLineId", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "posted", "data_type": "integer", "is_nullable": "NO", "column_default": "0"}, {"column_name": "lastUpdate", "data_type": "timestamp with time zone", "is_nullable": "NO", "column_default": "now()"}, {"column_name": "lastUpdateBy", "data_type": "character varying", "is_nullable": "NO", "column_default": null}]}, {"table_schema": "public", "table_name": "production_order", "table_type": "BASE TABLE", "columns": [{"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "created<PERSON>y", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "updatedBy", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "version", "data_type": "integer", "is_nullable": "NO", "column_default": "0"}, {"column_name": "orderName", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "recipeVersion", "data_type": "numeric", "is_nullable": "YES", "column_default": null}, {"column_name": "lotNumber", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "type", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "orderStatusCode", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "isErpSynced", "data_type": "boolean", "is_nullable": "NO", "column_default": "false"}, {"column_name": "isScadaSynced", "data_type": "boolean", "is_nullable": "NO", "column_default": "false"}, {"column_name": "factoryId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "processAreaId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "relatedOrder", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "orderNo", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "itemId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "quantity", "data_type": "numeric", "is_nullable": "YES", "column_default": "'0'::numeric"}, {"column_name": "trxUom", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "trxUomCode", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "poParentId", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "siteId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "lineId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "updatedDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "planStartDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"column_name": "planEndDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"column_name": "actualStartDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"column_name": "actualEndDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"column_name": "recipeId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "recipeCode", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "shiftId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "orderStatus", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "dateScadaSynced", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"column_name": "errorSyncScada", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"column_name": "operatorUserId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "weighStatus", "data_type": "integer", "is_nullable": "YES", "column_default": "0"}]}, {"table_schema": "public", "table_name": "production_order_material", "table_type": "BASE TABLE", "columns": [{"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "created<PERSON>y", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "updatedBy", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "version", "data_type": "integer", "is_nullable": "NO", "column_default": "0"}, {"column_name": "recipeVersion", "data_type": "numeric", "is_nullable": "YES", "column_default": null}, {"column_name": "lineId", "data_type": "integer", "is_nullable": "NO", "column_default": "nextval('\"production_order_material_lineId_seq\"'::regclass)"}, {"column_name": "materialCode", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "materialName", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "lineType", "data_type": "numeric", "is_nullable": "YES", "column_default": null}, {"column_name": "planTrxQty", "data_type": "numeric", "is_nullable": "YES", "column_default": null}, {"column_name": "actualTrxQty", "data_type": "numeric", "is_nullable": "YES", "column_default": null}, {"column_name": "lineTypeCode", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "processId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "orderNo", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "trxUomCode", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "trxUom", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "orderId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "updatedDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "recipeId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "materialId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "poParentId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "weighStatus", "data_type": "integer", "is_nullable": "YES", "column_default": "0"}]}, {"table_schema": "public", "table_name": "production_order_material_transaction", "table_type": "BASE TABLE", "columns": [{"column_name": "materialTransactionId", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "creationDate", "data_type": "timestamp without time zone", "is_nullable": "NO", "column_default": "now()"}, {"column_name": "transactionType", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "lotNumber", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "transactionUom", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "lastUpdateBy", "data_type": "integer", "is_nullable": "NO", "column_default": null}, {"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "updatedDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "updatedBy", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "version", "data_type": "integer", "is_nullable": "NO", "column_default": "0"}, {"column_name": "created<PERSON>y", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "lastUpdate", "data_type": "timestamp with time zone", "is_nullable": "NO", "column_default": "now()"}, {"column_name": "oeeCal", "data_type": "integer", "is_nullable": "YES", "column_default": null}, {"column_name": "transactionQty", "data_type": "numeric", "is_nullable": "YES", "column_default": null}, {"column_name": "orderId", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"column_name": "batchId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "machineId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "metricId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "materialId", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"column_name": "scadaTransactionId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "productionOrderMaterialId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "transactionDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"column_name": "fromtransactionDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"column_name": "totransactionDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"column_name": "fromUpdatedDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"column_name": "toUpdatedDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}]}, {"table_schema": "public", "table_name": "production_order_resource", "table_type": "BASE TABLE", "columns": [{"column_name": "recipeVersion", "data_type": "numeric", "is_nullable": "YES", "column_default": null}, {"column_name": "planProdQty", "data_type": "numeric", "is_nullable": "YES", "column_default": null}, {"column_name": "actualProdQty", "data_type": "numeric", "is_nullable": "YES", "column_default": null}, {"column_name": "planResourceUsage", "data_type": "numeric", "is_nullable": "YES", "column_default": null}, {"column_name": "actualResourceUsage", "data_type": "numeric", "is_nullable": "YES", "column_default": null}, {"column_name": "resourceCode", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "orderStepId", "data_type": "integer", "is_nullable": "NO", "column_default": "nextval('\"production_order_resource_orderStepId_seq\"'::regclass)"}, {"column_name": "orderNo", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "uomCode", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "uom", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "lineId", "data_type": "integer", "is_nullable": "NO", "column_default": "nextval('\"production_order_resource_lineId_seq\"'::regclass)"}, {"column_name": "orderId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "recipeId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "processId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "receiptResourceId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}]}, {"table_schema": "public", "table_name": "production_rejection", "table_type": "BASE TABLE", "columns": [{"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "created<PERSON>y", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "updatedBy", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "version", "data_type": "integer", "is_nullable": "NO", "column_default": "0"}, {"column_name": "transactionTypeCode", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "transactionTypeDetailCode", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "quantity", "data_type": "numeric", "is_nullable": "NO", "column_default": null}, {"column_name": "uomCode", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "postedTrx", "data_type": "integer", "is_nullable": "NO", "column_default": null}, {"column_name": "note", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "description", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "updatedDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "oeeCal", "data_type": "integer", "is_nullable": "YES", "column_default": "1"}, {"column_name": "siteId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "factoryId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "productionLineId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "processAreaId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "productionOrderId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "itemId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "shiftId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "rejectionReasonId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "transactionDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}]}, {"table_schema": "public", "table_name": "production_weight", "table_type": "BASE TABLE", "columns": [{"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "updatedDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "created<PERSON>y", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "updatedBy", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "version", "data_type": "integer", "is_nullable": "NO", "column_default": "0"}, {"column_name": "orderId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "orderNo", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "batchId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "batchNo", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "productionMaterialId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "itemId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "itemCode", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "lotNumber", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "tare<PERSON><PERSON>", "data_type": "numeric", "is_nullable": "YES", "column_default": null}, {"column_name": "tolerance", "data_type": "numeric", "is_nullable": "YES", "column_default": null}, {"column_name": "weighedQty", "data_type": "numeric", "is_nullable": "YES", "column_default": null}, {"column_name": "packQty", "data_type": "numeric", "is_nullable": "YES", "column_default": null}, {"column_name": "datetime", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"column_name": "qrcode", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "status", "data_type": "numeric", "is_nullable": "YES", "column_default": null}, {"column_name": "batchConsumptId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "batchConsumptNo", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "weightedUom", "data_type": "character varying", "is_nullable": "YES", "column_default": null}]}, {"table_schema": "public", "table_name": "quality_notification", "table_type": "BASE TABLE", "columns": [{"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "updatedDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "created<PERSON>y", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "updatedBy", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "version", "data_type": "integer", "is_nullable": "NO", "column_default": "0"}, {"column_name": "inspectionPlanId", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"column_name": "inspectionPlanType", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "sampleNo", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "productionOrderId", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"column_name": "productionBatchId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "result", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "qloneUpdateDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"column_name": "qloneUpdateBy", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "status", "data_type": "numeric", "is_nullable": "YES", "column_default": "'1'::numeric"}, {"column_name": "siteId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "factoryId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "productionLineId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "productionAreaId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "processId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "machineId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "machineParameterId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "tag<PERSON><PERSON><PERSON>", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "itemId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "qualityNotificationNo", "data_type": "character varying", "is_nullable": "NO", "column_default": null}]}, {"table_schema": "public", "table_name": "reason_master", "table_type": "BASE TABLE", "columns": [{"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "created<PERSON>y", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "updatedBy", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "version", "data_type": "integer", "is_nullable": "NO", "column_default": "0"}, {"column_name": "code", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "name", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"column_name": "category", "data_type": "character varying", "is_nullable": "YES", "column_default": "''::character varying"}, {"column_name": "group", "data_type": "character varying", "is_nullable": "YES", "column_default": "''::character varying"}, {"column_name": "subGroup", "data_type": "character varying", "is_nullable": "YES", "column_default": "''::character varying"}, {"column_name": "isActive", "data_type": "boolean", "is_nullable": "NO", "column_default": "true"}, {"column_name": "parentId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "note", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"column_name": "categoryCode", "data_type": "character varying", "is_nullable": "YES", "column_default": "''::character varying"}, {"column_name": "categoryId", "data_type": "character varying", "is_nullable": "YES", "column_default": "''::character varying"}, {"column_name": "groupCode", "data_type": "character varying", "is_nullable": "YES", "column_default": "''::character varying"}, {"column_name": "groupId", "data_type": "character varying", "is_nullable": "YES", "column_default": "''::character varying"}, {"column_name": "subGroupCode", "data_type": "character varying", "is_nullable": "YES", "column_default": "''::character varying"}, {"column_name": "subGroupId", "data_type": "character varying", "is_nullable": "YES", "column_default": "''::character varying"}, {"column_name": "description", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "updatedDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}]}, {"table_schema": "public", "table_name": "recipe", "table_type": "BASE TABLE", "columns": [{"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "created<PERSON>y", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "updatedBy", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "version", "data_type": "integer", "is_nullable": "NO", "column_default": "0"}, {"column_name": "processAreaId", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"column_name": "organizationCode", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "recipeNo", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "recipeDescription", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"column_name": "recipeStatus", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "formulaNo", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "routingNo", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "batchSize", "data_type": "numeric", "is_nullable": "NO", "column_default": null}, {"column_name": "batchUom", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "productId", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"column_name": "recipeVer", "data_type": "numeric", "is_nullable": "NO", "column_default": null}, {"column_name": "formulaVer", "data_type": "numeric", "is_nullable": "NO", "column_default": null}, {"column_name": "routingVer", "data_type": "numeric", "is_nullable": "NO", "column_default": null}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "updatedDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "isSyncScada", "data_type": "boolean", "is_nullable": "NO", "column_default": "false"}, {"column_name": "dateSyncScada", "data_type": "timestamp without time zone", "is_nullable": "YES", "column_default": null}, {"column_name": "errorSyncScada", "data_type": "text", "is_nullable": "YES", "column_default": null}]}, {"table_schema": "public", "table_name": "recipe_process", "table_type": "BASE TABLE", "columns": [{"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "created<PERSON>y", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "updatedBy", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "version", "data_type": "integer", "is_nullable": "NO", "column_default": "0"}, {"column_name": "recipeId", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"column_name": "processId", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"column_name": "operationCode", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "updatedDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}]}, {"table_schema": "public", "table_name": "recipe_process_item", "table_type": "BASE TABLE", "columns": [{"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "created<PERSON>y", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "updatedBy", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "version", "data_type": "integer", "is_nullable": "NO", "column_default": "0"}, {"column_name": "itemId", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"column_name": "recipeProcessId", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"column_name": "lineId", "data_type": "integer", "is_nullable": "NO", "column_default": null}, {"column_name": "typeCode", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "uom", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "quantity", "data_type": "numeric", "is_nullable": "YES", "column_default": null}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "updatedDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}]}, {"table_schema": "public", "table_name": "recipe_resource", "table_type": "BASE TABLE", "columns": [{"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "created<PERSON>y", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "updatedBy", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "version", "data_type": "integer", "is_nullable": "NO", "column_default": "0"}, {"column_name": "resource", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "resourceDesc", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"column_name": "resourceUsageUom", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "recipeProcessId", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"column_name": "resourceUsage", "data_type": "numeric", "is_nullable": "YES", "column_default": null}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "updatedDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}]}, {"table_schema": "public", "table_name": "rejection_reason", "table_type": "BASE TABLE", "columns": [{"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "updatedDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "created<PERSON>y", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "updatedBy", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "version", "data_type": "integer", "is_nullable": "NO", "column_default": "0"}, {"column_name": "code", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "name", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"column_name": "status", "data_type": "integer", "is_nullable": "YES", "column_default": null}]}, {"table_schema": "public", "table_name": "report_history", "table_type": "BASE TABLE", "columns": [{"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "reportId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "reportCode", "data_type": "character varying", "is_nullable": "NO", "column_default": "''::character varying"}, {"column_name": "linkDownload", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"column_name": "created<PERSON>y", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "completedDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"column_name": "email", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "input", "data_type": "jsonb", "is_nullable": "YES", "column_default": null}, {"column_name": "output", "data_type": "jsonb", "is_nullable": "YES", "column_default": null}, {"column_name": "reportName", "data_type": "character varying", "is_nullable": "NO", "column_default": "''::character varying"}, {"column_name": "status", "data_type": "character varying", "is_nullable": "YES", "column_default": null}]}, {"table_schema": "public", "table_name": "set_point", "table_type": "BASE TABLE", "columns": [{"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "created<PERSON>y", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "updatedBy", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "version", "data_type": "integer", "is_nullable": "NO", "column_default": "0"}, {"column_name": "targetValue", "data_type": "numeric", "is_nullable": "NO", "column_default": null}, {"column_name": "min", "data_type": "numeric", "is_nullable": "NO", "column_default": null}, {"column_name": "max", "data_type": "numeric", "is_nullable": "NO", "column_default": null}, {"column_name": "isActive", "data_type": "boolean", "is_nullable": "NO", "column_default": "true"}, {"column_name": "organizeUnitId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "recipeId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "itemId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "machineParameterId", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"column_name": "processId", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"column_name": "machineId", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "updatedDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "type", "data_type": "character varying", "is_nullable": "NO", "column_default": "'RECIPE'::character varying"}]}, {"table_schema": "public", "table_name": "setting_string", "table_type": "BASE TABLE", "columns": [{"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "updatedDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "created<PERSON>y", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "updatedBy", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "version", "data_type": "integer", "is_nullable": "NO", "column_default": "0"}, {"column_name": "code", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "name", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"column_name": "type", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "value", "data_type": "text", "is_nullable": "YES", "column_default": null}]}, {"table_schema": "public", "table_name": "shift", "table_type": "BASE TABLE", "columns": [{"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "code", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"column_name": "description", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "status", "data_type": "boolean", "is_nullable": "NO", "column_default": "false"}, {"column_name": "startTime", "data_type": "time without time zone", "is_nullable": "YES", "column_default": null}, {"column_name": "endTime", "data_type": "time without time zone", "is_nullable": "YES", "column_default": null}, {"column_name": "nightShift", "data_type": "boolean", "is_nullable": "NO", "column_default": "false"}, {"column_name": "validFrom", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"column_name": "validTo", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"column_name": "created<PERSON>y", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"column_name": "lastModifiedBy", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "lastModifiedDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}]}, {"table_schema": "public", "table_name": "sitewise_asset", "table_type": "BASE TABLE", "columns": [{"column_name": "description", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "awsId", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "sitewiseModelId", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "status", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "externalId", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "name", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "code", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "NO", "column_default": "now()"}, {"column_name": "updatedDate", "data_type": "timestamp with time zone", "is_nullable": "NO", "column_default": "now()"}]}, {"table_schema": "public", "table_name": "sitewise_model", "table_type": "BASE TABLE", "columns": [{"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "created<PERSON>y", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "updatedBy", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "version", "data_type": "integer", "is_nullable": "NO", "column_default": "0"}, {"column_name": "code", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"column_name": "description", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "awsId", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "isActive", "data_type": "boolean", "is_nullable": "NO", "column_default": "true"}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "updatedDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}]}, {"table_schema": "public", "table_name": "sitewise_properties", "table_type": "BASE TABLE", "columns": [{"column_name": "awsId", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "type", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "unit", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "sitewiseAssetId", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "name", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "externalId", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "code", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "typeValue", "data_type": "jsonb", "is_nullable": "YES", "column_default": null}, {"column_name": "dataType", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "dataTypeSpec", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "sitewiseModelId", "data_type": "character varying", "is_nullable": "YES", "column_default": null}]}, {"table_schema": "public", "table_name": "station", "table_type": "BASE TABLE", "columns": [{"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "updatedDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "created<PERSON>y", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "updatedBy", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "version", "data_type": "integer", "is_nullable": "NO", "column_default": "0"}, {"column_name": "code", "data_type": "integer", "is_nullable": "NO", "column_default": null}, {"column_name": "description", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"column_name": "typeGenCode", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "typeCode", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"column_name": "siteId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "factoryId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "lineId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "processId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "isActive", "data_type": "boolean", "is_nullable": "NO", "column_default": "true"}, {"column_name": "note", "data_type": "text", "is_nullable": "YES", "column_default": null}]}, {"table_schema": "public", "table_name": "timesheet_records", "table_type": "BASE TABLE", "columns": [{"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "created<PERSON>y", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "updatedBy", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "version", "data_type": "integer", "is_nullable": "NO", "column_default": "0"}, {"column_name": "type", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "userId", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"column_name": "productionAreaId", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"column_name": "shiftId", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"column_name": "workDate", "data_type": "date", "is_nullable": "YES", "column_default": null}, {"column_name": "productionAreaLocationX", "data_type": "numeric", "is_nullable": "NO", "column_default": null}, {"column_name": "productionAreaLocationY", "data_type": "numeric", "is_nullable": "NO", "column_default": null}, {"column_name": "checkInLocationX", "data_type": "numeric", "is_nullable": "YES", "column_default": null}, {"column_name": "checkInLocationY", "data_type": "numeric", "is_nullable": "YES", "column_default": null}, {"column_name": "checkOutLocationX", "data_type": "numeric", "is_nullable": "YES", "column_default": null}, {"column_name": "checkOutLocationY", "data_type": "numeric", "is_nullable": "YES", "column_default": null}, {"column_name": "toleranceIn", "data_type": "numeric", "is_nullable": "YES", "column_default": null}, {"column_name": "toleranceOut", "data_type": "numeric", "is_nullable": "YES", "column_default": null}, {"column_name": "lastUpdateBy", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "duration", "data_type": "numeric", "is_nullable": "NO", "column_default": "'0'::numeric"}, {"column_name": "earlyTime", "data_type": "numeric", "is_nullable": "NO", "column_default": "'0'::numeric"}, {"column_name": "lateTime", "data_type": "numeric", "is_nullable": "NO", "column_default": "'0'::numeric"}, {"column_name": "salaryTime", "data_type": "numeric", "is_nullable": "NO", "column_default": "'0'::numeric"}, {"column_name": "nightTime", "data_type": "numeric", "is_nullable": "NO", "column_default": "'0'::numeric"}, {"column_name": "diffTime", "data_type": "numeric", "is_nullable": "NO", "column_default": "'0'::numeric"}, {"column_name": "otTime", "data_type": "numeric", "is_nullable": "NO", "column_default": "'0'::numeric"}, {"column_name": "otHolidayTime", "data_type": "numeric", "is_nullable": "NO", "column_default": "'0'::numeric"}, {"column_name": "otNightTime", "data_type": "numeric", "is_nullable": "NO", "column_default": "'0'::numeric"}, {"column_name": "otHolidayNightTime", "data_type": "numeric", "is_nullable": "NO", "column_default": "'0'::numeric"}, {"column_name": "siteId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "factoryId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "updatedDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "checkInTime", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"column_name": "checkOutTime", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"column_name": "creationDate", "data_type": "timestamp with time zone", "is_nullable": "NO", "column_default": "now()"}, {"column_name": "lastUpdate", "data_type": "timestamp with time zone", "is_nullable": "NO", "column_default": "now()"}]}, {"table_schema": "public", "table_name": "typeorm_metadata", "table_type": "BASE TABLE", "columns": [{"column_name": "type", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "database", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "schema", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "table", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "name", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "value", "data_type": "text", "is_nullable": "YES", "column_default": null}]}, {"table_schema": "public", "table_name": "uom_conversions", "table_type": "BASE TABLE", "columns": [{"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "created<PERSON>y", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "updatedBy", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "version", "data_type": "integer", "is_nullable": "NO", "column_default": "0"}, {"column_name": "itemId", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"column_name": "fromUnit", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "toUnit", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "conversion", "data_type": "numeric", "is_nullable": "NO", "column_default": null}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "updatedDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}]}, {"table_schema": "public", "table_name": "uoms", "table_type": "BASE TABLE", "columns": [{"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "created<PERSON>y", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "updatedBy", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "version", "data_type": "integer", "is_nullable": "NO", "column_default": "0"}, {"column_name": "code", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "name", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "symbol", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "description", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "isactive", "data_type": "boolean", "is_nullable": "NO", "column_default": "true"}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "updatedDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}]}, {"table_schema": "public", "table_name": "user", "table_type": "BASE TABLE", "columns": [{"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "created<PERSON>y", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "updatedBy", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "version", "data_type": "integer", "is_nullable": "NO", "column_default": "0"}, {"column_name": "employeeCode", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "fullName", "data_type": "text", "is_nullable": "NO", "column_default": null}, {"column_name": "email", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"column_name": "phone", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "loginPreference", "data_type": "USER-DEFINED", "is_nullable": "NO", "column_default": "'MSSSO'::user_loginpreference_enum"}, {"column_name": "isActive", "data_type": "boolean", "is_nullable": "NO", "column_default": "false"}, {"column_name": "validFrom", "data_type": "date", "is_nullable": "NO", "column_default": null}, {"column_name": "validTo", "data_type": "date", "is_nullable": "YES", "column_default": null}, {"column_name": "productionAreaId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "updatedDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "imageUrl", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "hanetSyncStatus", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "hanetSyncDate", "data_type": "date", "is_nullable": "YES", "column_default": null}, {"column_name": "permissionId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}]}, {"table_schema": "public", "table_name": "user_group", "table_type": "BASE TABLE", "columns": [{"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "created<PERSON>y", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "updatedBy", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "version", "data_type": "integer", "is_nullable": "NO", "column_default": "0"}, {"column_name": "name", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "description", "data_type": "text", "is_nullable": "YES", "column_default": null}, {"column_name": "isActive", "data_type": "boolean", "is_nullable": "NO", "column_default": "true"}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "updatedDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "typeCode", "data_type": "character varying", "is_nullable": "NO", "column_default": "''::character varying"}, {"column_name": "typeId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}]}, {"table_schema": "public", "table_name": "user_group_members", "table_type": "BASE TABLE", "columns": [{"column_name": "groupId", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"column_name": "userId", "data_type": "uuid", "is_nullable": "NO", "column_default": null}]}, {"table_schema": "public", "table_name": "utility_meter", "table_type": "BASE TABLE", "columns": [{"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "created<PERSON>y", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "updatedBy", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "version", "data_type": "integer", "is_nullable": "NO", "column_default": "0"}, {"column_name": "code", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "name", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "tag_name", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "tag_address", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "factory_id", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"column_name": "uom_code", "data_type": "character varying", "is_nullable": "YES", "column_default": "'UOM'::character varying"}, {"column_name": "uom_detail_code", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "utility_meter_type_code", "data_type": "character varying", "is_nullable": "NO", "column_default": "'UTILITY_METER_TYPE'::character varying"}, {"column_name": "utility_meter_type_detail_code", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "data_type_code", "data_type": "character varying", "is_nullable": "NO", "column_default": "'DATA_TYPE'::character varying"}, {"column_name": "data_type_detail_code", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "is_active", "data_type": "boolean", "is_nullable": "NO", "column_default": "true"}, {"column_name": "interval", "data_type": "boolean", "is_nullable": "NO", "column_default": "false"}, {"column_name": "time_value", "data_type": "numeric", "is_nullable": "YES", "column_default": null}, {"column_name": "datetime_unit_code", "data_type": "character varying", "is_nullable": "YES", "column_default": "'DATETIME_UNIT'::character varying"}, {"column_name": "datetime_unit_detail_code", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "node_id", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "note", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "cal_method_code", "data_type": "character varying", "is_nullable": "YES", "column_default": "'CAL_TYPE'::character varying"}, {"column_name": "uom_cal_method_code", "data_type": "character varying", "is_nullable": "YES", "column_default": "'UOM'::character varying"}, {"column_name": "resource_code", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "asset_id", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "measurement_id", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "metric_id", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "updatedDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "parent_id", "data_type": "uuid", "is_nullable": "YES", "column_default": null}]}, {"table_schema": "public", "table_name": "utility_meter_detail", "table_type": "BASE TABLE", "columns": [{"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "created<PERSON>y", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "updatedBy", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "version", "data_type": "integer", "is_nullable": "NO", "column_default": "0"}, {"column_name": "utility_meter_id", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"column_name": "process_area_id", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"column_name": "is_assigned", "data_type": "boolean", "is_nullable": "NO", "column_default": "false"}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "updatedDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}]}, {"table_schema": "public", "table_name": "utility_transaction", "table_type": "BASE TABLE", "columns": [{"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "updatedDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "created<PERSON>y", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "updatedBy", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "version", "data_type": "integer", "is_nullable": "NO", "column_default": "0"}, {"column_name": "posted", "data_type": "boolean", "is_nullable": "YES", "column_default": "false"}, {"column_name": "value", "data_type": "numeric", "is_nullable": "YES", "column_default": null}, {"column_name": "date", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}, {"column_name": "shiftId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "meterId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}, {"column_name": "postedAt", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}]}, {"table_schema": "public", "table_name": "utility_transaction_allocation", "table_type": "BASE TABLE", "columns": [{"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "updatedDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "created<PERSON>y", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "updatedBy", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "version", "data_type": "integer", "is_nullable": "NO", "column_default": "0"}, {"column_name": "date", "data_type": "date", "is_nullable": "YES", "column_default": null}, {"column_name": "shiftId", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "resourceCode", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "processAreaId", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"column_name": "value", "data_type": "numeric", "is_nullable": "NO", "column_default": null}, {"column_name": "planResourceUsage", "data_type": "numeric", "is_nullable": "NO", "column_default": null}, {"column_name": "oldActualResourceUsage", "data_type": "numeric", "is_nullable": "NO", "column_default": null}, {"column_name": "actualTrxQty", "data_type": "numeric", "is_nullable": "NO", "column_default": null}, {"column_name": "planProdQty", "data_type": "numeric", "is_nullable": "NO", "column_default": null}, {"column_name": "poId", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "contribution", "data_type": "numeric", "is_nullable": "NO", "column_default": null}, {"column_name": "contrPercent", "data_type": "numeric", "is_nullable": "NO", "column_default": null}, {"column_name": "allocatedValue", "data_type": "numeric", "is_nullable": "NO", "column_default": null}, {"column_name": "status", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "lineId", "data_type": "double precision", "is_nullable": "YES", "column_default": null}, {"column_name": "utilityTransactionId", "data_type": "uuid", "is_nullable": "YES", "column_default": null}]}, {"table_schema": "public", "table_name": "weighing_tare", "table_type": "BASE TABLE", "columns": [{"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "updatedDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "created<PERSON>y", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "updatedBy", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "version", "data_type": "integer", "is_nullable": "NO", "column_default": "0"}, {"column_name": "code", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "weighingTareTypeCode", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "weighingTareTypeDetailCode", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "description", "data_type": "text", "is_nullable": "YES", "column_default": "''::text"}, {"column_name": "note", "data_type": "text", "is_nullable": "YES", "column_default": "''::text"}, {"column_name": "isActive", "data_type": "boolean", "is_nullable": "NO", "column_default": "true"}, {"column_name": "uomCode", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "uomDetailCode", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "value", "data_type": "numeric", "is_nullable": "NO", "column_default": null}]}, {"table_schema": "public", "table_name": "weighing_tare_detail", "table_type": "BASE TABLE", "columns": [{"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "updatedDate", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": "now()"}, {"column_name": "created<PERSON>y", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "updatedBy", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "version", "data_type": "integer", "is_nullable": "NO", "column_default": "0"}, {"column_name": "weighingTareId", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"column_name": "itemId", "data_type": "uuid", "is_nullable": "NO", "column_default": null}, {"column_name": "isAssigned", "data_type": "boolean", "is_nullable": "NO", "column_default": "true"}]}]