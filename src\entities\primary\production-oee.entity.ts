import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { IsUUID } from 'class-validator';
import { PrimaryBaseEntity } from '../primary-base.entity';
@Entity('production_oee')
export class ProductionOeeEntity extends PrimaryBaseEntity {
  @ApiProperty()
  @PrimaryGeneratedColumn('uuid')
  @IsUUID(4)
  productionOeeId?: string;

  @Index()
  @ApiProperty()
  @CreateDateColumn()
  creationDate?: Date;

  @ApiProperty({ description: 'loading_time', example: 1 })
  @Column({ nullable: true, type: 'numeric' })
  loadingTime: number;

  @ApiProperty({ description: 'operation_time', example: 1 })
  @Column({ nullable: true, type: 'numeric' })
  operationTime: number;

  @ApiProperty({ description: 'shutdown_loss_time', example: 1 })
  @Column({ nullable: true, type: 'numeric' })
  shutdownLossTime: number;

  @ApiProperty({ description: 'downtime', example: 1 })
  @Column({ nullable: true, type: 'numeric' })
  downtime: number;

  @ApiProperty({ description: 'utilisation_time', example: 1 })
  @Column({ nullable: true, type: 'numeric' })
  utilisationTime: number;

  @ApiProperty({ description: 'availability', example: 1 })
  @Column({ nullable: true, type: 'numeric' })
  availability: number;

  @ApiProperty({ description: 'expected_product_qty', example: 1 })
  @Column({ nullable: true, type: 'numeric' })
  expectedProductQty: number;

  @ApiProperty({ description: 'actualProductQty', example: 1 })
  @Column({ nullable: true, type: 'numeric' })
  actualProductQty: number;

  @ApiProperty({ description: 'performance', example: 1 })
  @Column({ nullable: true, type: 'numeric' })
  performance: number;

  @ApiProperty({ description: 'defectiveProducts', example: 1 })
  @Column({ nullable: true, type: 'numeric' })
  defectiveProducts: number;

  @ApiProperty({ description: 'quality', example: 1 })
  @Column({ nullable: true, type: 'numeric' })
  quality: number;

  @ApiProperty({ description: 'oee', example: 1 })
  @Column({ nullable: true, type: 'numeric' })
  oee: number;

  @ApiProperty({ description: 'ca sản xuất', example: '1234' })
  @Column({ type: 'uuid', nullable: true })
  shiftId: string;

  @ApiProperty({ description: 'Ngày sản xuất', example: '2024-03-06T14:30:00Z' })
  @Column({ type: 'timestamptz' })
  productionDate: Date | null;

  @ApiProperty({ description: 'Production line', example: 1 })
  @Column()
  productionLineId: string;

  @ApiProperty({ description: '', example: 0 })
  @Column({ default: 0 })
  posted: number;

  // @ApiProperty({ description: '', example: '-1' })
  // @Column()
  // createdBy?: string | null;

  @ApiProperty({
    description: 'Thời gian cập nhật cuối của dòng dữ liệu',
    example: '2024-03-06T14:30:00Z',
  })
  @UpdateDateColumn({ type: 'timestamptz' })
  lastUpdate: Date | null;

  @ApiProperty({ description: '', example: '-1' })
  @Column({ nullable: true })
  lastUpdateBy: string | null;
}
