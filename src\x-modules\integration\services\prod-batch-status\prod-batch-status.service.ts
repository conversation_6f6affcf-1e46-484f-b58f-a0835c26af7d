import { Injectable } from '@nestjs/common';
import { BindRepo } from '~/@core/decorator';
import { MesDxTransformRepo } from '~/repositories/scada';
import { JwtService } from '@nestjs/jwt';
import {
  MachineParameterRepo,
  MachineRepo,
  ProductionOrderRepo,
  ProductionBatchRepo,
  MesDxProdBatchStatusRepo,
} from '~/repositories/primary';
import { Not } from 'typeorm';

@Injectable()
export class ProdBatchStatusService {
  constructor(private jwtService: JwtService) {}

  // Admin Database
  @BindRepo(MachineRepo)
  private readonly machineRepo: MachineRepo;
  @BindRepo(MachineParameterRepo)
  private readonly machineParameterRepo: MachineParameterRepo;
  @BindRepo(ProductionOrderRepo)
  private productionOrderRepo: ProductionOrderRepo;
  @BindRepo(ProductionBatchRepo)
  private productionBatchRepo: ProductionBatchRepo;

  // Scada database
  @BindRepo(MesDxProdBatchStatusRepo)
  private mesDxProdBatchStatusRepo: MesDxProdBatchStatusRepo;
  @BindRepo(MesDxTransformRepo)
  private mesDxTransformRepo: MesDxTransformRepo;

  private async isStatusExists(machineIdInfo: string, batchIdInfo: string) {
    const statusData = await this.mesDxProdBatchStatusRepo.findOne({
      machineId: machineIdInfo,
      batchId: batchIdInfo,
    });
    if (!statusData) return false;

    return true;
  }

  private async onUpdatePrevStatus(getPrevStatus: any, endTime: any) {
    if (!getPrevStatus || !endTime) return;

    getPrevStatus['status'] = '3';
    getPrevStatus['endTime'] = endTime;
    getPrevStatus['lastUpdate'] = new Date();
    await this.mesDxProdBatchStatusRepo.update(
      { id: getPrevStatus.id, productionBatchStatusId: getPrevStatus.productionBatchStatusId },
      getPrevStatus,
    );
  }

  async createProdBatchStatus(transformData: any) {
    try {
      // Nhận giá trị từ transform
      const transformValue = transformData?.value ? transformData?.value.toString() : '';
      let transformEntityData = transformData;
      if (!transformEntityData) return;

      // Dựa vào transformId -> tìm kiếm dữ liệu tương ứng bảng machineParameter
      const getTransformId = transformEntityData?.transformId || '';
      const machineParameterData = await this.machineParameterRepo.findOne({
        where: { iotsitewisePropertyId: getTransformId },
      });
      if (!machineParameterData) return;

      // Kiểm tramachineParameterData có batchStatusControl khác 0
      const getBatchStatusControl = machineParameterData?.batchStatusControl || 0;
      if (!getBatchStatusControl) return;

      const getMachineId = machineParameterData?.machineId;

      // Tìm prev record batch_status
      const getPrevStatus = await this.mesDxProdBatchStatusRepo.findOne({
        where: { machineId: getMachineId, status: Not('3') },
        order: { createdDate: 'DESC' },
      });

      // Nếu giá trị bằng rỗng, hoặc null thì end Status trước và không làm gì thêm
      const regex = /^\d+\|[^|]+$/;
      const validValue = regex.test(transformValue) ? transformValue : null;
      if (
        !transformValue ||
        transformValue === '' ||
        transformValue.split('|').length <= 1 ||
        !validValue
      ) {
        await this.onUpdatePrevStatus(getPrevStatus, transformEntityData?.datetime);
        return;
      }

      // ---- Start: xử lý để tìm đúng batchID ----
      // Kiểm tra nếu transform có value rỗng
      const getTransformValue = transformEntityData?.value || '';
      if (!getTransformValue) return;

      // Dựa vào value - tách logic để có batchValue và orderNo
      const getOrderNo = getTransformValue.split('|')[1] || '';
      const getBatchValue = getTransformValue.split('|')[0] || 0;

      // Tìm kiếm productionOrder record có orderNo
      const productionOrderData = await this.productionOrderRepo.findOne({
        where: { orderNo: getOrderNo },
      });
      if (!productionOrderData) return;

      // Tìm kiếm productionBatch record có orderNo
      const getProductionId = productionOrderData?.id;
      const productionBatchData = await this.productionBatchRepo.findOne({
        where: { orderId: getProductionId, batchNumber: getBatchValue },
      });
      if (!productionBatchData) return;

      // ---- End: xử lý để tìm đúng batchID ----
      const isCheckStatus = await this.isStatusExists(getMachineId, productionBatchData?.id);
      if (getPrevStatus?.batchId === productionBatchData?.id || isCheckStatus) return;

      if (getPrevStatus)
        await this.onUpdatePrevStatus(getPrevStatus, transformEntityData?.datetime);

      const createParams = {
        machineId: getMachineId,
        batchId: productionBatchData?.id,
        startTime: transformEntityData?.datetime,
        createdByUser: '-1',
        lastUpdateBy: -1,
        status: '2',
      };

      return await this.mesDxProdBatchStatusRepo.save({ ...createParams });
    } catch (error) {
      console.log(error, 'error');
    }
  }
}
