import { Column, Entity } from 'typeorm';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { PrimaryBaseEntity } from '~/entities/primary-base.entity';

@Entity('log_job')
export class LogJobEntity extends PrimaryBaseEntity {
  @ApiProperty({ description: 'Message' })
  @Column()
  message: string;

  @ApiProperty({ description: 'Status' })
  @Column({})
  status: string;

  @ApiProperty({ description: 'Type' })
  @Column({})
  type: string;

  @ApiProperty({ description: 'Message SNS ID' })
  @Column({})
  messageSNSId: string;

  @ApiPropertyOptional({ description: 'Description' })
  @Column({ nullable: true, type: 'text' })
  description?: string;

  @ApiPropertyOptional({ description: 'Stacktrace' })
  @Column({ type: 'text', nullable: true })
  stacktrace?: string;

  @ApiPropertyOptional({ description: 'Metadata' })
  @Column({ type: 'jsonb', nullable: true })
  metadata?: Record<string, any>;
}
