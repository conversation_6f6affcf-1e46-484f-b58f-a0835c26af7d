import { Entity, PrimaryColumn, Column, ManyTo<PERSON>ne, Join<PERSON><PERSON>um<PERSON> } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { PrimaryBaseEntity } from '../primary-base.entity';

@Entity('material_consumption_scada')
export class MaterialConsumptionScadaEntity extends PrimaryBaseEntity {
  // /** [PK] là khóa chính bảng data */
  // @ApiProperty({ description: 'ID tiêu thụ vật liệu' })
  // @PrimaryColumn({ type: 'uuid' })
  // : string;

  /** ProductionOrderNumber */
  @ApiProperty({ description: 'Mã lệnh sản xuất' })
  @Column({ type: 'text' })
  orderNumber: string;

  /** [FK] Bảng Production Order */
  @ApiProperty({ description: 'ID lệnh sản xuất (liên kết đến ProductionOrder)' })
  @Column({ type: 'uuid' })
  orderId: string;

  /** BatchCode */
  @ApiProperty({ description: 'Mã mẻ sản xuất' })
  @Column({ type: 'text', nullable: true })
  batchCode: string;

  /** [FK] Bảng Production Batch */
  @ApiProperty({ description: 'ID mẻ sản xuất (liên kết đến ProductionBatch)' })
  @Column({ type: 'uuid', nullable: true })
  batchId: string;

  /** IngredientCode */
  @ApiProperty({ description: 'Mã vật liệu' })
  @Column({ type: 'text' })
  ingredientCode: string;

  /** [FK] Bảng Master Item */
  @ApiProperty({ description: 'ID vật liệu (liên kết đến MasterItem)' })
  @Column({ type: 'uuid' })
  ingredientId: string;

  /** Lot */
  @ApiProperty({ description: 'Số lô' })
  @Column({ type: 'text', nullable: true })
  lotNumber: string;

  /** Quantity */
  @ApiProperty({ description: 'Số lượng' })
  @Column({ type: 'numeric' })
  qty: number;

  /** UnitOfMeasurment */
  @ApiProperty({ description: 'Đơn vị tính' })
  @Column({ type: 'text' })
  uom: string;

  /** Datetime */
  @ApiProperty({ description: 'Thời gian' })
  @Column({ type: 'timestamptz' })
  datetime: Date;

  /** Operator ID */
  @ApiProperty({ description: 'ID người vận hành' })
  @Column({ type: 'text', nullable: true })
  operatorId: string;

  /** transaction_type */
  @ApiProperty({ description: 'Loại giao dịch' })
  @Column({ type: 'text' })
  transactionType: string;

  /** is_processed */
  @ApiProperty({ description: 'Đã xử lý' })
  @Column({ type: 'boolean', default: false })
  isProcessed: boolean;

  //   // Mối quan hệ với ProductionOrder
  //   @ManyToOne(() => ProductionOrder, productionOrder => productionOrder.materialConsumptionScadas)
  //   @JoinColumn({ name: 'orderId' })
  //   productionOrder: ProductionOrder;

  //   // Mối quan hệ với ProductionBatch
  //   @ManyToOne(() => ProductionBatch, productionBatch => productionBatch.materialConsumptionScadas)
  //   @JoinColumn({ name: 'batchId' })
  //   productionBatch: ProductionBatch;

  //   // Mối quan hệ với MasterItem
  //   @ManyToOne(() => MasterItem, masterItem => masterItem.materialConsumptionScadas)
  //   @JoinColumn({ name: 'ingredientId' })
  //   masterItem: MasterItem;
}
