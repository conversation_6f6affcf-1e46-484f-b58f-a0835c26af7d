import { HttpStatus, Injectable } from '@nestjs/common';
import { In } from 'typeorm';
import { BindRepo, DefTransaction } from '~/@core/decorator';
import { EHttpStatusMessage } from '~/@core/network';
import { ErpBusinessException } from '~/@systems/exceptions/dto/erp-exception';
import { NSRecipe, NSSQSS } from '~/common/enums';
import { ERPCreateRecipeReq, ERPRecipeStatus } from '~/dto/erp/recipe.dto';
import {
  ProcessEntity,
  RecipeEntity,
  RecipeProcessEntity,
  RecipeProcessItemEntity,
  RecipeResourceEntity,
} from '~/entities/primary';
import {
  ItemRepo,
  ProcessRepo,
  RecipeProcessItemRepo,
  RecipeProcessRepo,
  RecipeRepo,
  RecipeResourceRepo,
} from '~/repositories/primary';
import { SnsService } from '~/x-modules/sqs/sns.service';
@Injectable()
export class ERPRecipeIntegrateService {
  @BindRepo(RecipeRepo)
  private readonly recipeRepo: RecipeRepo;
  @BindRepo(ItemRepo)
  private readonly itemRepo: ItemRepo;
  @BindRepo(ProcessRepo)
  private readonly processRepo: ProcessRepo;
  @BindRepo(RecipeProcessRepo)
  private readonly recipeProcessRepo: RecipeProcessRepo;
  @BindRepo(RecipeProcessItemRepo)
  private readonly recipeProcessItemRepo: RecipeProcessItemRepo;
  @BindRepo(RecipeResourceRepo)
  private readonly recipeResourceRepo: RecipeResourceRepo;

  public mappingErpRecipeStatusToMes = {
    [ERPRecipeStatus.ObsoleteOrArchived]: NSRecipe.RecipeStatus.ObsoleteOrArchived,
    [ERPRecipeStatus.ApprovedForGeneralUse]: NSRecipe.RecipeStatus.ApprovedForGeneralUse,
  };

  constructor(private readonly snsService: SnsService) {}

  @DefTransaction()
  async createOrUpdate(data: ERPCreateRecipeReq): Promise<RecipeEntity> {
    let recipe: RecipeEntity | undefined;
    recipe = await this.recipeRepo.findOne({
      where: {
        recipeNo: data.RECIPE_NO,
        recipeVer: data.RECIPE_VER,
        organizationCode: data.ORGANIZATION,
      },
      relations: [
        'recipeProcesses',
        'recipeProcesses.recipeResources',
        'recipeProcesses.recipeProcessItems',
      ],
    });
    if (recipe) {
      await this.recipeResourceRepo.remove(
        recipe.recipeProcesses.map((p) => p.recipeResources).flat(),
      );
      await this.recipeProcessItemRepo.remove(
        recipe.recipeProcesses.map((p) => p.recipeProcessItems).flat(),
      );
      await this.recipeProcessRepo.remove(recipe.recipeProcesses);
    } else {
      recipe = new RecipeEntity();
    }
    recipe.recipeProcesses = [];
    return this.handleData(data, recipe);
  }

  async handleData(data: ERPCreateRecipeReq, recipe?: RecipeEntity): Promise<RecipeEntity> {
    if (!recipe) {
      recipe = new RecipeEntity();
    }
    recipe.organizationCode = data.ORGANIZATION;
    recipe.recipeNo = data.RECIPE_NO;
    recipe.recipeVer = Number(data.RECIPE_VER);
    recipe.recipeDescription = data.RECIPE_DESC;
    recipe.recipeStatus = this.mappingErpRecipeStatusToMes[data.RECIPE_STATUS];
    recipe.routingNo = data.ROUTING_NO;
    recipe.routingVer = data.ROUTING_VERS;
    recipe.formulaNo = data.FORMULA_NO;
    recipe.formulaVer = Number(data.FORMULA_VERSION);
    recipe.batchSize = data.BATCH_SIZE;
    recipe.batchUom = data.BATCH_SIZE_UOM;
    recipe.isSyncScada = false;
    // item
    const item = await this.itemRepo.findOne({ where: { code: data.PRODUCT } });
    if (!item) {
      throw new ErpBusinessException(
        EHttpStatusMessage[HttpStatus.NOT_FOUND],
        [
          {
            field: 'PRODUCT',
            message: `Product ${data.PRODUCT} not found`,
          },
        ],
        HttpStatus.NOT_FOUND,
      );
    }
    recipe.productId = item.id;

    // process
    const firstProcessItem = data.PROCESS?.[0];
    if (!firstProcessItem) {
      throw new ErpBusinessException(
        EHttpStatusMessage[HttpStatus.UNPROCESSABLE_ENTITY],
        [
          {
            field: 'PROCESS',
            message: 'Process should not be empty',
          },
        ],
        HttpStatus.UNPROCESSABLE_ENTITY,
      );
    }

    const firstProcess = await this.processRepo.findOne({
      where: { code: firstProcessItem.PROCESS },
    });
    if (!firstProcess) {
      throw new ErpBusinessException(
        EHttpStatusMessage[HttpStatus.NOT_FOUND],
        [
          {
            field: 'PROCESS',
            message: `Process ${firstProcessItem.PROCESS} not found`,
          },
        ],
        HttpStatus.NOT_FOUND,
      );
    }

    if (!firstProcess.organizationUnitId) {
      throw new ErpBusinessException(
        EHttpStatusMessage[HttpStatus.BAD_REQUEST],
        [
          {
            field: 'PROCESS',
            message: `Process ${firstProcessItem.PROCESS} does not have organizationUnitId`,
          },
        ],
        HttpStatus.BAD_REQUEST,
      );
    }

    recipe.processAreaId = firstProcess.organizationUnitId;

    // find all process
    const processes = await this.processRepo.find({
      where: {
        code: In(data.PROCESS.map((p) => p.PROCESS)),
      },
    });

    const mapProcess: Record<string, ProcessEntity> = processes.reduce((acc, cur) => {
      acc[cur.code] = cur;
      return acc;
    }, {});

    const recipeProcesses: RecipeProcessEntity[] = recipe.recipeProcesses || [];
    if (data.PROCESS) {
      for await (const processItem of data.PROCESS) {
        const process = mapProcess[processItem.PROCESS];
        const newRecipeProcess = new RecipeProcessEntity();
        if (!process) {
          throw new ErpBusinessException(
            EHttpStatusMessage[HttpStatus.NOT_FOUND],
            [
              {
                field: 'PROCESS',
                message: `Process ${processItem.PROCESS} not found`,
              },
            ],
            HttpStatus.NOT_FOUND,
          );
        }
        newRecipeProcess.processId = process.id;
        newRecipeProcess.operationCode = processItem.PROCESS;
        newRecipeProcess.recipeId = recipe.id;
        recipeProcesses.push(newRecipeProcess);

        // ingredients
        if (processItem.Ingredient) {
          for await (const ingredient of processItem.Ingredient) {
            const newRecipeProcessItem = new RecipeProcessItemEntity();
            const item = await this.itemRepo.findOne({ where: { code: ingredient.ING_NO } });
            if (!item) {
              throw new ErpBusinessException(
                EHttpStatusMessage[HttpStatus.NOT_FOUND],
                [
                  {
                    field: 'Item',
                    message: `Item ${ingredient.ING_NO} not found`,
                  },
                ],
                HttpStatus.NOT_FOUND,
              );
            }
            newRecipeProcessItem.recipeProcessId = newRecipeProcess.id;
            newRecipeProcessItem.itemId = item.id;
            newRecipeProcessItem.quantity = ingredient.ING_QUANTITY;
            newRecipeProcessItem.uom = ingredient.ING_UOM_CODE;
            newRecipeProcessItem.typeCode = NSRecipe.RecipeProcessItemTypeCode.Ingredient;
            newRecipeProcessItem.lineId = ingredient.ING_LINE_NO;

            if (!newRecipeProcess.recipeProcessItems) {
              newRecipeProcess.recipeProcessItems = [];
            }
            newRecipeProcess.recipeProcessItems.push(newRecipeProcessItem);
          }
        }

        // by products
        if (processItem.By_Product) {
          for await (const byProduct of processItem.By_Product) {
            const newByProductItem = new RecipeProcessItemEntity();
            const item = await this.itemRepo.findOne({ where: { code: byProduct.By_Product_NO } });
            if (!item) {
              throw new ErpBusinessException(
                `ByProduct ${byProduct.By_Product_NO} not found`,
                [
                  {
                    field: 'ByProduct',
                    message: `ByProduct ${byProduct.By_Product_NO} not found`,
                  },
                ],
                HttpStatus.NOT_FOUND,
              );
            }
            newByProductItem.recipeProcessId = newRecipeProcess.id;
            newByProductItem.itemId = item.id;
            newByProductItem.quantity = byProduct.By_Product_QUANTITY;
            newByProductItem.uom = byProduct.By_Product_UOM_CODE;
            newByProductItem.typeCode = NSRecipe.RecipeProcessItemTypeCode.ByProduct;
            newByProductItem.lineId = byProduct.By_Product_LINE_NO;

            if (!newRecipeProcess.recipeProcessItems) {
              newRecipeProcess.recipeProcessItems = [];
            }
            newRecipeProcess.recipeProcessItems.push(newByProductItem);
          }
        }

        // product
        if (processItem.Product) {
          for await (const product of processItem.Product) {
            const newProductItem = new RecipeProcessItemEntity();
            const item = await this.itemRepo.findOne({ where: { code: product.Product_NO } });
            if (!item) {
              throw new ErpBusinessException(
                `Product ${product.Product_NO} not found`,
                [
                  {
                    field: 'Product',
                    message: `Product ${product.Product_NO} not found`,
                  },
                ],
                HttpStatus.NOT_FOUND,
              );
            }
            newProductItem.recipeProcessId = newRecipeProcess.id;
            newProductItem.itemId = item.id;
            newProductItem.quantity = product.Product_QUANTITY;
            newProductItem.uom = product.Product_UOM_CODE;
            newProductItem.typeCode = NSRecipe.RecipeProcessItemTypeCode.Product;
            newProductItem.lineId = product.Product_LINE_NO;

            if (!newRecipeProcess.recipeProcessItems) {
              newRecipeProcess.recipeProcessItems = [];
            }
            newRecipeProcess.recipeProcessItems.push(newProductItem);
          }
        }
        // resources
        if (processItem.Resource) {
          for await (const resource of processItem.Resource) {
            const newRecipeResource = new RecipeResourceEntity();
            // newRecipeResource.recipeProcessId = newRecipeProcess.id;
            newRecipeResource.resource = resource.RESOURCES;
            newRecipeResource.resourceDesc = resource.RESOURCE_DESC;
            newRecipeResource.resourceUsage = resource.RESOURCE_USAGE;
            newRecipeResource.resourceUsageUom = resource.RESOURCE_USAGE_UOM;

            if (!newRecipeProcess.recipeResources) {
              newRecipeProcess.recipeResources = [];
            }
            newRecipeProcess.recipeResources.push(newRecipeResource);
          }
        }
      }
    }

    recipe.recipeProcesses = recipeProcesses;

    const resultSave = await this.recipeRepo.save(recipe);

    // Gửi SQS để đồng bộ data về scada
    this.snsService.sendMessage(
      {
        message: 'SyncRecipeToScada Manual',
        data: {
          recipeId: resultSave.id,
        },
      },
      NSSQSS.EMessageType.SyncRecipeToScada,
    );
    return resultSave;
  }
}
