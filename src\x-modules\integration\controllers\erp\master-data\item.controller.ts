import { Inject, Body, ParseArrayPipe, UseGuards } from '@nestjs/common';
import { ApiBody, ApiTags } from '@nestjs/swagger';
import { DefController, DefPost } from '~/@core/decorator';
import { ApiTokenGuard } from '~/@core/decorator/api-token.decorator';
import { ERPCreateItemReq } from '~/dto/erp/item.dto';
import { ERPItemIntegrateService } from '~/x-modules/integration/services';

@ApiTags('ERPItems')
@UseGuards(ApiTokenGuard)
@DefController('erp/items')
export class ERPItemsController {
  constructor(
    @Inject(ERPItemIntegrateService)
    private readonly integrateService: ERPItemIntegrateService,
  ) {}

  @DefPost('/batch', { summary: 'Tạo/cập nhật thành phần/nguyên liệu/sản phẩm theo danh sách' })
  @ApiBody({
    type: [ERPCreateItemReq],
    description: 'Danh sách thêm thành phần/nguyên liệu/sản phẩm',
  })
  async create(
    @Body(new ParseArrayPipe({ items: ERPCreateItemReq })) data: Array<ERPCreateItemReq>,
  ) {
    return this.integrateService.batchCreateOrUpdate(data);
  }

  @DefPost('', { summary: 'Tạo/cập nhật thành phần/nguyên liệu/sản phẩm' })
  async createSingle(@Body() item: ERPCreateItemReq) {
    return this.integrateService.createOrUpdate(item);
  }
}
