import { forwardRef, MiddlewareConsumer, NestModule, RequestMethod } from '@nestjs/common';
import { ChildModule } from '~/@core/decorator';
import { IntegrationMiddleware } from './integration.middleware';
import { RefixModule } from '../config-module';
import * as allController from './controllers';
import * as allService from './services';
import { AdminModule } from '~/x-modules/admin';
import { SitewiseInService, SocketGateway } from './services';
import { SQSModule } from '~/x-modules/sqs';
import { ApiTokenService, KPIAwardService, SitewiseLibService } from '~/x-modules/admin/services';
import { QualityNotificationService } from '~/x-modules/integration/services/quality-notification';
@ChildModule({
  imports: [forwardRef(() => AdminModule), forwardRef(() => SQSModule)],
  prefix: RefixModule.integration,
  providers: [
    ...Object.values(allService),
    ApiTokenService,
    QualityNotificationService,
    KPIAwardService,
    SitewiseLibService,
  ],
  controllers: [...Object.values(allController)],
  exports: [SitewiseInService, SocketGateway, allService.UtilityConsumptionKpi],
})
export class IntegrationModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(IntegrationMiddleware)
      .exclude()
      .forRoutes({ path: `${RefixModule.integration}*`, method: RequestMethod.ALL });
  }
}
