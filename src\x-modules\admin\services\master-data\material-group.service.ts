import { Injectable } from '@nestjs/common';
import { BindRepo } from '~/@core/decorator';
import { ItemRepo, MaterialGroupDetailRepo, MaterialGroupHeaderRepo } from '~/repositories/primary';
import { adminSessionContext } from '../../admin-session.context';
import { MaterialGroupDto, MaterialGroupReq } from '~/dto/material-group.dto';
import { ILike } from 'typeorm';

@Injectable()
export class MaterialGroupService {
  constructor() {}

  @BindRepo(MaterialGroupHeaderRepo)
  private readonly repo: MaterialGroupHeaderRepo;

  @BindRepo(MaterialGroupDetailRepo)
  private readonly detailRepo: MaterialGroupDetailRepo;

  @BindRepo(ItemRepo)
  private readonly itemRepo: ItemRepo;

  /**
   * Tìm tất cả lý do với bộ lọc tùy chọn
   * @param filterDto Tiêu chí lọc tùy chọn
   * @returns Danh sách phân trang
   */
  async pagination(params: MaterialGroupReq) {
    const whereCon: any = {};
    if (params.active !== undefined) whereCon.active = params.active;
    if (params.code) whereCon.materialGroupCode = ILike(`%${params.code}%`);
    if (params.description) whereCon.materialGroupDescription = ILike(`%${params.description}%`);
    return await this.repo.findPagination(
      { where: whereCon, order: { createdDate: 'DESC' } },
      { pageIndex: params.pageIndex, pageSize: params.pageSize },
    );
  }

  async getMaterialDetail(headerId: string) {
    const res = await this.detailRepo.find({ where: { headerId: headerId }, relations: ['item'] });
    // Dùng Promise.all để đợi tất cả các Promise trong map được giải quyết
    const data = await Promise.all(
      res.map(async (i) => {
        const item = await i.item; // item trả về một Promise, bạn cần await
        return {
          id: item.id,
          headerId: i.headerId,
          detailId: i.id,
          code: item.code,
          name: item.name,
          type: item.type,
          active: i.active,
          createdBy: i.createdBy,
          createdDate: i.createdDate,
        };
      }),
    );

    return data;
  }

  async create(body: MaterialGroupDto) {
    const { userId } = adminSessionContext;
    const today = new Date();
    const header: any = await this.repo.save({
      materialGroupCode: body.code,
      materialGroupDescription: body.description,
      active: body.active,
      createdBy: userId,
      updatedBy: userId,
      createdDate: today,
      updatedDate: today,
    });
    const lstDetail = body.lstItem.map((i) => ({
      headerId: header.id,
      itemId: i.id,
      active: i.active,
      createdBy: userId,
      updatedBy: userId,
      createdDate: today,
      updatedDate: today,
    }));
    await this.detailRepo.insert(lstDetail);
    header.lstDetail = lstDetail;
    return { message: 'Create Successfully!', data: header };
  }

  async update(id: string, body: MaterialGroupDto) {
    const entity: any = await this.repo.findOne({ where: { id: id } });
    if (!entity) throw new Error('Not Fount Material Group Header!');
    const { userId: userId } = adminSessionContext;
    const today = new Date();
    await this.repo.update(
      { id: id },
      {
        materialGroupCode: body.code,
        materialGroupDescription: body.description,
        active: body.active,
        updatedBy: userId,
        updatedDate: today,
      },
    );
    await this.detailRepo.delete({ headerId: id });
    const lstDetail = body.lstItem.map((i) => ({
      headerId: id,
      itemId: i.id,
      active: i.active,
      createdBy: i?.createdBy || userId,
      createdDate: i?.createdDate || today,
      updatedBy: userId,
      updatedDate: today,
    }));
    await this.detailRepo.insert(lstDetail);
    entity.lstDetail = lstDetail;
    return { message: 'Update Successfully!', data: entity };
  }

  async updateActive(id: string) {
    const entity = await this.repo.findOne({ where: { id: id } });
    if (!entity) throw new Error('Not Found Material Group Header');
    const { userId: userId } = adminSessionContext;
    const today = new Date();
    return await this.repo.update(
      { id: id },
      { active: !entity.active, updatedBy: userId, updatedDate: today },
    );
  }

  async loadItem() {
    return await this.itemRepo.find({ where: { status: 'ACTIVE' } });
  }
}
