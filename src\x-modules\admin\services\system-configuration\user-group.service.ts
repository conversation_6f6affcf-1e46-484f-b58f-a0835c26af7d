import { Body, Injectable, NotFoundException } from '@nestjs/common';
import { BindRepo } from '~/@core/decorator';
import { UserGroupEntity } from '~/entities/primary/user-group.entity';
import pageUtil, { PageRequest, PageResponse } from '~/@systems/utils/page.utils';
import { NSGeneralData } from '~/common/enums/NSGeneralData';
import { GeneralDataDetailRepo, GeneralDataRepo, UserGroupRepo } from '~/repositories/primary';
import { ILike, In } from 'typeorm';

@Injectable()
export class UserGroupService {
  @BindRepo(UserGroupRepo)
  private userGroupRepo: UserGroupRepo;
  @BindRepo(GeneralDataRepo)
  private generalDataRepo: GeneralDataRepo;
  @BindRepo(GeneralDataDetailRepo)
  private generalDataDetailRepo: GeneralDataDetailRepo;

  async findAll(query: PageRequest<UserGroupEntity>): Promise<PageResponse<UserGroupEntity>> {
    const { pageIndex, pageSize, filters, orders } = query;

    const queryBuilder = this.userGroupRepo
      .createQueryBuilder('userGroup')
      .leftJoinAndSelect('userGroup.users', 'users');

    // Add search conditions
    const conditions: string[] = [];
    const queryParams: any = {};

    if (filters?.name) {
      conditions.push('LOWER(userGroup.name) LIKE LOWER(:name)');
      queryParams.name = `%${filters.name}%`;
    }
    if (filters?.description) {
      conditions.push('LOWER(userGroup.description) LIKE LOWER(:description)');
      queryParams.description = `%${filters.description}%`;
    }

    if (conditions.length > 0) {
      queryBuilder.andWhere(`(${conditions.join(' OR ')})`, queryParams);
    }

    // Add sorting
    if (orders) {
      Object.entries(orders).forEach(([key, value]) => {
        queryBuilder.orderBy(`userGroup.${key}`, value);
      });
    } else {
      queryBuilder.orderBy('userGroup.createdAt', 'DESC');
    }

    // Add pagination
    queryBuilder.skip((pageIndex - 1) * pageSize).take(pageSize);

    const [data, total] = await queryBuilder.getManyAndCount();

    return {
      data,
      total,
    };
  }

  async search(
    body: {
      name?: string;
      description?: string;
      isActive?: boolean;
    } & PageRequest,
  ): Promise<PageResponse<UserGroupEntity>> {
    const { pageIndex, pageSize, name, description, isActive } = body;

    const [data, total] = await this.userGroupRepo.findAndCount({
      where: {
        ...(name && { name: ILike(`%${name}%`) }),
        ...(description && { description: ILike(`%${description}%`) }),
        ...(isActive !== undefined && { isActive }),
      },
      skip: pageIndex && pageSize ? (pageIndex - 1) * pageSize : undefined,
      take: pageIndex && pageSize ? pageSize : undefined,
    });

    return {
      data,
      total,
      meta: {
        totalRecords: total,
        totalPages: Math.ceil(total / pageSize),
        request: body,
      },
    };
  }

  async findOne(id: string): Promise<UserGroupEntity | undefined> {
    return this.userGroupRepo.findOne(id, { relations: ['users'] });
  }

  async create(createData: Partial<UserGroupEntity>): Promise<UserGroupEntity> {
    try {
      // Check if name already exists
      const existingGroup = await this.userGroupRepo.findOne({ where: { name: createData.name } });
      if (existingGroup) {
        throw new Error(`Nhóm người dùng với tên ${createData.name} đã tồn tại!`);
      }

      // Create new group without users first
      const { users, typeId, ...groupData } = createData;
      const type: any = await this.generalDataDetailRepo.findOne({
        where: { id: typeId },
        relations: ['general'],
        select: ['id', 'code', 'name'],
      });

      const userGroup = this.userGroupRepo.create({
        ...groupData,
        typeId: type?.id,
        typeCode: type?.code,
      });
      const savedGroup = await this.userGroupRepo.save(userGroup);

      // If users are provided, process them
      if (users && Array.isArray(users) && users.length > 0) {
        // Handle both array of strings (IDs) and array of objects
        const validUserIds = users
          .map((user) => {
            if (typeof user === 'string') {
              return user; // It's already an ID
            } else if (typeof user === 'object' && user.id) {
              return user.id; // Extract ID from object
            }
            return null;
          })
          .filter((id) => id !== null);

        if (validUserIds.length > 0) {
          const groupWithUsers = await this.userGroupRepo.addUsersToGroup(
            savedGroup.id,
            validUserIds,
          );
          if (!groupWithUsers) {
            throw new Error('Failed to add users to group');
          }
          return groupWithUsers;
        }
      }

      // If no users were added, return the group with empty users array
      return this.userGroupRepo.findOne(savedGroup.id, { relations: ['users'] });
    } catch (error) {
      throw error;
    }
  }

  async update(id: string, updateData: Partial<UserGroupEntity>): Promise<UserGroupEntity> {
    const group = await this.findOne(id);
    if (!group) {
      throw new NotFoundException(`Không tìm thấy nhóm người dùng với ID ${id}!`);
    }

    // If name is being updated, check if new name already exists
    if (updateData.name && updateData.name !== group.name) {
      const existingGroup = await this.userGroupRepo.findOne({ where: { name: updateData.name } });
      if (existingGroup) {
        throw new Error(`Nhóm người dùng với tên ${updateData.name} đã tồn tại!`);
      }
    }

    // Handle users separately
    const { users, typeId, ...groupData } = updateData;
    const type: any = await this.generalDataDetailRepo.findOne({
      where: { id: typeId },
      relations: ['general'],
      select: ['id', 'code'],
    });
    // Object.assign(group, groupData);
    this.userGroupRepo.merge(group, {
      ...groupData,
      typeId: type?.id,
      typeCode: type?.code,
    });
    const savedGroup = await this.userGroupRepo.save(group);

    // If users are provided, process them
    if (users && Array.isArray(users) && users.length > 0) {
      // Handle both array of strings (IDs) and array of objects
      const validUserIds = users
        .map((user) => {
          if (typeof user === 'string') {
            return user; // It's already an ID
          } else if (typeof user === 'object' && user.id) {
            return user.id; // Extract ID from object
          }
          return null;
        })
        .filter((id) => id !== null);

      if (validUserIds.length > 0) {
        const groupWithUsers = await this.userGroupRepo.addUsersToGroup(
          savedGroup.id,
          validUserIds,
        );
        if (!groupWithUsers) {
          throw new Error('Failed to add users to group');
        }
        return groupWithUsers;
      }
    }

    // Return group with users loaded
    return this.userGroupRepo.findOne(savedGroup.id, { relations: ['users'] });
  }

  async remove(id: string): Promise<void> {
    const group = await this.findOne(id);
    if (!group) {
      throw new NotFoundException(`Không tìm thấy nhóm người dùng với ID ${id}!`);
    }
    await this.userGroupRepo.remove(group);
  }

  async toggleStatus(id: string): Promise<UserGroupEntity> {
    const group = await this.findOne(id);
    if (!group) {
      throw new NotFoundException(`Không tìm thấy nhóm người dùng với ID ${id}!`);
    }
    group.isActive = !group.isActive;
    return this.userGroupRepo.save(group);
  }

  async findGroupsByStatus(isActive: boolean): Promise<UserGroupEntity[]> {
    return this.userGroupRepo.find({ where: { isActive } });
  }

  async findGroupWithUsers(id: string): Promise<UserGroupEntity> {
    const group = await this.userGroupRepo.findGroupWithUsers(id);
    if (!group) {
      throw new NotFoundException(`Không tìm thấy nhóm người dùng với ID ${id}!`);
    }
    return group;
  }

  async addUsers(id: string, userIds: string[]): Promise<UserGroupEntity> {
    const group = await this.userGroupRepo.addUsersToGroup(id, userIds);
    if (!group) {
      throw new NotFoundException(`Không tìm thấy nhóm người dùng với ID ${id}!`);
    }
    return group;
  }

  async removeUsers(id: string, userIds: string[]): Promise<UserGroupEntity> {
    const group = await this.userGroupRepo.removeUsersFromGroup(id, userIds);
    if (!group) {
      throw new NotFoundException(`Không tìm thấy nhóm người dùng với ID ${id}!`);
    }
    return group;
  }

  async getMetaTypes() {
    const genCodes = [NSGeneralData.EGeneralDataCode.USER_GROUP];

    const genData = await this.generalDataRepo.find({
      where: { code: In(genCodes) },
      select: ['id', 'code'],
    });

    // Lấy toàn bộ chi tiết theo các generalId
    const details = await this.generalDataDetailRepo.find({
      where: { generalId: In(genData.map((item) => item.id)) },
      select: ['id', 'code', 'name', 'generalId'],
    });

    const result = genData.reduce(
      (acc, item) => {
        acc[item.code] = details
          .filter((detail) => detail.generalId === item.id)
          .map(({ id, code, name }) => ({ id, code, name, genCode: item.code }));
        return acc;
      },
      {} as Record<string, { id: string; code: string; name: string; genCode: string }[]>,
    );

    return result;
  }
}
