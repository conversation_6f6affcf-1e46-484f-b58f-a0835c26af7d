import { Body, Controller, Param, Query, UseGuards } from '@nestjs/common';
import { ApiOperation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { DefController, DefDelete, DefGet, DefPost, DefPut } from '~/@core/decorator';
import { Roles } from '~/@core/decorator/roles.decorator';
import { RoleGuard } from '~/@systems/guard/role.guard';
import { PageRequest } from '~/@systems/utils/page.utils';
import { StationService } from '~/x-modules/admin/services';

@DefController('stations')
export class StationController {
  constructor(private readonly service: StationService) {}

  @DefPost('list')
  @Roles('/system-configuration/station', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Lấy tất cả ' })
  async getAll() {
    return this.service.getAll();
  }

  @DefPost('search')
  @Roles('/system-configuration/station', 'View')
  @UseGuards(RoleGuard)
  async search(@Body() req: any) {
    return this.service.search(req);
  }

  @DefPost('save')
  @Roles('/system-configuration/station', 'View')
  @UseGuards(RoleGuard)
  async save(@Body() req: any) {
    return this.service.save(req);
  }

  @DefGet('detail/:id')
  @Roles('/system-configuration/station', 'View')
  @UseGuards(RoleGuard)
  async detail(@Param('id') id: string) {
    return this.service.getDetail(id);
  }

  @DefPost('fetch-available-devices')
  @Roles('/system-configuration/station', 'View')
  @UseGuards(RoleGuard)
  async getAvailableDevices(@Body() body: any) {
    return this.service.getAvailableDevices(body);
  }

  @DefPost('change-assign/:processId/devices/:deviceId')
  @Roles('/system-configuration/station', 'View')
  @UseGuards(RoleGuard)
  public async changeAssign(
    @Param('processId') processId: string,
    @Param('deviceId') deviceId: string,
    @Body() body: { assign: boolean },
  ) {
    return this.service.changeAssignDevice(processId, deviceId, body.assign);
  }

  @DefPost('meta/fetch-station-types')
  @Roles('/system-configuration/station', 'View')
  @UseGuards(RoleGuard)
  async getMetaStationTypes(@Body() req: PageRequest) {
    return this.service.getMetaStationTypes(req);
  }

  @DefPost('meta/fetch-sites')
  @Roles('/system-configuration/station', 'View')
  @UseGuards(RoleGuard)
  async getMetaSites(@Body() req: PageRequest) {
    return this.service.getMetaSites(req);
  }

  @DefPost('meta/fetch-factories')
  @Roles('/system-configuration/station', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Lấy danh sách factory theo site' })
  async getMetaFactories(@Body() req: { userId?: string; siteId?: string } & PageRequest) {
    return this.service.getMetaFactories(req);
  }

  @DefPost('meta/fetch-lines')
  @Roles('/system-configuration/station', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Lấy danh sách line theo factory' })
  async getMetaLines(@Body() req: { userId?: string; factoryId?: string } & PageRequest) {
    return this.service.getMetaLines(req);
  }

  @DefPost('meta/fetch-processes')
  @Roles('/system-configuration/station', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Lấy danh sách process theo line' })
  async getMetaProcesses(@Body() req: { userId?: string; lineId?: string } & PageRequest) {
    return this.service.getMetaProcesses(req);
  }

  @DefPost('meta/fetch-device-types')
  @Roles('/system-configuration/station', 'View')
  @UseGuards(RoleGuard)
  async getMetaDeviceTypes(@Body() req: PageRequest) {
    return this.service.getMetaDeviceTypes(req);
  }

  @DefGet('meta/fetch-default-access')
  @Roles('/system-configuration/station', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Lấy quyền truy cập mặc định của user' })
  async getMetaDefaultAccess() {
    return this.service.getMetaDefaultAccess();
  }
}
