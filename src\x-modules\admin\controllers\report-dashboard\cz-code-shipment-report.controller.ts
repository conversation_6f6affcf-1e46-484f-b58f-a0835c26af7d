import { Body, UseGuards } from '@nestjs/common';
import { DefController, DefPost } from '~/@core/decorator';
import { Roles } from '~/@core/decorator/roles.decorator';
import { RoleGuard } from '~/@systems/guard/role.guard';
import { CreateCZCodeShipmentReportDto, ListFilterReportDto } from '~/dto';
import { CZCodeShipmentReportService } from '~/x-modules/admin/services/report-dashboard/cz-shipment-report.service';

@DefController('cz-code-shipment-reports')
export class CZCodeShipmentReportController {
  constructor(private readonly service: CZCodeShipmentReportService) {}

  @DefPost('search')
  @Roles('/report/cz-code-shipment-report', 'View')
  @UseGuards(RoleGuard)
  async search(@Body() req: ListFilterReportDto) {
    return this.service.search(req);
  }

  @DefPost('submit-report')
  @Roles('/report/cz-code-shipment-report', 'Create')
  @UseGuards(RoleGuard)
  async submitReport(@Body() req: CreateCZCodeShipmentReportDto) {
    return this.service.submitReport(req);
  }
}
