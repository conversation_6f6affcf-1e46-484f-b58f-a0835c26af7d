import { NestFactory } from '@nestjs/core';
import { Context, Handler } from 'aws-lambda';
import 'reflect-metadata';
import { AppModule } from '~/app.module';
import { ShiftService } from '~/x-modules/admin/services/system-configuration/shift.service';


// Singleton app instance to avoid recreating for each request
let cachedApp: any = null;

async function bootstrap() {
  if (!cachedApp) {
    const app = await NestFactory.create(AppModule, {
      logger: ['error', 'warn'], // Reduce logging
    });
    cachedApp = app;
  }
  return cachedApp;
}

// Helper function to create response
function createResponse(statusCode: number, body: any) {
  return {
    statusCode,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Credentials': true,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(body),
  };
}

// Get all shifts
export const findAll: Handler = async (event: any, context: Context) => {
  try {
    const app = await bootstrap();
    const shiftService = app.get(ShiftService);
    const result = await shiftService.findAll();
    return createResponse(200, result);
  } catch (error) {
    console.error('Error getting shifts:', error);
    return createResponse(500, { message: error.message || 'Internal server error' });
  }
};

// Search organization units
export const search: Handler = async (event: any, context: Context) => {
  try {
    const term = event.queryStringParameters?.term;
    const app = await bootstrap();
    const shiftService = app.get(ShiftService);
    const result = await shiftService.search(term);
    return createResponse(200, result);
  } catch (error) {
    console.error('Error searching organization units:', error);
    console.error('Error stack:', error.stack);
    return createResponse(500, { message: error.message || 'Internal server error' });
  }
};

// Get organization unit by code
export const findByCode: Handler = async (event: any, context: Context) => {
  try {
    const code = event.pathParameters?.code;
    const app = await bootstrap();
    const shiftService = app.get(ShiftService);
    const result = await shiftService.findByCode(code);

    return createResponse(200, result);
  } catch (error) {
    console.error('Error getting organization unit by code:', error);
    console.error('Error stack:', error.stack);
    return createResponse(500, { message: error.message || 'Internal server error' });
  }
};

// Get organization unit by ID
export const findOne: Handler = async (event: any, context: Context) => {
  try {
    const id = event.pathParameters?.id;
    const app = await bootstrap();
    const shiftService = app.get(ShiftService);
    const result = await shiftService.findOne(id);
    return createResponse(200, result);
  } catch (error) {
    console.error('Error getting shift by id:', error);
    return createResponse(error.message.includes('not found') ? 404 : 500, {
      message: error.message || 'Internal server error',
    });
  }
};

// Update shift
export const update: Handler = async (event: any, context: Context) => {
  try {
    const id = event.pathParameters?.id;
    const body = JSON.parse(event.body || '{}');

    const app = await bootstrap();
    const shiftService = app.get(ShiftService);
    
    // Gọi service để xử lý logic (bao gồm các rule kiểm tra)
    const result = await shiftService.update(id, body);

    return createResponse(200, result);
  } catch (error) {
    console.error('Error updating shift:', error);
    return createResponse(error.message.includes('not found') ? 404 : 500, {
      message: error.message || 'Internal server error',
    });
  }
};

export const create: Handler = async (event: any, context: Context) => {
  try {
    const body = JSON.parse(event.body || '{}');
    const app = await bootstrap();
    const shiftService = app.get(ShiftService);
    const result = await shiftService.create(body);
    return createResponse(201, result);
  } catch (error) {
    const statusCode = error.message.includes('already exists') || error.message.includes('Start time must be earlier than end time') ? 400 : 500;
    return createResponse(statusCode, { message: error.message || 'Internal server error' });
  }
};

// Handle OPTIONS request for CORS
export const options: Handler = async (event: any, context: Context) => {
  return {
    statusCode: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers':
        'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-Amz-User-Agent',
      'Access-Control-Allow-Methods': 'OPTIONS,GET,PUT',
      'Access-Control-Allow-Credentials': true,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({}),
  };
};
