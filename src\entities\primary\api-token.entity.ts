import { ApiProperty } from '@nestjs/swagger';
import { Column, Entity, Index } from 'typeorm';
import { PrimaryBaseEntity } from '../primary-base.entity';

@Entity('api_token')
@Index(['key', 'path', 'method'], { unique: true })
export class ApiTokenEntity extends PrimaryBaseEntity {
  /**
   * Key
   */
  @ApiProperty({
    example: '123-456-789',
    description: 'Key',
  })
  @Column({ type: 'text' })
  @Index({ unique: false })
  key: string;

  /**
   * Path
   */
  @ApiProperty({
    example: '/api/v1/recipes',
    description: 'Path',
  })
  @Index({ unique: false })
  @Column({ type: 'varchar', length: 300 })
  path: string;

  /**
   * Methods
   */
  @ApiProperty({
    example: 'GET',
    description: 'Methods',
  })
  @Index({ unique: false })
  @Column({ type: 'varchar', length: 255 })
  method: string;

  /**
   * Expired at
   */
  @ApiProperty({
    example: '2023-01-01 12:00:00',
    description: 'Expired at',
  })
  @Column({ type: 'timestamptz', nullable: true })
  expiredAt: Date;
}
