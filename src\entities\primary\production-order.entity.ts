import { ApiProperty } from '@nestjs/swagger';
import { Column, Entity, Join<PERSON><PERSON>umn, ManyToOne } from 'typeorm';
import { PrimaryBaseEntity } from '../primary-base.entity';
import { ItemEntity } from './item.entity';
import { OrganizationUnitEntity } from './organization-unit.entity';
import { RecipeEntity } from '~/entities/primary/recipe.entity';
import { ShiftEntity } from '~/entities/primary/shift.entity';
import { UserEntity } from '~/entities/primary/user.entity';

@Entity('production_order')
export class ProductionOrderEntity extends PrimaryBaseEntity {
  /** ID của production order cha */
  @ApiProperty({ description: 'PO parent id' })
  @Column({ nullable: true })
  poParentId: string;

  /** Mã khu vực sản xuất của 1 production line, chọn từ bảng orgchart - chọn code site, thứ tự : site ->factory->line->process area */
  @ApiProperty({ description: 'Id site' })
  @Column({ nullable: true })
  siteId: string;
  @ManyToOne(() => OrganizationUnitEntity, (org) => org.id)
  @JoinColumn({ name: 'siteId', referencedColumnName: 'id' })
  site?: OrganizationUnitEntity;

  /** Mã nhà máy, chọn từ bảng orgchart - chọn các code có level=3 - Factory */
  @ApiProperty({ description: 'Mã nhà máy' })
  @Column({ nullable: true })
  factoryId: string;
  @ManyToOne(() => OrganizationUnitEntity, (org) => org.id)
  @JoinColumn({ name: 'factoryId', referencedColumnName: 'id' })
  factory?: OrganizationUnitEntity;

  /** Mã khu vực sản xuất của 1 production line, chọn từ bảng orgchart - chọn code có level 6 - Process area. Chỉ hiện những Process Area thuộc Factory chọn ở trên */
  @ApiProperty({ description: 'Mã khu vực sản xuất' })
  @Column({ nullable: true })
  processAreaId: string;
  @ManyToOne(() => OrganizationUnitEntity, (org) => org.id)
  @JoinColumn({ name: 'processAreaId', referencedColumnName: 'id' })
  processArea?: OrganizationUnitEntity;

  /** Mã khu vực sản xuất của 1 production line, chọn từ bảng orgchart code =Line */
  @ApiProperty({ description: 'Id line' })
  @Column({ nullable: true })
  lineId: string;
  @ManyToOne(() => OrganizationUnitEntity, (org) => org.id)
  @JoinColumn({ name: 'lineId', referencedColumnName: 'id' })
  line?: OrganizationUnitEntity;

  /** Hệ thống tự sinh không trùng theo rule tự động. Productcode+Ddmmyy+Shiftcode+Sitecode+Seqnumber. Seqnumber: là hai ký tự tăng dần theo plan start date time. ddmmyy: theo plan start date time */
  @ApiProperty({ description: 'Mã lệnh sản xuất' })
  @Column({ type: 'varchar', length: 250, unique: true })
  orderNo: string;

  /** Tên lệnh sản xuất */
  @ApiProperty({ description: 'Tên lệnh sản xuất' })
  @Column({ type: 'varchar', length: 128, nullable: true })
  orderName: string;

  /** Chọn từ danh mục recipe (tỉnh năng số 21 - 22), chỉ hiện recipe thuộc org chart đang tạo */
  @ApiProperty({ description: 'Mã công thức' })
  @Column({ nullable: true })
  recipeId: string;
  @ManyToOne(() => RecipeEntity, (org) => org.id)
  @JoinColumn({ name: 'recipeId', referencedColumnName: 'id' })
  recipe?: RecipeEntity;

  /** Chọn từ danh mục recipe (tỉnh năng số 21 - 22), chỉ hiện recipe thuộc org chart đang tạo */
  @ApiProperty({ description: 'Mã code công thức' })
  @Column({ type: 'varchar', length: 50, nullable: true })
  recipeCode: string;

  /** Chọn từ danh mục recipe (tỉnh năng số 21 - 22), chỉ hiện recipe thuộc org chart đang tạo */
  @ApiProperty({ description: 'Phiên bản công thức' })
  @Column({ nullable: true, type: 'numeric', precision: 20, scale: 2 })
  recipeVersion: number;

  /** Ca sản xuất, chọn từ danh mục ca (tỉnh năng Shift ở phần System config) */
  @ApiProperty({ description: 'Mã ca sản xuất' })
  @Column({ nullable: true })
  shiftId: string;
  @ManyToOne(() => ShiftEntity, (org) => org.id)
  @JoinColumn({ name: 'shiftId', referencedColumnName: 'id' })
  shift?: ShiftEntity;

  /** Ngày bắt đầu sản xuất dự kiến */
  @ApiProperty({ description: 'Ngày bắt đầu sản xuất dự kiến' })
  @Column({ type: 'timestamptz', nullable: true })
  planStartDate?: Date;

  /** Ngày hoàn thành sản xuất dự kiến */
  @ApiProperty({ description: 'Ngày hoàn thành sản xuất dự kiến' })
  @Column({ type: 'timestamptz', nullable: true })
  planEndDate?: Date;

  /** Ngày bắt đầu sản xuất thực tế */
  @ApiProperty({ description: 'Ngày bắt đầu sản xuất thực tế' })
  @Column({ type: 'timestamptz', nullable: true })
  actualStartDate?: Date;

  /** Ngày hoàn thành sản xuất thực tế */
  @ApiProperty({ description: 'Ngày hoàn thành sản xuất thực tế' })
  @Column({ type: 'timestamptz', nullable: true })
  actualEndDate?: Date;

  /** Ddmmyy+Shiftcode+Factorycode+Productionlinecode+...+Sitecode. Với ddmmyy theo Plan start date time. Cho phép sửa lại Lot No khi tạo Production order */
  @ApiProperty({ description: 'Số lô sản xuất' })
  @Column({ type: 'varchar', length: 128, nullable: true })
  lotNumber: string;

  /** Id trạng thái */
  @ApiProperty({ description: 'id Trạng thái từ general data' })
  @Column({ type: 'uuid', nullable: true })
  orderStatus: string;

  /** Mã trạng thái */
  @ApiProperty({ description: 'Mã trạng thái' })
  @Column({ type: 'varchar', length: 50, nullable: true })
  orderStatusCode: string;

  /** Số PO id gốc, Nếu PO được gen tự động từ PO giai đoạn trước, thì hệ thống tự động input id của PO giai đoạn trước */
  @ApiProperty({ description: 'Mã PO liên quan' })
  @Column({ type: 'varchar', length: 36, nullable: true })
  relatedOrder: string;

  /** Trường lưu thông tin PO đã được đồng bộ lên ERP chưa */
  @ApiProperty({ description: 'Đã đồng bộ lên ERP chưa' })
  @Column({ type: 'boolean', default: false })
  isErpSynced: boolean;

  /** Trường lưu thông tin PO đã được đồng bộ xuống Scada chưa */
  @ApiProperty({ description: 'Đã đồng bộ xuống SCADA chưa' })
  @Column({ type: 'boolean', default: false })
  isScadaSynced: boolean;

  /** Ngày đồng bộ qua scada */
  @ApiProperty({ description: 'Ngày đồng bộ qua scada' })
  @Column({ type: 'timestamptz', nullable: true })
  dateScadaSynced?: Date;

  @ApiProperty({ description: 'Lỗi khi đồng bộ Scada', example: 'Can not find' })
  @Column({ type: 'text', nullable: true })
  errorSyncScada?: string;

  @ApiProperty({ description: 'Loại lệnh sx , enum : EProductOrderType' })
  @Column({ type: 'varchar', length: 50, nullable: true })
  type: string;

  @ApiProperty({ description: 'Mã vật liệu' })
  @Column({ nullable: true })
  itemId: string;
  @ManyToOne(() => ItemEntity, (p) => p.id)
  @JoinColumn({ name: 'itemId', referencedColumnName: 'id' })
  item: ItemEntity;

  @ApiProperty({ description: 'Số lượng vật liệu' })
  @Column({ type: 'numeric', nullable: true, default: 0 })
  quantity: number;

  /** Id đơn vị tính */
  @ApiProperty({ description: 'Đơn vị giao dịch' })
  @Column({ type: 'varchar', length: 36, nullable: true })
  trxUom: string;

  /** Mã đơn vị tính */
  @ApiProperty({ description: 'Đơn vị tính' })
  @Column({ type: 'varchar', length: 50, nullable: true })
  trxUomCode: string;

  /** Người điều hành */
  @ApiProperty({ description: 'Người điều hành' })
  @Column({ nullable: true })
  operatorUserId: string;
  @ManyToOne(() => UserEntity, (user) => user.id)
  @JoinColumn({ name: 'operatorUserId', referencedColumnName: 'id' })
  operatorUser?: UserEntity;

  /** Mục đích để tính năng cân kiểm soát batch đã cân xong (bằng 1) */
  @ApiProperty({
    description: 'Mục đích để tính năng cân kiểm soát batch đã cân xong (bằng 1)',
  })
  @Column({ type: 'int', nullable: true, default: 0 })
  weighStatus: number;

  @ApiProperty({ description: 'Loại PO' })
  @Column({ default: 0, type: 'numeric' })
  orderSerialized: number;

  @ApiProperty({ description: 'Id đơn hàng khách hàng' })
  @Column({ default: null, nullable: true })
  customerOrderId: string;

  @ApiProperty({ description: 'Mã đơn hàng khách hàng' })
  @Column({ nullable: true })
  customerOrderNo: string;

  @ApiProperty({ description: 'Số lượng đơn hàng khách hàng' })
  @Column({ type: 'numeric', nullable: true })
  customerOrderQty: number;

  @ApiProperty({ description: 'GTIN' })
  @Column({ default: null, nullable: true })
  gtin: string;

  @ApiProperty({ description: 'Mã sản phẩm khách hàng' })
  @Column({ default: null, nullable: true })
  customerProductCode: string;

  @ApiProperty({ description: 'Tên sản phẩm khách hàng' })
  @Column({ default: null, nullable: true })
  customerProductName: string;

  @ApiProperty({ description: 'Tên viết tắt sản phẩm khách hàng' })
  @Column({ default: null, nullable: true })
  customerShortName: string;

  // Mối quan hệ với các bảng khác (nếu có)
  // Ví dụ: Factory, ProcessArea, Recipe, Shift
  // ...
}
