// event-hub.service.ts
import { Injectable, Logger, OnModuleDestroy } from '@nestjs/common';
import { EventHubProducerClient, EventDataBatch } from '@azure/event-hubs';
import { configEnv } from '~/@config/env';
const { AZURE_EVENT_HUB_CONNECTION_STRING, AZURE_EVENT_HUB_NAME, IS_EVENT_HUB } = configEnv();
@Injectable()
export class EventHubService implements OnModuleDestroy {
  private producer: EventHubProducerClient;
  private readonly logger = new Logger(EventHubService.name);
  constructor() {
    // Thay bằng connection string của bạn và tên event hub
    const connectionString = AZURE_EVENT_HUB_CONNECTION_STRING || '';
    const eventHubName = AZURE_EVENT_HUB_NAME || '';

    if (AZURE_EVENT_HUB_CONNECTION_STRING && AZURE_EVENT_HUB_NAME && IS_EVENT_HUB) {
      try {
        this.producer = new EventHubProducerClient(connectionString, eventHubName);
      } catch (error) {
        this.logger.error('Error creating EventHubProducerClient:', error);
      }
    }
  }

  async sendEvents(messages: any[]) {
    // console.log('messages', messages);

    if (AZURE_EVENT_HUB_CONNECTION_STRING && AZURE_EVENT_HUB_NAME && IS_EVENT_HUB)
      try {
        if (!this.producer) {
          this.logger.error('EventHubProducerClient is not defined.');
          return;
        }
        let batch: EventDataBatch = await this.producer.createBatch();

        for (const msg of messages) {
          const isAdded = batch.tryAdd({ body: msg });
          if (!isAdded) {
            // Gửi batch hiện tại vì batch đầy
            await this.producer.sendBatch(batch);
            this.logger.log(`Gửi batch thành công với ${batch.count} message(s)`);

            // Tạo batch mới
            const newBatch = await this.producer.createBatch();
            if (!newBatch.tryAdd({ body: msg })) {
              throw new Error('Message quá lớn để gửi');
            }
            batch = newBatch;
          }
        }

        if (batch.count > 0) {
          await this.producer.sendBatch(batch);
          this.logger.log(`Gửi batch cuối cùng thành công với ${batch.count} message(s)`);
        }
      } catch (error) {
        console.log('error', error);
        this.logger.error('Gửi message lên Event Hub thất bại:', error);
        // throw error;
      }
  }

  async onModuleDestroy() {
    await this.producer.close();
    this.logger.log('Đóng kết nối Event Hub Producer');
  }

  mappingData(data: { table: string; data: any[]; timestamp: Date }) {
    // Hàm convert data sang dạng json như sau:
    // {
    //   "log_name": "name_of_table",
    //   "send_time": "time_to_send",
    //   "data": ["{\"first_name\" : \"Sally\", \"last_name\"  : \"Green\",\"age\"      : 27 }",
    // "{ \"first_name\" : \"Jim\",\"last_name\"  : \"Galley\",\"age\"       : 41 }"]
    // }
    // Lưu ý: data là mảng của các object và các cột của object phải convert sang snake_case. Ví dụ:
    // firstName -> first_name
    // lastName -> last_name
    // age -> age
    // Convert data sang snake_case
    const dataSnakeCase = data.data.map((item: any) => this.convertColumnName(item));

    const dataJson = JSON.stringify(dataSnakeCase);
    return {
      log_name: data.table,
      send_time: data.timestamp,
      data: [dataJson],
    };
  }

  // Viết hàm convert sang snake_case
  convertToSnakeCase = (str: string) => {
    return str.replace(/([A-Z])/g, '_$1').toLowerCase();
  };

  // Convert tên cột sang snake_case
  convertColumnName = (obj: any) => {
    const newObj = {};
    for (const key in obj) {
      if (obj.hasOwnProperty(key)) {
        const newKey = this.convertToSnakeCase(key);
        newObj[newKey] = obj[key];
      }
    }
    return newObj;
  };
}
