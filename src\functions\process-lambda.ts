import { Context, Handler } from 'aws-lambda';
import { NestFactory } from '@nestjs/core';
import { AppModule } from '~/app.module';
import { initializeTransactionalContext } from 'typeorm-transactional-cls-hooked';
import { ProcessService } from '~/x-modules/admin/services';
// import { ProcessService } from '~/x-modules/system-configuration/services/process.service';

let cachedApp: any = null;

async function bootstrap() {
  if (!cachedApp) {
    initializeTransactionalContext();
    const app = await NestFactory.createApplicationContext(AppModule);
    await app.init();
    cachedApp = app;
  }

  return cachedApp;
}

export const list: Handler = async (event: any, context: Context) => {
  const app = await bootstrap();
  const service = app.get(ProcessService);
  const params = event.queryStringParameters || {};

  const result = await service.list(params);
  return {
    statusCode: 200,
    body: JSON.stringify({ message: 'Data fetched successfully', data: result }),
  };
};

export const create: Handler = async (event: any, context: Context) => {
  const app = await bootstrap();
  const service = app.get(ProcessService);
  const body = JSON.parse(event.body);

  const result = await service.create(body);
  return {
    statusCode: 201,
    body: JSON.stringify({ message: 'Create process success', data: result }),
  };
};

export const update: Handler = async (event: any, context: Context) => {
  const app = await bootstrap();
  const service = app.get(ProcessService);
  const { id } = event.pathParameters;
  const body = JSON.parse(event.body);

  const result = await service.update(id, body);
  return {
    statusCode: 200,
    body: JSON.stringify({ message: 'Update machine success', data: result }),
  };
};

export const deleteHandler: Handler = async (event: any, context: Context) => {
  const app = await bootstrap();
  const service = app.get(ProcessService);
  const { id } = event.pathParameters;

  await service.delete(id);
  return {
    statusCode: 200,
    body: JSON.stringify({ message: 'Delete machine success' }),
  };
};

export const findByIdHandler: Handler = async (event: any, context: Context) => {
  try {
    const application = await bootstrap();
    const service = application.get(ProcessService);

    const { id } = event.pathParameters || {};
    if (!id) {
      return {
        statusCode: 400,
        body: JSON.stringify({ message: 'ID is required' }),
      };
    }

    const process = await service.findById(id);
    return {
      statusCode: 200,
      body: JSON.stringify({
        message: 'Find process success',
        data: process,
      }),
    };
  } catch (error) {
    console.error('Error:', error);
    return {
      statusCode: 404,
      body: JSON.stringify({ message: error.message || 'Failed to find process' }),
    };
  }
};

export const getMachineList: Handler = async (event: any, context: Context) => {
  const app = await bootstrap();
  const service = app.get(ProcessService);
  const params = event.queryStringParameters || {};

  const result = await service.getMachineList(params);
  return {
    statusCode: 200,
    body: JSON.stringify({ message: 'Data fetched successfully', data: result }),
  };
};

export const getForOrganizationUnits: Handler = async (event: any, context: Context) => {
  const app = await bootstrap();
  const service = app.get(ProcessService);
  const params = event.queryStringParameters || {};

  const result = await service.getForOrganizationUnits(params);
  return {
    statusCode: 200,
    body: JSON.stringify({ message: 'Data fetched successfully', data: result }),
  };
};