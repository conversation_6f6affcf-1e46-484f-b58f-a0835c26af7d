import { HttpStatus, Injectable } from '@nestjs/common';
import { BindRepo, DefTransaction } from '~/@core/decorator';
import { EHttpStatusMessage } from '~/@core/network';
import { ErpApiResponse } from '~/@systems/exceptions/dto/erp-exception';
import { NSItem } from '~/common/enums/NSItem';
import { ERPCreateItemReq, ERPItemStatus } from '~/dto/erp/item.dto';
import { ItemEntity, UomConversionEntity } from '~/entities/primary';
import { ItemRepo, UomConventionRepo } from '~/repositories/primary';

@Injectable()
export class ERPItemIntegrateService {
  constructor() {}
  @BindRepo(ItemRepo)
  private readonly itemRepo: ItemRepo;
  @BindRepo(UomConventionRepo)
  private readonly uomConventionRepo: UomConventionRepo;

  async batchCreateOrUpdate(data: Array<ERPCreateItemReq>) {
    try {
      const happened: Record<string, boolean> = {};
      const result: Array<ItemEntity> = [];
      for await (const item of data) {
        if (happened[item.ItemCode]) {
          throw new ErpApiResponse(EHttpStatusMessage[HttpStatus.CONFLICT], HttpStatus.CONFLICT, {
            message: `Item code ${item.ItemCode} duplicated.`,
          });
        }
        happened[item.ItemCode] = true;
        const itemResult = await this.createOrUpdate(item);
        result.push(itemResult);
      }
      return {
        status: HttpStatus.CREATED,
        message: 'Batch create or update successful.',
        data: result,
      };
    } catch (error) {
      if (error instanceof ErpApiResponse) {
        throw error;
      }
      throw new ErpApiResponse(
        EHttpStatusMessage[HttpStatus.INTERNAL_SERVER_ERROR],
        HttpStatus.INTERNAL_SERVER_ERROR,
        {
          message: error.message,
        },
      );
    }
  }

  @DefTransaction()
  async createOrUpdate(data: ERPCreateItemReq) {
    let item = await this.itemRepo.findOne({
      where: {
        code: data.ItemCode,
      },
      relations: ['uomConversions'],
    });

    if (item) {
      // Xoá tất cả các uomConversion cũ
      await this.uomConventionRepo.delete({
        itemId: item.id,
      });
    } else {
      item = new ItemEntity();
      item.code = data.ItemCode;
    }
    item.name = data.ItemName;
    item.baseUnit = data.BaseUnit;
    item.type = data.ItemType;
    item.group = data.Group;
    item.category = data.Category;
    item.brand = data.Brand;
    item.inventoryUnit = data.InventoryUnit;
    item.shelfLifeDay = data.ShelfLifeDay;
    item.status =
      data.ItemStatus == ERPItemStatus.Active ? NSItem.Status.Active : NSItem.Status.Inactive;

    item.uomConversions =
      data.MHUTypes?.map((uom) => {
        const uomConversion = new UomConversionEntity();
        uomConversion.fromUnit = uom.FromUnit;
        uomConversion.toUnit = uom.ToUnit;
        uomConversion.conversion = Number(uom.Conversion);
        return uomConversion;
      }) || [];

    await this.itemRepo.save(item);
    return item;
  }
}
