import { Injectable } from '@nestjs/common';
import { BindRepo } from '~/@core/decorator';
import moment from 'moment';
import { JwtService } from '@nestjs/jwt';
import { Connection, In, Not, Raw } from 'typeorm';
import { promises } from 'dns';
import { dateHelper } from '~/common/helpers/date.helper';
import {
  KpiScoreRepo,
  MaterialGroupDetailRepo,
  ProductionOrderMaterialRepo,
  ProductionOrderRepo,
  UomConventionRepo,
} from '~/repositories/primary';

export interface KpiData {
  materialGroupId: string;
  factoryId: string;
  siteId: string;
  productionAreaId: string;
  kpiSetHeaderId: string;
  kpiSetGroupId: string;
  kpiId: string;
  kpiSetDetailId: string;
}

export interface MaterialRow {
  itemId: string;
  shiftId: string;
  factoryId: string;
  productionDate: string;
  planTrxQty: number;
  actualTrxQty: number;
  planTrxQtyByLineType: number;
  distinctKey: string;
}

export interface MaterialDB {
  itemId: string;
  shiftId: string;
  productionDate: string;
  actualTrxQty: number;
  planTrxQty: number;
  lineType: string;
}

@Injectable()
export class LossPowderKpi {
  constructor(
    private jwtService: JwtService,
    private readonly connection: Connection
  ) {}
  @BindRepo(KpiScoreRepo)
  private readonly kpiScoreRepo: KpiScoreRepo;
  @BindRepo(ProductionOrderMaterialRepo)
  private readonly productionOrderMaterialRepo: ProductionOrderMaterialRepo;
  @BindRepo(MaterialGroupDetailRepo)
  private readonly materialGroupDetailRepo: MaterialGroupDetailRepo;
  @BindRepo(ProductionOrderRepo)
  private readonly productionOrderRepo: ProductionOrderRepo;
  @BindRepo(UomConventionRepo)
  private readonly uomConventionRepo: UomConventionRepo;
  
  async getDataByKpi() {
    const result = await this.connection.query(`
      SELECT ksh.* ,'||' "||", ksg."kpiGroupId",ksg."kpiGroupCode", ksg."kpiGroupName", ksg."kpiSetHeaderId", ksg.id as idbanggroup,
      '||' "||", ksd."kpiId", ksd."id" AS "kpiSetDetailId", ksd."kpiSetGroupId" ,'||' "||", k.code, k."shortName", k."utilityType" ,'||' "||", 
      kspa."factoryId" , ou.code , kspa."productionAreaId", pa.code , pa."name" , kspa."siteId", k."inputFrequency", k.unit, k."materialGroupId"
      --kp.kpivalue , kp."shiftId" , kp.podate 
      from kpi_set_header ksh 
      join kpi_set_group ksg  on ksh.id = ksg."kpiSetHeaderId"
      join kpi_set_detail ksd on ksd."kpiSetGroupId" = ksg.id 
      join kpi k  on k.id = ksd."kpiId"
      join kpi_set_production_area kspa  on ksh.id = kspa."kpiSetHeaderId"
      join organization_units ou on kspa."factoryId" = ou.id
      join production_area pa on pa.id = kspa."productionAreaId"
      --left join kpivalue as kp on kp.factory_id = kspa."factoryId" and kp."productionAreaId" =kspa."productionAreaId" and kp.utility_meter_type_detail_code =k."utilityType"
      where ksh.status = 1 and k."kpiMethod" = 'A' and k."autoFunction"  = 'F_KPI_MATERIAL'  and kspa.status =1 and k.status =1  
    `);
  
    return result;
  }

  async onGetKpiPeriodId (date: any) {
    const getDate = moment(date, "YYYY-MM-DD HH:mm:ss.SSS Z").format('YYYY-MM-DD');
    const KpiPeriodData = await this.connection.query(`
      SELECT kp.*
      FROM kpi_period kp
      join kpi_period_rule kpr on kp."kpiPeriodRuleId"::uuid = kpr."id"
      WHERE DATE($1) BETWEEN DATE(kp."startDate") AND DATE(kp."endDate")
      and kpr."cycleCode" = '1'
    `, [getDate]);
    return KpiPeriodData[0] ? KpiPeriodData[0]?.id : null;
  }

  async getMaterialDetailData (materialGroup: string) {
    const result = await this.materialGroupDetailRepo.find({
      where: {headerId : materialGroup, active: true}
    })
    if(!result || result?.length === 0) return '';

    const itemArray = result.map(e => e.itemId)
    return itemArray;
  }

  async getMaterialData (materialData: any, factoryId: string) {
    if(!materialData || materialData?.length === 0) return [];

    let queryObj = [factoryId]
    const countIndex = queryObj?.length + 1;
    const itemPlaceholders = materialData.map((_: any, index: number) => `$${countIndex + index}`).join(", ");
    queryObj = queryObj.concat(materialData)

    const result = await this.connection.query(`
      SELECT 
        po."planStartDate" AS "productionDate",
        SUM(pom."planTrxQty") AS "planTrxQtyValue",
        SUM(pom."actualTrxQty") as "actualTrxQty",
        po."shiftId",
        STRING_AGG(pom."orderNo", ', ') AS "orderNos",
        STRING_AGG(pom."id"::TEXT, ', ') AS "materialIds"
      FROM production_order_material pom
      JOIN production_order po ON pom."orderId" = po.id
      JOIN general_data_details gd ON gd."id" = po."orderStatus" and gd."id" = '26cb2e25-b6ed-4154-9ed6-2436ce741183'
      WHERE pom."materialId" in (${itemPlaceholders})
        and po."factoryId" = $1
        and pom."actualTrxQty" <> 0
      GROUP BY po."planStartDate", po."shiftId"
      ORDER BY po."planStartDate", po."shiftId";
    `, queryObj);

    return result;
  }

  async onSumProductionOee (materialInfo: any, factoryId: string) {
    let result = 0
    const productionOEEData = await this.connection.query(`
      SELECT 
            po."shiftId",
            s.code,
            SUM(po."actualProductQty") - SUM(po."defectiveProducts") AS actualqty,
            ou."parentId" AS factory_id,
            ou_fak.code,
           	DATE(po."productionDate") AS date
      FROM production_oee po
      INNER JOIN organization_units ou ON ou.id = CAST(po."productionLineId" AS uuid)
      INNER JOIN organization_units ou_fak ON ou."parentId" = ou_fak.id
      INNER JOIN shift s ON po."shiftId" = s.id
      WHERE po."shiftId" = $1
        AND ou."parentId" = $2
        AND po."productionDate" = $3
      GROUP BY po."shiftId", ou."parentId", po."productionDate", s.code, ou_fak.code
    `, [materialInfo?.shiftId, factoryId, materialInfo?.productionDate]);
    if(!productionOEEData || productionOEEData?.length === 0) return 0;

    await Promise.all(
      productionOEEData.map(async (productionOEE:any) => {
        const getActualqty = !isNaN(Number(productionOEE?.actualqty)) ? Number(productionOEE?.actualqty) : productionOEE?.actualqty
        result += getActualqty 
      })
    )
    return result;
  }

  async getBaseUOM (orderId: string, trxUom: string) {
    const productionOrderData = await this.productionOrderRepo.findOne({ where: {id: orderId}})
    if(!productionOrderData || trxUom === productionOrderData?.trxUom) return 1;

    const uomConventionData = await this.uomConventionRepo.find({ where: { itemId: productionOrderData?.itemId} })
    if(!uomConventionData && uomConventionData?.length === 0) return 1;

    const UOMConversionData = this.findSingleIntermediate(trxUom, productionOrderData?.trxUom, uomConventionData) || 1  
    return UOMConversionData
  }

  findSingleIntermediate(fromUom: any, toUom: any, lstUomConverse: any) {
    const unitMap = {};
    const conversionMap = {};
  
    // Tạo map các đơn vị kết nối lẫn nhau
    lstUomConverse.forEach(({ fromUnit, toUnit, conversion }) => {
      if (!unitMap[fromUnit]) unitMap[fromUnit] = new Set();
      if (!unitMap[toUnit]) unitMap[toUnit] = new Set();
  
      unitMap[fromUnit].add(toUnit);
      unitMap[toUnit].add(fromUnit); // 2 chiều
      // Lưu tỉ lệ quy đổi theo cả 2 chiều
      conversionMap[`${fromUnit}->${toUnit}`] = conversion;
      conversionMap[`${toUnit}->${fromUnit}`] = 1 / conversion;
    });
  
    const visited = new Set();
    const queue = [{ unit: fromUom, rate: 1 }];
  
    while (queue.length > 0) {
      const { unit, rate } = queue.shift();
      if (unit === toUom) {
        return rate;
      }
  
      visited.add(unit);
  
      const neighbors = unitMap[unit] || new Set();
      for (const neighbor of neighbors) {
        if (!visited.has(neighbor)) {
          const conversionRate = conversionMap[`${unit}->${neighbor}`];
          queue.push({ unit: neighbor, rate: rate * conversionRate });
        }
      }
    }
  
    return null; // Không tìm thấy chuyển đổi
  }

  async createLossPowderKpi(data: { fromDate?: string; toDate?: string }) {
    try {
      let getDateFrom = null
      let getDateTo = null
      if(data.fromDate && data.toDate){
        getDateFrom = dateHelper.formatDateByJobManual(data.fromDate)
        getDateTo = dateHelper.formatDateByJobManual(data.toDate)
      }
      // Set-up data
      const dataByKpis = await this.getDataByKpi()
      if(!dataByKpis || dataByKpis?.length === 0) return;

      await Promise.all(
        dataByKpis.map(async (dataByKpi:any) => {
          // B6 Lấy danh sách các material cần tính theo bộ KPI bằng cách lấy tất cả các material còn active trong các material group của các KPI ở B4.
          const getMaterialGroupId = dataByKpi?.materialGroupId
          const materialDetailData = await this.getMaterialDetailData(getMaterialGroupId)
          if(!materialDetailData || materialDetailData?.length === 0) return;

          //B7 Quét dữ liệu bảng production_order_material liên kết với bảng production order để lấy các order_id có plan start date theo tham số thời gian cần quét.
          const materialData = await this.getMaterialData(materialDetailData, dataByKpi?.factoryId)
          // console.log(materialData, 'materialData')
          if(!materialData || materialData?.length === 0) return;

          await materialData.reduce(async (previousPromise, material) => {
            // Chờ cho promise từ lần lặp trước hoàn thành
            await previousPromise;
            // A = planTrxQty
            // B = actualTrxQty
            // C = planTrxQtyByLineType
            const planTrxQtyValue = material?.planTrxQtyValue ? Number(material?.planTrxQtyValue) : 0
            const actualTrxQtyValue = material?.actualTrxQty ? Number(material?.actualTrxQty) : 0
            const orderNoStr = material?.orderNos || ''

            // Tách chuỗi thành mảng, loại bỏ khoảng trắng và lọc phần tử trùng
            const orderNos = [...new Set(orderNoStr.split(',').map((s: string) => s.trim()))];
            let planTrxQtyByLineType  = 0
            const materialLineTypeData = await this.productionOrderMaterialRepo.find({
              where: {
                orderNo: In(orderNos),
                lineType: '1',
                actualTrxQty: Not(0)
              }
            })
            await Promise.all(materialLineTypeData.map(async(materialLineType) => {
              const getBaseUOMValue = await this.getBaseUOM(materialLineType?.orderId, materialLineType?.trxUom)
              planTrxQtyByLineType += (Number(materialLineType?.planTrxQty) * getBaseUOMValue)
            }))

            // E = sumProductionOee
            const sumProductionOee = await this.onSumProductionOee(material, dataByKpi?.factoryId)

            // F = E*A/C theo material_group_code
            const materialRequirement = (sumProductionOee * planTrxQtyValue)/ planTrxQtyByLineType

            // materialLossRate =((B-F)/F)*100
            const materialLossRate = ((actualTrxQtyValue - materialRequirement)/materialRequirement) * 100
            
            if(!isFinite(materialLossRate)) return;

            const getKpiPeriodId = await this.onGetKpiPeriodId(material?.productionDate) || null;
            if(!getKpiPeriodId) return;

            if(!dataByKpi?.factoryId) return;

            const kpiScoreObj = {
              kpiSetHeaderId: dataByKpi?.kpiSetHeaderId,
              siteId: dataByKpi?.siteId,
              factoryId: dataByKpi?.factoryId,
              productionAreaId: dataByKpi?.productionAreaId,
              kpiSetGroupId: dataByKpi?.kpiSetGroupId,
              kpiId: dataByKpi?.kpiId,
              kpiSetDetailId: dataByKpi?.kpiSetDetailId,
              shiftId: material?.shiftId,
              kpiPeriodId: getKpiPeriodId,
              actualScore: materialLossRate,
              actualValue: planTrxQtyByLineType,
              actualQty: sumProductionOee,
              scoreDate: material?.productionDate,
              source: 'Job40.2'
            }
            const handleScoreDate =  moment(material?.productionDate, "YYYY-MM-DD HH:mm:ss.SSS Z").format('YYYY-MM-DD')
            const existing = await this.kpiScoreRepo.findOne({
              where: {
                productionAreaId: kpiScoreObj?.productionAreaId,
                kpiSetHeaderId: kpiScoreObj?.kpiSetHeaderId,
                shiftId: kpiScoreObj?.shiftId,
                kpiSetGroupId: kpiScoreObj?.kpiSetGroupId,
                kpiId: kpiScoreObj?.kpiId,
                factoryId: kpiScoreObj?.factoryId,
                scoreDate: Raw((alias) => `DATE(${alias}) = DATE(:scoreDate)`, {
                  scoreDate: handleScoreDate,
                }),
                // scoreDate: kpiScoreObj?.scoreDate
              },
            });

            if(existing){
              await this.kpiScoreRepo.update(
                { id: existing?.id },
                { 
                  actualScore: kpiScoreObj.actualScore,
                  actualValue: kpiScoreObj.actualValue,
                  actualQty: kpiScoreObj.actualQty
                }
              )
            }else{
              await this.kpiScoreRepo.save({...kpiScoreObj})
            }

            return Promise.resolve(); 
          }, Promise.resolve());
        })
      )

    } catch (error) {
    }
  }
}
