// // event-consumer.service.ts
// import { Injectable, OnModuleInit, OnModuleDestroy } from '@nestjs/common';
// import {
//   EventHubConsumerClient,
//   earliestEventPosition,
//   ReceivedEventData,
//   PartitionContext,
//   Subscription,
// } from '@azure/event-hubs';
// import { configEnv } from '~/@config/env';
// const { AZURE_EVENT_HUB_CONNECTION_STRING, AZURE_EVENT_HUB_NAME } = configEnv();

// @Injectable()
// export class EventConsumerService implements OnModuleInit, OnModuleDestroy {
//   private consumerClient: EventHubConsumerClient;
//   private subscription: Subscription;

//   constructor() {
//     // Thay thế các giá trị placeholder
//     const connectionString = AZURE_EVENT_HUB_CONNECTION_STRING;
//     const eventHubName = AZURE_EVENT_HUB_NAME; // Cùng Event Hub Name với bên gửi
//     // Consumer group mặc định là "$Default", bạn có thể tạo consumer group riêng nếu cần
//     const consumerGroup = '$Default'; // Hoặc tên consumer group của bạn

//     if (!connectionString || !eventHubName || !consumerGroup) {
//       console.log('Connection string or event hub name is not defined.');
//     } else
//       this.consumerClient = new EventHubConsumerClient(
//         consumerGroup,
//         connectionString,
//         eventHubName,
//       );
//   }

//   async onModuleInit() {
//     console.log('Initializing Event Hub consumer...');
//     await this.startConsumingEvents();
//   }

//   async onModuleDestroy() {
//     console.log('Stopping Event Hub consumer...');
//     if (this.subscription) {
//       await this.subscription.close();
//     }
//     if (this.consumerClient) {
//       await this.consumerClient.close();
//     }
//     console.log('Event Hub consumer stopped.');
//   }

//   private async startConsumingEvents(): Promise<void> {
//     try {
//       this.subscription = this.consumerClient.subscribe(
//         {
//           // Hàm xử lý khi nhận được event
//           processEvents: async (events: ReceivedEventData[], context: PartitionContext) => {
//             if (events.length === 0) {
//               // console.log(`No events received on partition ${context.partitionId}.`);
//               return;
//             }

//             console.log(`Received ${events.length} events from partition ${context.partitionId}:`);
//             for (const event of events) {
//               console.log('------------------------------------------------------');
//               console.log(`Event Body:`, event.body);
//               console.log(`Sequence Number: ${event.sequenceNumber}`);
//               console.log(`Offset: ${event.offset}`);
//               console.log(`Enqueued Time: ${event.enqueuedTimeUtc.toISOString()}`);
//               if (event.properties) {
//                 console.log(`Properties:`, event.properties);
//               }
//               if (event.systemProperties) {
//                 console.log(`System Properties:`, event.systemProperties);
//               }
//               console.log('------------------------------------------------------');

//               // QUAN TRỌNG: Xử lý checkpoint để consumer biết đã xử lý event đến đâu.
//               // Điều này giúp tránh xử lý lại event đã xử lý nếu consumer bị restart.
//               // Checkpoint thường xuyên (ví dụ, sau mỗi batch hoặc sau một số lượng event nhất định).
//               try {
//                 await context.updateCheckpoint(event);
//                 // console.log(`Successfully checkpointed event on partition ${context.partitionId} at sequence number ${event.sequenceNumber}`);
//               } catch (checkpointErr) {
//                 console.error(
//                   `Error checkpointing event on partition ${context.partitionId}:`,
//                   checkpointErr,
//                 );
//                 // Xử lý lỗi checkpointing (ví dụ: retry, log)
//               }
//             }
//           },

//           // Hàm xử lý khi có lỗi xảy ra trong quá trình nhận event
//           processError: async (err: Error, context: PartitionContext) => {
//             console.error(`Error on partition "${context.partitionId}": ${err.message}`);
//             // Xử lý các lỗi cụ thể ở đây. Ví dụ:
//             if (err.name === 'ReceiverDisconnectedError') {
//               console.error(
//                 `Receiver disconnected for partition ${context.partitionId}. Attempting to reconnect or restart subscription might be needed.`,
//               );
//             }
//             // Một số lỗi có thể yêu cầu đóng và mở lại subscription hoặc client.
//           },

//           // (Tùy chọn) Hàm xử lý khi subscription được khởi tạo cho một partition
//           processInitialize: async (context: PartitionContext) => {
//             console.log(`Subscription initialized for partition ${context.partitionId}.`);
//           },

//           // (Tùy chọn) Hàm xử lý khi subscription bị đóng cho một partition
//           processClose: async (reason, context: PartitionContext) => {
//             console.log(
//               `Subscription closed for partition ${context.partitionId}. Reason: ${reason}`,
//             );
//           },
//         },
//         {
//           // Bắt đầu đọc từ event sớm nhất trong partition
//           // Các tùy chọn khác: latestEventPosition, hoặc chỉ định offset/sequenceNumber cụ thể
//           startPosition: earliestEventPosition,
//           // (Tùy chọn) Số lượng event tối đa được trả về trong một lần gọi `processEvents`
//           // maxBatchSize: 10,
//           // (Tùy chọn) Thời gian chờ tối đa (ms) để `processEvents` được gọi nếu không có event nào
//           // maxWaitTimeInSeconds: 5,
//         },
//       );

//       console.log('Event Hub consumer started and subscribed to events.');
//     } catch (err) {
//       console.error('Failed to start Event Hub consumer:', err);
//     }
//   }

//   // (Tùy chọn) Một hàm để lấy các event đã nhận gần đây (cho mục đích test đơn giản)
//   // Lưu ý: Cách tiếp cận này không phù hợp cho production vì event sẽ liên tục đến.
//   // Dưới đây chỉ là một ví dụ rất cơ bản nếu bạn muốn xem chúng từ một endpoint.
//   // private receivedEventsForTesting: any[] = [];

//   // getRecentEvents() {
//   //   const events = [...this.receivedEventsForTesting];
//   //   this.receivedEventsForTesting = []; // Clear after fetching
//   //   return events;
//   // }
// }
