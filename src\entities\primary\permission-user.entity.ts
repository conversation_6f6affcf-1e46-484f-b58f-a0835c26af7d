import { <PERSON>ti<PERSON>, Column, <PERSON><PERSON>o<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, CreateDate<PERSON><PERSON>umn } from 'typeorm';
import { PrimaryBaseEntity } from '../primary-base.entity';
import { PermissionEntity } from './permission.entity';
import { UserEntity } from './user.entity';
import { ApiProperty } from '@nestjs/swagger';

@Entity('permission-user')
export class PermissionUserEntity extends PrimaryBaseEntity {
  /** Id nhóm quyền */
  @Column({
    name: 'permissionId',
    nullable: true,
  })
  permissionId: string;

  /** Production Area */
  @ManyToOne(() => PermissionEntity, (p) => p.permissionUsers)
  @JoinColumn({ name: 'permissionId', referencedColumnName: 'id' })
  permission: Promise<PermissionEntity>;

  /** Id nhân viên thuộc nhóm quyền đó */
  @Column({
    name: 'userId',
    nullable: true,
  })
  userId: string;

  /** Production Area */
  @ManyToOne(() => UserEntity, (p) => p.permissionUsers)
  @JoinColumn({ name: 'userId', referencedColumnName: 'id' })
  user: Promise<UserEntity>;

  /** Activity status */
  @Column({
    type: 'boolean',
    default: false,
  })
  @ApiProperty({ description: 'User activity status', type: 'boolean' })
  isActive: boolean;

  /** Account valid from date */
  @Column({
    type: 'timestamptz',
    nullable: true,
  })
  @ApiProperty({ description: 'Account valid from date', type: 'string', required: true })
  validFrom: Date;

  /** Account valid to date */
  @Column({
    type: 'timestamptz',
    nullable: true,
  })
  @ApiProperty({ description: 'Account valid to date', type: 'string', required: false })
  validTo: Date;
}
