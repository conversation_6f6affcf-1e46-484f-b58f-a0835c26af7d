import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsNotEmpty, IsNumber, IsOptional, IsString, IsUUID, IsBoolean } from 'class-validator';

export class StationReq {
  @ApiPropertyOptional({ description: 'ID' })
  @IsOptional()
  @IsUUID()
  id?: string;

  @ApiProperty({ description: 'Mã' })
  @IsNumber()
  code: number;

  @ApiProperty({ description: 'Mô tả' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: 'Station type code' })
  @IsNotEmpty()
  @IsString()
  typeCode: string;

  @ApiProperty({ description: 'Site ID' })
  @IsOptional()
  // @IsUUID()
  siteId?: string;

  @ApiProperty({ description: 'Factory ID' })
  @IsOptional()
  // @IsUUID()
  factoryId?: string;

  @ApiProperty({ description: 'Line ID' })
  @IsOptional()
  // @IsUUID()
  lineId?: string;

  @ApiProperty({ description: 'Process ID' })
  @IsOptional()
  // @IsUUID()
  processId?: string;

  @ApiProperty({ description: 'Is active' })
  @IsBoolean()
  isActive: boolean;

  @ApiProperty({ description: 'Ghi chú' })
  @IsOptional()
  @IsString()
  note?: string;

  @ApiProperty({ description: 'Danh sách device' })
  @IsOptional()
  deviceIds?: string[];
}
