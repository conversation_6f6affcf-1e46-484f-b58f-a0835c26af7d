import { Body, Query, UseGuards } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { DefController, DefGet, DefPost } from '~/@core/decorator';
import { UtilityTransactionService } from '../../services';
import {
  ListUtilityTransaction,
  UtilityMonitoringReq,
  UtilityTransactionReq,
} from '~/dto/utility-transaction.dto';
import { Roles } from '~/@core/decorator/roles.decorator';
import { RoleGuard } from '~/@systems/guard/role.guard';

@ApiTags('Utility Transaction')
@DefController('utility-transaction')
export class UtilityTransactionController {
  constructor(private readonly service: UtilityTransactionService) {}

  @ApiOperation({ summary: 'Danh sách phân trang utility transaction' })
  @DefGet('')
  @Roles('/production-execution/utility-transaction', 'View')
  @UseGuards(RoleGuard)
  async list(@Query() params: ListUtilityTransaction) {
    return this.service.pagination(params);
  }

  @ApiOperation({ summary: 'Tạo Utility Transaction' })
  @DefPost('')
  @Roles('/production-execution/utility-transaction', 'View')
  @UseGuards(RoleGuard)
  async create(@Body() body: UtilityTransactionReq) {
    return await this.service.create(body);
  }

  @ApiOperation({ summary: 'Danh sách utility meter' })
  @DefGet('load-meter')
  @Roles('/production-execution/utility-transaction', 'View')
  @UseGuards(RoleGuard)
  async loadUtilityMeter(@Query('factoryId') factoryId: string) {
    return await this.service.loadUtilityMeter(factoryId);
  }

  @ApiOperation({ summary: 'Danh sách load-shift' })
  @DefGet('load-shift')
  @Roles('/production-execution/utility-transaction', 'View')
  @UseGuards(RoleGuard)
  async loadShift(@Query('factoryId') factoryId: string) {
    return await this.service.loadShift(factoryId);
  }

  @ApiOperation({ summary: 'Utility Monitoring' })
  @DefGet('list-utility-monitoring')
  @Roles('/production-execution/utility-transaction', 'View')
  @UseGuards(RoleGuard)
  async getUtilityMonitor(@Query() params: UtilityMonitoringReq) {
    return await this.service.getUtilityMonitor(params);
  }
}
