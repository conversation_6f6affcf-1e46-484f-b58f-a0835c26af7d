import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { NestFactory } from '@nestjs/core';
import { AppModule } from '~/app.module';
import { UtilityMetersService } from '~/x-modules/admin/services/system-configuration/utility-meters.service';

let cachedApp: any;

const createResponse = (statusCode: number, body: any = {}): APIGatewayProxyResult => ({
  statusCode,
  headers: {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Credentials': true,
    'Content-Type': 'application/json',
  },
  body: JSON.stringify(body),
});

export const findAll = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    if (!cachedApp) {
      cachedApp = await NestFactory.createApplicationContext(AppModule);
    }
    const service = cachedApp.get(UtilityMetersService);
    const result = await service.findAll();
    return createResponse(200, result);
  } catch (error) {
    console.error('Error in findAll:', error);
    return createResponse(500, { message: 'Internal server error' });
  }
};

export const findOne = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    if (!cachedApp) {
      cachedApp = await NestFactory.createApplicationContext(AppModule);
    }
    const service = cachedApp.get(UtilityMetersService);
    const id = event.pathParameters?.id;
    if (!id) {
      return createResponse(400, { message: 'ID is required' });
    }
    const result = await service.findOne(id);
    return createResponse(200, result);
  } catch (error) {
    console.error('Error in findOne:', error);
    if (error.message.includes('not found')) {
      return createResponse(404, { message: error.message });
    }
    return createResponse(500, { message: 'Internal server error' });
  }
};

export const getAllData = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    if (!cachedApp) {
      cachedApp = await NestFactory.createApplicationContext(AppModule);
    }
    const service = cachedApp.get(UtilityMetersService);
    const id = event.pathParameters?.id;
    if (!id) {
      return createResponse(400, { message: 'ID is required' });
    }
    const result = await service.getAllData(id);
    return createResponse(200, result);
  } catch (error) {
    console.error('Error in getAllData:', error);
    if (error.message.includes('not found')) {
      return createResponse(404, { message: error.message });
    }
    return createResponse(500, { message: 'Internal server error' });
  }
};

export const create = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    if (!cachedApp) {
      cachedApp = await NestFactory.createApplicationContext(AppModule);
    }
    const service = cachedApp.get(UtilityMetersService);
    const data = JSON.parse(event.body || '{}');
    const result = await service.create(data);
    return createResponse(201, result);
  } catch (error) {
    console.error('Error in create:', error);
    if (error.message.includes('already exists')) {
      return createResponse(400, { message: error.message });
    }
    return createResponse(500, { message: 'Internal server error' });
  }
};

export const update = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    if (!cachedApp) {
      cachedApp = await NestFactory.createApplicationContext(AppModule);
    }
    const service = cachedApp.get(UtilityMetersService);
    const id = event.pathParameters?.id;
    if (!id) {
      return createResponse(400, { message: 'ID is required' });
    }
    const data = JSON.parse(event.body || '{}');
    const result = await service.update(id, data);
    return createResponse(200, result);
  } catch (error) {
    console.error('Error in update:', error);
    if (error.message.includes('not found')) {
      return createResponse(404, { message: error.message });
    }
    if (error.message.includes('already exists')) {
      return createResponse(400, { message: error.message });
    }
    return createResponse(500, { message: 'Internal server error' });
  }
};

export const remove = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    if (!cachedApp) {
      cachedApp = await NestFactory.createApplicationContext(AppModule);
    }
    const service = cachedApp.get(UtilityMetersService);
    const id = event.pathParameters?.id;
    if (!id) {
      return createResponse(400, { message: 'ID is required' });
    }
    await service.remove(id);
    return createResponse(204);
  } catch (error) {
    console.error('Error in remove:', error);
    if (error.message.includes('not found')) {
      return createResponse(404, { message: error.message });
    }
    return createResponse(500, { message: 'Internal server error' });
  }
};

export const updateStatus = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    if (!cachedApp) {
      cachedApp = await NestFactory.createApplicationContext(AppModule);
    }
    const service = cachedApp.get(UtilityMetersService);
    const id = event.pathParameters?.id;
    if (!id) {
      return createResponse(400, { message: 'ID is required' });
    }
    const data = JSON.parse(event.body || '{}');
    if (typeof data.isActive !== 'boolean') {
      return createResponse(400, { message: 'isActive must be a boolean' });
    }
    const result = await service.updateStatus(id, data.isActive);
    return createResponse(200, result);
  } catch (error) {
    console.error('Error in updateStatus:', error);
    if (error.message.includes('not found')) {
      return createResponse(404, { message: error.message });
    }
    return createResponse(500, { message: 'Internal server error' });
  }
};

export const findActiveMeters = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    if (!cachedApp) {
      cachedApp = await NestFactory.createApplicationContext(AppModule);
    }
    const service = cachedApp.get(UtilityMetersService);
    const result = await service.findActiveMeters();
    return createResponse(200, result);
  } catch (error) {
    console.error('Error in findActiveMeters:', error);
    return createResponse(500, { message: 'Internal server error' });
  }
};

export const findInactiveMeters = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    if (!cachedApp) {
      cachedApp = await NestFactory.createApplicationContext(AppModule);
    }
    const service = cachedApp.get(UtilityMetersService);
    const result = await service.findInactiveMeters();
    return createResponse(200, result);
  } catch (error) {
    console.error('Error in findInactiveMeters:', error);
    return createResponse(500, { message: 'Internal server error' });
  }
};

export const searchByTerm = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    if (!cachedApp) {
      cachedApp = await NestFactory.createApplicationContext(AppModule);
    }
    const service = cachedApp.get(UtilityMetersService);
    const term = event.queryStringParameters?.term;
    if (!term) {
      return createResponse(400, { message: 'Search term is required' });
    }
    const result = await service.searchByTerm(term);
    return createResponse(200, result);
  } catch (error) {
    console.error('Error in searchByTerm:', error);
    return createResponse(500, { message: 'Internal server error' });
  }
};

export const findMetersByProcessArea = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    if (!cachedApp) {
      cachedApp = await NestFactory.createApplicationContext(AppModule);
    }
    const service = cachedApp.get(UtilityMetersService);
    const processAreaId = event.pathParameters?.processAreaId;
    if (!processAreaId) {
      return createResponse(400, { message: 'Process area ID is required' });
    }
    const result = await service.findMetersByProcessArea(processAreaId);
    return createResponse(200, result);
  } catch (error) {
    console.error('Error in findMetersByProcessArea:', error);
    return createResponse(500, { message: 'Internal server error' });
  }
};

export const findMetersByType = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    if (!cachedApp) {
      cachedApp = await NestFactory.createApplicationContext(AppModule);
    }
    const service = cachedApp.get(UtilityMetersService);
    const typeId = event.pathParameters?.typeId;
    if (!typeId) {
      return createResponse(400, { message: 'Type ID is required' });
    }
    const result = await service.findMetersByType(typeId);
    return createResponse(200, result);
  } catch (error) {
    console.error('Error in findMetersByType:', error);
    return createResponse(500, { message: 'Internal server error' });
  }
};

export const findMetersByUOM = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    if (!cachedApp) {
      cachedApp = await NestFactory.createApplicationContext(AppModule);
    }
    const service = cachedApp.get(UtilityMetersService);
    const uomId = event.pathParameters?.uomId;
    if (!uomId) {
      return createResponse(400, { message: 'UOM ID is required' });
    }
    const result = await service.findMetersByUOM(uomId);
    return createResponse(200, result);
  } catch (error) {
    console.error('Error in findMetersByUOM:', error);
    return createResponse(500, { message: 'Internal server error' });
  }
};

export const findMetersByDataType = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    if (!cachedApp) {
      cachedApp = await NestFactory.createApplicationContext(AppModule);
    }
    const service = cachedApp.get(UtilityMetersService);
    const dataTypeId = event.pathParameters?.dataTypeId;
    if (!dataTypeId) {
      return createResponse(400, { message: 'Data type ID is required' });
    }
    const result = await service.findMetersByDataType(dataTypeId);
    return createResponse(200, result);
  } catch (error) {
    console.error('Error in findMetersByDataType:', error);
    return createResponse(500, { message: 'Internal server error' });
  }
};