import { Injectable } from '@nestjs/common';
import { In } from 'typeorm';
import { BindRepo } from '~/@core/decorator';
import { CardRepo } from '~/repositories/primary';

@Injectable()
export class CardService {
  constructor() {}

  @BindRepo(CardRepo)
  private cardRepo: CardRepo;

  async createCard(name: string, code: string): Promise<any> {
    console.log('Vô hàm');
    const result = await this.cardRepo.query(
      'INSERT INTO card (name, code) VALUES ($1, $2) RETURNING *',
      [name, code],
    );
    return result;
  }

  async updateCard(id: number, name: string, code: string): Promise<any> {
    const result = await this.cardRepo.query(
      'UPDATE card SET name = $1, code = $2 WHERE id = $3 RETURNING *',
      [name, code, id],
    );
    return result;
  }

  async findCardById(id: number): Promise<any> {
    const result = await this.cardRepo.query('SELECT * FROM card WHERE id = $1', [id]);
    return result;
  }
}
