import { EntityRepository } from 'typeorm';
import { OrganizationUnitEntity } from '../../entities/primary/organization-unit.entity';
import { PrimaryRepo } from '../primary.repo';

@EntityRepository(OrganizationUnitEntity)
export class OrganizationUnitRepo extends PrimaryRepo<OrganizationUnitEntity> {
  async findByCode(code: string): Promise<OrganizationUnitEntity | undefined> {
    return this.findOne({ where: { code } });
  }

  async findRoots(): Promise<OrganizationUnitEntity[]> {
    return this.find({ where: { parentId: null } });
  }

  async findChildren(parentId: string): Promise<OrganizationUnitEntity[]> {
    return this.find({ where: { parentId } });
  }

  async searchByTerm(term: string): Promise<OrganizationUnitEntity[]> {
    return this.createQueryBuilder('orgUnit')
      .where('orgUnit.name ILIKE :term OR orgUnit.code ILIKE :term', { term: `%${term}%` })
      .getMany();
  }

  async hasChildren(id: string): Promise<boolean> {
    const count = await this.count({ where: { parentId: id } });
    return count > 0;
  }
}
