import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { DefController, DefDelete, DefGet, DefPost, DefPut } from '~/@core/decorator';
import { KPIAwardService, KPISetService } from '../../services';
import { Body, Param, ParseUUIDPipe, Query, UseGuards } from '@nestjs/common';
import { Roles } from '~/@core/decorator/roles.decorator';
import { RoleGuard } from '~/@systems/guard/role.guard';

@ApiTags('KPIAward')
@DefController('kpi-award')
export class KPIAwardController {
  constructor(private service: KPIAwardService) {}

  @ApiOperation({ summary: '', description: 'Kiểm tra ngày công nhân viên ' })
  @DefGet('check-work-today/:userId')
  @Roles('/kpi-award', 'View')
  @UseGuards(RoleGuard)
  async checkUserWorkedToday(@Param('userId') userId: string) {
    return this.service.checkUserWorkedToday(userId);
  }

  @ApiOperation({ summary: '', description: 'Thông tin kết quả thưởng theo nhân viên ' })
  @DefGet('find/:userId')
  @Roles('/kpi-award', 'View')
  @UseGuards(RoleGuard)
  async findByUserIdWithWorkDateToday(@Param('userId') userId: string) {
    return this.service.findByUserIdWithWorkDateToday(userId);
  }
}
