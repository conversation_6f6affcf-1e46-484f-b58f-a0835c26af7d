import { forwardRef, Inject, Injectable } from '@nestjs/common';
import dayjs from 'dayjs';
import { BindRepo } from '~/@core/decorator';
import { ListenZktEventReq, ListStatusSyncUserReq, ZktFaceIdReq } from '~/dto/zkt-face-id.dto';
import { DeviceRepo, StationDetailRepo, StationRepo, UserRepo } from '~/repositories/primary';
import { S3PrivateService } from '~/x-modules/@global/services';
import { TimeSheetRecordService } from '~/x-modules/admin/services';
import { SocketGateway } from '~/x-modules/integration/services/socket.gateway';

@Injectable()
export class ZktFaceIdService {
  constructor(
    @Inject(forwardRef(() => TimeSheetRecordService))
    private readonly timeSheetRecordService: TimeSheetRecordService,
    private readonly s3PrivateService: S3PrivateService,
    private readonly socketGateway: SocketGateway,
  ) {}

  @BindRepo(UserRepo)
  private UserRepo: UserRepo;

  @BindRepo(DeviceRepo)
  private DeviceRepo: DeviceRepo;

  @BindRepo(StationRepo)
  private StationRepo: StationRepo;

  @BindRepo(StationDetailRepo)
  private StationDetailRepo: StationDetailRepo;

  async syncListUserZKT(params: ZktFaceIdReq) {
    const device = await this.DeviceRepo.findOne({
      where: {
        serialNumber: params.SN,
      },
    });

    const detail = await this.StationDetailRepo.findOne({
      where: {
        deviceId: device.id,
        assign: true,
      },
    });

    const station = await this.StationRepo.findOne({
      where: {
        id: detail.stationId,
        isActive: true,
      },
    });

    const users = await this.UserRepo.createQueryBuilder('user')
      .leftJoin('user.productionArea', 'pa')
      .where('user.isActive = :isActive', { isActive: true })
      .andWhere('user.imageUrl IS NOT NULL')
      .andWhere('user.userNo IS NOT NULL')
      .andWhere('pa.organizationUnitId = :factoryId', { factoryId: station.factoryId })
      .getMany();

    const listUser = [];

    for (const user of users) {
      const imageUrl = await this.s3PrivateService.getS3Link(user.imageUrl);

      listUser.push({
        PIN: user.userNo,
        Name: user.fullName,
        Password: String(user.userNo),
        Grp: 1, // Group của user trên device
        Pri: 14, // 0 User, 14 Admin
        Verify: -1, // Sử dụng face id
        TZ: '0000000100000000', // Timezone của group
        ImageURL: imageUrl,
      });
    }

    return listUser;
  }

  async listenZktEvent(params: ListenZktEventReq) {
    const { userNo, datetime } = params;

    if (userNo && datetime) {
      const now = dayjs();
      const request = dayjs(datetime, 'YYYY-MM-DD HH:mm:ss');
      const diff = now.diff(request, 'minutes');

      // Không sử dụng record quá 1 phút
      if (diff <= 1) {
        const userTimeSheet = await this.timeSheetRecordService.getUserTimeSheet(params.userNo);

        this.socketGateway.emitAuthZktFace({
          ...params,
          ...userTimeSheet,
        });
      }
    }
  }

  async listStatusSyncUser(params: ListStatusSyncUserReq) {
    const { listSuccess, listError } = params;

    for (const user of listSuccess) {
      await this.UserRepo.update(
        { userNo: user.userNo },
        { syncStatus: 'success', syncDate: new Date() },
      );
    }

    for (const user of listError) {
      await this.UserRepo.update(
        { userNo: user.userNo },
        { syncStatus: user.status, syncDate: new Date() },
      );
    }
  }
}
