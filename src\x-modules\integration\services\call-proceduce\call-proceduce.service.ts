import { Injectable } from '@nestjs/common';
import { BindRepo } from '~/@core/decorator';
import { RecipeRepo } from '~/repositories/primary';
import { dateHelper } from '~/common/helpers/date.helper';
import { Connection } from 'typeorm';
@Injectable()
export class CallProcedureService {
  constructor( private readonly connection: Connection ) {}

  @BindRepo(RecipeRepo)
  private recipeRepo: RecipeRepo;
  async utilityTransactionAllocation(data: { fromDate?: string; toDate?: string }) {
    try {
      const result = await this.recipeRepo.query(`CALL utility_transaction_allocation()`);
    } catch (error) {
      console.log(error, 'error');
    }
  }

  async manhourTransactionAllocation(data: { fromDate?: string; toDate?: string }) {
    try {
      const result = await this.recipeRepo.query(`CALL manhour_transaction_allocation()`);
    } catch (error) {
      console.log(error, 'error');
    }
  }

  async createManhourTransactions(data: { fromDate?: string; toDate?: string }) {
    try {
      const result = await this.recipeRepo.query(`CALL create_manhour_transactions()`);
    } catch (error) {
      console.log(error, 'error');
    }
  }

  async createOeekpi(data: { fromDate?: string; toDate?: string }) {
    try {
      let getDateFrom = null
      let getDateTo = null
      if(data.fromDate && data.toDate){
        getDateFrom = dateHelper.formatDateByJobManual(data.fromDate)
        getDateTo = dateHelper.formatDateByJobManual(data.toDate)
      }
      const result = await this.connection.query(`SELECT calculate_kpi_oee($1, $2)`, [getDateFrom, getDateTo]);
    } catch (error) {
      console.log(error, 'error');
    }
  }
}
