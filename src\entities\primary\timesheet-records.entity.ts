import { ApiProperty } from '@nestjs/swagger';
import { Column, Entity, JoinColumn, ManyToOne, OneToMany, OneToOne } from 'typeorm';
import { PrimaryBaseEntity } from '../primary-base.entity';
import { ProductionAreaEntity } from './production-area.entity';
import { ShiftEntity } from './shift.entity';
import { UserEntity } from './user.entity';

@Entity('timesheet_records')
export class TimeSheetRecordEntity extends PrimaryBaseEntity {
  @ApiProperty({ description: 'Loại bản ghi chấm công (VÀO/RA)' })
  @Column({ type: 'varchar', length: 15 })
  type!: string; // "IN" hoặc "OUT"

  @ApiProperty({ description: 'ID của người dùng thực hiện chấm công' })
  @Column({ type: 'uuid' })
  userId!: string;

  @ApiProperty({ description: 'ID của khu vực sản xuất' })
  @Column({ type: 'uuid' })
  productionAreaId!: string;

  @ApiProperty({ description: 'ID của ca làm việc' })
  @Column({ type: 'uuid' })
  shiftId!: string;

  @ApiProperty({ description: 'ID của site' })
  @Column({ type: 'uuid', nullable: true })
  siteId: string;

  @ApiProperty({ description: 'ID của factory' })
  @Column({ type: 'uuid', nullable: true })
  factoryId: string;

  @ApiProperty({ description: 'Thời gian check-in' })
  @Column({ type: 'timestamptz', nullable: true })
  checkInTime?: Date;

  @ApiProperty({ description: 'Thời gian check-out' })
  @Column({ type: 'timestamptz', nullable: true })
  checkOutTime?: Date;

  @ApiProperty({ description: 'Ngày làm việc dựa trên ca làm và thời điểm check-in' })
  @Column({ type: 'date', nullable: true })
  workDate?: string;

  @ApiProperty({ description: 'Tọa độ X của khu vực sản xuất' })
  @Column({ type: 'decimal' })
  productionAreaLocationX!: number;

  @ApiProperty({ description: 'Tọa độ Y của khu vực sản xuất' })
  @Column({ type: 'decimal' })
  productionAreaLocationY!: number;

  @ApiProperty({ description: 'Tọa độ X tại vị trí check-in thực tế' })
  @Column({ type: 'decimal', nullable: true })
  checkInLocationX?: number;

  @ApiProperty({ description: 'Tọa độ Y tại vị trí check-in thực tế' })
  @Column({ type: 'decimal', nullable: true })
  checkInLocationY?: number;

  @ApiProperty({ description: 'Tọa độ X tại vị trí check-out thực tế' })
  @Column({ type: 'decimal', nullable: true })
  checkOutLocationX?: number;

  @ApiProperty({ description: 'Tọa độ Y tại vị trí check-out thực tế' })
  @Column({ type: 'decimal', nullable: true })
  checkOutLocationY?: number;

  @ApiProperty({ description: 'Mức dung sai cho tọa độ check-in' })
  @Column({ type: 'numeric', nullable: true })
  toleranceIn?: number;

  @ApiProperty({ description: 'Mức dung sai cho tọa độ check-out' })
  @Column({ type: 'numeric', nullable: true })
  toleranceOut?: number;

  @ApiProperty({ description: 'Thời gian tạo bản ghi' })
  @Column({ type: 'timestamptz', default: () => 'CURRENT_TIMESTAMP' })
  creationDate!: Date;

  @ApiProperty({ description: 'Người tạo bản ghi' })
  @Column({ type: 'varchar', length: 128 })
  createdBy!: string;

  @ApiProperty({ description: 'Thời gian cập nhật lần cuối' })
  @Column({
    type: 'timestamptz',
    default: () => 'CURRENT_TIMESTAMP',
    onUpdate: 'CURRENT_TIMESTAMP',
  })
  lastUpdate?: Date;

  @ApiProperty({ description: 'Người cập nhật lần cuối' })
  @Column({ type: 'varchar', length: 128, nullable: true })
  lastUpdateBy?: string;

  @ApiProperty({ description: 'Tổng giờ chấm công' })
  @Column({ type: 'numeric', default: 0 })
  duration!: number;

  @ApiProperty({ description: 'Thời gian về sớm' })
  @Column({ type: 'numeric', default: 0 })
  earlyTime!: number;

  @ApiProperty({ description: 'Thời gian đi trễ' })
  @Column({ type: 'numeric', default: 0 })
  lateTime!: number;

  @ApiProperty({ description: 'Giờ công tính lương' })
  @Column({ type: 'numeric', default: 0 })
  salaryTime!: number;

  @ApiProperty({ description: 'Giờ công ca đêm' })
  @Column({ type: 'numeric', default: 0 })
  nightTime!: number;

  @ApiProperty({ description: 'Giờ công chênh lệch so với giờ công chuẩn' })
  @Column({ type: 'numeric', default: 0 })
  diffTime!: number;

  @ApiProperty({ description: 'OT Ngày thường' })
  @Column({ type: 'numeric', default: 0 })
  otTime!: number;

  @ApiProperty({ description: 'OT Ngày lễ' })
  @Column({ type: 'numeric', default: 0 })
  otHolidayTime!: number;

  @ApiProperty({ description: 'OT đêm thường' })
  @Column({ type: 'numeric', default: 0 })
  otNightTime!: number;

  @ApiProperty({ description: 'OT đêm lễ' })
  @Column({ type: 'numeric', default: 0 })
  otHolidayNightTime!: number;

  // Quan hệ với các bảng khác
  @ManyToOne(() => UserEntity, (user) => user.timesheets)
  @JoinColumn({ name: 'userId' })
  user!: UserEntity;

  @ManyToOne(() => ProductionAreaEntity, (area) => area.timesheets)
  @JoinColumn({ name: 'productionAreaId' })
  productionArea!: ProductionAreaEntity;

  @ManyToOne(() => ShiftEntity, (shift) => shift.timesheets)
  @JoinColumn({ name: 'shiftId' })
  shift!: ShiftEntity;
}
