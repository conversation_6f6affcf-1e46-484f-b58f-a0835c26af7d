import { EntityRepository } from 'typeorm';
import { PrimaryRepo } from '../primary.repo';
import { UtilityMetersEntity } from '~/entities/primary/utility-meters.entity';

@EntityRepository(UtilityMetersEntity)
export class UtilityMetersRepo extends PrimaryRepo<UtilityMetersEntity> {
  async findByCode(code: string): Promise<UtilityMetersEntity | undefined> {
    return this.findOne({ where: { code } });
  }

  async findActiveMeters(): Promise<UtilityMetersEntity[]> {
    return this.find({ where: { isActive: true } });
  }

  async findInactiveMeters(): Promise<UtilityMetersEntity[]> {
    return this.find({ where: { isActive: false } });
  }

  async searchByTerm(term: string): Promise<UtilityMetersEntity[]> {
    return this.createQueryBuilder('meter')
      .where('meter.name ILIKE :term OR meter.code ILIKE :term', { term: `%${term}%` })
      .getMany();
  }

  async findMetersByType(type: string): Promise<UtilityMetersEntity[]> {
    return this.find({ where: { utilityMetersType: type } });
  }

  async findMetersByNodeId(nodeId: string): Promise<UtilityMetersEntity[]> {
    return this.find({ where: { nodeId } });
  }

  async findMetersByProcessArea(processAreaId: string): Promise<UtilityMetersEntity[]> {
    return this.createQueryBuilder('meter')
      .innerJoin('meter.processAreas', 'processArea')
      .where('processArea.id = :processAreaId', { processAreaId })
      .getMany();
  }

  async findMetersWithReadings(): Promise<UtilityMetersEntity[]> {
    return this.createQueryBuilder('meter')
      .leftJoinAndSelect('meter.readings', 'reading')
      .getMany();
  }

  async findMeterWithReadings(id: string): Promise<UtilityMetersEntity | undefined> {
    return this.createQueryBuilder('meter')
      .leftJoinAndSelect('meter.readings', 'reading')
      .where('meter.id = :id', { id })
      .getOne();
  }

  async findWithRelations(): Promise<UtilityMetersEntity[]> {
    return this.createQueryBuilder('meter')
      .leftJoinAndSelect('meter.type', 'type')
      .leftJoinAndSelect('meter.uom', 'uom')
      .leftJoinAndSelect('meter.dataType', 'dataType')
      .leftJoinAndSelect('meter.processArea', 'processArea')
      .getMany();
  }

  async findOneWithRelations(id: string): Promise<UtilityMetersEntity | undefined> {
    return this.createQueryBuilder('meter')
      .leftJoinAndSelect('meter.factory', 'factory')
      .leftJoinAndSelect('meter.uomDetail', 'uomDetail')
      .leftJoinAndSelect('meter.utilityMeterType', 'utilityMeterType')
      .leftJoinAndSelect('meter.dataType', 'dataType')
      .leftJoinAndSelect('meter.datetimeUnit', 'datetimeUnit')
      .leftJoinAndSelect('meter.details', 'details')
      .where('meter.id = :id', { id })
      .getOne();
  }

  async findWithReadings(id: string): Promise<UtilityMetersEntity | undefined> {
    return this.createQueryBuilder('meter')
      .leftJoinAndSelect('meter.readings', 'reading')
      .where('meter.id = :id', { id })
      .getOne();
  }
} 