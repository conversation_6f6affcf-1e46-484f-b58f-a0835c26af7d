import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsString } from 'class-validator';
import { PageRequest } from '~/@systems/utils';

export class ListProductionDowntime extends PageRequest {
  @ApiProperty({ description: 'Mã Factory' })
  factoryId: string;

  @ApiProperty({ description: 'Mã Line' })
  lineId?: string;

  @ApiProperty({ description: 'Start Time lọc từ đến từ' })
  start: Date;

  @ApiProperty({ description: 'End Time lọc từ đến từ' })
  end: Date;

  @ApiProperty({ description: 'End Time' })
  endTime?: string;

  @ApiProperty({ description: 'Cancelled Flag' })
  @Type(() => Number)
  cancelledFlag?: number;
}

export class ProductionDowntimeReq {
  processAreaId: string;
  machineId: string;
  startTime: Date;
  endTime: Date;
  reasonId: string;
  cancelledFlag: number;
  // note: string
  description: string;
}
