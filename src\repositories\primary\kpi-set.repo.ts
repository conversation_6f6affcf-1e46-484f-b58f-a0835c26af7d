import { EntityRepository } from 'typeorm';
import { PrimaryRepo } from '../primary.repo';
import {
  KpiSetDetailEntity,
  KpiSetGroupEntity,
  KpiSetHeaderEntity,
  KpiSetProductAreaEntity,
} from '~/entities/primary';

@EntityRepository(KpiSetHeaderEntity)
export class KpiSetHeaderRepo extends PrimaryRepo<KpiSetHeaderEntity> {}

@EntityRepository(KpiSetGroupEntity)
export class KpiSetGroupRepo extends PrimaryRepo<KpiSetGroupEntity> {}

@EntityRepository(KpiSetDetailEntity)
export class KpiSetDetailRepo extends PrimaryRepo<KpiSetDetailEntity> {}

@EntityRepository(KpiSetProductAreaEntity)
export class KpiSetProductAreaRepo extends PrimaryRepo<KpiSetProductAreaEntity> {}
