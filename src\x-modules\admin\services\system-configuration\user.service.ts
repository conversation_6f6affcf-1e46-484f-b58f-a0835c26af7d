import { ConflictException, Injectable, NotFoundException } from '@nestjs/common';
import dayjs from 'dayjs';
import { BindRepo, DefTransaction } from '~/@core/decorator';
import { UserActiveStatus } from '~/dto/user.dto';
import { UserEntity } from '~/entities/primary/user.entity';
import { ICreateUserRequest, IFilterGetUserList, IUpdateUserRequest } from '~/functions/user';
import { PermissionRepo } from '~/repositories/primary';
import { ProductionAreaRepo } from '~/repositories/primary/production-area.repo';
import { UserRepo } from '~/repositories/primary/user.repo';
import { S3PrivateService } from '~/x-modules/@global/services';

@Injectable()
export class UserService {
  constructor(private readonly s3PrivateService: S3PrivateService) {}

  @BindRepo(UserRepo)
  private userRepo: UserRepo;

  @BindRepo(ProductionAreaRepo)
  private productionAreaRepo: ProductionAreaRepo;

  @BindRepo(PermissionRepo)
  private permissionRepo: PermissionRepo;

  async getUserList(params: IFilterGetUserList | undefined): Promise<{
    items: UserEntity[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
  }> {
    // Tạo query builder
    const queryBuilder = this.userRepo
      .createQueryBuilder('user')
      .leftJoinAndSelect('user.productionArea', 'productionArea')
      .leftJoinAndSelect('user.permission', 'permission');

    // Thêm điều kiện tìm kiếm cho từng trường nếu có
    const conditions = [];
    const queryParams: any = {};

    if (params?.employeeCode) {
      conditions.push('LOWER(user.employeeCode) LIKE LOWER(:employeeCode)');
      queryParams.employeeCode = `%${params.employeeCode}%`;
    }

    if (params?.fullName) {
      conditions.push('LOWER(user.fullName) LIKE LOWER(:fullName)');
      queryParams.fullName = `%${params.fullName}%`;
    }

    if (params?.email) {
      conditions.push('LOWER(user.email) LIKE LOWER(:email)');
      queryParams.email = `%${params.email}%`;
    }

    // Thêm điều kiện lọc theo isActive
    if (params?.isActive && params.isActive !== UserActiveStatus.FULL) {
      queryBuilder.andWhere('user.isActive = :isActive', {
        isActive: params.isActive === UserActiveStatus.ACTIVE,
      });
    }

    // Thêm điều kiện WHERE nếu có ít nhất một điều kiện tìm kiếm
    if (conditions.length > 0) {
      queryBuilder.andWhere(`(${conditions.join(' OR ')})`, queryParams);
    }

    // Sắp xếp theo ngày tạo mới nhất
    queryBuilder.orderBy('user.createdDate', 'DESC');

    // Tính toán skip và take cho phân trang
    const page = params?.page || 1;
    const limit = params?.limit || 10;
    const skip = (page - 1) * limit;

    // Thực hiện đếm tổng số bản ghi
    const total = await queryBuilder.getCount();

    // Thêm phân trang vào query
    queryBuilder.skip(skip).take(limit);

    // Lấy kết quả
    const items = await queryBuilder.getMany();

    return {
      items,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  @DefTransaction()
  async createUser(user: ICreateUserRequest, avatarFile?: Express.Multer.File): Promise<void> {
    const { employeeCode, productionAreaId, permissionId, email } = user;

    if (employeeCode) {
      const existingUser = await this.userRepo.findOne({
        where: { employeeCode },
      });

      if (existingUser) {
        throw new ConflictException('Mã nhân viên đã tồn tại!');
      }
    }

    if (email) {
      const existingEmail = await this.userRepo.findOne({
        where: { email },
      });

      if (existingEmail) {
        throw new ConflictException('Email nhân viên đã tồn tại!');
      }
    }

    if (productionAreaId) {
      const productionArea = await this.productionAreaRepo.findOne({
        where: { id: productionAreaId, isActive: true },
      });

      if (!productionArea) {
        throw new NotFoundException('Khu vực sản xuất không tồn tại!');
      }
    }

    if (permissionId) {
      const permission = await this.permissionRepo.findOne({
        where: { id: permissionId, isActive: true },
      });

      if (!permission) {
        throw new NotFoundException('Quyền không tồn tại!');
      }
    }

    let maxUserNo = await this.userRepo
      .createQueryBuilder('user')
      .select('MAX(user.userNo)', 'max')
      .getRawOne();
    // Trên device admin là 1

    if (maxUserNo.max == null || maxUserNo.max == 0) {
      maxUserNo = 2;
    } else {
      maxUserNo = maxUserNo.max + 1;
    }

    const createdUser = await this.userRepo.save({
      ...user,
      employeeCode: employeeCode.toUpperCase(),
      userNo: maxUserNo,
    });

    if (avatarFile) {
      const awsRes = await this.s3PrivateService.uploadS3(
        avatarFile,
        `${createdUser.id}_${dayjs().format('YYYYMMDDHHmmss')}`,
        'private',
      );

      createdUser.imageUrl = awsRes.fileUrl;
      await this.userRepo.save(createdUser);
    }
  }

  async getUser(id: string): Promise<UserEntity | null> {
    const user = await this.userRepo.findOne(id, {
      relations: ['productionArea'],
    });

    if (user && user.imageUrl) {
      user.imageUrl = await this.s3PrivateService.getS3Link(user.imageUrl);
    }

    return user ?? null;
  }

  async updateUser(
    user: UserEntity,
    updateUser: IUpdateUserRequest,
    avatarFile?: Express.Multer.File,
  ): Promise<void> {
    // Kiểm tra employeeCode đã tồn tại chưa nếu có thay đổi
    if (updateUser.employeeCode && updateUser.employeeCode !== user.employeeCode) {
      const existingUser = await this.userRepo.findOne({
        where: { employeeCode: updateUser.employeeCode },
      });

      if (existingUser) {
        throw new Error('Employee Code đã tồn tại!');
      }
    }
    if (updateUser.email && updateUser.email !== user.email) {
      const existingEmail = await this.userRepo.findOne({
        where: { email: updateUser.email },
      });

      if (existingEmail) {
        throw new Error('Email đã tồn tại!');
      }
    }

    if (!updateUser.validTo) {
      updateUser.validTo = null;
    }

    // Kiểm tra production area nếu có
    if (updateUser.productionAreaId) {
      const productionArea = await this.productionAreaRepo.findOne({
        where: {
          id: updateUser.productionAreaId,
          isActive: true,
        },
      });

      if (!productionArea) {
        throw new Error('Production Area không tồn tại!');
      }
    }

    if (updateUser.permissionId) {
      const permission = await this.permissionRepo.findOne({
        where: {
          id: updateUser.permissionId,
          isActive: true,
        },
      });

      if (!permission) {
        throw new Error('Permission không tồn tại!');
      }
    }

    try {
      if (avatarFile) {
        const awsRes = await this.s3PrivateService.uploadS3(
          avatarFile,
          `${user.id}_${dayjs().format('YYYYMMDDHHmmss')}`,
          'private',
        );

        updateUser.imageUrl = awsRes.fileUrl;
      }
    } catch (err) {}

    const cleanedUpdateUser = Object.fromEntries(
      Object.entries(updateUser).filter(([_, value]) => value !== undefined),
    );

    if (Object.keys(cleanedUpdateUser).length === 0) {
      throw new Error('Không có thông tin nào để cập nhật!');
    }

    await this.userRepo.update({ id: user.id }, cleanedUpdateUser);
  }

  async isUserExist(email: string): Promise<UserEntity | null> {
    return this.userRepo.findOne({
      where: { email, isActive: true },
    });
  }

  async uploadImage(file: Express.Multer.File, fileName: string) {
    return await this.s3PrivateService.uploadS3(file, fileName, 'private');
  }
}
