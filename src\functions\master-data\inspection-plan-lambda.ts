import { NestFactory } from '@nestjs/core';
import { Context, Handler } from 'aws-lambda';
import { AppModule } from '../../app.module';
import { InspectionPlanService } from '~/x-modules/admin/services';

let cachedApp: any = null;

async function bootstrap() {
  if (!cachedApp) {
    const app = await NestFactory.createApplicationContext(AppModule);
    await app.init();
    cachedApp = app;
  }
  return cachedApp;
}

/**
 * Create a new inspection plan
 */
export const createInspectionPlan: Handler = async (event: any, context: Context) => {
  const app = await bootstrap();
  const service = app.get(InspectionPlanService);
  const body = JSON.parse(event.body);

  try {
    const result = await service.create(body);
    return {
      statusCode: 201,
      body: JSON.stringify(result),
      headers: {
        'Content-Type': 'application/json',
      },
      message: result.message,
    };
  } catch (error) {
    return {
      statusCode: error.status || 500,
      body: JSON.stringify({
        message: error.message || 'Internal server error',
        error: error.response?.error || error.name,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    };
  }
};

/**
 * Update a inspection plan
 */
export const updateInspectionPlan: Handler = async (event: any, context: Context) => {
  const app = await bootstrap();
  const service = app.get(InspectionPlanService);
  const body = JSON.parse(event.body);

  try {
    const result = await service.update(body);
    return {
      statusCode: 200,
      body: JSON.stringify(result),
      headers: {
        'Content-Type': 'application/json',
      },
      message: result.message,
    };
  } catch (error) {
    return {
      statusCode: error.status || 500,
      body: JSON.stringify({
        message: error.message || 'Internal server error',
        error: error.response?.error || error.name,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    };
  }
};

/**
 * Get paginated list
 */
export const paginationInspectionPlan: Handler = async (event: any, context: Context) => {
  const app = await bootstrap();
  const service = app.get(InspectionPlanService);
  const body = JSON.parse(event.body);

  try {
    const result = await service.pagination(body);
    return {
      statusCode: 200,
      body: JSON.stringify(result),
      headers: {
        'Content-Type': 'application/json',
      },
      message: 'Get paginated inspection plan successfully!',
    };
  } catch (error) {
    return {
      statusCode: error.status || 500,
      body: JSON.stringify({
        message: error.message || 'Internal server error',
        error: error.response?.error || error.name,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    };
  }
};

/**
 * Get a inspection plan by ID
 */
export const findInspectionPlanById: Handler = async (event: any, context: Context) => {
  const app = await bootstrap();
  const service = app.get(InspectionPlanService);

  try {
    const id = event.pathParameters?.id;
    if (!id) {
      return {
        statusCode: 400,
        body: JSON.stringify({
          message: 'Missing ID parameter',
        }),
        headers: {
          'Content-Type': 'application/json',
        },
      };
    }

    const result = await service.findOne(id);
    return {
      statusCode: 200,
      body: JSON.stringify(result),
      headers: {
        'Content-Type': 'application/json',
      },
      message: 'Get reason master successfully!',
    };
  } catch (error) {
    return {
      statusCode: error.status || 500,
      body: JSON.stringify({
        message: error.message || 'Internal server error',
        error: error.response?.error || error.name,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    };
  }
};

/**
 * Get data select for create inspection plan
 */
export const loadDataSelectForCreate: Handler = async (event: any, context: Context) => {
  const app = await bootstrap();
  const service = app.get(InspectionPlanService);

  try {
    const result = await service.loadDataSelectForCreate();
    return {
      statusCode: 200,
      body: JSON.stringify(result),
      headers: {
        'Content-Type': 'application/json',
      },
      message: 'Get data successfully!',
    };
  } catch (error) {
    return {
      statusCode: error.status || 500,
      body: JSON.stringify({
        message: error.message || 'Internal server error',
        error: error.response?.error || error.name,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    };
  }
};
