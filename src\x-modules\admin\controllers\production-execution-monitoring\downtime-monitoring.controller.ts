import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { DefController, DefGet, DefPost, DefPut } from '~/@core/decorator';
import { DowntimeMonitoringService } from '../../services';
import { Body, Param, Query, UseGuards } from '@nestjs/common';
import { ListProductionDowntime, ProductionDowntimeReq } from '~/dto/production-downtime.dto';
import { Roles } from '~/@core/decorator/roles.decorator';
import { RoleGuard } from '~/@systems/guard/role.guard';

@ApiTags('DowntimeMonitoring')
@DefController('downtime-monitoring')
export class DowntimeMonitoringController {
  constructor(private readonly service: DowntimeMonitoringService) {}

  @ApiOperation({ summary: 'Tạo Production Downtime' })
  @DefGet('')
  @Roles('/production-execution/downtime-monitoring', 'View')
  @UseGuards(RoleGuard)
  async list(@Query() params: ListProductionDowntime) {
    return this.service.pagination(params);
  }

  @ApiOperation({ summary: 'Tạo Production Downtime' })
  @DefPost('')
  @Roles('/production-execution/downtime-monitoring', 'View')
  @UseGuards(RoleGuard)
  async create(@Body() body: ProductionDowntimeReq) {
    return this.service.create(body);
  }

  @ApiOperation({ summary: 'Tạo Production Downtime' })
  @DefPut('/:id')
  @Roles('/production-execution/downtime-monitoring', 'View')
  @UseGuards(RoleGuard)
  async update(@Param('id') id: string, @Body() body: ProductionDowntimeReq) {
    return this.service.update(id, body);
  }

  @ApiOperation({ summary: 'Danh sách Process Area theo factoryId' })
  @DefGet('list-process-area')
  @Roles('/production-execution/downtime-monitoring', 'View')
  @UseGuards(RoleGuard)
  async getProcessArea(@Query('factoryId') factoryId: string) {
    return await this.service.getProcessArea(factoryId);
  }

  @ApiOperation({ summary: 'Danh sách Process theo factoryId' })
  @DefGet('list-process')
  @Roles('/production-execution/downtime-monitoring', 'View')
  @UseGuards(RoleGuard)
  async getProcess(@Query('factoryId') factoryId: string) {
    return await this.service.getProcess(factoryId);
  }

  @ApiOperation({ summary: 'Danh sách Machine theo factoryId' })
  @DefGet('list-machine')
  @Roles('/production-execution/downtime-monitoring', 'View')
  @UseGuards(RoleGuard)
  async getMachine(@Query('factoryId') factoryId: string) {
    return await this.service.getMachine(factoryId);
  }

  @ApiOperation({ summary: 'Danh sách Reason' })
  @DefGet('list-reason')
  @Roles('/production-execution/downtime-monitoring', 'View')
  @UseGuards(RoleGuard)
  async getReason() {
    return await this.service.getReason();
  }
}
