import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { IsUUID } from 'class-validator';
import { PrimaryBaseEntity } from '~/entities/primary-base.entity';

@Entity('holiday_detail')
export class HolidayDetailEntity extends PrimaryBaseEntity {
  @ApiProperty({ description: 'Tên ngày lễ' })
  @Column({ name: 'description' })
  description: string;

  @ApiProperty({ description: 'Ngày' })
  @Column({ type: 'timestamptz', nullable: true })
  dateDetail?: Date;

  @ApiProperty({ description: 'Năm' })
  @Column({ name: 'yearDetail', type: 'varchar', length: 4, nullable: true })
  yearDetail?: string;

  @ApiProperty({ description: 'Trạng thái' })
  @Column({ name: 'status', default: false })
  status: boolean;

  @ApiProperty({ description: 'Holiday Id' })
  @Column({ name: 'holidayId', default: false })
  holidayId: string;
}
