import { INestApplication } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import { SchemasObject } from '@nestjs/swagger/dist/interfaces/open-api-spec.interface';
import { getFromContainer, MetadataStorage } from 'class-validator';
import { validationMetadatasToSchemas } from 'class-validator-jsonschema';
import dayjs from 'dayjs';
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter';
import timezone from 'dayjs/plugin/timezone';
import utc from 'dayjs/plugin/utc';
import weekday from 'dayjs/plugin/weekday';
import * as dotenv from 'dotenv';
import * as express from 'express';
import 'extensionsjs/lib';
import { I18nValidationExceptionFilter } from 'nestjs-i18n';
import 'reflect-metadata';
import { configEnv } from './@config/env';
import { setupTransactionContext } from './@core/decorator';
import { ApiException } from './@systems/exceptions';
import { HttpExceptionFilter, TypeOrmFilter } from './@systems/exceptions/http-exception-filter';
import { ValidatePipe } from './@systems/pipe';
import { AppModule } from './app.module';
import { GlobalPrefix } from './common/constants';
import { RedisIoAdapter } from './x-modules/integration/services/redis.adapter';
import { NestExpressApplication } from '@nestjs/platform-express';
dotenv.config();

dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(weekday);
dayjs.extend(isSameOrAfter);

const configSwagger = (app: INestApplication) => {
  const { SWAGGER_TITLE, SWAGGER_DESCRIPTION, SWAGGER_VERSION, IS_DOCKER_SERVER } = configEnv();
  const options = new DocumentBuilder()
    .setTitle(SWAGGER_TITLE)
    .setDescription(SWAGGER_DESCRIPTION)
    .setVersion(SWAGGER_VERSION)
    .addSecurity('bearer', {
      type: 'http',
      scheme: 'bearer',
      bearerFormat: 'JWT',
    })
    .build();

  const document = SwaggerModule.createDocument(app, options, {
    // extraModels: [PageResponse]
  });

  // Creating all the swagger schemas based on the class-validator decorators
  const metadatas = (getFromContainer(MetadataStorage) as any).validationMetadatas;
  const targetSchemas = document.components.schemas || {};
  const schemasBinding = validationMetadatasToSchemas(metadatas) || {};

  Object.keys(schemasBinding).forEach((key) => {
    const value = schemasBinding[key] as SchemasObject;
    if (!targetSchemas[key]) {
      Object.assign(targetSchemas, {
        key: value,
      });
    } else {
      const targetValue = targetSchemas[key] as SchemasObject;

      Object.assign(targetValue.properties, value.properties);
      targetValue.required = value.required;
      Object.assign(targetSchemas, {
        key: targetValue,
      });
    }
  });
  document.components.schemas = Object.assign({}, targetSchemas);
  SwaggerModule.setup('swagger', app, document);
};

const bootstrap = async () => {
  setupTransactionContext();
  const { IS_DOCKER_SERVER } = configEnv();
  // const app = await NestFactory.create(AppModule);

  const app = await NestFactory.create<NestExpressApplication>(AppModule);

  const port = process.env.PORT || 3000;

  if (!IS_DOCKER_SERVER) {
    const redisIoAdapter = new RedisIoAdapter(app);
    await redisIoAdapter.connectToRedis();
    app.useWebSocketAdapter(redisIoAdapter);
  }

  //#region Common config
  app.enableCors({
    origin: '*',
  });
  app.setGlobalPrefix(GlobalPrefix.API);
  app.useGlobalPipes(
    new ValidatePipe({
      whitelist: false, // Chỉ giữ lại các field có trong DTO
      forbidNonWhitelisted: false, // Chặn field không có trong DTO
      transform: true, // Chuyển đổi kiểu dữ liệu tự động
    }),
  );
  app.useGlobalFilters(new I18nValidationExceptionFilter());
  // app.useGlobalInterceptors(new TransformInterceptor())
  app.useGlobalFilters(new HttpExceptionFilter(), new TypeOrmFilter());
  app.use((req: express.Request, res: express.Response, next: express.NextFunction) => {
    const { REQUEST_TIMEOUT = 30 * 60 * 1000 } = configEnv();
    res.setTimeout(REQUEST_TIMEOUT, () => {
      next(new ApiException('REQUEST TIMEOUT'));
    });
    next();
  });

  //#endregion
  configSwagger(app);
  app.set('trust proxy', true);
  await app.listen(port);

  console.log(`Server start on port ${port}. Open http://localhost:${port} to see results`);
  console.log(`API DOCUMENT Open http://localhost:${port}/swagger`);
  console.log(`API DOCUMENT JSON Open http://localhost:${port}/swagger-json`);
  console.log('TIMEZONE: ', process.env.TZ);
};

bootstrap();
