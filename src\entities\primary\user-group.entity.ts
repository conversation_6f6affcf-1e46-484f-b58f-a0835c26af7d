import { <PERSON>umn, <PERSON><PERSON><PERSON>, ManyToMany, Join<PERSON><PERSON>, ManyToOne, Join<PERSON><PERSON>um<PERSON> } from 'typeorm';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { PrimaryBaseEntity } from '~/entities/primary-base.entity';
import { UserEntity } from './user.entity';
import { GeneralDataDetailEntity } from '~/entities/primary/general-data-detail.entity';

/** Nhóm người dùng */
@Entity('user_group')
export class UserGroupEntity extends PrimaryBaseEntity {
  /** Group name */
  @Column({
    type: 'varchar',
    length: 100,
    nullable: false,
  })
  @ApiProperty({ description: 'Group name', type: 'string', required: true })
  name: string;

  /** Group description */
  @Column({
    type: 'text',
    nullable: true,
  })
  @ApiProperty({ description: 'Group description', type: 'string', required: false })
  description: string;

  /** Activity status */
  @Column({
    type: 'boolean',
    default: true,
  })
  @ApiProperty({ description: 'Group activity status', type: 'boolean', default: true })
  isActive: boolean;

  /** Users in the group */
  @ManyToMany(() => UserEntity, (user) => user.groups)
  @JoinTable({
    name: 'user_group_members',
    joinColumn: {
      name: 'groupId',
      referencedColumnName: 'id',
    },
    inverseJoinColumn: {
      name: 'userId',
      referencedColumnName: 'id',
    },
  })
  @ApiProperty({ description: 'Users in the group', type: [UserEntity], required: false })
  users?: UserEntity[];

  /**
   * Mã GeneralDataDetail của Type.
   */
  @ApiPropertyOptional({ description: 'Mã GeneralDataDetail của Type' })
  @Column({ default: '' })
  typeCode?: string;

  /**  ID của Type, liên kết với GeneralDataDetailEntity.
   */
  @ApiPropertyOptional({ description: 'ID của Type' })
  @Column({ nullable: true })
  typeId: string;
  @ManyToOne(() => GeneralDataDetailEntity)
  @JoinColumn({ name: 'typeId', referencedColumnName: 'id' })
  type: Promise<GeneralDataDetailEntity>;
}
