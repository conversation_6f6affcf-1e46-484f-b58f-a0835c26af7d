import { Body, Controller, UseGuards } from '@nestjs/common';
import { ApiOperation } from '@nestjs/swagger';
import { DefPost } from '~/@core/decorator';
import { ZktFaceIdAuthGuard } from '~/@systems/guard/zkt-face-id.guard';
import { ListenZktEventReq, ListStatusSyncUserReq, ZktFaceIdReq } from '~/dto/zkt-face-id.dto';
import { ZktFaceIdService } from '~/x-modules/integration/services';

@UseGuards(ZktFaceIdAuthGuard)
@Controller('zkt-face-id')
export class ZktFaceIdController {
  constructor(private readonly zktFaceIdService: ZktFaceIdService) {}

  @DefPost('/sync-list-user')
  @ApiOperation({ summary: 'sync list user info to the device' })
  async syncListUserZKT(@Body() params: ZktFaceIdReq) {
    const result = await this.zktFaceIdService.syncListUserZKT(params);

    return {
      status: true,
      data: result,
    };
  }

  @DefPost('/list-status-sync-user')
  @ApiOperation({ summary: 'list status sync from device' })
  async listStatusSyncUser(@Body() params: ListStatusSyncUserReq) {
    await this.zktFaceIdService.listStatusSyncUser(params);

    return {
      status: true,
    };
  }

  @DefPost('/listen-zkt-event')
  @ApiOperation({ summary: 'Listen for ZKTeco device events' })
  async listenZktEvent(@Body() params: ListenZktEventReq) {
    try {
      const result = await this.zktFaceIdService.listenZktEvent(params);

      return {
        status: true,
        data: result,
      };
    } catch (error) {
      console.error('Failed to handle ZKTeco event:', error);
      return {
        status: false,
        message: error?.message ?? 'Internal Server Error',
      };
    }
  }
}
