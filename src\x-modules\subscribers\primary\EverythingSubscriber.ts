import {
  EventSubscriber,
  EntitySubscriberInterface,
  InsertEvent,
  UpdateEvent,
  RemoveEvent,
} from 'typeorm';
// import { configEnv } from '~/@config/env';
// import { EventHubService } from '~/x-modules/@global/services/event-hub.service';
// import * as aws from 'aws-sdk';
// import { NSSQSS } from '~/common/enums/NSSQS';
// const {
//   IS_DOCKER_SERVER,
//   IS_DISABLE_SQS,
//   AWS_SQS_URL,
//   AWS_SQS_REGION,
//   AWS_SQS_ACCESS_KEY_ID,
//   AWS_SQS_SECRET_ACCESS_KEY,
//   AWS_API_VERSION,
// } = configEnv();
@EventSubscriber()
export class EverythingSubscriber implements EntitySubscriberInterface {
  // private eventHubService: EventHubService;
  // private readonly sqs: aws.SQS;
  // private queueUrl = '';

  constructor() {
    // Tạo instance thủ công service, chú ý service này có thể phụ thuộc khác sẽ không được inject
    // this.eventHubService = new EventHubService(/* nếu cần truyền tham số */);
    // this.queueUrl = AWS_SQS_URL || '';
    // aws.config.update({
    //   region: AWS_SQS_REGION || 'ap-southeast-1',
    //   accessKeyId: AWS_SQS_ACCESS_KEY_ID || '',
    //   secretAccessKey: AWS_SQS_SECRET_ACCESS_KEY || '',
    // });
    // const sqs = new aws.SQS({
    //   apiVersion: AWS_API_VERSION || '2023-03-09',
    // });
    // this.sqs = sqs;
  }

  /**
   * Called before entity insertion.
   */
  beforeInsert(event: InsertEvent<any>) {
    // const tableName = event.metadata.tableName;
    // if (tableName === 'ape_token') return;
    // const msg = this.eventHubService.mappingData({
    //   table: tableName,
    //   data: [event.entity],
    //   timestamp: new Date(),
    // });
    // if (IS_DOCKER_SERVER) {
    //   this.sendMessage(
    //     {
    //       message: 'Action Send Event To Event Hub',
    //       data: [msg],
    //     },
    //     NSSQSS.EMessageType.OnDockerEventHandling,
    //   );
    // } else {
    //   this.eventHubService.sendEvents([msg]);
    // }
    // console.log(`BEFORE ENTITY INSERTED: `, event.entity);
  }

  /**
   * Called before entity insertion.
   */
  beforeUpdate(event: UpdateEvent<any>) {
    // const tableName = event.metadata.tableName;
    // if (tableName === 'ape_token') return;
    // const msg = this.eventHubService.mappingData({
    //   table: tableName,
    //   data: [event.entity],
    //   timestamp: new Date(),
    // });
    // if (IS_DOCKER_SERVER) {
    //   this.sendMessage(
    //     {
    //       message: 'Action Send Event To Event Hub',
    //       data: [msg],
    //     },
    //     NSSQSS.EMessageType.OnDockerEventHandling,
    //   );
    // } else {
    //   this.eventHubService.sendEvents([msg]);
    // }
    // console.log(`BEFORE ENTITY UPDATED: `, msg);
  }

  /**
   * Called before entity insertion.
   */
  beforeRemove(event: RemoveEvent<any>) {
    // console.log(`BEFORE ENTITY WITH ID ${event.entityId} REMOVED: `, event.entity);
  }

  /**
   * Called after entity insertion.
   */
  afterInsert(event: InsertEvent<any>) {
    // console.log(`AFTER ENTITY INSERTED: `, event.entity);
  }

  /**
   * Called after entity insertion.
   */
  afterUpdate(event: UpdateEvent<any>) {
    // console.log(`AFTER ENTITY UPDATED: `, event.entity);
  }

  /**
   * Called after entity insertion.
   */
  afterRemove(event: RemoveEvent<any>) {
    // console.log(`AFTER ENTITY WITH ID ${event.entityId} REMOVED: `, event.entity);
  }

  /**
   * Called after entity is loaded.
   */
  afterLoad(entity: any) {
    // console.log(`AFTER ENTITY LOADED: `, entity);
  }

  // sendMessage(
  //   message: {
  //     message: string;
  //     data: any;
  //   },
  //   type?: string,
  //   delaySeconds = 0,
  // ) {
  //   if (this.sqs) {
  //     return new Promise(async (resolve, reject) => {
  //       try {
  //         const messageBody = {
  //           ...message,
  //           type: type,
  //         };
  //         const params = {
  //           DelaySeconds: delaySeconds,
  //           MessageBody: JSON.stringify(messageBody),
  //           QueueUrl: this.queueUrl,
  //         };
  //         this.sqs.sendMessage(params, async (err, data) => {
  //           if (err) {
  //             console.log('Error sending message to SQS');
  //           } else {
  //             resolve(data);
  //           }
  //         });
  //         // }
  //       } catch (error) {
  //         console.error('Error sending message to SQS');
  //         // reject(error);
  //       }
  //     });
  //   }
  // }
}
