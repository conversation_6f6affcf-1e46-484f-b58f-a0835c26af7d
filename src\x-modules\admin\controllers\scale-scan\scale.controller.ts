import { Body, Controller, Get, Post, Query, UseGuards } from '@nestjs/common';
import { DefController, DefGet, DefPost } from '~/@core/decorator';
import { Roles } from '~/@core/decorator/roles.decorator';
import { RoleGuard } from '~/@systems/guard/role.guard';
import {
  CheckConnectReq,
  ConvertUomReq,
  CreateScaleRequestDto,
  GetLotRequestDto,
  UpdateWeighedStatusReq,
} from '~/dto/scale.dto';
import { PrinterService } from '~/x-modules/admin/services';
import { ScaleApiService } from '~/x-modules/admin/services/scale-scan/scale-api.service';
import { ScaleService } from '~/x-modules/admin/services/scale-scan/scale.service';

@DefController('scale')
export class ScaleController {
  constructor(
    private readonly scaleService: ScaleService,
    private readonly scaleApiService: ScaleApiService,
    private readonly printerService: PrinterService,
  ) {}

  @DefGet('get-production-area')
  @Roles('/production-execution/weighing-screen', 'View')
  @UseGuards(RoleGuard)
  async getProductionArea(@Query('organizationUnitId') organizationUnitId: string) {
    return this.scaleService.getProductionAreaByOrgUnit(organizationUnitId);
  }
  @DefGet('get-process-area')
  @Roles('/production-execution/weighing-screen', 'View')
  @UseGuards(RoleGuard)
  async getProcessArea(@Query('lineId') lineId: string) {
    return this.scaleService.getProcessArea(lineId);
  }

  @DefGet('get-po')
  @Roles('/production-execution/weighing-screen', 'View')
  @UseGuards(RoleGuard)
  async getPOByProcessArea(@Query('processAreaId') processAreaId: string) {
    return this.scaleService.getPOByProcessArea(processAreaId);
  }

  @DefGet('get-batch')
  @Roles('/production-execution/weighing-screen', 'View')
  @UseGuards(RoleGuard)
  async getBatchByPO(@Query('orderId') orderId: string) {
    return this.scaleService.getBatchByPO(orderId);
  }

  @DefGet('get-item')
  @Roles('/production-execution/weighing-screen', 'View')
  @UseGuards(RoleGuard)
  async getItem(
    @Query('orderId') orderId: string,
    @Query('processId') processId: string,
    @Query('batchId') batchId: string,
  ) {
    return this.scaleService.getItem(orderId, processId, batchId);
  }
  @Get('get-lot')
  @Roles('/production-execution/weighing-screen', 'View')
  @UseGuards(RoleGuard)
  async getLot(@Query() data: GetLotRequestDto) {
    return this.scaleService.getLot(data);
  }

  @Get('get-station')
  @Roles('/production-execution/weighing-screen', 'View')
  @UseGuards(RoleGuard)
  async getStation(@Query('processId') processId: string) {
    return this.scaleService.getStation(processId);
  }

  @Get('get-device')
  @Roles('/production-execution/weighing-screen', 'View')
  @UseGuards(RoleGuard)
  async getDevice(@Query('stationId') stationId: string) {
    return this.scaleService.getDevice(stationId);
  }

  @Get('get-data-scale-info')
  @Roles('/production-execution/weighing-screen', 'View')
  @UseGuards(RoleGuard)
  async getDataScaleInfo(
    @Query('orderId') orderId: string,
    @Query('batchId') batchId: string,
    @Query('itemCode') itemCode: string,
    @Query('materialLotNumber') materialLotNumber: string,
  ) {
    return this.scaleService.getDataScaleInfo(orderId, batchId, itemCode, materialLotNumber);
  }

  @Post('create')
  @Roles('/production-execution/weighing-screen', 'View')
  @UseGuards(RoleGuard)
  create(@Body() dto: CreateScaleRequestDto) {
    return this.scaleService.createDataScale(dto);
  }

  @Get('get-data-scale')
  @Roles('/production-execution/weighing-screen', 'View')
  @UseGuards(RoleGuard)
  async list(@Query('orderId') orderId: string, @Query('batchId') batchId: string) {
    return this.scaleService.listScale(orderId, batchId);
  }

  @Post('update-weighed-status')
  @Roles('/production-execution/weighing-screen', 'View')
  @UseGuards(RoleGuard)
  async updateWeighedStatus(@Body() dto: UpdateWeighedStatusReq) {
    return this.scaleService.updateWeighedStatus(dto.batchId);
  }

  @Post('convert-uom')
  @Roles('/production-execution/weighing-screen', 'View')
  @UseGuards(RoleGuard)
  async convertUom(@Body() dto: ConvertUomReq) {
    return this.scaleService.convertUom(dto);
  }

  @DefGet('check-connection')
  @Roles('/production-execution/weighing-screen', 'View')
  @UseGuards(RoleGuard)
  async getExternalData(@Query() params: CheckConnectReq) {
    try {
      const response = await this.scaleApiService.callApiHelperGet(
        '/scale/connection-status',
        params,
      );
      return response;
    } catch (error) {
      return error;
    }
  }

  @DefPost('reconnect')
  @Roles('/production-execution/weighing-screen', 'View')
  @UseGuards(RoleGuard)
  async reconnect(@Body() params: CheckConnectReq) {
    try {
      const response = await this.scaleApiService.callApiHelperPost('/scale/reconnect', params);
      return response;
    } catch (error) {
      return error;
    }
  }

  // controller check connect print
  @DefGet('check-connect-printer')
  @Roles('/production-execution/weighing-screen', 'View')
  @UseGuards(RoleGuard)
  async checkConnectPrinter(@Query() params: CheckConnectReq) {
    try {
      const response = await this.printerService.checkConnectPrinter(
        params.deviceIp,
        params.devicePort,
      );
      return response;
    } catch (error) {
      return error;
    }
  }

  // controler reconnect printer
  @DefPost('reconnect-printer')
  @Roles('/production-execution/weighing-screen', 'View')
  @UseGuards(RoleGuard)
  async reconnectPrinter(@Body() params: CheckConnectReq) {
    try {
      const response = await this.printerService.reconnectPrinter(
        params.deviceIp,
        params.devicePort,
      );
      return response;
    } catch (error) {
      return error;
    }
  }

  @DefGet('fake-data')
  @Roles('/production-execution/weighing-screen', 'View')
  @UseGuards(RoleGuard)
  async fakeData() {
    return this.scaleService.fakeData();
  }
}
