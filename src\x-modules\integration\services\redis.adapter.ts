import { IoAdapter } from '@nestjs/platform-socket.io';
import { createAdapter } from '@socket.io/redis-adapter';
import { createClient } from 'redis';
import { configEnv } from '~/@config/env';
const { REDIS_URL } = configEnv();
export class RedisIoAdapter extends IoAdapter {
  private adapterConstructor: ReturnType<typeof createAdapter>;

  async connectToRedis(): Promise<void> {
    const pubClient = createClient({
      // url: 'redis://default:<EMAIL>:6379',
      url: REDIS_URL,
      socket: {
        tls: true,
      },
    });
    const subClient = pubClient.duplicate();

    await pubClient.connect();
    await subClient.connect();
    console.log('✅ Redis connected');
    this.adapterConstructor = createAdapter(pubClient, subClient);
  }

  createIOServer(port: number, options?: any): any {
    const server = super.createIOServer(port, options);
    server.adapter(this.adapterConstructor);
    return server;
  }
}
