import { Injectable, NotFoundException } from '@nestjs/common';
import { ILike } from 'typeorm';
import { BindRepo } from '~/@core/decorator';
import {
  InspectionPlanRepo,
  OrganizationUnitRepo,
  ProductionBatchRepo,
  ProductionOrderRepo,
  QualityNotificationRepo,
  SettingStringRepo,
} from '~/repositories/primary';
import { RejectionMonitoringService } from '~/x-modules/admin/services/production-execution-monitoring/rejection-monitoring.service';

import { QualityNotificationPaginationDto } from '~/dto/quality-notification.dto';
import { NSSetting } from '~/common/enums/NSSetting.enum';
import axios from 'axios';
import { BusinessException } from '~/@systems/exceptions';
const AWS = require('aws-sdk');
import { configEnv } from '~/@config/env';
const { QLONE_SAMPLE_URL } = configEnv();

@Injectable()
export class QualityNotificationService {
  constructor(private readonly rejectionMonitoringService: RejectionMonitoringService) {}

  @BindRepo(QualityNotificationRepo)
  private readonly repo: QualityNotificationRepo;

  @BindRepo(InspectionPlanRepo)
  private readonly inspectionPlanRepo: InspectionPlanRepo;

  @BindRepo(SettingStringRepo)
  private readonly settingStringRepo: SettingStringRepo;

  @BindRepo(ProductionOrderRepo)
  private readonly productionOrderRepo: ProductionOrderRepo;

  @BindRepo(ProductionBatchRepo)
  private readonly productionBatchRepo: ProductionBatchRepo;

  @BindRepo(OrganizationUnitRepo)
  private readonly organizationUnitRepo: OrganizationUnitRepo;

  private qloneUrl = QLONE_SAMPLE_URL;

  /**
   * Tìm tất cả lý do với bộ lọc tùy chọn
   * @param filterDto Tiêu chí lọc tùy chọn
   * @returns Danh sách phân trang
   */
  async pagination(params: QualityNotificationPaginationDto) {
    const {
      pageIndex = 1,
      pageSize = 10,
      siteId,
      factoryId,
      lineId,
      createdDate,
      status,
      qualityNotificationNo,
      itemId,
      qloneNo,
    } = params;

    const where: any = {};

    if (siteId) where.siteId = siteId;
    if (factoryId) where.factoryId = factoryId;
    if (lineId) where.lineId = lineId;
    if (createdDate) where.createdDate = createdDate;
    if (status) where.status = status;
    if (qualityNotificationNo) where.qualityNotificationNo = ILike(`%${qualityNotificationNo}%`);
    if (itemId) where.itemId = itemId;
    if (qloneNo) where.qloneNo = ILike(`%${qloneNo}%`);

    const [data, total] = await this.repo.findAndCount({
      where,
      skip: (pageIndex - 1) * pageSize,
      take: pageSize,
      order: {
        createdDate: 'DESC',
        qualityNotificationNo: 'DESC',
      },
    });

    const enhancedData = await Promise.all(
      data.map(async (item) => {
        const [inspection, productionOrder, productionBatch, productionLine] = await Promise.all([
          this.inspectionPlanRepo.findOne({
            where: { id: item.inspectionPlanId },
            select: ['dataType'],
          }),
          this.productionOrderRepo.findOne({
            where: { id: item.productionOrderId },
            select: ['orderNo'],
          }),
          this.productionBatchRepo.findOne({
            where: { id: item.productionBatchId },
            select: ['batchNumber'],
          }),
          this.organizationUnitRepo.findOne({
            where: { id: item.productionLineId },
          }),
        ]);

        let factory = null;
        let site = null;

        if (productionLine?.parentId) {
          factory = await this.organizationUnitRepo.findOne({
            where: { id: productionLine.parentId },
          });
          if (factory?.parentId) {
            site = await this.organizationUnitRepo.findOne({
              where: { id: factory.parentId },
            });
          }
        } else {
          throw new NotFoundException(`Không tìm thấy thông tin nhà máy`);
        }

        return {
          ...item,
          site: site?.name || null,
          factory: factory?.name || null,
          productionLine: productionLine?.name || null,
          dataType: inspection?.dataType || null,
          orderNo: productionOrder?.orderNo || null,
          batchNo: productionBatch?.batchNumber || null,
        };
      }),
    );

    return { data: enhancedData, total };
  }

  async getQualityInformation(id: string): Promise<any> {
    const qual = await this.repo.findOne({ where: { id } });
    if (!qual) throw new NotFoundException('Không tìm thấy dữ liệu Qualitication');

    const { qualityNotificationNo, inspectionPlanId } = qual;
    const plan = await this.inspectionPlanRepo.findOne({ where: { id: inspectionPlanId } });

    if (!plan) throw new NotFoundException('Không tìm thấy InspectionPlan');

    const { dataType } = plan;

    const token = await this.getTokenQloneCognito();

    const response = await axios.get(this.qloneUrl, {
      params: {
        NotificationNumber: qualityNotificationNo,
        QANo: qualityNotificationNo,
        DataType: dataType,
      },
      headers: {
        Authorization: `Bearer ${token}`,
      },
    });

    return response.data;
  }
  async getTokenQloneCognito() {
    const checkSetting = await this.settingStringRepo.findOne({
      where: { code: NSSetting.ESettingCode.COGNITO_TOKEN },
    });

    // Nếu token lưu trong db còn hạn thì lấy từ db ra
    if (checkSetting?.value) {
      const objValue = JSON.parse(checkSetting.value);
      const { expiredTime, IdToken } = objValue;
      const curTime = new Date().getTime();
      if (expiredTime > curTime && IdToken) {
        return { IdToken };
      }
    }

    const payload = {
      AuthFlow: 'USER_PASSWORD_AUTH',
      ClientId: process.env.CLIENT_ID,
      AuthParameters: {
        USERNAME: process.env.USERNAME_COGNITO,
        PASSWORD: process.env.PASSWORD_COGNITO,
      },
    };
    const cognitoEndpoint = `https://cognito-idp.${process.env.REGION}.amazonaws.com/`;

    try {
      const res = await axios.post(cognitoEndpoint, payload, {
        headers: {
          'X-Amz-Target': 'AWSCognitoIdentityProviderService.InitiateAuth',
          'Content-Type': 'application/x-amz-json-1.1',
        },
      });
      const { AccessToken, IdToken, RefreshToken, ExpiresIn } = res?.data?.AuthenticationResult;
      if (!IdToken) throw new BusinessException('Hệ thống QLONE không trả về IdToken');
      const expiredTime = new Date().getTime() + (ExpiresIn - 5 * 60) * 1000; // 5 phút trước khi hết hạn

      // Lưu lại token vào db sử dụng cho lần gọi sau
      if (checkSetting) {
        await this.settingStringRepo.update(checkSetting.id, {
          value: JSON.stringify({ AccessToken, IdToken, RefreshToken, expiredTime }),
          updatedDate: new Date(),
        });
      } else {
        await this.settingStringRepo.insert({
          code: NSSetting.ESettingCode.COGNITO_TOKEN,
          type: NSSetting.EDataType.STRING,
          value: JSON.stringify({ AccessToken, IdToken, RefreshToken, expiredTime }),
          // createdBy: '-1',
        });
      }
      return { IdToken };
    } catch (error) {
      throw new BusinessException(
        'Lỗi khi lấy token từ hệ thống QLONE, chi tiết: ' + error.toString(),
      );
    }
  }
  async resultQLONESample(body: any) {
    const { NotificationNumber, SampleNumber, Decision, DecisionTimeStamp, DecisionBy } = body;

    if (!NotificationNumber || !SampleNumber || !Decision || !DecisionTimeStamp || !DecisionBy) {
      throw new Error('Missing required fields');
    }

    //     const result = await this.repo.save({
    //   inspection_plan_notification_no: NotificationNumber,
    //   qlone_no: SampleNumber,
    //   result: Decision,
    //   qlone_update_date: DecisionTimeStamp,
    //   qlone_update_by: DecisionBy,
    // });

    // TODO: nhờ check lại
    const toSave = this.repo.create({
      qualityNotificationNo: NotificationNumber,
      sampleNo: SampleNumber,
      result: Decision,
      qloneUpdateDate: DecisionTimeStamp,
      qloneUpdateBy: DecisionBy,
    });

    const result = await this.repo.save(toSave);

    return { message: 'Created successfully', data: result };
  }
}
