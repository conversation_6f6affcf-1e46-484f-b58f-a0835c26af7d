import { Injectable } from '@nestjs/common';
import { BindRepo } from '~/@core/decorator';
import { dayjs, numberHelper } from '~/common/helpers';
import { filterReq } from '~/dto/dashboard-pqcds.dto';
import {
  KpiRepo,
  KpiScoreRepo,
  KpiStandardScoreRepo,
  ProductionOeeRepo,
} from '~/repositories/primary';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import isoWeek from 'dayjs/plugin/isoWeek';
dayjs.extend(isoWeek);
dayjs.extend(utc);
dayjs.extend(timezone);
@Injectable()
export class DashboardPqcdsService {
  constructor() {}
  @BindRepo(ProductionOeeRepo)
  private productionOeeRepo: ProductionOeeRepo;

  @BindRepo(KpiRepo)
  private kpiRepo: KpiRepo;

  @BindRepo(KpiScoreRepo)
  private kpiScoreRepo: KpiScoreRepo;

  @BindRepo(KpiStandardScoreRepo)
  private kpiStandardScoreRepo: KpiStandardScoreRepo;

  async getProductionOee(data: filterReq) {
    try {
      // Filter data
      let query = `
        SELECT * FROM production_oee oee
        JOIN organization_units ou_line ON ou_line.id::text = oee."productionLineId"
        JOIN organization_units ou_factory ON ou_factory.id = ou_line."parentId"
        JOIN organization_units ou_site ON ou_site.id = ou_factory."parentId"
        AND ou_factory.id = $1
        AND ou_site.id = $2
        `;

      const params = [data.factoryId, data.siteId];

      if (data.categoryCode) {
        query += ` AND ou_factory."categoryGeneralDataDetailCode" = $3`;
        params.push(data.categoryCode);
      }

      const result = await this.productionOeeRepo.query(query, params);
      let oeeToday;
      let oeeYesterday;
      let oeeThisWeek;
      let oeeThisMonth;
      let oeePreviousMonth;
      // Nhóm data result theo nhóm yesterday, today, this week, this month, previous month dựa vào cột productionDate trong bảng production_oee
      const grouped = this.groupByProductionDate(result);

      if (grouped.today && grouped.today.length > 0) {
        oeeToday = await this.calculateOEE(grouped.today);
      }
      if (grouped.yesterday && grouped.yesterday.length > 0) {
        oeeYesterday = await this.calculateOEE(grouped.yesterday);
      }
      if (grouped.thisWeek && grouped.thisWeek.length > 0) {
        oeeThisWeek = await this.calculateOEE(grouped.thisWeek);
      }
      if (grouped.thisMonth && grouped.thisMonth.length > 0) {
        oeeThisMonth = await this.calculateOEE(grouped.thisMonth);
      }
      if (grouped.previousMonth && grouped.previousMonth.length > 0) {
        oeePreviousMonth = await this.calculateOEE(grouped.previousMonth);
      }

      const oee = {
        today: oeeToday ? numberHelper.formatNumber(oeeToday) : null,
        yesterday: oeeYesterday ? numberHelper.formatNumber(oeeYesterday) : null,
        thisWeek: oeeThisWeek ? numberHelper.formatNumber(oeeThisWeek) : null,
        thisMonth: oeeThisMonth ? numberHelper.formatNumber(oeeThisMonth) : null,
        previousMonth: oeePreviousMonth ? numberHelper.formatNumber(oeePreviousMonth) : null,
      };
      return oee;
    } catch (error) {
      console.log('Error get production oee: ', error);
    }
  }

  async getKpi(data: filterReq) {
    try {
      let query = `
      SELECT  
        ks.*, 
        kpi.*
      FROM kpi_score as ks
      JOIN organization_units ou_factory ON ou_factory.id = ks."factoryId"
      JOIN organization_units ou_site ON ou_site.id = ou_factory."parentId"
      JOIN kpi ON ks."kpiId" = kpi.id 
      WHERE ou_factory.id = $1
      AND ou_site.id = $2
      AND kpi."kpiMethod" = 'A'
      AND kpi."status" = 1
    `;

      const params = [data.factoryId, data.siteId];

      if (data.categoryCode) {
        query += ` AND ou_factory."categoryGeneralDataDetailCode" = $3`;
        params.push(data.categoryCode);
      }

      const res = await this.kpiScoreRepo.query(query, params);

      let kpiToday;
      let kpiYesterday;
      let kpiThisWeek;
      let kpiThisMonth;
      let kpiPreviousMonth;

      const grouped = this.groupByCreateDate(res);

      if (grouped.today?.length) {
        kpiToday = await this.calculateKpi(grouped.today);
      }
      if (grouped.yesterday?.length) {
        kpiYesterday = await this.calculateKpi(grouped.yesterday);
      }
      if (grouped.thisWeek?.length) {
        kpiThisWeek = await this.calculateKpi(grouped.thisWeek);
      }
      if (grouped.thisMonth?.length) {
        kpiThisMonth = await this.calculateKpi(grouped.thisMonth);
      }
      if (grouped.previousMonth?.length) {
        kpiPreviousMonth = await this.calculateKpi(grouped.previousMonth);
      }

      const kpi = this.mergeKpiData({
        today: kpiToday || '',
        yesterday: kpiYesterday || '',
        thisWeek: kpiThisWeek || '',
        thisMonth: kpiThisMonth || '',
        previousMonth: kpiPreviousMonth || '',
      });

      return kpi;
    } catch (error) {
      console.log('Error get kpi: ', error);
    }
  }

  async getKpiManual(data: filterReq) {
    try {
      let query = `
      SELECT  
        ks.*, 
        kpi.*
      FROM kpi_score as ks
      JOIN organization_units ou_factory ON ou_factory.id = ks."factoryId"
      JOIN organization_units ou_site ON ou_site.id = ou_factory."parentId"
      JOIN kpi ON ks."kpiId" = kpi.id 
      WHERE ou_factory.id = $1
      AND ou_site.id = $2
      AND kpi."kpiMethod" = 'M'
      AND kpi."status" = 1
    `;

      const params = [data.factoryId, data.siteId];

      if (data.categoryCode) {
        query += ` AND ou_factory."categoryGeneralDataDetailCode" = $3`;
        params.push(data.categoryCode);
      }

      const res = await this.kpiScoreRepo.query(query, params);

      const grouped = this.groupByCreateDate(res);

      const kpiToday = grouped.today?.length ? await this.calculateKpiManual(grouped.today) : '';
      const kpiYesterday = grouped.yesterday?.length
        ? await this.calculateKpiManual(grouped.yesterday)
        : '';
      const kpiThisWeek = grouped.thisWeek?.length
        ? await this.calculateKpiManual(grouped.thisWeek)
        : '';
      const kpiThisMonth = grouped.thisMonth?.length
        ? await this.calculateKpiManual(grouped.thisMonth)
        : '';
      const kpiPreviousMonth = grouped.previousMonth?.length
        ? await this.calculateKpiManual(grouped.previousMonth)
        : '';

      const kpi = this.mergeKpiData({
        today: kpiToday,
        yesterday: kpiYesterday,
        thisWeek: kpiThisWeek,
        thisMonth: kpiThisMonth,
        previousMonth: kpiPreviousMonth,
      });

      return kpi;
    } catch (error) {
      console.log('Error get kpi manual: ', error);
    }
  }

  async getKpiProductOutput(data: filterReq) {
    try {
      let query = `
      SELECT * FROM production_oee oee
      JOIN organization_units ou_line ON ou_line.id::text = oee."productionLineId"
      JOIN organization_units ou_factory ON ou_factory.id = ou_line."parentId"
      JOIN organization_units ou_site ON ou_site.id = ou_factory."parentId"
      WHERE ou_factory.id = $1
      AND ou_site.id = $2
    `;

      const params = [data.factoryId, data.siteId];

      if (data.categoryCode) {
        query += ` AND ou_line."categoryGeneralDataDetailCode" = $3`;
        params.push(data.categoryCode);
      }

      const result = await this.productionOeeRepo.query(query, params);

      const grouped = this.groupByProductionDate(result);

      const oeeToday = grouped.today?.length
        ? await this.calculateKpiProductOutput(grouped.today)
        : null;
      const oeeYesterday = grouped.yesterday?.length
        ? await this.calculateKpiProductOutput(grouped.yesterday)
        : null;
      const oeeThisWeek = grouped.thisWeek?.length
        ? await this.calculateKpiProductOutput(grouped.thisWeek)
        : null;
      const oeeThisMonth = grouped.thisMonth?.length
        ? await this.calculateKpiProductOutput(grouped.thisMonth)
        : null;
      const oeePreviousMonth = grouped.previousMonth?.length
        ? await this.calculateKpiProductOutput(grouped.previousMonth)
        : null;

      return {
        today: oeeToday,
        yesterday: oeeYesterday,
        thisWeek: oeeThisWeek,
        thisMonth: oeeThisMonth,
        previousMonth: oeePreviousMonth,
      };
    } catch (error) {
      console.log('Error get kpi product output: ', error);
    }
  }

  async calculateOEE(data) {
    // Cách tính OEE: duyệt qua tất cả các dòng dữ liệu OEE thỏa điều kiện filter và thời gian từ tháng trước đến ngày hiện tại. Tính OEE cho Yesterday, Today, This Week, This Month, Previous Month
    // - OEE=A*P*Q
    // - Availability=((Loading time - downtime)/loading time)*100
    // - Performance=Actual product qty/Expected product qty*100
    // - Quality=(Actual product qty - Defective products)/Actual product qty*100
    // Khi tính OEE cho Yesterday, Today, This Week, This Month, Previous Month thì các chỉ số Loading time, downtime, Actual product qty, Expected product qty, Defective products được tính dựa vào bảng production_oee bằng cách cộng các dòng theo trường productionDate tương ứng khi tính cho yesterday, today, this week, this month, previous month.
    let loadingTime = 0;
    let downtime = 0;
    let actualQty = 0;
    let expectedQty = 0;
    let defective = 0;
    data.forEach((row) => {
      loadingTime += Number(row.loadingTime);
      downtime += Number(row.downtime);
      actualQty += Number(row.actualProductQty);
      expectedQty += Number(row.expectedProductQty);
      defective += Number(row.defectiveProducts);
    });
    const availability = loadingTime > 0 ? ((loadingTime - downtime) / loadingTime) * 100 : 0;
    const performance =
      expectedQty > 0
        ? ((actualQty === 0 ? 1 : actualQty) / (expectedQty === 0 ? 1 : expectedQty)) * 100
        : 0;
    const quality =
      actualQty > 0
        ? ((actualQty === 0 ? 1 : actualQty) / (expectedQty === 0 ? 1 : actualQty)) * 100
        : 0;
    const oee = (availability * performance * quality) / 10000;

    return oee;
  }

  //  Calculate the total KPI value from an array of KPI data
  async calculateKpi(data) {
    const groupedData = {};
    // Nhóm dữ liệu theo autoFunction__shortName
    for (const item of data) {
      const key = `${item.autoFunction}__${item.shortName}`;
      if (!groupedData[key]) {
        groupedData[key] = [];
      }
      groupedData[key].push(item);
    }
    const result = [];
    for (const key in groupedData) {
      const items = groupedData[key];
      const [autoFunction, shortName] = key.split('__');
      let totalQty = 0;
      let totalValue = 0;
      let totalScore = 0;
      let countAvg = 0;
      let avg = 0;

      for (const item of items) {
        totalQty += Number(item.actualQty || 0);
        totalValue += Number(item.actualValue || 0);
        totalScore += Number(item.actualScore || 0);
        if (item.calType === 'AVG') {
          countAvg++;
        }
        if (item.autoFunction === 'F_KPI_REJECTION') {
          avg++;
        }
      }
      let kpiValue = 0;
      switch (autoFunction) {
        case 'F_KPI_MAN-HOUR':
        case 'F_KPI_UTILITY':
          kpiValue = totalQty !== 0 ? (totalValue * 1000) / totalQty : 0;
          break;
        case 'F_KPI_REJECTION':
          kpiValue = totalScore / avg;
          break;
        case 'F_KPI_MATERIAL':
          kpiValue = totalQty !== 0 ? ((totalValue - totalQty) / totalQty) * 100 : 0;
          break;
        default:
          const calType = items[0]?.calType;
          if (calType === 'SUM') {
            kpiValue = totalScore;
          } else if (calType === 'AVG') {
            kpiValue = countAvg !== 0 ? totalScore / countAvg : 0;
          }
          break;
      }

      result.push({
        autoFunction,
        shortName,
        kpiValue,
      });
    }
    return result;
  }

  async calculateKpiManual(data) {
    const groupedData = {};
    for (const item of data) {
      const key = item.shortName;
      if (!groupedData[key]) {
        groupedData[key] = [];
      }
      groupedData[key].push(item);
    }
    let result = [];
    for (const shortName in groupedData) {
      const items = groupedData[shortName];
      let kpiValue = 0;
      let avgTotal = 0;
      let avgCount = 0;

      for (const item of items) {
        const score = Number(item.actualScore);
        if (item.calType === 'SUM') {
          kpiValue += score;
        } else if (item.calType === 'AVG') {
          avgTotal += score;
          avgCount += 1;
          kpiValue = avgTotal / avgCount;
        }
      }

      result.push({
        shortName,
        kpiValue,
      });
    }
    return result;
  }

  async calculateKpiProductOutput(data) {
    let actualQty = 0;
    let defective = 0;
    data.forEach((row) => {
      actualQty += Number(row.actualProductQty);
      defective += Number(row.defectiveProducts);
    });
    const kpiValue = actualQty - defective;
    return kpiValue;
  }

  async calculateHe(data: filterReq) {
    try {
      const res = await this.kpiStandardScoreRepo.query(
        `
        SELECT  
            k.code,
            k."shortName",
            k."kpiCategory",
            k."calMethod",
            AVG(kss."levelScore") FILTER (WHERE kss."level" = 'HE') AS levelScoreHE,
            AVG(kss."levelScore") FILTER (WHERE kss."level" = 'E') AS levelScoreE
        FROM kpi_standard_score AS kss
        JOIN kpi AS k ON kss."kpiCodeId" = k.id
        JOIN kpi_set_header AS ksh ON ksh.id = kss."kpiSetId"
        JOIN kpi_set_production_area AS kspa ON kspa."kpiSetHeaderId" = ksh.id
        WHERE 1 = 1
          AND kss."level" IN ('HE', 'E')
          AND ksh."status" = 1
          AND kspa."factoryId" = $2
          AND kspa."siteId" = $1
          AND kspa."status" = 1 
          AND kss."effectiveDateFrom" <= CURRENT_TIMESTAMP
          AND (kss."effectiveDateTo" IS NULL OR kss."effectiveDateTo" >= CURRENT_TIMESTAMP)
          AND k."kpiCategory" = 'IN'
        GROUP BY 
            k.code,
            k."shortName",
            k."kpiCategory",
            k."calMethod";
        `,
        [data.siteId, data.factoryId],
      );
      // for qua res format number he to 2 decimal places
      res.forEach((item) => {
        item.he = numberHelper.formatNumber(item.he);
      });
      return res;
    } catch (error) {
      console.log('Error to calculate HE', error);
    }
  }
  groupByProductionDate(result) {
    const groupedData = {
      yesterday: [],
      today: [],
      thisWeek: [],
      thisMonth: [],
      previousMonth: [],
    };

    // set the dates for grouping
    const now = dayjs();
    const todayStart = now.startOf('day');
    const yesterdayStart = todayStart.subtract(1, 'day');
    const weekStart = todayStart.startOf('isoWeek');
    const weekEnd = todayStart.endOf('isoWeek');
    const monthStart = todayStart.startOf('month');
    const monthEnd = todayStart.endOf('month');
    const previousMonthStart = todayStart.subtract(1, 'month').startOf('month');
    const previousMonthEnd = todayStart.subtract(1, 'month').endOf('month');
    // loop through each item in the result array
    for (const item of result) {
      const itemDate = dayjs(item.productionDate).startOf('day');

      if (itemDate.isSame(todayStart, 'day')) {
        groupedData.today.push(item);
      }
      if (itemDate.isSame(yesterdayStart, 'day')) {
        groupedData.yesterday.push(item);
      }
      if (itemDate.isSameOrAfter(weekStart) && itemDate.isSameOrBefore(weekEnd)) {
        groupedData.thisWeek.push(item);
      }
      if (itemDate.isSameOrAfter(monthStart) && itemDate.isSameOrBefore(monthEnd)) {
        groupedData.thisMonth.push(item);
      }
      if (itemDate.isSameOrAfter(previousMonthStart) && itemDate.isSameOrBefore(previousMonthEnd)) {
        groupedData.previousMonth.push(item);
      }
    }
    return groupedData;
  }
  groupByCreateDate(result) {
    const groupedData = {
      yesterday: [],
      today: [],
      thisWeek: [],
      thisMonth: [],
      previousMonth: [],
      specificDate: [],
    };

    const now = dayjs();
    const todayStart = now.startOf('day');
    const yesterdayStart = todayStart.subtract(1, 'day');
    const weekStart = todayStart.startOf('isoWeek');
    const weekEnd = todayStart.endOf('isoWeek');
    const monthStart = todayStart.startOf('month');
    const monthEnd = todayStart.endOf('month');
    const previousMonthStart = todayStart.subtract(1, 'month').startOf('month');
    const previousMonthEnd = todayStart.subtract(1, 'month').endOf('month');

    for (const item of result) {
      const itemDate = dayjs(item.scoreDate).startOf('day');
      if (itemDate.isSame(todayStart, 'day')) {
        groupedData.today.push(item);
      }
      if (itemDate.isSame(yesterdayStart, 'day')) {
        groupedData.yesterday.push(item);
      }
      if (itemDate.isSameOrAfter(weekStart) && itemDate.isSameOrBefore(weekEnd)) {
        groupedData.thisWeek.push(item);
      }
      if (itemDate.isSameOrAfter(monthStart) && itemDate.isSameOrBefore(monthEnd)) {
        groupedData.thisMonth.push(item);
      }
      if (itemDate.isSameOrAfter(previousMonthStart) && itemDate.isSameOrBefore(previousMonthEnd)) {
        groupedData.previousMonth.push(item);
      }
    }
    return groupedData;
  }

  mergeKpiData(kpiDataObj) {
    const result = {};

    const keys = ['today', 'yesterday', 'thisWeek', 'thisMonth', 'previousMonth'];
    for (const key of keys) {
      const kpis = kpiDataObj[key] || [];
      for (const item of kpis) {
        const shortName = item.shortName.trim();
        if (!result[shortName]) {
          result[shortName] = {};
        }
        result[shortName][key] = item.kpiValue;
      }
    }
    return result;
  }
}
