import { Entity, Column, OneToMany } from 'typeorm';
import { PrimaryBaseEntity } from '../primary-base.entity';
import { PermissionUserEntity } from './permission-user.entity';
import { ApiProperty } from '@nestjs/swagger';
import { UserEntity } from '~/entities/primary/user.entity';

@Entity('permission')
export class PermissionEntity extends PrimaryBaseEntity {
  /**Mã nhóm quyền */
  @Column({
    type: 'varchar',
    length: 100,
    nullable: false,
    unique: true,
  })
  code: string;

  /*Tên nhóm quyền */
  @Column({
    type: 'varchar',
    length: 150,
    nullable: false,
  })
  name: string;

  @Column({
    type: 'varchar',
    nullable: true,
  })
  description: string;

  @ApiProperty({ description: 'Trạng thái hoạt động' })
  @Column({ nullable: false, default: true })
  isActive: boolean;

  /**JSON Stringify of phân quyền */
  @Column({ type: 'text', nullable: true })
  roleStringify: string;

  @OneToMany(() => UserEntity, (e) => e.permission)
  users: Promise<UserEntity[]>;

  @OneToMany(() => PermissionUserEntity, (e) => e.permission)
  permissionUsers: Promise<PermissionUserEntity[]>;
}
