import { Column, Entity, OneToMany, PrimaryGeneratedColumn } from 'typeorm';
import { AssignShiftEntity } from './assign-shift.entity';
import { ApiProperty } from '@nestjs/swagger';
import { TimeSheetRecordEntity } from './timesheet-records.entity';
import { PrimaryBaseEntity } from '~/entities/primary-base.entity';

@Entity('shift')
export class ShiftEntity extends PrimaryBaseEntity {
  /** Mã ca làm việc */
  @ApiProperty({ description: 'Mã ca' })
  @Column({ type: 'text', unique: true })
  code: string;

  /** <PERSON>ô tả ca làm việc */
  @ApiProperty({ description: 'Tên ca', nullable: true })
  @Column({ name: 'description' })
  description: string;

  /** Thời gian bắt đầu ca làm việc */
  @ApiProperty({ description: 'Thời gian bắt đầu ca' })
  @Column({ name: 'startTime', type: 'time', nullable: true })
  startTime: string;

  /** Thời gian kết thúc ca làm việc */
  @ApiProperty({ description: 'Thời gian kết thúc ca' })
  @Column({ name: 'endTime', type: 'time', nullable: true })
  endTime: string;

  /** Ca đêm */
  @ApiProperty({ description: 'Đây là ca đêm?' })
  @Column({ name: 'nightShift', default: false })
  nightShift: boolean;

  /** Thời gian bắt đầu hiệu lực */
  @ApiProperty({ description: 'Thời gian hiệu lực từ' })
  @Column({ name: 'validFrom', nullable: true, type: 'timestamptz' })
  validFrom: Date;

  /** Thời gian bắt đầu hiệu lực */
  @ApiProperty({ description: 'Thời gian hiệu lực đến' })
  @Column({ name: 'validTo', nullable: true, type: 'timestamptz' })
  validTo: Date;

  /** Trạng thái ca */
  @ApiProperty({ description: 'Trạng thái ca' })
  @Column({ name: 'status', default: false })
  status: boolean;
  // @ApiProperty({ description: 'Cập nhật bởi' })
  // @Column({ name: 'lastModifiedBy', nullable: true })
  // lastModifiedBy: string;
  // @ApiProperty({ description: 'Ngày bắt đầu' })
  // @Column({ name: 'lastModifiedDate', type: 'timestamptz', nullable: true })
  // lastModifiedDate: Date;
  /** Quan hệ 1-nhiều với AssignShiftEntity */
  @OneToMany(() => AssignShiftEntity, (assignShift) => assignShift.shift)
  assignShifts?: AssignShiftEntity[];

  /** Relation với bảng TimeSheetRecord */
  @OneToMany(() => TimeSheetRecordEntity, (timesheet) => timesheet.shift)
  timesheets!: TimeSheetRecordEntity[];
}
