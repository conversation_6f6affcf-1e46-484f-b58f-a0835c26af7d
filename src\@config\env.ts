import dotenv from 'dotenv';
import { join } from 'path';
import { ConnectionOptions } from 'typeorm';

dotenv.config();

const stringToBoolean = (value: string | boolean) => {
  return Boolean(JSON.parse(`${value}`));
};

export type IEnvConfig = {
  DBS: ConnectionOptions[];
  MS_CLIENT_ID: string;
  MS_CLIENT_SECRET: string;
  MS_TENANT_ID: string;
  CONNECTORS?: {
    SSO: {
      baseUrl: string;
    };
  };
} & NodeJS.ProcessEnv;

export function configEnv(): IEnvConfig {
  const {
    PORT = 3000,
    TZ,
    REQUEST_TIMEOUT = 3 * 60 * 1000,
    DB_PRIMARY_HOST,
    DB_PRIMARY_PORT,
    DB_PRIMARY_USERNAME,
    DB_PRIMARY_PASSWORD,
    DB_PRIMARY_DATABASE,
    DB_PRIMARY_SYNCHRONIZE = false,
    DB_PRIMARY_SSL = false,
    DB_PRIMARY_SSL_REJECT_UNAUTHORIZED = true,
    // MS SSO
    MS_CLIENT_ID,
    MS_CLIENT_SECRET,
    MS_TENANT_ID,
    //SCADA
    DB_SCADA_HOST,
    DB_SCADA_PORT,
    DB_SCADA_USERNAME,
    DB_SCADA_PASSWORD,
    DB_SCADA_DATABASE,
    DB_SCADA_SYNCHRONIZE = false,
    DB_SCADA_SSL = false,
    DB_SCADA_SSL_REJECT_UNAUTHORIZED = true,
    // SWAGGER CONFIG
    SWAGGER_TITLE = 'BASE API',
    SWAGGER_DESCRIPTION = 'The BASE API',
    SWAGGER_VERSION = '1.0',
    JWT_SECRET,
    JWT_EXPIRY,
    API_TOKEN_CACHE_INTERVAL = 1,
    API_TOKEN_SECRET,
    AWS_ACCESS_KEY_ID,
    AWS_SECRET_ACCESS_KEY,
    AWS_REGION,
    DOCKER_SERVER_IP,
    TOKEN_ERP_URL,
    ERP_INVENTORY,
    EBS_MES_USERNAME,
    EBS_MES_PASSWORD,
    IS_DOCKER_SERVER = false,
    IS_DISABLE_SQS = true,
    IS_EVENT_HUB = false,
    INVENTORY_ERP_URL,
    AWS_SQS_URL,
    AWS_SQS_URL_CUSTOM,
    AWS_SQS_REGION,
    AWS_SQS_ACCESS_KEY_ID,
    AWS_SQS_SECRET_ACCESS_KEY,
    AWS_API_VERSION,
    AWS_SNS_ARN,
    REDIS_URL,
    ACCEPT_PUBLIC_IP,

    // AWS S3
    LINK_UPLOAD_S3,
    AWS_S3_BUCKET_NAME,
    AWS_S3_ACCESS_KEY_ID,
    AWS_S3_SECRET_ACCESS_KEY,
    AWS_S3_REGION,
    AWS_S3_UPLOAD_FOLDER,

    //
    QLONE_URL,
    QLONE_SAMPLE_URL,
    QLONE_CREATE_SAMPLE_URL,
    QLONE_VISUAL_INSPECTION_URL,

    // Azure Event Hub
    AZURE_EVENT_HUB_CONNECTION_STRING,
    AZURE_EVENT_HUB_NAME,
  } = process.env;

  return {
    TZ: TZ,
    REQUEST_TIMEOUT: Number(REQUEST_TIMEOUT),
    SWAGGER_TITLE,
    SWAGGER_DESCRIPTION,
    SWAGGER_VERSION,
    JWT_SECRET,
    JWT_EXPIRY,
    MS_CLIENT_ID,
    MS_CLIENT_SECRET,
    MS_TENANT_ID,
    API_TOKEN_CACHE_INTERVAL: Number(API_TOKEN_CACHE_INTERVAL),
    API_TOKEN_SECRET,
    AWS_ACCESS_KEY_ID,
    AWS_SECRET_ACCESS_KEY,
    AWS_REGION,
    DOCKER_SERVER_IP,
    TOKEN_ERP_URL,
    ERP_INVENTORY,
    EBS_MES_USERNAME,
    EBS_MES_PASSWORD,
    IS_DOCKER_SERVER: stringToBoolean(IS_DOCKER_SERVER),
    IS_DISABLE_SQS: stringToBoolean(IS_DISABLE_SQS),
    IS_EVENT_HUB: stringToBoolean(IS_EVENT_HUB),
    INVENTORY_ERP_URL,
    AWS_SQS_URL,
    AWS_SQS_URL_CUSTOM,
    AWS_SQS_REGION,
    AWS_SQS_ACCESS_KEY_ID,
    AWS_SQS_SECRET_ACCESS_KEY,
    AWS_API_VERSION,
    AWS_SNS_ARN,
    REDIS_URL,
    ACCEPT_PUBLIC_IP,
    LINK_UPLOAD_S3,
    AWS_S3_BUCKET_NAME,
    AWS_S3_ACCESS_KEY_ID,
    AWS_S3_SECRET_ACCESS_KEY,
    AWS_S3_REGION,
    AWS_S3_UPLOAD_FOLDER,
    QLONE_URL,
    QLONE_SAMPLE_URL,
    QLONE_CREATE_SAMPLE_URL,
    QLONE_VISUAL_INSPECTION_URL,
    AZURE_EVENT_HUB_CONNECTION_STRING,
    AZURE_EVENT_HUB_NAME,
    DBS: [
      {
        name: 'default',
        type: 'postgres',
        host: DB_PRIMARY_HOST,
        port: Number(DB_PRIMARY_PORT),
        username: DB_PRIMARY_USERNAME,
        password: DB_PRIMARY_PASSWORD,
        database: DB_PRIMARY_DATABASE,
        synchronize: stringToBoolean(DB_PRIMARY_SYNCHRONIZE),
        ssl: stringToBoolean(DB_PRIMARY_SSL)
          ? { rejectUnauthorized: stringToBoolean(DB_PRIMARY_SSL_REJECT_UNAUTHORIZED) }
          : undefined,
        entities: [join(__dirname, '../entities/primary/**/**{.ts,.js}')],
        subscribers: [join(__dirname, '../x-modules/subscribers/primary/**/**{.ts,.js}')],
        logging: [
          'log',
          'error',
          'info',
          // "query"
        ],
      },
      {
        name: 'scada',
        type: 'postgres',
        host: DB_SCADA_HOST,
        port: Number(DB_SCADA_PORT),
        username: DB_SCADA_USERNAME,
        password: DB_SCADA_PASSWORD,
        database: DB_SCADA_DATABASE,
        synchronize: stringToBoolean(DB_SCADA_SYNCHRONIZE),
        ssl: stringToBoolean(DB_SCADA_SSL)
          ? { rejectUnauthorized: stringToBoolean(DB_SCADA_SSL_REJECT_UNAUTHORIZED) }
          : undefined,
        entities: [join(__dirname, '../entities/scada/**/**{.ts,.js}')],
        subscribers: [join(__dirname, '../x-modules/subscribers/scada/**/**{.ts,.js}')],
        logging: [
          'log',
          'error',
          'info',
          // "query"
        ],
      },
    ],
  } as IEnvConfig;
}
