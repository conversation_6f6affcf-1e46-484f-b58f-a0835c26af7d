import { Inject, Param, Query, UseGuards } from '@nestjs/common';
import { ApiBody, ApiTags } from '@nestjs/swagger';
import { DefController, DefGet, DefPost } from '~/@core/decorator';
import { ApiTokenGuard } from '~/@core/decorator/api-token.decorator';
import { GetProductionOrderMaterialTransactionsRequest } from '~/dto/erp/production-order-material.dto';
import {
  ERPProductionOrderIntegrateService,
  ProdOrderMaterialTransactionService,
} from '~/x-modules/integration/services';

@ApiTags('ERPProductionOrder')
@UseGuards(ApiTokenGuard)
@DefController('erp/production-order')
export class ERPProductionOrderController {
  constructor(
    @Inject(ERPProductionOrderIntegrateService)
    private readonly integrateService: ERPProductionOrderIntegrateService,
    @Inject(ProdOrderMaterialTransactionService)
    private readonly prodOrderMaterialTransactionService: ProdOrderMaterialTransactionService,
  ) {}

  @DefGet('list', { summary: 'Danh sách PO' })
  @ApiBody({
    description: 'Danh sách PO',
  })
  async list(@Query() param: any) {
    return this.integrateService.list(param);
  }

  @DefGet('get-material-transaction', {
    summary: 'API của MES để tích hợp dữ liệu Material Transactions về hệ thống ERP',
  })
  async getProductionOrderMaterialTransactions(
    @Query() param: GetProductionOrderMaterialTransactionsRequest,
  ) {
    const response =
      await this.prodOrderMaterialTransactionService.getProductionOrderMaterialTransactions(param);

    return response;
  }
}
