// import moment from 'moment';
// import 'moment-timezone';
import dayjs, { Dayjs } from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';
import { configEnv } from '~/@config/env';

// const moment = require('moment-timezone');
import moment from 'moment-timezone';
dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(isSameOrAfter);
dayjs.extend(isSameOrBefore);
const TZ = configEnv().TZ || 'Asia/Ho_Chi_Minh';
/**
 * Lấy danh sách ngày trong tháng
 * @param year - Năm
 * @param month - Tháng (0-based, 0 = Tháng 1, 11 = Tháng 12)
 * @returns Mảng chứa các ngày trong tháng
 */
function getDaysInMonth(year: number, month: number): number[] {
  // month is 0-based (0 = January, 11 = December)
  const daysInMonth = new Date(year, month + 1, 0).getDate(); // Lấy ngày cuối cùng của tháng
  return Array.from({ length: daysInMonth }, (_, i) => i + 1); // Tạo danh sách ngày
}

/**
 * Lấy danh sách ngày trong tháng từ ngày hiện tại
 * @param date - Ngày hiện tại
 * @returns Mảng chứa các ngày trong tháng
 */
function getDaysInMonthFromDate(date: Date): number[] {
  return getDaysInMonth(date.getFullYear(), date.getMonth());
}

/**
 * Trừ hai đối tượng ngày
 * @param date1 - Đối tượng ngày thứ nhất
 * @param date2 - Đối tượng ngày thứ hai
 * @returns Chuỗi thời gian kết quả
 */
function subtractDateObjects(date1, date2) {
  if (!(date1 instanceof Date) || !(date2 instanceof Date)) {
    return 'Invalid input. Both inputs must be Date objects.';
  }

  const differenceMs = Math.abs(date1.getTime() - date2.getTime());
  const differenceSeconds = Math.floor(differenceMs / 1000);
  const hours = Math.floor(differenceSeconds / 3600);
  const minutes = Math.floor((differenceSeconds % 3600) / 60);
  const seconds = differenceSeconds % 60;

  const formattedHours = String(hours).padStart(2, '0');
  const formattedMinutes = String(minutes).padStart(2, '0');
  const formattedSeconds = String(seconds).padStart(2, '0');

  return `${formattedHours}:${formattedMinutes}:${formattedSeconds}`;
}

/**
 * Trừ hai chuỗi thời gian
 * @param time1 - Chuỗi thời gian thứ nhất HH:mm:ss
 * @param time2 - Chuỗi thời gian thứ hai HH:mm:ss
 * @returns Chuỗi thời gian kết quả HH:mm:ss
 */
function subtractTimeStrings(time1, time2) {
  const parseTime = (time) => {
    const parts = time.split(':').map(Number);
    return parts[0] * 3600 + parts[1] * 60 + parts[2];
  };

  const formatTime = (seconds) => {
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${String(hours).padStart(2, '0')}:${String(minutes).padStart(2, '0')}:${String(secs).padStart(2, '0')}`;
  };

  let seconds1 = parseTime(time1);
  let seconds2 = parseTime(time2);

  // Nếu time2 nhỏ hơn time1 → coi time2 là hôm sau
  if (seconds2 < seconds1) {
    seconds2 += 24 * 3600; // cộng thêm 1 ngày
  }

  const differenceSeconds = seconds2 - seconds1;
  return formatTime(differenceSeconds);
}

/**
 * Trừ hai đối tượng ngày và trả về chuỗi thời gian dạng "HH:mm:ss"
 * @param date1 - Đối tượng ngày thứ nhất
 * @param date2 - Đối tượng ngày thứ hai
 * @returns Chuỗi thời gian dạng "HH:mm:ss"
 */
function subtractDatesAndGetHHmmss(date1: Date, date2: Date): string {
  const diffInMs = Math.abs(date1.getTime() - date2.getTime());
  const duration = moment.duration(diffInMs);

  const hours = duration.hours().toString().padStart(2, '0');
  const minutes = duration.minutes().toString().padStart(2, '0');
  const seconds = duration.seconds().toString().padStart(2, '0');

  return `${hours}:${minutes}:${seconds}`;
}

/**
 * Trừ hai đối tượng ngày và trả về số giờ
 * @param date1 - Đối tượng ngày thứ nhất
 * @param date2 - Đối tượng ngày thứ hai
 * @returns Số giờ
 */
function subtractDatesAndGetHours(date1: Date, date2: Date): number {
  const diffInMs = Math.abs(date1.getTime() - date2.getTime());
  return diffInMs / (1000 * 60 * 60);
}

/**
 * Chuyển đổi chuỗi thời gian dạng "HH:mm:ss" thành số phút
 * @param timeString - Chuỗi thời gian dạng "HH:mm:ss"
 * @returns Số phút
 */
function convertHHMMSSToMM(timeString) {
  if (typeof timeString !== 'string') {
    return 0; // Kiểm tra đầu vào
  }

  const timeParts = timeString.split(':');

  if (timeParts.length !== 3) {
    return 0; //Kiểm tra định dạng
  }

  const hours = parseInt(timeParts[0], 10);
  const minutes = parseInt(timeParts[1], 10);
  const seconds = parseInt(timeParts[2], 10);

  if (isNaN(hours) || isNaN(minutes) || isNaN(seconds)) {
    return 0; // kiểm tra thành phần thời gian là số
  }

  return hours * 60 + minutes + seconds / 60;
}

/**
 * Thêm 24 giờ vào ngày hiện tại
 * @param date - Ngày hiện tại
 * @returns Ngày sau khi thêm 24 giờ
 */
function add24Hours(date: Date): Date {
  const millisecondsIn24Hours: number = 24 * 60 * 60 * 1000;
  return new Date(date.getTime() + millisecondsIn24Hours);
}

/**
 * Lấy thời gian dạng "HH:mm:ss" từ chuỗi ngày tháng
 * @param dateString - Chuỗi ngày tháng
 * @param timezone - Múi giờ (ví dụ: 'Asia/Ho_Chi_Minh')
 * @returns Chuỗi thời gian dạng "HH:mm:ss" hoặc null nếu chuỗi ngày tháng không hợp lệ
 */
function getHHmmss(dateString: string, timezone: string = 'Asia/Ho_Chi_Minh'): string | null {
  try {
    const date = new Date(dateString);

    if (isNaN(date.getTime())) {
      return null; // Invalid date string
    }

    const options: Intl.DateTimeFormatOptions = {
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      timeZone: timezone,
    };

    const formattedTime = date.toLocaleString('en-US', options);
    return formattedTime;
  } catch (error) {
    console.error('Error processing date string:', error);
    return null; // Error occurred, return null.
  }
}

/**
 * Lấy thời gian dạng "HH:mm:ss" từ đối tượng ngày
 * @param date - Đối tượng ngày
 * @returns Chuỗi thời gian dạng "HH:mm:ss"
 */
function getHHmmssFromDate(date: Date): string {
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');
  const seconds = date.getSeconds().toString().padStart(2, '0');
  return `${hours}:${minutes}:${seconds}`;
}

function createDateWithTimeZone(date, timeZone) {
  const options = { timeZone: timeZone };
  const dateString = date.toLocaleString('en-US', options);
  return new Date(dateString);
}

/**
 * Lấy ngày hiện tại và thời gian tùy chỉnh
 * @param timeString - Chuỗi thời gian dạng "HH:mm:ss"
 * @param timeZoneIn - Múi giờ đầu vào
 * @param timeZoneOut - Múi giờ đầu ra
 * @returns Ngày và thời gian đã được tùy chỉnh hoặc null nếu chuỗi thời gian không hợp lệ
 */
function getTodayWithCustomTime(
  timeString: string,
  timeZoneIn: string = 'Asia/Ho_Chi_Minh',
  timeZoneOut: string = 'UTC',
): Date | null {
  try {
    const [hours, minutes] = timeString.split(':').map(Number);

    if (isNaN(hours) || isNaN(minutes) || hours < 0 || hours > 23 || minutes < 0 || minutes > 59) {
      return null; // Invalid time string
    }

    // Get today's date in the input timezone
    const today = moment().tz(timeZoneIn);

    // Set the time from timeString
    const dateWithTime = today.hours(hours).minutes(minutes).seconds(0).milliseconds(0);

    // Convert to output timezone
    const convertedDate = dateWithTime.tz(timeZoneOut).toDate();

    if (isNaN(convertedDate.getTime())) {
      return null; // Handle unexpected date creation error
    }

    return convertedDate;
  } catch (error) {
    console.error('Error creating custom date:', error);
    return null;
  }
}

/**
 * Hàm tạo startTime và endTime chuẩn theo shift, xử lý ca qua đêm
 */
function getTodayWithCustomTimePair(
  startTimeStr: string,
  endTimeStr: string,
  timeZoneIn: string = 'Asia/Ho_Chi_Minh',
  timeZoneOut: string = 'Asia/Ho_Chi_Minh',
): { startTime: Date; endTime: Date } | null {
  try {
    const [startHour, startMinute, startSecond = 0] = startTimeStr.split(':').map(Number);
    const [endHour, endMinute, endSecond = 0] = endTimeStr.split(':').map(Number);

    if (
      [startHour, startMinute, endHour, endMinute].some(
        (v) => isNaN(v) || v < 0 || v > 59 || (v > 23 && (v === startHour || v === endHour)),
      )
    ) {
      return null;
    }

    const now = moment().tz(timeZoneIn);
    const today = now.clone().startOf('day');

    const startMoment = today.clone().hour(startHour).minute(startMinute).second(startSecond);
    const endMomentRaw = today.clone().hour(endHour).minute(endMinute).second(endSecond);

    let finalStart = startMoment;
    let finalEnd: moment.Moment;

    // Case 1: cùng ngày
    if (startMoment.isBefore(endMomentRaw)) {
      finalEnd = endMomentRaw;
    } else {
      // Case 2: ca qua đêm
      finalEnd = endMomentRaw.clone().add(1, 'day');

      // Nếu hiện tại chưa đến giờ start (tức ca là hôm trước)
      if (now.isBefore(startMoment)) {
        finalStart.subtract(1, 'day');
        finalEnd.subtract(1, 'day');
      }
    }

    return {
      startTime: finalStart.tz(timeZoneOut).toDate(),
      endTime: finalEnd.tz(timeZoneOut).toDate(),
    };
  } catch (error) {
    console.error('Error in getTodayWithCustomTimePair:', error);
    return null;
  }
}

/**
 * Lấy thời gian dạng "HH:mm:ss" từ đối tượng ngày
 * @param date - Đối tượng ngày
 * @returns Chuỗi thời gian dạng "HH:mm:ss"
 */
function getTimeInSeconds(date: Date): number {
  return Math.floor(date.getTime() / 1000);
}

function newDate(timeZone: string = 'Asia/Ho_Chi_Minh'): Date {
  return moment().tz(timeZone).toDate();
}

function formatDateForQueryWithTimeZoneMoment(date: Date, timeZone: string): string {
  const formattedDate = moment(date).tz(timeZone).format('YYYY-MM-DD HH:mm:ss.SSS Z');
  return formattedDate;
}

function formatDateByJobManual(date: string): string {
  let dateValue = date
  const isISODate = /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}:\d{2}.\d{3}Z$/.test(date)
  if(isISODate) dateValue = moment(date).format('DD-MM-YYYY HH:mm:ss');

  const [getDate, getTime] = dateValue.split(' ').map(String);
  const [day, month, year] = getDate.split('-').map(Number);
  const getDateResult = dateHelper.getTodayWithCustomTime(
    getTime ? getTime : '00:00:00',
    'Asia/Ho_Chi_Minh',
    'Asia/Ho_Chi_Minh',
  );
  let getTimeResult = moment(getDateResult)
    .utcOffset(7 * 60)
    .set({
      year,
      month: month - 1,
      date: day,
    });
  return getTimeResult.format('YYYY-MM-DD HH:mm:ss.SSS Z');
}

function toStartOfDay(date: Date): string {
  const year = date.getFullYear();
  const month = `${date.getMonth() + 1}`.padStart(2, '0'); // month: 0-11
  const day = `${date.getDate()}`.padStart(2, '0');

  return `${year}-${month}-${day} 00:00:00`;
}
function toEndOfDay(date: Date): string {
  const year = date.getFullYear();
  const month = `${date.getMonth() + 1}`.padStart(2, '0'); // month: 0-11
  const day = `${date.getDate()}`.padStart(2, '0');

  return `${year}-${month}-${day} 23:59:59`;
}
function formatDateForOeeChart(date: Date) {
  const d = new Date(date);
  const day = String(d.getDate()).padStart(2, '0');
  const month = String(d.getMonth() + 1).padStart(2, '0');
  const year = String(d.getFullYear()).slice(-2);
  return `${day}${month}${year}`;
}

export const dateHelper = {
  getDaysInMonth,
  getDaysInMonthFromDate,
  subtractDateObjects,
  subtractTimeStrings,
  convertHHMMSSToMM,
  add24Hours,
  getHHmmss,
  getTodayWithCustomTime,
  getTodayWithCustomTimePair,
  getHHmmssFromDate,
  subtractDatesAndGetHHmmss,
  subtractDatesAndGetHours,
  getTimeInSeconds,
  newDate,
  formatDateForQueryWithTimeZoneMoment,
  formatDateByJobManual,
  toStartOfDay,
  toEndOfDay,
  formatDateForOeeChart,
};
export { moment, dayjs, Dayjs, TZ };
