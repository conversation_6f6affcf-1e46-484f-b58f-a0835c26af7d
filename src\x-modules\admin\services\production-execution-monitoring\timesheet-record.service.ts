import { Injectable } from '@nestjs/common';
import dayjs from 'dayjs';
import isBetween from 'dayjs/plugin/isBetween';
import moment from 'moment';
import { Between, In, IsNull, LessThanOrEqual, MoreThanOrEqual, Not } from 'typeorm';
import { BindRepo } from '~/@core/decorator';
import { BusinessException } from '~/@systems/exceptions';
import { NSGeneralData } from '~/common/enums';
import { EOrganizationLevel } from '~/common/enums/organization-level.enum';
import {
  RequestCheckInDTO,
  RequestCheckOutDTO,
  RequestCreateTimeSheetDTO,
  RequestDeleteTimeSheetDTO,
  RequestDetailTimeSheetDTO,
  RequestGetListStation,
  RequestListDevice,
  RequestListHoliadyDTO,
  RequestListShiftTimeSheetDTO,
  RequestListUserTimeSheetDTO,
  RequestPaginationTimeSheetDTO,
  RequestProductionAreaSelectionTimeSheetDTO,
  RequestShiftSelectionSheetTimeDTO,
  RequestUpdateTimeSheetDTO,
  RequestUserSelectionTimeSheetDTO,
  ResponseGetListStation,
} from '~/dto/timesheet-record.dto';
import { list } from '~/functions/general-data';
import {
  DeviceRepo,
  HolidayRepo,
  OrganizationUnitRepo,
  ProductionAreaRepo,
  StationDetailRepo,
  StationRepo,
  TimeSheetRecordRepository,
} from '~/repositories/primary';
import { AccessRepo } from '~/repositories/primary/access.repo';
import { AssignShiftRepository } from '~/repositories/primary/assign-shift.repo';
import { HolidayDetailRepo } from '~/repositories/primary/holiday-detail.repo';
import { ShiftRepo } from '~/repositories/primary/shift.repo';
import { UserRepo } from '~/repositories/primary/user.repo';
import { S3PrivateService } from '~/x-modules/@global/services';

dayjs.extend(isBetween);

@Injectable()
export class TimeSheetRecordService {
  @BindRepo(TimeSheetRecordRepository)
  private TimeSheetRepo: TimeSheetRecordRepository;

  @BindRepo(UserRepo)
  private UserRepo: UserRepo;

  @BindRepo(ProductionAreaRepo)
  private ProductionAreaRepo: ProductionAreaRepo;

  @BindRepo(ShiftRepo)
  private ShiftRepo: ShiftRepo;

  @BindRepo(AssignShiftRepository)
  private AssignRepo: AssignShiftRepository;

  @BindRepo(OrganizationUnitRepo)
  private OrganUnit: OrganizationUnitRepo;

  @BindRepo(HolidayRepo)
  private HolidayRepo: HolidayRepo;

  @BindRepo(HolidayDetailRepo)
  private HolidayDetailRepo: HolidayDetailRepo;

  @BindRepo(AccessRepo)
  private accessRepo: AccessRepo;

  @BindRepo(DeviceRepo)
  private deviceRepo: DeviceRepo;

  @BindRepo(StationRepo)
  private stationRepo: StationRepo;

  @BindRepo(StationDetailRepo)
  private stationDetailRepo: StationDetailRepo;

  constructor(private readonly s3PrivateService: S3PrivateService) {}

  /**
   * Lấy danh sách bảng chấm công theo phân trang và bộ lọc.
   * @param params RequestPaginationTimeSheetDTO chứa thông tin về số trang, kích thước trang và các điều kiện lọc.
   * @returns Đối tượng chứa danh sách chấm công, tổng số bản ghi, số trang hiện tại và kích thước trang.
   */
  async pagination(params: RequestPaginationTimeSheetDTO) {
    const { pageSize = 10, currentPage = 1 } = params;
    const skip = (currentPage - 1) * pageSize;

    const whereCon: any = {};

    const DATETIME_FORMAT = 'YYYY-MM-DD HH:mm:ss';

    if (params.startDate && params.endDate) {
      whereCon.workDate = Between(
        moment(params.startDate, DATETIME_FORMAT).toDate(),
        moment(params.endDate, DATETIME_FORMAT).toDate(),
      );
    } else if (params.startDate) {
      whereCon.workDate = MoreThanOrEqual(moment(params.startDate, DATETIME_FORMAT).toDate());
    } else if (params.endDate) {
      whereCon.workDate = LessThanOrEqual(moment(params.endDate, DATETIME_FORMAT).toDate());
    }

    if (params?.userId) {
      whereCon.userId = params.userId;
    }

    if (params?.productionAreaId) {
      whereCon.productionAreaId = params.productionAreaId;
    }

    if (params?.shiftId) {
      whereCon.shiftId = params.shiftId;
    }

    const [data, total] = await Promise.all([
      this.TimeSheetRepo.find({
        where: whereCon,
        skip,
        take: pageSize,
        order: { creationDate: 'DESC' },
        relations: ['user', 'productionArea', 'shift'],
      }),
      this.TimeSheetRepo.count({ where: whereCon }),
    ]);

    const transformedData = data.map(({ user, productionArea, shift, ...timesheet }) => ({
      ...timesheet,
      userName: user?.fullName || null,
      userEmail: user?.email || null,
      productionArea: productionArea?.name || null,
      shiftName: shift?.description || null,
    }));

    return {
      pagination: transformedData,
      total,
      pageSize,
      currentPage,
    };
  }

  /**
   * Tạo một bản ghi chấm công mới cho người dùng trong khu vực sản xuất và ca làm việc cụ thể.
   * @param params RequestCreateTimeSheetDTO chứa thông tin về người dùng, khu vực sản xuất, ca làm việc, thời gian check-in/out, tọa độ và người tạo.
   * @returns Đối tượng phản hồi với trạng thái và thông tin bản ghi đã tạo.
   */
  async createTimeSheetRecord(params: RequestCreateTimeSheetDTO) {
    const { userId, productionAreaId, shiftId, checkInTime, checkOutTime, createdBy } = params;

    const [user, productionArea, shift] = await Promise.all([
      this.UserRepo.findOne({ where: { id: userId } }),
      this.ProductionAreaRepo.findOne({ where: { id: productionAreaId } }),
      this.ShiftRepo.findOne({ where: { id: shiftId } }),
    ]);

    if (!user || !productionArea || !shift) {
      throw new BusinessException('User, địa điểm, hoặc ca không tồn tại.');
    }

    let workDate = dayjs(checkInTime);

    if (shift.nightShift) {
      const checkin = dayjs(workDate);
      const midnight = checkin.clone().hour(0).minute(0).second(0);
      const noon = checkin.clone().hour(12).minute(0).second(0);

      // nếu giờ hiện tại 00:00 - 12:00 pm, thì trừ -1 để đúng workdate lúc check-in
      if (checkin.isBetween(midnight, noon)) {
        workDate = workDate.subtract(1, 'days');
      }
    }

    const existing = await this.TimeSheetRepo.findOne({
      where: {
        siteId: params.siteId,
        factoryId: params.factoryId,
        productionAreaId: params.productionAreaId,
        shiftId: params.shiftId,
        userId: params.userId,
        workDate: workDate.format('YYYY-MM-DD'),
      },
    });

    if (existing) {
      throw new BusinessException('Người dùng đã hoàn thành ca hôm nay');
    }

    const timesheet = this.TimeSheetRepo.create({
      user,
      productionArea,
      shift,
      siteId: params.siteId,
      factoryId: params.factoryId,
      type: checkOutTime ? 'OUT' : 'IN',
      checkInTime: checkInTime ? dayjs(checkInTime).toDate() : null,
      checkOutTime: checkOutTime ? dayjs(checkOutTime).toDate() : null,
      workDate: workDate.format('YYYY-MM-DD'),
      productionAreaLocationX: productionArea.locationX,
      productionAreaLocationY: productionArea.locationY,
      createdBy: createdBy,
      duration: params?.duration ? Number(params.duration) : 0,
      earlyTime: params?.earlyTime ? Number(params.earlyTime) : 0,
      lateTime: params?.lateTime ? Number(params.lateTime) : 0,
      salaryTime: params?.salaryTime ? Number(params.salaryTime) : 0,
      nightTime: params?.nightTime ? Number(params.nightTime) : 0,
      diffTime: params?.diffTime ? Number(params.diffTime) : 0,
      otTime: params?.otTime ? Number(params.otTime) : 0,
      otHolidayTime: params?.otHolidayTime ? Number(params.otHolidayTime) : 0,
      otNightTime: params?.otNightTime ? Number(params.otNightTime) : 0,
      otHolidayNightTime: params?.otHolidayNightTime ? Number(params.otHolidayNightTime) : 0,
    });

    return await this.TimeSheetRepo.save(timesheet);
  }

  /**
   * Lấy thông tin chi tiết của một bản ghi chấm công.
   * @param params RequestDetailTimeSheetDTO chứa ID của bản ghi cần lấy.
   * @returns Thông tin chi tiết về bản ghi chấm công bao gồm người dùng, khu vực sản xuất, ca làm việc.
   */
  async detailTimeSheetRecord(params: RequestDetailTimeSheetDTO) {
    const timesheet = await this.TimeSheetRepo.findOne({
      where: {
        id: params.id,
      },
      order: { creationDate: 'DESC' },
      relations: ['user', 'productionArea', 'shift'],
    });

    if (!timesheet) {
      throw new BusinessException('Không tìm thấy ca');
    }

    const { user, productionArea, shift, ...remain } = timesheet;

    const transformedData = {
      ...remain,
      userId: user?.id,
      userName: user?.fullName || null,
      userEmail: user?.email || null,
      productionAreaId: productionArea?.id || null,
      productionArea: productionArea || null,
      shiftId: shift?.id || null,
      shift,
    };

    return transformedData;
  }

  /**
   * Cập nhật thông tin bản ghi chấm công.
   * @param params RequestUpdateTimeSheetDTO chứa ID bản ghi và thông tin cần cập nhật.
   * @returns Đối tượng đã được cập nhật và lưu trữ trong cơ sở dữ liệu.
   */
  async updateTimeSheetRecord(params: RequestUpdateTimeSheetDTO) {
    const { id, userId, productionAreaId, shiftId, checkInTime, checkOutTime, updatedBy } = params;

    const timesheet = await this.TimeSheetRepo.findOne({
      where: { id },
    });

    if (!timesheet) {
      throw new BusinessException('Không tìm thấy ca');
    }

    const [user, productionArea, shift] = await Promise.all([
      this.UserRepo.findOne({ where: { id: userId } }),
      this.ProductionAreaRepo.findOne({ where: { id: productionAreaId } }),
      this.ShiftRepo.findOne({ where: { id: shiftId } }),
    ]);

    if (!user || !productionArea || !shift) {
      throw new BusinessException('User, địa điểm, hoặc ca không tồn tại.');
    }

    let workDate = dayjs(checkInTime);

    if (shift.nightShift) {
      const checkin = dayjs(workDate);
      const midnight = checkin.clone().hour(0).minute(0).second(0);
      const noon = checkin.clone().hour(12).minute(0).second(0);

      // nếu giờ hiện tại 00:00 - 12:00 pm, thì trừ -1 để đúng workdate lúc check-in
      if (checkin.isBetween(midnight, noon)) {
        workDate = workDate.subtract(1, 'days');
      }
    }

    const update: any = {
      user,
      productionArea,
      shift,
      siteId: params.siteId,
      factoryId: params.factoryId,
      type: checkOutTime ? 'OUT' : 'IN',
      checkInTime: checkInTime ? dayjs(checkInTime).toDate() : timesheet.checkInTime,
      checkOutTime: checkOutTime ? dayjs(checkOutTime).toDate() : null,
      workDate: workDate.format('YYYY-MM-DD'),
      productionAreaLocationX: productionArea.locationX,
      productionAreaLocationY: productionArea.locationY,
      lastUpdateBy: updatedBy,
      lastUpdate: dayjs().toDate(),
      duration: params?.duration ? Number(params.duration) : 0,
      earlyTime: params?.earlyTime ? Number(params.earlyTime) : 0,
      lateTime: params?.lateTime ? Number(params.lateTime) : 0,
      salaryTime: params?.salaryTime ? Number(params.salaryTime) : 0,
      nightTime: params?.nightTime ? Number(params.nightTime) : 0,
      diffTime: params?.diffTime ? Number(params.diffTime) : 0,
      otTime: params?.otTime ? Number(params.otTime) : 0,
      otHolidayTime: params?.otHolidayTime ? Number(params.otHolidayTime) : 0,
      otNightTime: params?.otNightTime ? Number(params.otNightTime) : 0,
      otHolidayNightTime: params?.otHolidayNightTime ? Number(params.otHolidayNightTime) : 0,
    };

    Object.assign(timesheet, update);

    return await this.TimeSheetRepo.save(timesheet);
  }

  /**
   * Xóa một bản ghi chấm công theo ID.
   * @param params RequestDeleteTimeSheetDTO chứa ID của bản ghi cần xóa.
   * @returns Kết quả xóa bản ghi khỏi cơ sở dữ liệu.
   */
  async deleteTimeSheetRecord(params: RequestDeleteTimeSheetDTO) {
    const timesheet = await this.TimeSheetRepo.findOne({
      where: { id: params.id },
    });

    if (!timesheet) {
      throw new BusinessException('Không tìm thấy ca');
    }

    return await this.TimeSheetRepo.delete(timesheet.id);
  }

  /**
   * Lấy danh sách người dùng để lựa chọn khi tạo/chỉnh sửa bảng chấm công.
   * @param params UserSelectionTimeSheetRecordDTO chứa các điều kiện lọc (nếu có).
   * @returns Danh sách người dùng từ cơ sở dữ liệu.
   */
  async userSelection(params: RequestUserSelectionTimeSheetDTO) {
    const listUser = await this.UserRepo.find();

    return listUser.map((u) => {
      return {
        employeeCode: u.employeeCode,
        productionAreaId: u.productionAreaId,
        fullName: u.fullName,
        email: u.email,
        phone: u.phone,
      };
    });
  }

  async getWorkingTimeSheet(userId: string) {
    const today = dayjs().format('YYYY-MM-DD');

    let timesheet = await this.TimeSheetRepo.findOne({
      where: {
        userId: userId,
        workDate: today,
      },
      relations: ['shift'],
    });

    if (!timesheet) {
      // Quên checkout trong ngày hoặc ca đêm
      const yesterday = dayjs().subtract(1, 'days').format('YYYY-MM-DD');
      const timeSheetYesterday = await this.TimeSheetRepo.findOne({
        where: {
          userId: userId,
          workDate: yesterday,
          checkOutTime: null,
        },
        order: {
          createdDate: 'DESC',
        },
        relations: ['shift'],
      });

      timesheet = timeSheetYesterday;
    }

    return timesheet;
  }

  /**
   * Lấy danh sách khu vực sản xuất để lựa chọn khi tạo/chỉnh sửa bảng chấm công.
   * @param params ProductionAreaSelectionTimeSheetDTO chứa các điều kiện lọc (nếu có).
   * @returns Danh sách khu vực sản xuất từ cơ sở dữ liệu.
   */
  async productionAreaSelection(params: RequestProductionAreaSelectionTimeSheetDTO) {
    return await this.ProductionAreaRepo.find();
  }

  /**
   * Lấy danh sách ca làm việc để lựa chọn khi tạo/chỉnh sửa bảng chấm công.
   * @param params ShiftSelectionSheetRecordDTO chứa các điều kiện lọc (nếu có).
   * @returns Danh sách ca làm việc từ cơ sở dữ liệu.
   */
  async shiftSelection(params: RequestShiftSelectionSheetTimeDTO) {
    return await this.ShiftRepo.find();
  }

  /**
   * Lấy danh sách thông tin timesheet mobile
   * @param params
   * @returns
   */
  async userTimeSheet(userNo: number) {
    const user = await this.UserRepo.findOne({ where: { userNo: userNo } });

    if (!user) {
      throw new BusinessException('Người dùng không tồn tại');
    }

    const timesheet = await this.getWorkingTimeSheet(user.id);

    return {
      user,
      timesheet,
    };
  }

  /**
   * Check in mobile
   * @param params
   * @returns
   */
  async checkIn(params: RequestCheckInDTO) {
    const [user, productionArea, shift] = await Promise.all([
      this.UserRepo.findOne({ where: { id: params.userId } }),
      this.ProductionAreaRepo.findOne({ where: { id: params.productionAreaId } }),
      this.ShiftRepo.findOne({ where: { id: params.shiftId } }),
    ]);

    if (!user || !productionArea || !shift) {
      throw new BusinessException('User, địa điểm, hoặc ca không tồn tại.');
    }

    if (user.productionAreaId == null) {
      throw new BusinessException('User không thuộc production area nào');
    }

    const existing = await this.getWorkingTimeSheet(user.id);

    if (existing?.checkInTime) {
      throw new BusinessException('Bạn đã vào ca rồi.');
    }

    const now = dayjs().toDate();

    const factory = await this.OrganUnit.findOne({
      where: {
        id: productionArea.organizationUnitId,
      },
    });

    const site = await this.OrganUnit.findOne({
      where: {
        id: factory.parentId,
      },
    });

    let workDate = dayjs();

    if (shift.nightShift) {
      // Ca đêm
      workDate = workDate.subtract(12, 'hours');
    }

    const timeSheet = await this.TimeSheetRepo.create({
      siteId: site.id,
      factoryId: factory.id,
      user: { id: user.id },
      productionArea: { id: productionArea.id },
      shift: { id: shift.id },
      type: 'IN',
      checkInTime: now,
      workDate: workDate.format('YYYY-MM-DD'),
      checkInLocationX: params.userLocationX,
      checkInLocationY: params.userLocationY,
      productionAreaLocationX: productionArea.locationX,
      productionAreaLocationY: productionArea.locationY,
      toleranceIn: params.distance,
    });

    return await this.TimeSheetRepo.save(timeSheet);
  }

  /**
   * Check-out mobile
   * @param params
   * @returns
   */
  async checkOut(params: RequestCheckOutDTO) {
    const [user, productionArea, shift] = await Promise.all([
      this.UserRepo.findOne({ where: { id: params.userId } }),
      this.ProductionAreaRepo.findOne({ where: { id: params.productionAreaId } }),
      this.ShiftRepo.findOne({ where: { id: params.shiftId } }),
    ]);

    if (!user || !productionArea || !shift) {
      throw new BusinessException('User or Production or Shift is not existing');
    }

    if (user.productionAreaId == null) {
      throw new BusinessException('User không thuộc production area nào');
    }

    const timesheet = await this.getWorkingTimeSheet(user.id);

    const listHoliday = await this.listHoliday({});

    const now = dayjs().toDate();

    if (!timesheet) {
      // Check out mà không check in

      // Ca A - Ca ngày
      let workDate = dayjs();

      // Ca B - Ca đêm
      if (shift.code == 'B') {
        workDate = dayjs().subtract(6, 'hours');
      }

      // Ca C - Ca đêm
      if (shift.nightShift) {
        workDate = dayjs().subtract(12, 'hours');
      }

      const checkoutNoCheckin = await this.TimeSheetRepo.create({
        user: user,
        productionArea: productionArea,
        shift: shift,
        type: 'OUT',
        workDate: workDate.format('YYYY-MM-DD'),
        checkInTime: null,
        checkOutTime: now,
        checkOutLocationX: params.userLocationX,
        checkOutLocationY: params.userLocationY,
        productionAreaLocationX: productionArea.locationX,
        productionAreaLocationY: productionArea.locationY,
        toleranceOut: params.distance,
        lastUpdateBy: params.userId,
        lastUpdate: now,
        // ...information,
      });

      return this.TimeSheetRepo.save({
        ...checkoutNoCheckin,
        // ...information,
      });
    } else {
      // Check out sau check in

      if (timesheet?.checkOutTime) {
        throw new BusinessException('Bạn đã hoàn thành ca rồi.');
      }

      let information = await this.calculateTimeSheetInfo({
        checkIn: dayjs(timesheet.checkInTime).toDate(),
        checkOut: now,
        startShift: shift.startTime,
        endShift: shift.endTime,
        nightShift: shift.nightShift,
        listHoliday,
      });

      return await this.TimeSheetRepo.save(
        Object.assign(timesheet, {
          type: 'OUT',
          checkOutTime: now,
          checkOutLocationX: params.userLocationX,
          checkOutLocationY: params.userLocationY,
          toleranceOut: params.distance,
          lastUpdateBy: params.userId,
          lastUpdate: now,
          ...information,
        }),
      );
    }
  }

  /**
   * Lấy thông tin giờ làm việc, còn một function tương tự ở front-end nhưng có nhiều validate hơn
   * @param params
   * @returns
   */
  async calculateTimeSheetInfo({
    checkIn,
    checkOut,
    startShift,
    endShift,
    nightShift,
    listHoliday,
  }: {
    checkIn: Date;
    checkOut: Date;
    startShift: string;
    endShift: string;
    nightShift: boolean;
    listHoliday: any[];
  }) {
    if (!checkIn || !checkOut || !startShift || !endShift || nightShift === undefined) {
      return {
        duration: 0,
        earlyTime: 0,
        lateTime: 0,
        salaryTime: 0,
        nightTime: 0,
        diffTime: 0,
        otTime: 0,
        otHolidayTime: 0,
        otNightTime: 0,
        otHolidayNightTime: 0,
      };
    }

    let today = dayjs();
    const DATE_FORMAT = 'YYYY-MM-DD';
    const TIME_FORMAT = 'HH:mm:ss';
    const DATETIME_FORMAT = `${DATE_FORMAT} ${TIME_FORMAT}`;
    let workDate = today;

    let duration = 0;
    let earlyTime = 0;
    let lateTime = 0;
    let salaryTime = 0;
    let nightTime = 0;
    let diffTime = 0;
    let otTime = 0;
    let otHolidayTime = 0;
    let otNightTime = 0;
    let otHolidayNightTime = 0;

    let startTime = dayjs(`${workDate.format(DATE_FORMAT)} ${startShift}`, DATETIME_FORMAT);

    let endTime = dayjs(`${workDate.format(DATE_FORMAT)} ${endShift}`, DATETIME_FORMAT);

    if (startTime.second() >= 59) {
      startTime.add(1, 'minutes').second(0);
    }

    if (endTime.second() >= 59) {
      endTime.add(1, 'minutes').second(0);
    }

    let checkInTime = dayjs(checkIn);

    let checkOutTime = dayjs(checkOut);

    const strStartShift = startTime.format('HH:mm:ss');

    const strEndShift = endTime.format('HH:mm:ss');

    if (!nightShift) {
      // Ca ngày

      workDate = dayjs(checkInTime);

      let startShiftCheckIn = dayjs(`${checkInTime.format('YYYY-MM-DD')} ${strStartShift}`);

      let endShiftCheckIn = dayjs(`${checkInTime.format('YYYY-MM-DD')} ${strEndShift}`);

      if (checkInTime && checkOutTime) {
        duration = Math.abs(checkOutTime.diff(startShiftCheckIn, 'minutes'));
      }

      if (endShiftCheckIn.isAfter(checkOutTime)) {
        earlyTime = Math.ceil(endShiftCheckIn.diff(checkOutTime, 'minutes', true));
      }

      if (checkInTime.isAfter(startShiftCheckIn)) {
        lateTime = Math.floor(checkInTime.diff(startShiftCheckIn, 'minutes', true));
      }

      if (duration > 0) {
        salaryTime = Math.abs(480 - earlyTime - lateTime);

        if (salaryTime > 480) {
          salaryTime = 480;
        }
      }

      if (nightShift) {
        nightTime = salaryTime;
      }

      let overtime = Number(duration) - 480;

      if (overtime > 0) {
        if (overtime >= 240) {
          diffTime = 4;
        } else {
          diffTime = Math.floor(overtime / 15) * 0.25;
        }
      }
    } else {
      // Ca đêm

      workDate = dayjs(checkInTime).subtract(8, 'hours');

      let startShiftCheckIn = dayjs(
        `${workDate.format(DATE_FORMAT)} ${strStartShift}`,
        DATETIME_FORMAT,
      );

      let endShiftCheckIn = dayjs(
        `${workDate.add(1, 'days').format(DATE_FORMAT)} ${strEndShift}`,
        DATETIME_FORMAT,
      );

      if (checkInTime && checkOutTime) {
        duration = Math.abs(checkOutTime.diff(startShiftCheckIn, 'minutes'));
      }

      if (endShiftCheckIn.isAfter(checkOutTime)) {
        earlyTime = Math.ceil(endShiftCheckIn.diff(checkOutTime, 'minutes', true));
      }

      if (checkInTime.isAfter(startShiftCheckIn)) {
        lateTime = Math.floor(checkInTime.diff(startShiftCheckIn, 'minutes', true));
      }

      workDate = dayjs(checkInTime).subtract(8, 'hours');
    }

    if (duration > 0) {
      salaryTime = 480 - earlyTime - lateTime;

      if (salaryTime > 480) {
        salaryTime = 480;
      }
    }

    if (nightShift) {
      nightTime = salaryTime;
    }

    let overtime = Number(duration) - 480;

    if (overtime > 0) {
      if (overtime >= 240) {
        diffTime = 4;
      } else {
        diffTime = Math.floor(overtime / 15) * 0.25;
      }
    }

    let isHoliday = listHoliday.some((h) => {
      return workDate.isSame(dayjs(h.dateDetail), 'day');
    });

    if (!isHoliday && !nightShift) {
      otTime = diffTime;
    }

    if (isHoliday && !nightShift) {
      otHolidayTime = diffTime;
    }

    if (!isHoliday && nightShift) {
      otNightTime = diffTime;
    }

    if (isHoliday && nightShift) {
      otHolidayNightTime = diffTime;
    }

    const processResult = (number) => {
      if (typeof number != 'number') {
        return 0;
      }
      return Math.abs(number / 60).toFixed(3);
    };

    return {
      duration: processResult(duration),
      earlyTime: processResult(earlyTime),
      lateTime: processResult(lateTime),
      salaryTime: processResult(salaryTime),
      nightTime: processResult(nightTime),
      diffTime: diffTime,
      otTime: otTime,
      otHolidayTime: otHolidayTime,
      otNightTime: otNightTime,
      otHolidayNightTime: otHolidayNightTime,
    };
  }

  /**
   * Lấy danh sách site
   * @param params
   * @returns
   */

  async listSite(params: any) {
    const userId = params?.userId;
    if (userId) {
      const lstFactoryId = await this.accessRepo.find({
        where: { user: { id: userId }, status: true },
        relations: ['organizationUnit', 'user'],
      });
      if (lstFactoryId.length > 0) {
        const factoryIds = lstFactoryId.map((item) => item.organizationUnit.id);
        const lstFactory = await this.OrganUnit.find({
          where: { id: In(factoryIds) },
        });
        return await this.OrganUnit.find({
          where: { id: In(lstFactory.map((i) => i.parentId)), isActive: true },
        });
      }
    }
  }

  /**
   * Lấy danh sách factory
   * @param params
   * @returns
   */

  async listFactory(params: any) {
    if (params.siteId == 'All') {
      return await this.OrganUnit.find({
        where: {
          isActive: true,
        },
      });
    }

    return await this.OrganUnit.find({
      where: {
        parentId: params.siteId,
        levelGeneralDataParentCode: NSGeneralData.EGeneralDataCode.ORG_LEVEL,
        levelGeneralDataDetailCode: EOrganizationLevel.Factory,
        isActive: true,
      },
    });
  }

  /**
   * Lấy danh sách production area
   * @param params
   * @returns
   */
  async listProductionArea(params: RequestProductionAreaSelectionTimeSheetDTO) {
    let defaultAccess = null;

    if (params?.userId) {
      defaultAccess = await this.getDefaultAccessByUserNoDefault(params.userId);
    }

    let records = [];

    if (params.factoryId === 'All') {
      records = await this.ProductionAreaRepo.find({
        order: {
          createdDate: 'DESC',
        },
      });
    } else {
      records = await this.ProductionAreaRepo.find({
        where: {
          organizationUnitId: params.factoryId,
        },
        order: {
          createdDate: 'DESC',
        },
      });
    }

    return records.map((item) => {
      const isDefault = defaultAccess && item.organizationUnitId == defaultAccess?.factoryId;

      return {
        ...item,
        isDefaultAccess: Boolean(isDefault),
      };
    });
  }

  /**
   * Lấy danh sách ca
   * @param params
   * @returns
   */
  async listShift(params: RequestListShiftTimeSheetDTO) {
    if (params.productionAreaId == 'All') {
      return this.ShiftRepo.find({
        where: {
          status: true,
        },
      });
    }

    const listPro = await this.ProductionAreaRepo.find({
      where: { id: params.productionAreaId },
    });

    const productionAreaIds = listPro.map((i) => i.organizationUnitId);

    if (productionAreaIds.length === 0) {
      return [];
    }

    const listOrgan = await this.AssignRepo.find({
      where: { organizationId: In(productionAreaIds) },
    });

    if (listOrgan.length === 0) {
      return [];
    }

    return this.ShiftRepo.find({
      where: {
        status: true,
      },
    });
  }

  /**
   * Lấy danh sách user cho devive
   * @param params
   * @returns
   */
  async listUserForDevice(params: RequestListUserTimeSheetDTO) {
    let listUser = [];

    let where: any = {
      isActive: true,
      imageUrl: Not(IsNull()),
      userNo: Not(IsNull()),
    };

    if (params.productionAreaId !== 'All') {
      where.productionAreaId = params.productionAreaId;
    }

    listUser = await this.UserRepo.find({
      where,
      select: ['id', 'employeeCode', 'fullName', 'imageUrl'],
    });

    for (let user of listUser) {
      user.imageUrl = await this.s3PrivateService.getS3Link(user.imageUrl);
    }

    return listUser;
  }

  /**
   * Lấy danh sách user
   * @param params
   * @returns
   */
  async listUser(params: RequestListUserTimeSheetDTO) {
    let listUser = [];

    let where: any = {
      isActive: true,
      imageUrl: Not(IsNull()),
      userNo: Not(IsNull()),
    };

    if (params.productionAreaId !== 'All') {
      where.productionAreaId = params.productionAreaId;
    }

    listUser = await this.UserRepo.find({
      where,
      select: ['id', 'employeeCode', 'fullName', 'imageUrl'],
    });

    for (let user of listUser) {
      user.imageUrl = await this.s3PrivateService.getS3Link(user.imageUrl);
    }

    return listUser;
  }

  /**
   * Lấy danh sách holiday
   * @param params
   * @returns
   */
  async listHoliday(params: RequestListHoliadyDTO) {
    const holidays = await this.HolidayRepo.find({
      where: { status: true },
    });

    if (holidays.length === 0) return [];

    const holidayIds = holidays.map((i) => i.id);

    return await this.HolidayDetailRepo.find({
      where: {
        holidayId: In(holidayIds),
        status: true,
      },
    });
  }

  /**
   * Acccess site and factory default cho user, nếu không có trả null không mặc định
   * @param params
   * @returns
   */

  async getDefaultAccessByUserNoDefault(userId: string) {
    const defaultAccess = await this.accessRepo.findOne({
      where: { user: { id: userId }, status: true, default: true },
      relations: ['organizationUnit'],
    });

    if (!defaultAccess) {
      return null;
    }

    const factory = await this.OrganUnit.findOne({
      where: { id: defaultAccess.organizationUnit.id },
    });

    const site = await this.OrganUnit.findOne({ where: { id: factory.parentId } });

    return {
      factoryName: factory.name,
      factoryCode: factory.code,
      factoryId: factory.id,
      siteId: site.id,
      siteCode: site.code,
      siteName: site.name,
    };
  }

  /**
   * list device màn hình thiết lập camera và ipc
   * @param params
   * @returns
   */
  async listDevice(params: RequestListDevice) {
    const listDevice = await this.deviceRepo.find({
      where: {
        deviceTypeDetailCode: 3,
        isActive: true,
      },
    });

    return listDevice;
  }

  /**
   * get device thuoc ve station
   * @param params
   * @returns
   */
  async getListStation(params: RequestGetListStation, clientIP?: string) {
    const listAvaiableStation: ResponseGetListStation = [];

    const listStation = await this.stationRepo.find({
      where: {
        isActive: true,
        typeCode: 'CHAMCONG',
      },
    });

    for (let station of listStation) {
      let availableStation: ResponseGetListStation[number] = {
        stationId: station.id,
        stationCode: station.code,
        stationDescription: station.description,
        ipcIP: '',
        clientIP,
        isAcceptClientIP: false,
        listDevice: [],
      };

      const stationDetails = await this.stationDetailRepo.find({
        where: {
          stationId: station.id,
          assign: true,
        },
      });

      if (stationDetails.length >= 2) {
        for (let detail of stationDetails) {
          // Chỉ lấy các thiết bị loại CHAMCONG và IPC
          const device = await this.deviceRepo.findOne({
            where: {
              id: detail.deviceId,
              isActive: true,
              deviceTypeDetailCode: In([3, 4]),
            },
            select: [
              'id',
              'code',
              'description',
              'ipAddress',
              'serialNumber',
              'deviceTypeDetailCode',
            ],
          });

          if (device) {
            availableStation.listDevice.push({
              deviceId: device.id,
              deviceCode: device.code,
              deviceDescription: device.description,
              ipAddress: device.ipAddress,
              serialNumber: device.serialNumber,
              deviceTypeDetailCode: device.deviceTypeDetailCode,
            });

            // IPC type 4
            if (device.deviceTypeDetailCode == '4') {
              availableStation.ipcIP = device.ipAddress;
            }
          }
        }

        listAvaiableStation.push(availableStation);
      }
    }

    for (let station of listAvaiableStation) {
      if (station.ipcIP == clientIP) {
        station.isAcceptClientIP = true;
      }
    }

    return listAvaiableStation;
  }

  async getUserTimeSheet(userNo: number) {
    const user = await this.UserRepo.findOne({
      where: {
        userNo: userNo, // Pin ZKT,
        isActive: true,
        imageUrl: Not(IsNull()),
      },
      select: ['id', 'imageUrl', 'fullName', 'userNo', 'employeeCode', 'productionAreaId'],
    });

    if (user) {
      const timesheet = await this.getWorkingTimeSheet(String(user.id));

      user.imageUrl = await this.s3PrivateService.getS3Link(user.imageUrl);

      return {
        user,
        timesheet,
      };
    }

    return null;
  }
}
