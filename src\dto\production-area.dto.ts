import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';

export class ProductionAreaReqDto {
  @ApiProperty()
  code: string;

  @ApiProperty()
  name: string;

  @ApiPropertyOptional()
  description: string;

  @ApiPropertyOptional()
  note: string;

  @ApiProperty()
  organizationUnitId: string;

  @ApiPropertyOptional()
  isActive: boolean;
}

export class ProductionAreaDetailReqDto {
  @ApiProperty()
  organizationUnitId: string;

  @ApiProperty()
  isActive: boolean;

  @ApiProperty()
  createdBy: string;

  @ApiProperty()
  createdDate: Date;
}

export class ProductionAreaDetailUpdateDto extends ProductionAreaDetailReqDto {
  @ApiProperty()
  id: string;
}

export class ProductionAreaUpdateDto extends ProductionAreaReqDto {
  @ApiProperty()
  id: string;
}

export enum ProcessAreaActiveStatus {
  FULL = 'null',
  ACTIVE = 'yes',
  DISABLE = 'no',
}
