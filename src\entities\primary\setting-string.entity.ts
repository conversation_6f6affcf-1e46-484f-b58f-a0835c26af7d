import { ApiProperty } from '@nestjs/swagger';
import { Column, Entity } from 'typeorm';
import { PrimaryBaseEntity } from '../primary-base.entity';

/** <PERSON><PERSON><PERSON> lưu các giá trị thiết lập */
@Entity('setting_string')
export class SettingStringEntity extends PrimaryBaseEntity {
  @ApiProperty({ description: 'Mã NSSetting.ESettingCode' })
  @Column({ type: 'varchar', length: 100, nullable: true })
  code: string;

  @ApiProperty({ description: 'Tên' })
  @Column({ type: 'text', nullable: true })
  name: string;

  @ApiProperty({ description: 'Kiểu dữ liệu STRING/DOUBLE NSSetting.EDataType' })
  @Column({ type: 'varchar', length: 100, nullable: true })
  type: string;

  @ApiProperty({ description: 'Giá trị' })
  @Column({ type: 'text', nullable: true })
  value: string;
}
