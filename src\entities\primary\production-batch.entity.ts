import { Entity, PrimaryColumn, Column, ManyTo<PERSON>ne, Join<PERSON>olumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
// import { ProductionOrderEntity } from './production-order.entity'; // Import ProductionOrder entity
// import { ProductionOrderMaterialEntity } from './production-order-material.entity'; // Import ProductionOrderMaterial entity
import { PrimaryBaseEntity } from '../primary-base.entity';

@Entity('production_batch')
export class ProductionBatchEntity extends PrimaryBaseEntity {
  /** Mã lệnh sản xuất link từ bảng production order */
  @ApiProperty({ description: 'Mã lệnh sản xuất (liên kết đến ProductionOrder)' })
  @Column({ type: 'uuid', nullable: true })
  orderId: string;

  /** Số thứ tự của mẻ sản xuất trong lệnh */
  @ApiProperty({ description: '<PERSON><PERSON> thứ tự của mẻ sản xuất trong lệnh' })
  @Column({ type: 'numeric', nullable: true })
  batchNumber: number;

  // @ApiProperty({ description: 'id product order material' })
  // @Column({ type: 'uuid', nullable: true })
  // orderProductId: string;

  /** Mã thành phẩm link từ bảng production_order_material, line type = 1 - thành phẩm */
  @ApiProperty({ description: 'Mã thành phẩm (liên kết đến ProductionOrderMaterial)' })
  @Column({ type: 'uuid', nullable: true })
  productId: string;

  /** Số lượng cần sản xuất trong mẻ, dựa vào batch size của recipe, hệ thống tự tỉnh ra số lượng cần chạy trong 1 mẻ */
  @ApiProperty({ description: 'Số lượng cần sản xuất trong mẻ' })
  @Column({ type: 'numeric', nullable: true })
  planProdQty: number;

  /** Số lượng thực tế sản xuất của mẻ, được cập nhật về từ Scada */
  @ApiProperty({ description: 'Số lượng thực tế sản xuất của mẻ' })
  @Column({ type: 'numeric', nullable: true })
  actualProdQty: number;

  /** Đơn vị tỉnh thành phẩm cần sản xuất */
  @ApiProperty({ description: 'Đơn vị tính thành phẩm' })
  @Column({ type: 'varchar', length: 50, nullable: true })
  uom: string;

  /** Thời gian dự kiến bắt đầu chạy mẻ */
  @ApiProperty({ description: 'Thời gian dự kiến bắt đầu chạy mẻ' })
  @Column({ nullable: true, type: 'timestamptz' })
  planStartTime: Date;

  /** Thời gian dự kiến kết thúc mẻ */
  @ApiProperty({ description: 'Thời gian dự kiến kết thúc mẻ' })
  @Column({ nullable: true, type: 'timestamptz' })
  planEndTime: Date;

  /** Thời gian thực tế bắt đầu chạy mẻ, được cập nhật về từ scada */
  @ApiProperty({ description: 'Thời gian thực tế bắt đầu chạy mẻ' })
  @Column({ nullable: true, type: 'timestamptz' })
  actualStartTime: Date;

  /** Thời gian thực tế kết thúc mẻ, được cập nhật về từ scada */
  @ApiProperty({ description: 'Thời gian thực tế kết thúc mẻ' })
  @Column({ nullable: true, type: 'timestamptz' })
  actualEndTime: Date;

  /** Trạng thái (Pending, Running, Completed, Failed) */
  @ApiProperty({ description: 'Trạng thái mẻ sản xuất' })
  @Column({ type: 'varchar', nullable: true })
  status: string;

  /** Đã gửi xuống SCADA hay chưa */
  @ApiProperty({ description: 'Đã gửi xuống SCADA hay chưa' })
  @Column({ type: 'boolean', default: false })
  scadaSynced: boolean;

  /** Ngày đồng bộ qua scada */
  @ApiProperty({ description: 'Ngày đồng bộ qua scada' })
  @Column({ type: 'timestamptz', nullable: true })
  dateScadaSynced?: Date;

  /** Mục đích để tính năng cân & scan kiểm soát batch đã cân xong (bằng 1), đã scan xong (bằng 2) */
  @ApiProperty({
    description:
      'Mục đích để tính năng cân & scan kiểm soát batch đã cân xong (bằng 1), đã scan xong (bằng 2)',
  })
  @Column({ type: 'int', nullable: true, default: 0 })
  weighStatus: number;

  //   // Mối quan hệ với ProductionOrder
  //   @ManyToOne(() => ProductionOrder, productionOrder => productionOrder.productionBatches)
  //   @JoinColumn({ name: 'orderId' })
  //   productionOrder: ProductionOrder;

  //   // Mối quan hệ với ProductionOrderMaterial
  //   @ManyToOne(
  //     () => ProductionOrderMaterial,
  //     productionOrderMaterial => productionOrderMaterial.productionBatches,
  //   )
  //   @JoinColumn({ name: 'productId' })
  //   productionOrderMaterial: ProductionOrderMaterial;
}
