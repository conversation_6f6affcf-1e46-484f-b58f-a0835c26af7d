import { ApiProperty } from '@nestjs/swagger';
import { IsDate, IsNotEmpty, IsNumber, IsOptional, IsString, IsUUID } from 'class-validator';
import { PageRequest } from '~/@systems/utils';

export class CreateScaleRequestDto {
  id: string;

  @IsUUID()
  orderId: string;

  @IsOptional()
  @IsString()
  orderNo?: string;

  @IsUUID()
  batchId: string;

  @IsOptional()
  @IsString()
  batchNo?: string;

  @IsOptional()
  @IsUUID()
  productionMaterialId?: string;

  @IsOptional()
  @IsUUID()
  itemId?: string;

  @IsOptional()
  @IsString()
  itemCode?: string;

  @IsOptional()
  @IsString()
  itemName?: string;

  @IsOptional()
  @IsString()
  lotNumber?: string;

  @IsOptional()
  @IsNumber()
  tareQty?: number;

  @IsOptional()
  @IsNumber()
  tolerance?: number;

  @IsOptional()
  @IsNumber()
  weighedQty?: number;

  @IsOptional()
  @IsString()
  weightedUom?: string;

  @IsOptional()
  @IsNumber()
  packQty?: number;

  @IsOptional()
  datetime?: Date;

  @IsOptional()
  @IsString()
  qrcode?: string;

  @IsOptional()
  @IsString()
  materialHandleType?: string;

  @IsOptional()
  @IsNumber()
  status?: number;

  @IsOptional()
  @IsUUID()
  batchConsumptId?: string;

  @IsOptional()
  @IsString()
  batchConsumpNo?: string;

  @IsOptional()
  @IsUUID()
  created_by?: string;

  @IsOptional()
  @IsDate()
  last_update?: Date;

  @IsOptional()
  expiredDate?: string;

  @IsOptional()
  productionDate?: string;

  @IsOptional()
  @IsUUID()
  last_update_by?: string;

  @IsOptional()
  weighedTolerance?: number;

  @IsOptional()
  weightStandard?: number;

  @IsOptional()
  @IsString()
  deviceIp?: string;

  @IsOptional()
  @IsNumber()
  devicePort?: number;

  @IsOptional()
  @IsNumber()
  printType?: number;
}

export class ListScaleReq extends PageRequest {}
export class GetLotRequestDto {
  site: string;
  storageLocation: string;
  ingredientLocator: string;
  materialCode: string;
}
export class UpdateWeighedStatusReq {
  batchId: string;
}
export class ConvertUomReq {
  itemId: string;
  value: number;
}
export class CheckConnectReq {
  @ApiProperty({
    description: 'Địa chỉ IP của thiết bị',
    example: '*************',
    required: true,
  })
  @IsString()
  @IsNotEmpty()
  deviceIp: string;

  @ApiProperty({
    description: 'Cổng kết nối của thiết bị',
    example: 8080,
  })
  @IsNotEmpty()
  devicePort: number;
}

export class ScaleDataReq {
  @IsString()
  @IsNotEmpty()
  type: string;

  @IsString()
  @IsNotEmpty()
  raw: string;

  @IsString()
  @IsOptional()
  weight?: string;

  @IsString()
  @IsOptional()
  unit?: string;

  @IsOptional()
  timestamp?: string;

  @IsOptional()
  deviceIp: string;

  @IsOptional()
  devicePort: string;
}
