import { NestFactory } from '@nestjs/core';
import { Context, Handler } from 'aws-lambda';
import { AppModule } from '../app.module';
import { ProductionOrderService } from '~/x-modules/admin/services';
import { setupTransactionContext } from '~/@core/decorator';

let cachedApp: any = null;

async function bootstrap() {
  if (!cachedApp) {
    const app = await NestFactory.createApplicationContext(AppModule);
    setupTransactionContext();
    await app.init();
    cachedApp = app;
  }
  return cachedApp;
}

/**
 * Create a new Production Order
 */
export const createProductionOrder: Handler = async (event: any, context: Context) => {
  const app = await bootstrap();
  const service = app.get(ProductionOrderService);
  const body = JSON.parse(event.body);

  try {
    const result = await service.create(body);
    return {
      statusCode: 201,
      body: JSON.stringify(result),
      headers: { 'Content-Type': 'application/json' },
      message: result.message,
    };
  } catch (error) {
    return {
      statusCode: error.status || 500,
      body: JSON.stringify({
        message: error.message || 'Internal server error',
        error: error.response?.error || error.name,
      }),
      headers: { 'Content-Type': 'application/json' },
    };
  }
};

/**
 * Update a Production Order
 */
export const updateProductionOrder: Handler = async (event: any, context: Context) => {
  const app = await bootstrap();
  const service = app.get(ProductionOrderService);
  const body = JSON.parse(event.body);

  try {
    const result = await service.update(body);
    return {
      statusCode: 200,
      body: JSON.stringify(result),
      headers: { 'Content-Type': 'application/json' },
      message: result.message,
    };
  } catch (error) {
    return {
      statusCode: error.status || 500,
      body: JSON.stringify({
        message: error.message || 'Internal server error',
        error: error.response?.error || error.name,
      }),
      headers: { 'Content-Type': 'application/json' },
    };
  }
};

/**
 * Get paginated list
 */
export const paginationProductionOrder: Handler = async (event: any, context: Context) => {
  const app = await bootstrap();
  const service = app.get(ProductionOrderService);
  const body = JSON.parse(event.body);

  try {
    const result = await service.pagination(body);
    return {
      statusCode: 200,
      body: JSON.stringify(result),
      headers: { 'Content-Type': 'application/json' },
      message: 'Get paginated inspection plan successfully!',
    };
  } catch (error) {
    return {
      statusCode: error.status || 500,
      body: JSON.stringify({
        message: error.message || 'Internal server error',
        error: error.response?.error || error.name,
      }),
      headers: { 'Content-Type': 'application/json' },
    };
  }
};

/**
 * Get by ID
 */
export const findProductionOrderById: Handler = async (event: any, context: Context) => {
  const app = await bootstrap();
  const service = app.get(ProductionOrderService);

  try {
    const id = event.pathParameters?.id;
    if (!id) {
      return {
        statusCode: 400,
        body: JSON.stringify({
          message: 'Missing ID parameter',
        }),
        headers: {
          'Content-Type': 'application/json',
        },
      };
    }

    const result = await service.findOne(id);
    return {
      statusCode: 200,
      body: JSON.stringify(result),
      headers: { 'Content-Type': 'application/json' },
      message: 'Get reason master successfully!',
    };
  } catch (error) {
    return {
      statusCode: error.status || 500,
      body: JSON.stringify({
        message: error.message || 'Internal server error',
        error: error.response?.error || error.name,
      }),
      headers: { 'Content-Type': 'application/json' },
    };
  }
};

/**
 * delete by ID
 */
export const deleteProductionOrderById: Handler = async (event: any, context: Context) => {
  const app = await bootstrap();
  const service = app.get(ProductionOrderService);

  try {
    const id = event.pathParameters?.id;
    if (!id) {
      return {
        statusCode: 400,
        body: JSON.stringify({
          message: 'Missing ID parameter',
        }),
        headers: {
          'Content-Type': 'application/json',
        },
      };
    }

    const result = await service.delete(id);
    return {
      statusCode: 200,
      body: JSON.stringify(result),
      headers: { 'Content-Type': 'application/json' },
      message: 'Get reason master successfully!',
    };
  } catch (error) {
    return {
      statusCode: error.status || 500,
      body: JSON.stringify({
        message: error.message || 'Internal server error',
        error: error.response?.error || error.name,
      }),
      headers: { 'Content-Type': 'application/json' },
    };
  }
};

/**
 * Get data select for create
 */
export const loadDataSelectForCreateProductionOrder: Handler = async (
  event: any,
  context: Context,
) => {
  const app = await bootstrap();
  const service = app.get(ProductionOrderService);

  try {
    const result = await service.loadDataSelectForCreate();
    return {
      statusCode: 200,
      body: JSON.stringify(result),
      headers: {
        'Content-Type': 'application/json',
      },
      message: 'Get data successfully!',
    };
  } catch (error) {
    return {
      statusCode: error.status || 500,
      body: JSON.stringify({
        message: error.message || 'Internal server error',
        error: error.response?.error || error.name,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    };
  }
};
