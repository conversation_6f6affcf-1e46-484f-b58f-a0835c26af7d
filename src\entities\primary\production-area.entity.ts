import { ApiProperty } from '@nestjs/swagger';
import { Column, Entity, JoinColumn, ManyToOne, OneToMany } from 'typeorm';
import { PrimaryBaseEntity } from '../primary-base.entity';
import { GeneralDataDetailEntity } from './general-data-detail.entity';
import { OrganizationUnitEntity } from './organization-unit.entity';
import { ProductionAreaDetailEntity } from './production-area-detail.entity';
import { TimeSheetRecordEntity } from './timesheet-records.entity';

@Entity('production_area')
export class ProductionAreaEntity extends PrimaryBaseEntity {
  /** Mã khu vực sản xuất */
  @ApiProperty({ description: 'Mã khu vực sản xuất' })
  @Column({ nullable: false, unique: true })
  code: string;

  /** Tên khu vực sản xuất */
  @ApiProperty({ description: 'Tên khu vực sản xuất' })
  @Column({ nullable: false })
  name: string;

  /** <PERSON>ô tả khu vực sản xuất */
  @ApiProperty({ description: '<PERSON>ô tả khu vực sản xuất' })
  @Column({ nullable: true })
  description: string;

  /** ID Đơn vị tổ chức */
  @ApiProperty({ description: 'processAreaId' })
  @Column({ nullable: false })
  organizationUnitId: string;

  /** ID Đơn vị UOM */
  @ApiProperty({ description: 'ID Đơn vị UOM' })
  @Column({ nullable: true })
  uomID: string;

  /** Trạng thái hoạt động */
  @ApiProperty({ description: 'Trạng thái hoạt động' })
  @Column({ nullable: false, default: true })
  isActive: boolean;

  /** Ghi chú */
  @ApiProperty({ description: 'Ghi chú' })
  @Column({ nullable: true })
  note: string;

  // /** Relation với bảng OrganizationUnit */
  @ManyToOne(() => OrganizationUnitEntity, (org) => org.productionAreas, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'organizationUnitId', referencedColumnName: 'id' })
  organization?: OrganizationUnitEntity;

  /** Tọa độ X của Production Area */
  @ApiProperty({ description: 'Tọa độ X của Production Area' })
  @Column({ type: 'decimal', nullable: true })
  locationX: number;

  /** Tọa đô Y của Production Area */
  @ApiProperty({ description: 'Tọa đô Y của Production Are' })
  @Column({ type: 'decimal', nullable: true })
  locationY: number;

  /** Production Area Tolerance */
  @ApiProperty({ description: 'Production Area Tolerance' })
  @Column({ type: 'decimal', nullable: true })
  tolerance: number;

  /** Production Area Man Hour Resource Code */
  @ApiProperty({ description: 'Production Area Man Hour Resource Code' })
  @Column({ type: 'text', nullable: true })
  manHourResourceCode?: string;

  // /** Relation với bảng GeneralDataDetail */
  @ManyToOne(() => GeneralDataDetailEntity, { onDelete: 'SET NULL', nullable: true })
  @JoinColumn({ name: 'uomID', referencedColumnName: 'id' })
  uom?: GeneralDataDetailEntity;

  /** Mã đơn vị UOM */
  @ApiProperty({ description: 'Mã đơn vị UOM' })
  @Column({ nullable: true })
  uomGeneralCode: string;

  /** Mã đơn vị cấp thấp của UOM */
  @ApiProperty({ description: 'Mã đơn vị cấp thấp của UOM' })
  @Column({ nullable: true })
  uomGeneralDetailCode: string;

  /** Tên đơn vị cấp thấp của UOM*/
  @ApiProperty({ description: 'Tên đơn vị cấp thấp của UOM' })
  @Column({ nullable: true })
  uomGeneralDetailName: string;

  // /** Relation với bảng ProductionAreaDetailEntity */
  @OneToMany(() => ProductionAreaDetailEntity, (e) => e.productionArea)
  details: ProductionAreaDetailEntity[];

  /** Relation với bảng TimeSheetRecord */
  @OneToMany(() => TimeSheetRecordEntity, (timesheet) => timesheet.productionArea)
  timesheets!: TimeSheetRecordEntity[];
}
