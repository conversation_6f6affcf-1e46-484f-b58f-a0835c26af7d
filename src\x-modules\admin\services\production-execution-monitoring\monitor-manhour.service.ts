import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import moment from 'moment';
import { Between, ILike, In, LessThanOrEqual, Like, MoreThanOrEqual } from 'typeorm';
import { BindRepo } from '~/@core/decorator';
import { ListProductionRejection, ProductionRejectionReq } from '~/dto/rejection-monitoring.dto';
import {
  OrganizationUnitRepo,
  ProductionAreaRepo,
  TimeSheetRecordRepository,
  UtilityMetersRepo,
  UtilityTransactionRepo,
} from '~/repositories/primary';
import { adminSessionContext } from '../../admin-session.context';
import { ShiftRepo } from '~/repositories/primary/shift.repo';
import { UtilityTransactionReq } from '~/dto/utility-transaction.dto';
import { AssignShiftRepository } from '~/repositories/primary/assign-shift.repo';
import { ListMonitorManhour, MonitoringManhourReq } from '~/dto/monitor-manhour.dto';
import { UserRepo } from '~/repositories/primary/user.repo';
import { ManhourTransactionReq } from '~/dto/manhour-transaction.dto';
import { ManhourTransactionRepo } from '~/repositories/primary/manhour-transaction.repo';
import { AccessService } from '~/x-modules/admin/services/system-configuration/access.service';
import dayjs from 'dayjs';
import { AccessRepo } from '~/repositories/primary/access.repo';
import { RejectionMonitoringService } from '~/x-modules/admin/services/production-execution-monitoring/rejection-monitoring.service';
import { EOrganizationLevel } from '~/common/enums/organization-level.enum';
const AWS = require('aws-sdk');

@Injectable()
export class MonitorManhourService {
  constructor(private readonly rejectionMonitoringService: RejectionMonitoringService) {}

  @BindRepo(ManhourTransactionRepo)
  private readonly repo: ManhourTransactionRepo;

  @BindRepo(TimeSheetRecordRepository)
  private readonly timeSheetRecordRepo: TimeSheetRecordRepository;

  @BindRepo(ShiftRepo)
  private readonly shiftRepo: ShiftRepo;

  @BindRepo(AssignShiftRepository)
  private readonly assignShiftRepo: AssignShiftRepository;

  @BindRepo(OrganizationUnitRepo)
  private readonly organizationUnitRepo: OrganizationUnitRepo;

  @BindRepo(ProductionAreaRepo)
  private readonly productionAreaRepo: ProductionAreaRepo;

  @BindRepo(UserRepo)
  private readonly userRepo: UserRepo;

  @BindRepo(AccessRepo)
  private readonly accessRepo: AccessRepo;

  /**
   * Tìm tất cả lý do với bộ lọc tùy chọn
   * @param filterDto Tiêu chí lọc tùy chọn
   * @returns Danh sách phân trang
   */
  async pagination(params: ListMonitorManhour) {
    if (!params.siteId || !params.factoryId)
      return {
        data: [],
        total: 0,
      };
    const whereConSite: any = {};
    const whereConFactory: any = {};
    const whereConProductionArea: any = {};
    if (params.siteId) whereConSite.id = params.siteId;
    if (params.factoryId) whereConFactory.id = params.factoryId;
    if (params.productionAreaId) whereConProductionArea.id = params.productionAreaId;
    const site = await this.organizationUnitRepo.find({
      where: whereConSite,
    });
    whereConFactory.parentId = In(site.map((i) => i.id));
    const factory = await this.organizationUnitRepo.find({
      where: whereConFactory,
    });
    whereConProductionArea.organizationUnitId = In(factory.map((i) => i.id));
    const productionArea = await this.productionAreaRepo.find({
      where: whereConProductionArea,
    });

    const whereCon: any = { productionAreaId: In(productionArea.map((i) => i.id)) };
    if (params.productionAreaId) {
      whereCon.productionAreaId = params.productionAreaId;
    }
    if (params.startDate && params.endDate)
      whereCon.productionDate = Between(new Date(params.startDate), new Date(params.endDate));
    if (params.shiftId) whereCon.shiftId = params.shiftId;
    const result = await this.repo.findPagination(
      {
        where: whereCon,
        order: {
          productionDate: 'ASC',
        },
      },
      { pageSize: params.pageSize, pageIndex: params.pageIndex },
    );
    const shifts = await this.shiftRepo.find({
      where: { id: In(result.data.map((i) => i.shiftId)) },
    });
    const productionAreas = await this.productionAreaRepo.find({
      where: { id: In(result.data.map((i) => i.productionAreaId)) },
    });
    result.data.forEach((item: any) => {
      const shift = shifts.find((i) => i.id === item.shiftId);
      item.shiftName = shift?.code + ' - ' + shift?.description;
      const productionArea = productionAreas.find((i) => i.id === item.productionAreaId);
      item.productionAreaCode = productionArea?.code || '';
      item.shiftCode = shift?.code || '';
      item.productionAreaName = productionArea?.name || '';
      item.posted = 0;
    });
    result.data.sort((a: any, b: any) => {
      if (a.productionDate < b.productionDate) return -1;
      if (a.productionDate > b.productionDate) return 1;

      if (a.productionAreaCode < b.productionAreaCode) return -1;
      if (a.productionAreaCode > b.productionAreaCode) return 1;

      if (a.shiftCode < b.shiftCode) return -1;
      if (a.shiftCode > b.shiftCode) return 1;

      return 0;
    });

    return result;
  }

  async getOptions(levelCode?: string): Promise<any[]> {
    levelCode = levelCode?.replace(/\?$/, '');
    try {
      const data = await this.organizationUnitRepo.find({
        where: { levelGeneralDataDetailCode: levelCode, isActive: true },
      });
      return data.map((i) => ({
        label: i.code + ' - ' + i.name,
        value: i.id,
        parentId: i.parentId,
        categoryId: i.categoryId,
      }));
    } catch (error) {
      console.error('Error in getOptions:', error);
      return [];
    }
  }

  async getProcessArea(): Promise<any[]> {
    try {
      const data = await this.productionAreaRepo.find({
        where: { isActive: true },
      });
      return data.map((i) => ({
        label: i.code + ' - ' + i.name,
        value: i.id,
        organizationUnitId: i.organizationUnitId,
      }));
    } catch (error) {
      console.error('Error in getOptions:', error);
      return [];
    }
  }

  async create(body: ManhourTransactionReq) {
    const { userId: userId } = adminSessionContext;
    const today = new Date();
    const shift = await this.shiftRepo.findOne({ where: { id: body.shiftId, status: true } });
    if (!shift) throw new Error('Not Found Shift!');
    const productionArea = await this.productionAreaRepo.findOne({
      where: { id: body.productionAreaId },
    });
    if (!productionArea) throw new Error('Not Found Production Area');
    const data = await this.repo.save({
      ...body,
      posted: false,
      source: null,
      createdBy: userId,
      createdDate: today,
      updatedBy: userId,
      updatedDate: today,
      lastUpdateBy: -1,
    });
    return { message: 'Create Successfully!', data: data };
  }
  async loadShiftWithProductionDate(productionDate: Date) {
    const timeZone = 'Asia/Ho_Chi_Minh';

    // Chuyển productionDate sang giờ VN
    const timeString = new Intl.DateTimeFormat('en-GB', {
      timeZone,
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false, // Sử dụng định dạng 24h
    }).format(productionDate);

    const shift = await this.shiftRepo.findOne({
      where: {
        startTime: LessThanOrEqual(timeString),
        endTime: MoreThanOrEqual(timeString),
      },
    });

    if (!shift) {
      return [];
    }

    return [
      {
        label: `${shift.code} - ${shift.description}`,
        value: shift.id,
      },
    ];
  }

  async loadShift() {
    const shifts = await this.shiftRepo.find({
      where: {
        status: true,
      },
    });

    return shifts.map((shift) => ({
      label: `${shift.code} - ${shift.description}`,
      value: shift.id,
    }));
  }

  async loadShiftWithFactory(factoryId: string) {
    const assignShift = await this.assignShiftRepo.find({
      where: { organizationId: factoryId, status: true },
    });

    if (!assignShift.length) return [];

    const shifts = await this.shiftRepo.find({
      where: {
        id: In(assignShift.map((i) => i.shiftId)),
        status: true,
      },
    });

    return shifts.map((shift) => ({
      label: `${shift.code} - ${shift.description}`,
      value: shift.id,
    }));
  }

  async getProcessAreaWithFactory(factoryId: string) {
    return await this.productionAreaRepo.find({
      where: { organizationUnitId: factoryId },
    });
  }

  async getDefaultOptions(userId?: string, productionDate?: Date): Promise<any> {
    const user = await this.userRepo.findOne({
      where: { id: userId },
    });
    if (!user) return null;
    let shiftData = [];

    if (productionDate) {
      try {
        const currentTime = dayjs(productionDate).tz('Asia/Ho_Chi_Minh', true);
        const nowMinutes = currentTime.hour() * 60 + currentTime.minute();

        const listShift = await this.shiftRepo.find();

        for (const shift of listShift) {
          const [startH, startM] = shift.startTime.split(':').map(Number);
          const [endH, endM] = shift.endTime.split(':').map(Number);
          const startMinutes = startH * 60 + startM;
          const endMinutes = endH * 60 + endM;

          const isInShift = shift.nightShift
            ? nowMinutes >= startMinutes || nowMinutes <= endMinutes
            : nowMinutes >= startMinutes && nowMinutes <= endMinutes;

          if (isInShift) {
            shiftData = [
              {
                label: `${shift.code} - ${shift.description}`,
                value: shift.id,
                nightShift: shift.nightShift,
                startTime: shift.startTime,
                endTime: shift.endTime,
              },
            ];
            break;
          }
        }
      } catch (error) {
        console.error('Error loading shift:', error);
        shiftData = [];
      }
    }

    return {
      shift: shiftData,
    };
  }

  async getMonitorManhour(params: MonitoringManhourReq) {
    if (!params.siteId || !params.factoryId)
      return {
        data: [],
        total: 0,
      };
    const whereTimeSheet: any = {};
    if (params.factoryId) whereTimeSheet.factoryId = params.factoryId;
    if (params.siteId) whereTimeSheet.siteId = params.siteId;
    if (params.productionAreaId) whereTimeSheet.productionAreaId = params.productionAreaId;
    if (params.shiftId) whereTimeSheet.shiftId = params.shiftId;
    if (params.productionDate) {
      let shift;
      if (params.shiftId) {
        shift = await this.shiftRepo.findOne({ where: { id: params.shiftId } });
      } else {
        shift = await this.shiftRepo.findOne({ where: { code: 'C' } });
      }
      const now = dayjs.utc(params.productionDate);
      const startOfDay = dayjs(params.productionDate).tz('Asia/Ho_Chi_Minh').startOf('day').utc();
      const [hours, minutes, seconds] = shift.endTime.split(':').map(Number);
      const endShift = startOfDay.add(hours, 'hour').add(minutes, 'minute').add(seconds, 'second');

      if (shift?.code === 'C' && now.isBetween(startOfDay, endShift)) {
        whereTimeSheet.workDate = dayjs(now)
          .tz('Asia/Ho_Chi_Minh')
          .subtract(1, 'day')
          .format('YYYY-MM-DD');
      } else {
        whereTimeSheet.workDate = now.tz('Asia/Ho_Chi_Minh').format('YYYY-MM-DD');
      }
    }

    const timeSheet = await this.timeSheetRecordRepo.find({ where: whereTimeSheet });

    if (!timeSheet.length) {
      return { data: [], totalManhour: 0 };
    }

    const shiftIds = [...new Set(timeSheet.map((r) => r.shiftId))];
    const shifts = await this.shiftRepo.find({ where: { id: In(shiftIds) } });
    const shiftMap = new Map(shifts.map((shift) => [shift.id, shift]));

    const productionAreaIds = [...new Set(timeSheet.map((r) => r.productionAreaId))];
    const productionAreas = await this.productionAreaRepo.find({
      where: { id: In(productionAreaIds) },
    });
    const productionAreaMap = new Map(productionAreas.map((area) => [area.id, area]));

    const areaGroupMap = new Map<string, { users: Set<string>; records: typeof timeSheet }>();

    for (const record of timeSheet) {
      const areaId = record.productionAreaId;
      const userShiftKey = `${record.userId}-${record.shiftId}`;
      if (!areaGroupMap.has(areaId)) {
        areaGroupMap.set(areaId, {
          users: new Set(),
          records: [],
        });
      }
      const group = areaGroupMap.get(areaId)!;
      group.users.add(userShiftKey);
      group.records.push(record);
    }

    const groupedResult: any[] = [];

    for (const [areaId, group] of areaGroupMap.entries()) {
      const productionArea = productionAreaMap.get(areaId);
      if (!productionArea) continue;

      let totalManhour = 0;

      for (const record of group.records) {
        const shift = shiftMap.get(record.shiftId);
        if (shift?.startTime && shift?.endTime) {
          const [startHour, startMinute, startSecond] = shift.startTime.split(':').map(Number);
          const [endHour, endMinute, endSecond] = shift.endTime.split(':').map(Number);

          const startTime = new Date();
          startTime.setHours(startHour, startMinute, startSecond, 0);

          const endTime = new Date();
          endTime.setHours(endHour, endMinute, endSecond, 0);

          if (endTime < startTime) {
            endTime.setDate(endTime.getDate() + 1);
          }

          const shiftDuration = Math.round(
            (endTime.getTime() - startTime.getTime()) / (1000 * 60 * 60),
          );
          totalManhour += shiftDuration;
        }
      }

      groupedResult.push({
        productionAreaCode: productionArea.code,
        productionAreaName: productionArea.name,
        operator: group.users.size,
        manhour: totalManhour,
      });
    }

    return {
      total: groupedResult.length,
      data: groupedResult,
      totalOperator: groupedResult.reduce((sum, item) => sum + item.operator, 0),
      totalManhour: groupedResult.reduce((sum, item) => sum + item.manhour, 0),
    };
  }
}
