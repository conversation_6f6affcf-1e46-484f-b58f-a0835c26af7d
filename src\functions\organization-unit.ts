import { NestFactory } from '@nestjs/core';
import { Context, Handler } from 'aws-lambda';
import { AppModule } from '../app.module';
import { OrganizationUnitService } from '../x-modules/admin/services/system-configuration/organization-unit.service';
let cachedApp: any = null;

async function bootstrap() {
  if (!cachedApp) {
    const app = await NestFactory.createApplicationContext(AppModule);
    // const app = await NestFactory.create(AppModule);
    await app.init();
    cachedApp = app;
  }
  return cachedApp;
}

// Helper function to create response
function createResponse(statusCode: number, body: any) {
  return {
    statusCode,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Credentials': true,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(body),
  };
}

// Create organization unit
export const create: Handler = async (event: any, context: Context) => {
  try {
    console.log('Received create request:', event.body);

    // Parse request body
    const body = JSON.parse(event.body || '{}');
    console.log('Parsed request body:', body);

    // Initialize app and get service
    const app = await bootstrap();
    const organizationUnitService = app.get(OrganizationUnitService);
    console.log('Got OrganizationUnitService');

    // Call service
    const result = await organizationUnitService.create(body);
    console.log('Service returned result:', result);

    return createResponse(201, result);
  } catch (error) {
    console.error('Error creating organization unit:', error);
    console.error('Error stack:', error.stack);
    return createResponse(500, { message: error.message || 'Internal server error' });
  }
};

// Get all organization units
export const findAll: Handler = async (event: any, context: Context) => {
  try {
    console.log('Received findAll request');

    // Parse query parameters for pagination
    const queryParams = event.queryStringParameters || {};
    const pageSize = parseInt(queryParams.pageSize) || 10;
    const pageIndex = parseInt(queryParams.pageIndex) || 1;

    const app = await bootstrap();
    const organizationUnitService = app.get(OrganizationUnitService);
    console.log('Got OrganizationUnitService');

    // Use the list method instead of findAll
    const result = await organizationUnitService.list({ pageSize, pageIndex });
    console.log('Service returned result:', result);

    return createResponse(200, result);
  } catch (error) {
    console.error('Error getting organization units:', error);
    console.error('Error stack:', error.stack);
    return createResponse(500, { message: error.message || 'Internal server error' });
  }
};

// Get root organization units
export const findRoots: Handler = async (event: any, context: Context) => {
  try {
    console.log('Received findRoots request');
    const app = await bootstrap();
    const organizationUnitService = app.get(OrganizationUnitService);
    console.log('Got OrganizationUnitService');

    const result = await organizationUnitService.findRoots();
    console.log('Service returned result:', result);

    return createResponse(200, result);
  } catch (error) {
    console.error('Error getting root organization units:', error);
    console.error('Error stack:', error.stack);
    return createResponse(500, { message: error.message || 'Internal server error' });
  }
};

// Get children organization units
export const findChildren: Handler = async (event: any, context: Context) => {
  try {
    const parentId = event.pathParameters?.parentId;
    console.log('Received findChildren request for parent:', parentId);

    const app = await bootstrap();
    const organizationUnitService = app.get(OrganizationUnitService);
    console.log('Got OrganizationUnitService');

    const result = await organizationUnitService.findChildren(parentId);
    console.log('Service returned result:', result);

    return createResponse(200, result);
  } catch (error) {
    console.error('Error getting children organization units:', error);
    console.error('Error stack:', error.stack);
    return createResponse(500, { message: error.message || 'Internal server error' });
  }
};

// Search organization units
export const search: Handler = async (event: any, context: Context) => {
  try {
    const term = event.queryStringParameters?.term;
    console.log('Received search request with term:', term);

    const app = await bootstrap();
    const organizationUnitService = app.get(OrganizationUnitService);
    console.log('Got OrganizationUnitService');

    const result = await organizationUnitService.search(term);
    console.log('Service returned result:', result);

    return createResponse(200, result);
  } catch (error) {
    console.error('Error searching organization units:', error);
    console.error('Error stack:', error.stack);
    return createResponse(500, { message: error.message || 'Internal server error' });
  }
};

// Get organization unit by code
export const findByCode: Handler = async (event: any, context: Context) => {
  try {
    const code = event.pathParameters?.code;
    console.log('Received findByCode request for code:', code);

    const app = await bootstrap();
    const organizationUnitService = app.get(OrganizationUnitService);
    console.log('Got OrganizationUnitService');

    const result = await organizationUnitService.findByCode(code);
    console.log('Service returned result:', result);

    return createResponse(200, result);
  } catch (error) {
    console.error('Error getting organization unit by code:', error);
    console.error('Error stack:', error.stack);
    return createResponse(500, { message: error.message || 'Internal server error' });
  }
};

// Get organization unit by ID
export const findOne: Handler = async (event: any, context: Context) => {
  try {
    const id = event.pathParameters?.id;
    console.log('Received findOne request for id:', id);

    const app = await bootstrap();
    const organizationUnitService = app.get(OrganizationUnitService);
    console.log('Got OrganizationUnitService');

    const result = await organizationUnitService.findOne(id);
    console.log('Service returned result:', result);

    return createResponse(200, result);
  } catch (error) {
    console.error('Error getting organization unit by id:', error);
    console.error('Error stack:', error.stack);
    return createResponse(500, { message: error.message || 'Internal server error' });
  }
};

// Update organization unit
export const update: Handler = async (event: any, context: Context) => {
  try {
    const id = event.pathParameters?.id;
    const body = JSON.parse(event.body || '{}');
    console.log('Received update request for id:', id, 'with body:', body);

    const app = await bootstrap();
    const organizationUnitService = app.get(OrganizationUnitService);
    console.log('Got OrganizationUnitService');

    const result = await organizationUnitService.update(id, body);
    console.log('Service returned result:', result);

    return createResponse(200, result);
  } catch (error) {
    console.error('Error updating organization unit:', error);
    console.error('Error stack:', error.stack);
    return createResponse(500, { message: error.message || 'Internal server error' });
  }
};

// Delete organization unit
export const remove: Handler = async (event: any, context: Context) => {
  try {
    const id = event.pathParameters?.id;
    console.log('Received remove request for id:', id);

    const app = await bootstrap();
    const organizationUnitService = app.get(OrganizationUnitService);
    console.log('Got OrganizationUnitService');

    await organizationUnitService.remove(id);
    console.log('Organization unit removed successfully');

    return createResponse(200, { message: 'Organization unit removed successfully' });
  } catch (error) {
    console.error('Error removing organization unit:', error);
    console.error('Error stack:', error.stack);
    return createResponse(500, { message: error.message || 'Internal server error' });
  }
};

// Handle OPTIONS request for CORS
export const options: Handler = async (event: any, context: Context) => {
  return {
    statusCode: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers':
        'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-Amz-User-Agent',
      'Access-Control-Allow-Methods': 'OPTIONS,POST,GET,PUT,DELETE',
      'Access-Control-Allow-Credentials': true,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({}),
  };
};

// List organization units with pagination and filters
export const list: Handler = async (event: any, context: Context) => {
  try {
    console.log('Received list request with query params:', event.queryStringParameters);

    // Parse query parameters
    const queryParams = event.queryStringParameters || {};
    const pageSize = parseInt(queryParams.pageSize) || 10;
    const pageIndex = parseInt(queryParams.pageIndex) || 1;

    // Extract filter parameters
    const { level, isActive, code, name, category, createdDateFrom, createdDateTo } = queryParams;

    // Initialize app and get service
    const app = await bootstrap();
    const organizationUnitService = app.get(OrganizationUnitService);
    console.log('Got OrganizationUnitService');

    // Call list method with all parameters
    const result = await organizationUnitService.list({
      pageSize,
      pageIndex,
      level,
      isActive,
      code,
      name,
      category,
      createdDateFrom,
      createdDateTo,
    });

    console.log('Service returned result:', result);

    return createResponse(200, result);
  } catch (error) {
    console.error('Error listing organization units:', error);
    console.error('Error stack:', error.stack);
    return createResponse(500, { message: error.message || 'Internal server error' });
  }
};
