import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsIP,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  IsUUID,
  MaxLength,
  ValidateNested,
} from 'class-validator';
import { PageRequest } from '~/@systems/utils';
import { NSGeneralData } from '~/common/enums';

export class ListMachineReq extends PageRequest {
  code?: string;
  name?: string;
  isActive?: boolean;
}

export class MachineReq {
  @ApiProperty({ description: 'Mã duy nhất của máy, dùng để định danh', maxLength: 50 })
  @IsString()
  @MaxLength(50)
  code: string;

  @ApiProperty({ description: 'Tên của máy', maxLength: 255 })
  @IsString()
  @MaxLength(255)
  name: string;

  @ApiPropertyOptional({ description: '<PERSON><PERSON> tả về máy (c<PERSON> thể null)', type: String })
  @IsOptional()
  @IsString()
  desc?: string;

  @ApiProperty({ description: '<PERSON><PERSON><PERSON> thứ<PERSON> kết nối của máy (<PERSON><PERSON>TT, HTTP, v.v.)', maxLength: 50 })
  @IsString()
  @MaxLength(50)
  gatewayProtocol: string;

  @ApiProperty({ description: 'Địa chỉ IP của máy', maxLength: 50 })
  @IsString()
  // @IsIP()
  @MaxLength(50)
  ipAddress: string;

  @ApiPropertyOptional({ description: 'Ghi chú thêm về máy', type: String })
  @IsOptional()
  @IsString()
  note?: string;

  @ApiPropertyOptional({ description: 'Dữ liệu thô về máy', type: String })
  @IsOptional()
  @IsString()
  rawDataTable?: string;

  @ApiPropertyOptional({
    description: 'Trạng thái hoạt động của máy, mặc định true',
    type: Boolean,
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  isActive: boolean = true;
}

export class ListParameterReq extends PageRequest {
  tagName?: string;
  iotsitewisePropertyId?: string;
  iotsitewiseAssetId?: string;
}

export class ParameterReq {
  @ApiPropertyOptional({
    description: 'Id của parameter (chỉ dùng khi cập nhật, tạo mới không cần gửi)',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @IsOptional()
  @IsString()
  id?: string;

  @ApiPropertyOptional({
    description: 'Mô tả tham số (có thể null)',
    example: 'Cảm biến đo nhiệt độ môi trường',
  })
  @IsOptional()
  @IsString()
  desc?: string;

  @ApiProperty({
    description: 'Địa chỉ IP của máy',
    maxLength: 50,
    example: '*************',
  })
  // @IsString()
  @MaxLength(50)
  ipAddress: string;

  @ApiProperty({
    description: 'Tên tag của parameter',
    example: 'Temperature Sensor',
  })
  @IsString()
  tagName: string;

  @ApiProperty({
    description: 'Địa chỉ tag của parameter',
    example: '0x01A3',
  })
  @IsString()
  tagAddress: string;

  @ApiPropertyOptional({
    description: 'Có tính toán theo khoảng thời gian không? (true = có, false = lấy realtime)',
    example: true,
  })
  @IsOptional()
  @IsBoolean()
  interval?: boolean;

  @ApiPropertyOptional({
    description: 'Giá trị thời gian (chỉ nhập khi interval = true), đơn vị tính theo `timeUnit`',
    example: 30,
  })
  @IsOptional()
  // @IsNumber()
  timeValue?: number;

  @ApiPropertyOptional({
    description: 'ID của Node (nếu parameter thuộc một Node cụ thể)',
    example: 'NODE_001',
  })
  @IsOptional()
  @IsString()
  nodeId?: string;

  @ApiPropertyOptional({
    description: 'Ghi chú thêm về parameter',
    example: 'Parameter cần kiểm tra mỗi 10 phút',
  })
  @IsOptional()
  @IsString()
  note?: string;

  @ApiPropertyOptional({
    description: 'iotsitewisePropertyId từ aws',
    example: 'uuid',
  })
  @IsOptional()
  @IsString()
  iotsitewisePropertyId: string;

  /** IOT Sitewise Asset ID */
  @ApiPropertyOptional({
    description: 'iotsitewiseAssetId từ aws',
    example: 'uuid',
  })
  @IsOptional()
  @IsString()
  iotsitewiseAssetId?: string;

  /** OEE_Cal */
  @ApiPropertyOptional({
    description: 'Trạng thái oeeCal (-1=undefined, 0=stop, 1=start, 2=start&stop) ',
    example: 0,
  })
  @IsOptional()
  // @IsNumber()
  oeeCal: number;

  @ApiPropertyOptional({
    description: 'Trạng thái batch control (-1=undefined, 0=stop, 1=start, 2=start&stop) ',
    example: 0,
  })
  @IsOptional()
  // @IsNumber()
  batchStatusControl: number;

  @ApiPropertyOptional({
    description: 'is show on Dashboard',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  showOnDashboard?: boolean;

  @ApiPropertyOptional({
    description: 'is show on Rejection Monitoring',
    example: false,
  })
  @IsOptional()
  @IsBoolean()
  showOnRejectionMonitoring: boolean;

  @ApiPropertyOptional({
    description: 'Trạng thái hoạt động của parameter (true = đang hoạt động, false = vô hiệu hóa)',
    example: true,
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean = true;

  // ========================= Type =========================

  /**
   * Mã GeneralData của Type.
   */
  @ApiPropertyOptional({ description: 'Mã GeneralData của Type' })
  typeGeneralCode?: string;

  /**
   * Mã GeneralDataDetail của Type.
   */
  @ApiPropertyOptional({ description: 'Mã GeneralDataDetail của Type' })
  typeCode?: string;

  /**
   * ID của Type, liên kết với GeneralDataDetailEntity.
   */
  @ApiPropertyOptional({ description: 'ID của Type' })
  typeId?: string;

  // ========================= Data Type =========================

  /**
   * Mã GeneralData của Data Type.
   */
  @ApiPropertyOptional({ description: 'Mã GeneralData của Data Type' })
  dataTypeGeneralCode?: string;

  /**
   * Mã GeneralDataDetail của Data Type.
   */
  @ApiPropertyOptional({ description: 'Mã GeneralDataDetail của Data Type' })
  dataTypeCode?: string;

  /**
   * ID của Data Type, liên kết với GeneralDataDetailEntity.
   */
  @ApiPropertyOptional({ description: 'ID của Data Type' })
  dataTypeId?: string;

  // ========================= UOM (Đơn vị đo lường) =========================

  /**
   * Mã GeneralData của UOM.
   */
  @ApiPropertyOptional({ description: 'Mã GeneralData của UOM' })
  uomGeneralCode?: string;

  /**
   * Mã GeneralDataDetail của UOM.
   */
  @ApiPropertyOptional({ description: 'Mã GeneralDataDetail của UOM' })
  uomCode?: string;

  /**
   * ID của UOM, liên kết với GeneralDataDetailEntity.
   */
  @ApiPropertyOptional({ description: 'ID của UOM' })
  uomId?: string;

  // ========================= Datetime Unit =========================

  /**
   * Mã GeneralData của Datetime Unit.
   */
  @ApiPropertyOptional({ description: 'Mã GeneralData của Datetime Unit' })
  datetimeUnitGeneralCode?: string;

  /**
   * Mã GeneralDataDetail của Datetime Unit.
   */
  @ApiPropertyOptional({ description: 'Mã GeneralDataDetail của Datetime Unit' })
  datetimeUnitCode?: string;

  /**
   * ID của Datetime Unit, liên kết với GeneralDataDetailEntity.
   */
  @ApiPropertyOptional({ description: 'ID của Datetime Unit' })
  datetimeUnitId?: string;

  // ========================= IoT Sitewise Model Property Type =========================

  /**
   * Mã GeneralData của IoT Sitewise  Property Type.
   */
  @ApiPropertyOptional({ description: 'Mã GeneralData của IoT Sitewise  Property Type' })
  iotsitewisePropertyTypeGeneralCode?: string;

  /**
   * Mã GeneralDataDetail của IoT Sitewise  Property Type.
   */
  @ApiPropertyOptional({ description: 'Mã GeneralDataDetail của IoT Sitewise  Property Type' })
  iotsitewisePropertyTypeCode?: string;

  /**
   * ID của IoT Sitewise  Property Type, liên kết với GeneralDataDetailEntity.
   */
  @ApiPropertyOptional({ description: 'ID của IoT Sitewise  Property Type' })
  iotsitewisePropertyTypeId?: string;
}

// export class BatchUpdateParameterReq {
//   @ApiProperty({
//     description: 'Danh sách các tham số của máy',
//     type: [ParameterReq],
//   })
//   @IsArray()
//   @ValidateNested({ each: true })
//   @Type(() => ParameterReq)
//   parameters: ParameterReq[];
// }
