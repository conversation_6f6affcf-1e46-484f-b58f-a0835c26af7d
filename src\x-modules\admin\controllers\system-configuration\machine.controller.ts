import { Body, Param, ParseArrayPipe, ParseUUIDPipe, Query, UseGuards } from '@nestjs/common';
import { ApiBody, ApiOperation, ApiTags } from '@nestjs/swagger';
import { DefController, Def<PERSON><PERSON>te, DefGet, DefPost, DefPut } from '~/@core/decorator';
import { Roles } from '~/@core/decorator/roles.decorator';
import { RoleGuard } from '~/@systems/guard/role.guard';
import { MachineReq, ListMachineReq, ListParameterReq, ParameterReq } from '~/dto/machine.dto';
import { MachineService } from '~/x-modules/admin/services/system-configuration/machine.service';

@DefController('machines')
export class MachineController {
  constructor(private readonly machinesService: MachineService) {}

  @DefPost('find')
  @Roles('/system-configuration/machines', 'View')
  @UseGuards(RoleGuard)
  public async find(@Body() body: { processId: string }) {
    return this.machinesService.find(body);
  }

  @DefPost('find_parameter')
  @Roles('/system-configuration/machines', 'View')
  @UseGuards(RoleGuard)
  public async findMachineParameter(@Body() body: { machineId: string }) {
    return this.machinesService.findMachineParameter(body);
  }

  @ApiOperation({ summary: 'Tạo máy mới', description: 'Tạo một máy với thông tin đầu vào.' })
  @DefPost('')
  @Roles('/system-configuration/machines', 'View')
  @UseGuards(RoleGuard)
  async create(@Body() body: MachineReq) {
    return this.machinesService.create(body);
  }

  @ApiOperation({
    summary: 'Danh sách máy',
    description: 'Lấy danh sách các máy theo tiêu chí tìm kiếm.',
  })
  @DefGet('')
  @Roles('/system-configuration/machines', 'View')
  @UseGuards(RoleGuard)
  async list(@Query() params: ListMachineReq) {
    return this.machinesService.list(params);
  }

  @ApiOperation({
    summary: 'Chi tiết máy',
    description: 'Lấy thông tin chi tiết của một máy theo UUID.',
  })
  @DefGet(':id')
  @Roles('/system-configuration/machines', 'View')
  @UseGuards(RoleGuard)
  async detail(@Param('id', new ParseUUIDPipe()) id: string) {
    return this.machinesService.detail(id);
  }

  @ApiOperation({ summary: 'Cập nhật máy', description: 'Cập nhật thông tin của một máy hiện có.' })
  @DefPut(':id')
  @Roles('/system-configuration/machines', 'View')
  @UseGuards(RoleGuard)
  async update(@Param('id', new ParseUUIDPipe()) id: string, @Body() body: MachineReq) {
    return this.machinesService.update(id, body);
  }

  @ApiOperation({ summary: 'Xóa máy', description: 'Xóa một máy theo UUID.' })
  @DefDelete(':id')
  @Roles('/system-configuration/machines', 'View')
  @UseGuards(RoleGuard)
  async delete(@Param('id', new ParseUUIDPipe()) id: string) {
    return this.machinesService.delete(id);
  }

  @ApiOperation({ summary: 'Lấy danh sách Parameters của máy với phân trang' })
  @DefGet(':machineId/parameters')
  @Roles('/system-configuration/machines', 'View')
  @UseGuards(RoleGuard)
  async listParameters(
    @Param('machineId', new ParseUUIDPipe()) machineId: string,
    @Query() params: ListParameterReq,
  ) {
    return this.machinesService.listParameters(machineId, params);
  }

  @ApiOperation({ summary: 'Lưu parameters của máy' })
  @ApiBody({ type: ParameterReq })
  @DefPost(':machineId/parameters')
  @Roles('/system-configuration/machines', 'View')
  @UseGuards(RoleGuard)
  async saveParameter(
    @Param('machineId', new ParseUUIDPipe()) machineId: string,
    @Body() body: ParameterReq,
  ) {
    return this.machinesService.saveParameter(machineId, body);
  }

  // 🔄 Batch update parameters (chỉ thêm mới hoặc xóa, không sửa)
  @ApiOperation({ summary: 'Batch update parameters của máy' })
  @ApiBody({ type: [ParameterReq] })
  @DefPut(':machineId/parameters/batch')
  @Roles('/system-configuration/machines', 'View')
  @UseGuards(RoleGuard)
  async batchUpdateParameters(
    @Param('machineId', new ParseUUIDPipe()) machineId: string,
    @Body(new ParseArrayPipe({ items: ParameterReq })) body: ParameterReq[],
  ) {
    // return this.machinesService.batchUpdateParameters(machineId, body);
    return { message: 'Tạm thời không dùng nữa' };
  }

  @ApiOperation({ summary: 'Lấy danh sách data select' })
  @DefGet('parameters/data-select')
  @Roles('/system-configuration/machines', 'View')
  @UseGuards(RoleGuard)
  async loadParameterDataSelect() {
    return await this.machinesService.loadParameterDataSelect();
  }
}
