import { Enti<PERSON>, Column, <PERSON>To<PERSON>ne, Join<PERSON>olumn, Index, CreateDateColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { ProductionOrderEntity } from './production-order.entity'; // Import ProductionOrder entity
import { PrimaryBaseEntity } from '../primary-base.entity';

@Index(['uniqueCode'], { unique: true })
@Entity('unique_code_activation')
export class UniqueCodeActivationEntity extends PrimaryBaseEntity {
  /** Mỗi Production Order được tạo, thông tin detail của Production Order được lưu vào bảng Material. Order id link từ bảng Production Order. */
  @ApiProperty({ description: 'ID lệnh sản xuất (liên kết đến ProductionOrder)' })
  @Column({ type: 'uuid', nullable: true })
  orderId: string;

  @ApiProperty({ description: 'Mã lệnh sản xuất' })
  @Column({ type: 'varchar', length: 250})
  orderNo: string;

  @ApiProperty({ description: 'Mã CZ' })
  @Column()
  uniqueCode: string;

  @ApiProperty({ description: 'Trạng thái xử lý mã CZ' })
  @Column()
  status: number;

  @ApiProperty({ description: 'Ngày active czCode' })
  @CreateDateColumn({ type: 'timestamptz', nullable: true })
  activateDatetime?: Date;

  @ApiProperty({ description: 'planStartDate của PO' })
  @Column({ type: 'timestamptz', nullable: true })
  productionDate?: Date;

  @ApiProperty({
    description:
      'Lấy production date + số tháng, với số tháng = self life trên bảng item chia cho 30 làm tròn',
  })
  @Column({ type: 'timestamptz', nullable: true })
  expiredDate?: Date;

  @ApiProperty({ description: 'Trạng thái xử lý mã CZ' })
  @Column({ default: 0 })
  posted: number;

  // M - Active/Reject từ tính năng Scan Manual (từ máy PDA)
  // A - Active/Reject từ tính năng tự động
  @ApiProperty({ description: 'Active by Manual or Automation' })
  @Column({ nullable: true })
  source: string;

  // /** Relation với bảng GeneralDataDetail */
  @ManyToOne(() => ProductionOrderEntity)
  @JoinColumn({ name: 'orderId', referencedColumnName: 'id' })
  productionOrder: ProductionOrderEntity;
}
