import { Api<PERSON><PERSON>ation, ApiResponse, ApiTags } from '@nestjs/swagger';
import { DefController, Def<PERSON><PERSON>te, DefGet, DefPost, DefPut } from '~/@core/decorator';
import { SuccessResponse } from '~/@systems/utils';
import { Body, Param, Query, UseGuards } from '@nestjs/common';
import { ProcessReqDto } from '~/dto/process.dto';
import { ProcessService } from '../../services';
import { Roles } from '~/@core/decorator/roles.decorator';
import { RoleGuard } from '~/@systems/guard/role.guard';
@ApiTags('Process')
@DefController('process')
export class ProcessController {
  constructor(private readonly service: ProcessService) {}

  @DefPost('find')
  @Roles('/system-configuration/process', 'View')
  @UseGuards(RoleGuard)
  public async updateAfindssign(@Body() body: { organizationUnitId: string }) {
    return this.service.find(body);
  }

  @DefGet('')
  @Roles('/system-configuration/process', 'View')
  @UseGuards(RoleGuard)
  public async list(@Query() query: any) {
    return this.service.list(query);
  }

  @DefPost('', {
    responseType: SuccessResponse,
  })
  @Roles('/system-configuration/process', 'View')
  @UseGuards(RoleGuard)
  public async create(@Body() data: ProcessReqDto) {
    return this.service.create(data);
  }

  @DefPut(':id', {
    responseType: SuccessResponse,
  })
  @Roles('/system-configuration/process', 'View')
  @UseGuards(RoleGuard)
  public async update(@Param('id') id: string, @Body() data: ProcessReqDto) {
    return this.service.update(id, data);
  }

  @DefDelete(':id')
  @Roles('/system-configuration/process', 'View')
  @UseGuards(RoleGuard)
  public async delete(@Param('id') id: string) {
    return this.service.delete(id);
  }

  @DefGet('machines')
  @Roles('/system-configuration/process', 'View')
  @UseGuards(RoleGuard)
  public async listMachines(@Query() query: any) {
    return this.service.listMachines(query);
  }

  @DefGet(':id')
  @Roles('/system-configuration/process', 'View')
  @UseGuards(RoleGuard)
  public async findById(@Param('id') param: string) {
    return this.service.findById(param);
  }

  @DefPost('machines/:id')
  @Roles('/system-configuration/process', 'View')
  @UseGuards(RoleGuard)
  public async updateAssign(
    @Param('id') machineId: string,
    @Body() body: { processId: string; assign: boolean },
  ) {
    return this.service.updateMachineAssign(body.processId, machineId, body.assign);
  }
}
