import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { PrimaryBaseEntity } from '../primary-base.entity';
import { ApiProperty } from '@nestjs/swagger';
import { IsUUID } from 'class-validator';

@Index('idx_batch_machine_measure_time', ['batchId', 'machineId', 'measurementId', 'dateTime'])
@Entity('production_batch_paramaters')
export class MesDxProdBatchParamatersEntity extends PrimaryBaseEntity {
  @ApiProperty()
  @PrimaryGeneratedColumn('uuid')
  @IsUUID(4)
  bacthProcessParameterId?: string;

  // @Index()
  // @ApiProperty()
  // @CreateDateColumn({ type: 'timestamptz' })
  // creationDate?: Date;

  @ApiProperty({ description: 'Batch id của mẻ sản xuất', example: '1234' })
  @Column()
  machineId: string;

  @ApiProperty({ description: '<PERSON><PERSON><PERSON> mà mẻ sản xuất chạy qua', example: '6789' })
  @Column()
  batchId: string;

  @ApiProperty({ description: 'Mã tín hiệu của máy Scada', example: '6789' })
  @Column()
  measurementId: string;

  @ApiProperty({ description: 'Mã tín hiệu của máy Scada', example: '6789' })
  @Column()
  value: string;

  @ApiProperty({
    description: 'thời gian tín hiệu trả về của máy',
    example: '2024-03-06T14:30:00Z',
  })
  @UpdateDateColumn({ type: 'timestamptz' })
  dateTime: Date;

  // @ApiProperty({
  //   description: 'Thời gian cập nhật cuối của dòng dữ liệu',
  //   example: '2024-03-06T14:30:00Z',
  // })
  // // @UpdateDateColumn({ nullable: true, type: 'timestamptz' })
  // lastUpdate: Date;

  @ApiProperty({ description: '', example: '-1' })
  @Column({ nullable: true })
  lastUpdateBy: number;

  // @ApiProperty({ description: 'User Log', example: 'Admin' })
  // @Column()
  // createdBy: number;
}
