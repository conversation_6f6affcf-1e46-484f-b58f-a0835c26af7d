import { EntityRepository } from 'typeorm';
import { PrimaryRepo } from '~/repositories/primary.repo';
import {
  RecipeEntity,
  SitewiseAssetEntity,
  SitewiseModelEntity,
  SitewisePropertiesEntity,
} from '~/entities/primary';

@EntityRepository(SitewiseModelEntity)
export class SitewiseModelRepo extends PrimaryRepo<SitewiseModelEntity> {}

@EntityRepository(SitewiseAssetEntity)
export class SitewiseAssetRepo extends PrimaryRepo<SitewiseAssetEntity> {}

@EntityRepository(SitewisePropertiesEntity)
export class SitewisePropertiesRepo extends PrimaryRepo<SitewisePropertiesEntity> {}
