import { APIGatewayProxyEvent, APIGatewayProxyResult } from 'aws-lambda';
import { NestFactory } from '@nestjs/core';
import { AppModule } from '~/app.module';
import { UOMService } from '~/x-modules/admin/services/system-configuration/uom.service';

let cachedApp: any;

const createResponse = (statusCode: number, body: any = {}): APIGatewayProxyResult => ({
  statusCode,
  headers: {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Credentials': true,
    'Content-Type': 'application/json',
  },
  body: JSON.stringify(body),
});

export const findAll = async (event: APIGatewayProxyEvent): Promise<APIGatewayProxyResult> => {
  try {
    if (!cachedApp) {
      cachedApp = await NestFactory.createApplicationContext(AppModule);
    }
    const service = cachedApp.get(UOMService);
    const result = await service.findAll();
    return createResponse(200, result);
  } catch (error) {
    console.error('Error in findAll:', error);
    return createResponse(500, { message: 'Internal server error' });
  }
}; 