import {
  BadRequestException,
  Body,
  InternalServerErrorException,
  Param,
  ParseIntPipe,
  UseGuards,
} from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { DefController, DefDelete, DefGet, DefPost, DefPut } from '~/@core/decorator';
import { CreateShiftDto, UpdateShiftDto } from '~/dto/shift.dto';
import { ShiftService } from '../../services/system-configuration/shift.service';
import { Roles } from '~/@core/decorator/roles.decorator';
import { RoleGuard } from '~/@systems/guard/role.guard';

@ApiTags('Shift')
@DefController('shifts')
export class ShiftController {
  constructor(private readonly shiftsService: ShiftService) {}

  @DefPost('', {
    summary: 'Create a new shift', // Mô tả cho Swagger
    bodyType: CreateShiftDto, // Kiểu dữ liệu body
    responseType: CreateShiftDto, // <PERSON><PERSON><PERSON> dữ liệu trả về (có thể thay đổi tùy logic)
    statusCode: 201, // 201 Created
  })
  @Roles('/system-configuration/shift', 'View')
  @UseGuards(RoleGuard)
  async create(@Body() createShiftDto: CreateShiftDto) {
    try {
      return await this.shiftsService.create(createShiftDto);
    } catch (error) {
      if (
        error.message === 'Start time must be earlier than end time' ||
        error.message === 'Shift with this code already exists'
      ) {
        throw new BadRequestException(error.message);
      }
      throw new InternalServerErrorException('Something went wrong');
    }
  }

  @DefGet('', {
    summary: 'Get all shifts', // Mô tả cho Swagger
    responseType: [CreateShiftDto], // Trả về mảng các shift
  })
  @Roles('/system-configuration/shift', 'View')
  @UseGuards(RoleGuard)
  async findAll() {
    return this.shiftsService.findAll();
  }

  @DefGet(':id', {
    summary: 'Get a shift by ID', // Mô tả cho Swagger
    responseType: CreateShiftDto, // Kiểu dữ liệu trả về
  })
  @Roles('/system-configuration/shift', 'View')
  @UseGuards(RoleGuard)
  async findOne(@Param('id') id: string) {
    return this.shiftsService.findOne(id);
  }

  @DefPut(':id', {
    summary: 'Update a shift', // Mô tả cho Swagger
    bodyType: UpdateShiftDto, // Kiểu dữ liệu body
    responseType: UpdateShiftDto, // Kiểu dữ liệu trả về (có thể thay đổi tùy logic)
  })
  @Roles('/system-configuration/shift', 'View')
  @UseGuards(RoleGuard)
  async update(@Param('id') id: string, @Body() updateShiftDto: UpdateShiftDto) {
    try {
      await this.shiftsService.update(id, updateShiftDto);
    } catch (error) {
      // console.log(error, 'qwe');
      if (error.message) {
        throw new BadRequestException(error);
      }
      throw new InternalServerErrorException('Something went wrong');
    }
  }

  @DefDelete(':id', {
    summary: 'Delete a shift', // Mô tả cho Swagger
    statusCode: 204, // 204 No Content
  })
  @Roles('/system-configuration/shift', 'View')
  @UseGuards(RoleGuard)
  async remove(@Param('id') id: string) {
    return this.shiftsService.remove(id);
  }

  @DefPost('find-shift-by-line-factory')
  @Roles('/system-configuration/shift', 'View')
  @UseGuards(RoleGuard)
  async findShiftByLineOrFactory(@Body() body: { lineId: string }) {
    return await this.shiftsService.findShiftByLineOrFactory(body);
  }
}
