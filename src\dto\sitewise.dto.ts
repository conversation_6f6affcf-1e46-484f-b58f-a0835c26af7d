import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import {
  IsUUID,
  IsString,
  IsNumber,
  IsOptional,
  IsDate,
  IsBoolean,
  IsArray,
} from 'class-validator';
import { PageRequest } from '~/@systems/utils';

export class ListMeasurementReq {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  createdBy?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  updatedBy?: string;

  @ApiProperty()
  @IsString()
  gatewayName: string;

  @ApiProperty()
  @IsString()
  datasourceName: string;

  @ApiProperty()
  @IsString()
  modelName: string;

  @ApiProperty()
  @IsString()
  assetName: string;

  @ApiProperty()
  @IsString()
  assetId: string;

  @ApiProperty()
  @IsString()
  measurementName: string;

  @ApiProperty()
  @IsString()
  measurementId: string;

  @ApiProperty()
  @IsNumber()
  value: string;
}

export class ListMetricsReq {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  createdBy?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  updatedBy?: string;

  @ApiProperty()
  @IsString()
  gatewayName: string;

  @ApiProperty()
  @IsString()
  datasourceName: string;

  @ApiProperty()
  @IsString()
  modelName: string;

  @ApiProperty()
  @IsString()
  assetName: string;

  @ApiProperty()
  @IsString()
  metricName: string;

  @ApiProperty()
  @IsString()
  metricId: string;

  @ApiProperty()
  @IsNumber()
  value: string;
}

export class ListTransformReq {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  createdBy?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  updatedBy?: string;

  @ApiProperty()
  @IsString()
  gatewayName: string;

  @ApiProperty()
  @IsString()
  datasourceName: string;

  @ApiProperty()
  @IsString()
  modelName: string;

  @ApiProperty()
  @IsString()
  assetName: string;

  @ApiProperty()
  @IsString()
  transformName: string;

  @ApiProperty()
  @IsString()
  transformId: string;

  @ApiProperty()
  @IsNumber()
  value: string;
}

export class SitewiseModelReq {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  createdBy?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  updatedBy?: string;

  @ApiProperty()
  @IsString()
  code: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  description: string;

  @ApiProperty()
  @IsString()
  awsId: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}

export class SitewiseAssetReq {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  createdBy?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  updatedBy?: string;

  @ApiProperty()
  @IsString()
  code: string;

  @ApiProperty()
  @IsString()
  name: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  description: string;

  @ApiProperty()
  @IsString()
  awsId: string;

  @ApiProperty()
  @IsString()
  externalId: string;

  @ApiProperty()
  @IsString()
  sitewiseModelId: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}

export class SitewiseAssetImportReq {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  createdBy?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  updatedBy?: string;

  @ApiProperty()
  @IsString()
  code: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  description: string;

  @ApiProperty()
  @IsString()
  awsId: string;

  @ApiProperty()
  @IsString()
  externalId: string;

  @ApiProperty()
  @IsString()
  sitewiseModelCode: string;
}

export class SitewisePropertiesReq {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  createdBy?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  updatedBy?: string;

  @ApiProperty()
  @IsString()
  code: string;

  @ApiProperty()
  @IsString()
  description: string;

  @ApiProperty()
  @IsString()
  awsId: string;

  @ApiProperty()
  @IsString()
  type: string;

  @ApiProperty()
  @IsString()
  unit: string;

  @ApiProperty()
  @IsString()
  sitewiseAssetId: string;
}

export class SitewisePropertiesImportReq {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  createdBy?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  updatedBy?: string;

  @ApiProperty()
  @IsString()
  code: string;

  @ApiProperty()
  @IsString()
  description: string;

  @ApiProperty()
  @IsString()
  awsId: string;

  @ApiProperty()
  @IsString()
  type: string;

  @ApiProperty()
  @IsString()
  unit: string;

  @ApiProperty()
  @IsString()
  sitewiseAssetCode: string;
}

export class ListSitewiseModelReq extends PageRequest {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  code?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  awsId?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}

export class ListSitewiseAssetReq extends PageRequest {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  code?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  awsId?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  sitewiseModelId?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  status?: string;
}

export class ListSitewisePropertiesReq extends PageRequest {
  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  code?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  awsId?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  sitewiseAssetId?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  type?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  unit?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean;
}

export class AssetPropertyValueReq {
  @ApiProperty({ required: true, description: 'Id asset' })
  @IsString()
  assetId: string;

  @ApiProperty({ required: true, description: 'Id asset' })
  @IsString()
  assetId1: string;

  @ApiProperty({ required: true, description: 'Id asset' })
  @IsString()
  assetId2: string;

  @ApiProperty({ required: true, description: 'Id property' })
  @IsString()
  propertyIdStart?: string;

  @ApiProperty({ required: true, description: 'Id property' })
  @IsString()
  propertyIdEnd?: string;

  /** SUM_TOTAL_COUNT - SUM_TOTAL_GOOD */
  @ApiProperty({ required: true, description: 'Id property' })
  @IsString()
  propertyIdG1?: string;

  /** SUM_TOTAL_COUNT - SUM_TOTAL_GOOD */
  @ApiProperty({ required: true, description: 'Id property' })
  @IsString()
  propertyIdG2?: string;

  /** SUM_TOTAL_COUNT - SUM_TOTAL_GOOD */
  @ApiProperty({ required: true, description: 'Id property' })
  @IsString()
  propertyIdG3?: string;

  /** SUM_TOTAL_COUNT - SUM_TOTAL_REJECTION */
  @ApiProperty({ required: true, description: 'Id property' })
  @IsString()
  propertyIdR1?: string;

  /** SUM_TOTAL_COUNT - SUM_TOTAL_REJECTION */
  @ApiProperty({ required: true, description: 'Id property' })
  @IsString()
  propertyIdR2?: string;

  /** SUM_TOTAL_COUNT - SUM_TOTAL_REJECTION */
  @ApiProperty({ required: true, description: 'Id property' })
  @IsString()
  propertyIdR3?: string;

  @ApiProperty({ required: true, description: 'Id property' })
  @IsString()
  propertyId?: string;

  /** Dùng cho  Electricity/shift*/
  @ApiProperty({ required: false, description: 'List Id property' })
  @IsArray()
  propertyIds?: string[];

  /** Dùng cho lable Material consumption */
  @ApiProperty({ required: true, description: 'Id property batch' })
  @IsString()
  propertyBatchId?: string;

  /** Dùng cho lable Material consumption */
  @ApiProperty({ required: true, description: 'Id property product' })
  @IsString()
  propertyProductId?: string;

  @ApiPropertyOptional({ description: 'Loại label' })
  @IsString()
  case?: string;

  // @ApiProperty({ required: false, description: 'OrgUnitLineId Line Id lấy từ bảng orgUnit' })
  // @IsString()
  // orgUnitLineId?: string;

  // @ApiProperty({ required: false, description: 'OrgUnitFactoryId Factory Id lấy từ bảng orgUnit' })
  // @IsString()
  // orgUnitFactoryId?: string;

  /** Dùng cho lable Flour loss shift */
  @ApiProperty({ required: false, description: 'ProcessAreaId' })
  @IsString()
  processAreaId?: string;
}

export class MonitorLineDashboardByIdReq {
  @ApiProperty({ description: 'Id property' })
  @IsString()
  assetName: string;

  @ApiProperty({ description: 'orgUnitId Line Id lấy từ bảng orgUnit' })
  @IsString()
  orgUnitLineId?: string;

  // @ApiProperty({ description: 'orgUnitId Factory Id lấy từ bảng orgUnit' })
  // @IsString()
  // orgUnitFactoryId?: string;

  @ApiProperty({ description: 'shiftId' })
  @IsString()
  shiftId?: string;

  @ApiProperty({ description: 'Danh sách các MSI-Line Title' })
  lineItem: AssetPropertyValueReq[];
}

export class CalculateOEEReq {
  @ApiProperty({ required: true, description: 'shiftId' })
  @IsString()
  shiftId: string;

  @ApiProperty({ required: true, description: 'orgUnitLineId' })
  @IsString()
  orgUnitLineId: string;
}

export class getOeeChartReq {
  @ApiProperty({ required: true, description: 'productionLineId' })
  @IsOptional()
  productionLineId: string;

  @ApiProperty({ required: true, description: 'categoryId' })
  @IsOptional()
  categoryId: string;

  @ApiProperty({ required: true, description: 'siteId' })
  @IsOptional()
  siteId: string;

  @ApiProperty({ required: true, description: 'factoryId' })
  @IsOptional()
  factoryId: string;

  @ApiProperty({ description: 'Start Date' })
  @IsOptional()
  startDate?: Date;

  @ApiProperty({ description: 'End Date' })
  @IsOptional()
  endDate?: Date;
}
export class getOeeDetailsReq {
  @ApiProperty({ required: true, description: 'productionLineId' })
  @IsOptional()
  productionLineId: string;

  @ApiProperty({ required: true, description: 'categoryId' })
  @IsOptional()
  categoryId: string;

  @ApiProperty({ required: true, description: 'siteId' })
  @IsOptional()
  siteId: string;

  @ApiProperty({ required: true, description: 'factoryId' })
  @IsOptional()
  factoryId: string;

  @ApiProperty({ description: 'Start Date' })
  @IsOptional()
  startDate?: Date;

  @ApiProperty({ description: 'End Date' })
  @IsOptional()
  endDate?: Date;
}
