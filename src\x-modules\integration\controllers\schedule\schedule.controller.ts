// x_module/integration/controller/my-controller.controller.ts

import { Controller, Post, UseGuards, Get, Body, Inject, forwardRef } from '@nestjs/common';
import { ApiOperation } from '@nestjs/swagger';
import { DefPost } from '~/@core/decorator';
import { ScheduleAuthGuard } from '~/@systems/guard/schedule-auth.guard';
import { KPIAwardService } from '~/x-modules/admin/services';
import { ScheduleService } from '~/x-modules/integration/services/schedule';
@Controller('schedule')
@UseGuards(ScheduleAuthGuard)
export class ScheduleController {
  constructor(
    private service: ScheduleService,
    @Inject(forwardRef(() => KPIAwardService))
    private kpiAwardService: KPIAwardService,
  ) {}

  @Post('update-kpi-period-yearly')
  async runYearlyUpdate() {
    return await this.service.runYearlyUpdate();
  }

  @Post('reject-reason-mid-night')
  async handleMidnightCron() {
    return await this.service.handleMidnightCron();
  }

  @Post('create-log-schedule')
  async createLogSchedule(@Body() body: { message: string; type: string }) {
    return await this.service.createLogSchedule(body);
  }

  @Post('create-sample')
  async createSample() {
    return await this.service.createSampleQLONE();
  }

  @ApiOperation({ summary: '', description: 'Tính toán kết quả thưởng theo nhân viên ' })
  @DefPost('calculate')
  async calculateKpiDataForDate() {
    return this.kpiAwardService.calculateKpiDataForDate();
  }
}
