import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsArray } from 'class-validator';
import { PageRequest } from '~/@systems/utils';

export class ListFilterReportDto extends PageRequest {
  @ApiPropertyOptional({ description: 'Người submit report', required: false })
  createdByUser?: string;

  @ApiPropertyOptional({ description: 'Người submit report', required: false })
  email?: string;

  @ApiPropertyOptional({ description: 'Từ ngày planStartDate', required: false })
  fromDate?: string;

  @ApiPropertyOptional({ description: 'Đến ngày planStartDate', required: false })
  toDate?: string;
}

export class CreateReportDto {
  @ApiPropertyOptional({ description: 'Danh sách ID của site', required: false })
  siteIdLst?: string[];

  @ApiPropertyOptional({ description: 'Danh sách ID của factory', required: false })
  factoryIdLst?: string[];

  @ApiPropertyOptional({ description: 'Danh sách ID của line', required: false })
  lineIdLst?: string[];

  @ApiPropertyOptional({ description: 'Số hiệu đơn hàng', required: false })
  orderNo?: string;

  @ApiPropertyOptional({ description: 'Danh sách ID của trạng thái', required: false })
  statusIdLst?: string[];

  @ApiPropertyOptional({ description: 'Từ ngày planStartDate', required: true })
  fromDate: string;

  @ApiPropertyOptional({ description: 'Đến ngày planStartDate', required: true })
  toDate: string;

  @ApiPropertyOptional({ description: 'Số hiệu lô hàng', required: false })
  batchNumber?: string;
}

export class CreateCZCodeShipmentReportDto {
  @ApiProperty({ description: 'Số đơn hàng của khách hàng', required: true })
  customerOrderNo: string;
}
