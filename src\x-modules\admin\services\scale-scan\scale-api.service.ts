import { Injectable } from '@nestjs/common';
import axios from 'axios';

@Injectable()
export class ScaleApiService {
  private readonly baseUrl: string;

  constructor() {
    this.baseUrl = process.env.EXTERNAL_API_URL || 'http://localhost:9000';
    const env = process.env.EXTERNAL_API_URL + '/scale/connection-status';
    console.log(env);
  }

  async callApiHelperGet(endpoint: string, params?: any): Promise<any> {
    try {
      const config = {
        params,
        headers: {
          'Content-Type': 'application/json',
        },
      };

      const response = await axios.get(`${this.baseUrl}${endpoint}`, config);
      return response.data;
    } catch (error) {
      console.error(`Error calling external API GET ${endpoint}:`, error);
      throw error;
    }
  }

  async callApiHelperPost(endpoint: string, data: any): Promise<any> {
    try {
      const config = {
        headers: {
          'Content-Type': 'application/json',
        },
      };

      const response = await axios.post(`${this.baseUrl}${endpoint}`, data, config);
      return response.data;
    } catch (error) {
      console.error(`Error calling external API POST ${endpoint}:`, error);
      throw error;
    }
  }
}
