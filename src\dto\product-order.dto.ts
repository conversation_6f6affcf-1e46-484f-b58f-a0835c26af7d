import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';
import { PageRequest } from '~/@systems/utils';

export class CreateProductionOrderDto {
  @IsOptional()
  @IsString()
  poParentId?: string;

  @IsOptional()
  @IsString()
  productionOrderNo: string;

  @IsOptional()
  @IsString()
  orderName: string;

  @ApiProperty({
    description: 'Id site',
  })
  @IsOptional()
  @IsString()
  siteId: string;

  @ApiProperty({
    description: 'Id line',
  })
  @IsOptional()
  @IsString()
  lineId: string;

  @ApiProperty({
    description: 'Mã nhà máy, chọn từ bảng orgchart - chọn các code có level = 3 - Factory',
  })
  @IsNotEmpty()
  @IsString()
  factoryId: string;

  @ApiProperty({
    description:
      'Mã khu vực sản xuất của 1 production line, chọn từ bảng orgchart (level 6 - Process area)',
  })
  @IsNotEmpty()
  @IsString()
  processAreaId: string;

  @ApiProperty({ description: 'Id Sản phẩm' })
  @IsNotEmpty()
  @IsString()
  materialId: string;

  @ApiProperty({ description: 'Mã recipe, chọn từ danh mục recipe' })
  @IsString()
  recipeId: string;

  @IsNotEmpty()
  planStartDate: Date;

  @IsNotEmpty()
  @IsString()
  orderStatus: string;

  @ApiProperty({ description: 'Ca sản xuất, chọn từ danh mục ca' })
  @IsString()
  shiftId: string;

  @IsNotEmpty()
  @IsString()
  uom: string;

  @IsOptional()
  planEndDate?: Date;

  @IsNotEmpty()
  @IsNumber()
  quantity: number;

  @IsOptional()
  @IsString()
  lot: string;

  @IsOptional()
  isSendScada: boolean;

  @ApiProperty({ description: 'Người điều hành' })
  @IsOptional()
  @IsString()
  operatorUserId: string;

  @ApiProperty({ description: 'Mã đơn hàng khách hàng' })
  @IsOptional()
  @IsString()
  customerOrderId?: string;

  @ApiProperty({ description: 'Mã đơn hàng khách hàng' })
  @IsOptional()
  @IsString()
  customerOrderNo?: string;

  @ApiProperty({ description: 'Mã sản phẩm khách hàng' })
  @IsOptional()
  @IsString()
  customerProductCode?: string;

  @ApiProperty({ description: 'Tên sản phẩm khách hàng' })
  @IsOptional()
  @IsString()
  customerProductName?: string;

  @ApiProperty({ description: 'Tên viết tắt khách hàng' })
  @IsOptional()
  @IsString()
  customerShortName?: string;

  @ApiProperty({ description: 'Mã GTIN' })
  @IsOptional()
  @IsString()
  gtin?: string;

  @ApiProperty({ description: 'Số lượng đơn hàng khách hàng' })
  @IsOptional()
  customerOrderQty?: number;

  @ApiProperty({ description: 'Danh sách file đính kèm' })
  @IsOptional()
  @IsArray()
  lstFile?: any[];

  @ApiProperty({ description: 'Loại đơn hàng' })
  @IsOptional()
  orderSerialized: number;
}

export class UpdateProductionOrderDto extends CreateProductionOrderDto {
  @ApiProperty({ description: 'ID' })
  @IsNotEmpty()
  @IsString()
  id: string;
}

export class SendScadaDto {
  @IsOptional()
  lstId?: string[];
}

export class ProductOrderPageReq extends PageRequest {
  @ApiProperty({ description: '' })
  @IsOptional()
  lineId: string;

  @ApiProperty({ description: '' })
  @IsOptional()
  siteId: string;

  @ApiProperty({ description: '' })
  @IsOptional()
  factoryId: string;

  @ApiProperty({ description: '' })
  @IsOptional()
  itemId: string;

  @ApiProperty({ description: '' })
  @IsOptional()
  processAreaId: string;

  @ApiProperty({ description: '' })
  @IsOptional()
  orderStatus: string;

  @ApiProperty({ description: '' })
  @IsOptional()
  orderNo: string;

  @ApiProperty({ description: '' })
  @IsOptional()
  planStartDate: Date[];
}

// export class ProductionOrderGenLotNumberDto {
//   @IsOptional()
//   @IsString()
//   productionOrderNo: string;
// }

export class LstPOIdDto {
  @IsNotEmpty()
  @IsArray()
  poIds: Array<string>;
}

export class CreateBTPDTO {
  @ApiProperty({ description: 'Danh sách order bán thành phẩm' })
  @IsArray()
  @IsNotEmpty()
  lstPo: CreateProductionOrderDto[];
}
