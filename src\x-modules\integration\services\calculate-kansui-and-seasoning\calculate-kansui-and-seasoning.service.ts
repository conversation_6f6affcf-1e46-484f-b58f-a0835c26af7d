import { Injectable } from '@nestjs/common';
import { BindRepo } from '~/@core/decorator';
import { JwtService } from '@nestjs/jwt';
import moment from 'moment';
import {
  MachineParameterRepo,
  MachineRepo,
  ItemRepo,
  UomConventionRepo,
  ProcessRepo,
  ProductionOrderRepo,
  ProductionBatchRepo,
  MesDxProdBatchStatusRepo,
  ProductionOrderMaterialRepo,
  ProductionOrderMaterialTransactionRepo,
} from '~/repositories/primary';
import { In, Not } from 'typeorm';
// import { SitewiseLibService } from '~/x-modules/integration/services/uom-conversion-lib.service'

@Injectable()
export class CalculateKansuiAndSeasoningService {
  constructor(private jwtService: JwtService) {}

  @BindRepo(ItemRepo)
  private itemRepo: ItemRepo;
  @BindRepo(ProcessRepo)
  private processRepo: ProcessRepo;
  @BindRepo(ProductionBatchRepo)
  private productionBatchRepo: ProductionBatchRepo;
  @BindRepo(MesDxProdBatchStatusRepo)
  private mesDxProdBatchStatusRepo: MesDxProdBatchStatusRepo;
  @BindRepo(ProductionOrderRepo)
  private readonly productionOrderRepo: ProductionOrderRepo;
  @BindRepo(ProductionOrderMaterialRepo)
  private productionOrderMaterialRepo: ProductionOrderMaterialRepo;
  @BindRepo(ProductionOrderMaterialTransactionRepo)
  private productionOrderMaterialTransactionRepo: ProductionOrderMaterialTransactionRepo;
  @BindRepo(MachineParameterRepo)
  private readonly machineParameterRepo: MachineParameterRepo;

  // Quy đồi UOM
  @BindRepo(UomConventionRepo)
  private readonly uomConventionRepo: UomConventionRepo;

  onUOMConversionCoefficient(
    uomConventionData: any,
    fromUnitInfo: string,
    toUnitInfo: string,
    itemIdInfo: string,
  ) {
    if (!uomConventionData || uomConventionData?.length === 0) return 1;

    const getConversionValue = (fromUnit: string, toUnit: string, itemId: string): number => {
      const match = uomConventionData.find((ele: any) => {
        return ele.itemId === itemId && ele.fromUnit === fromUnit && ele.toUnit === toUnit;
      });
      return match && match?.conversion ? match.conversion : 0;
    };
    const uomConventionValue = getConversionValue(fromUnitInfo, toUnitInfo, itemIdInfo);
    if (uomConventionValue !== 0)
      return !isNaN(Number(uomConventionValue)) ? Number(uomConventionValue) : 1;

    const uomConventionOppositeValue = getConversionValue(toUnitInfo, fromUnitInfo, itemIdInfo);
    if (uomConventionOppositeValue !== 0)
      return !isNaN(Number(uomConventionOppositeValue))
        ? 1 / Number(uomConventionOppositeValue)
        : 1;

    return 1;
  }

  findSingleIntermediate(fromUom: any, toUom: any, lstUomConverse: any) {
    const unitMap = {};
    const conversionMap = {};

    // Tạo map các đơn vị kết nối lẫn nhau
    lstUomConverse.forEach(({ fromUnit, toUnit, conversion }) => {
      if (!unitMap[fromUnit]) unitMap[fromUnit] = new Set();
      if (!unitMap[toUnit]) unitMap[toUnit] = new Set();

      unitMap[fromUnit].add(toUnit);
      unitMap[toUnit].add(fromUnit); // 2 chiều
      // Lưu tỉ lệ quy đổi theo cả 2 chiều
      conversionMap[`${fromUnit}->${toUnit}`] = conversion;
      conversionMap[`${toUnit}->${fromUnit}`] = 1 / conversion;
    });

    const visited = new Set();
    const queue = [{ unit: fromUom, rate: 1 }];

    while (queue.length > 0) {
      const { unit, rate } = queue.shift();
      if (unit === toUom) {
        return rate;
      }

      visited.add(unit);

      const neighbors = unitMap[unit] || new Set();
      for (const neighbor of neighbors) {
        if (!visited.has(neighbor)) {
          const conversionRate = conversionMap[`${unit}->${neighbor}`];
          queue.push({ unit: neighbor, rate: rate * conversionRate });
        }
      }
    }

    return null; // Không tìm thấy chuyển đổi
  }

  onHandleTransactionDate(date: any) {
    let getTime = moment().utcOffset(7 * 60);
    if (date) getTime = moment(date, 'YYYY-MM-DD HH:mm:ss.SSS Z').utcOffset(7 * 60);
    getTime.startOf('day');
    return getTime.format('YYYY-MM-DD HH:mm:ss.SSS Z');
  }

  async getStatusWithBatchAndOrder() {
    // 1. Lấy danh sách production_batch_status có status = '3'
    const statusData = await this.mesDxProdBatchStatusRepo.find({
      where: { status: '3' },
    });

    // 2. Lấy danh sách batch tương ứng
    const batchIds = statusData.map((s) => s.batchId);
    const batches = await this.productionBatchRepo.findByIds(batchIds);

    // 3. Lấy danh sách order tương ứng
    const orderIds = batches.map((b) => b.orderId);
    const orders = await this.productionOrderRepo.findByIds(orderIds);

    // 4. Gộp lại dữ liệu (merge thủ công)
    const merged = statusData.map((s) => {
      const batch = batches.find((b) => b.id === s.batchId);
      const order = batch ? orders.find((o) => o.id === batch.orderId) : null;

      return {
        ...s,
        batchNumber: batch?.batchNumber || null,
        orderNo: order?.orderNo || null,
      };
    });

    const groupedByOrder: Record<string, any> = {};
    merged.forEach((record) => {
      const orderNoValue = record.orderNo;
      if (!groupedByOrder[orderNoValue]) {
        groupedByOrder[orderNoValue] = record;
      } else {
        const existing = groupedByOrder[orderNoValue];
        const isNewer = new Date(record.createdDate) > new Date(existing.createdDate);
        if (isNewer) {
          groupedByOrder[orderNoValue] = record;
        }
      }
    });

    // 6. Lấy danh sách record mới nhất theo từng orderId
    const latestRecords = Object.values(groupedByOrder);
    return latestRecords;
  }

  async createKansuiAndSeasoningCalculation() {
    try {
      // set-up data
      const uomConventionData = await this.uomConventionRepo.find();
      // Tìm record trong production_batch_status có status = 3
      const statusData = await this.getStatusWithBatchAndOrder();
      if (!statusData || statusData?.length === 0) return;
      await Promise.all(
        statusData.map(async (status) => {
          const getBatchid = status?.batchId;
          // B1: Tìm dữ liệu với bảng production_batch, production_order
          const productionBatchData = await this.productionBatchRepo.findOne({
            where: { id: getBatchid },
          });
          if (!productionBatchData) return;

          const getOrderId = productionBatchData?.orderId;
          const getProductionOrder = await this.productionOrderRepo.findOne({
            where: { id: getOrderId },
          });
          if (!getProductionOrder) return;

          // B2: tìm kiếm dữ liệu production_order_material
          // Liên kết bộ dữ liệu của B1 với bảng production_order_material theo order_id + chỉ lấy line_type = 1 + chỉ lấy 1 dòng product nếu có nhiều
          const productionOrderMaterialData = await this.productionOrderMaterialRepo.findOne({
            where: { orderId: getProductionOrder?.id, lineType: 1 },
          });
          if (!productionOrderMaterialData) return;

          // Tìm kiếm process id và chỉ lấy các process có setup OUTPUT_CALCULATION_METHOD=1
          const processData = await this.processRepo.findOne({
            where: {
              id: productionOrderMaterialData?.processId,
              outputCalculationMethodCode: '1',
            },
          });
          if (!processData) return;

          const getProcessId = productionOrderMaterialData?.processId || '';
          const prodOrderMaterialDetailData = await this.productionOrderMaterialRepo.find({
            where: {
              processId: getProcessId,
              orderId: getProductionOrder?.id,
              lineType: -1,
            },
          });

          // B3: Cần tính actual_trx_qty cho các dòng dữ liệu của B2
          const atualTrxQtyValueByprodOrderMaterial = !isNaN(
            Number(productionOrderMaterialData?.actualTrxQty),
          )
            ? Number(productionOrderMaterialData?.actualTrxQty)
            : productionOrderMaterialData?.actualTrxQty;
          let atualTrxQtyValue = 0;
          await Promise.all(
            prodOrderMaterialDetailData.map(async (prodOrderMaterialDetail) => {
              // Tìm dữ liệu item
              const itemData = await this.itemRepo.findOne({
                where: { code: prodOrderMaterialDetail?.materialCode },
              });

              const uomConventionByItem = uomConventionData.filter(
                (item) => item.itemId === itemData?.id,
              );
              const getTrxUom = prodOrderMaterialDetail?.trxUom;
              const UOMConversionData =
                this.findSingleIntermediate(
                  getTrxUom,
                  getProductionOrder?.trxUom,
                  uomConventionByItem,
                ) || 1;
              const getPlanTrxQty = !isNaN(Number(prodOrderMaterialDetail?.actualTrxQty))
                ? Number(prodOrderMaterialDetail?.actualTrxQty)
                : prodOrderMaterialDetail?.actualTrxQty;
              atualTrxQtyValue += getPlanTrxQty * UOMConversionData;
            }),
          );
          // Valid stored data: Nếu không có thay đổi thì out
          const getAtualTrxQtyValue = atualTrxQtyValue - atualTrxQtyValueByprodOrderMaterial;
          const isCheckChangeData = getAtualTrxQtyValue === 0;
          if (isCheckChangeData) return;

          const getAtualTrxQty = Math.abs(Math.round(getAtualTrxQtyValue * 100000) / 100000);
          if (getAtualTrxQty === 0) return;

          const objTransaction = {
            orderId: getOrderId,
            // batchId: getBatchid,
            oeeCal: 0,
            productionOrderMaterialId: productionOrderMaterialData?.id,
            materialId: productionOrderMaterialData?.materialId,
            transactionDate: this.onHandleTransactionDate(getProductionOrder?.planStartDate),
            transactionType: getAtualTrxQtyValue > 0 ? 'WIP_COMPLETION' : 'WIP_COMPLETION_RETURN',
            lotNumber: getProductionOrder?.lotNumber,
            transactionQty: Math.abs(getAtualTrxQtyValue),
            transactionUom: productionOrderMaterialData?.trxUom,
            createdByUser: '-1',
            lastUpdate: new Date(),
            lastUpdateBy: -1,
          };
          await this.productionOrderMaterialTransactionRepo.save({ ...(objTransaction as any) });
          await this.productionOrderMaterialRepo.update(
            { id: productionOrderMaterialData?.id },
            { actualTrxQty: atualTrxQtyValueByprodOrderMaterial + getAtualTrxQtyValue },
          );
        }),
      );
    } catch (error) {
      console.log(error, 'error');
    }
  }
}
