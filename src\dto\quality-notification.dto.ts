import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';
import { PageRequest } from '~/@systems/utils';
import { KpiEntity } from '~/entities/primary/kpi.entity';

export class QualityNotificationDetailDto {
  @ApiProperty({ description: 'Id quality notification' })
  @IsNotEmpty()
  @IsString()
  id: string;

  @ApiProperty({ description: '' })
  @IsNotEmpty()
  @IsString()
  notificationNumber: string;

  @ApiProperty({ description: '' })
  @IsNotEmpty()
  @IsString()
  qaNo: string;

  @ApiProperty({ description: '' })
  @IsNotEmpty()
  @IsString()
  dataType: string;
}
export class QualityNotificationPaginationDto extends PageRequest {
  @ApiProperty({ description: 'Site code or name' })
  @IsOptional()
  @IsString()
  siteId: string;

  @ApiProperty({ description: 'Factory code or name' })
  @IsOptional()
  @IsString()
  factoryId: string;

  @ApiProperty({ description: 'Production line' })
  @IsOptional()
  @IsString()
  lineId: string;

  @ApiProperty({ description: 'Date the notification was created (YYYY-MM-DD)' })
  @IsOptional()
  createdDate: string;

  @ApiProperty({ description: 'Status of the quality notification' })
  @IsOptional()
  @IsString()
  status: string;

  @ApiProperty({ description: 'Quality Notification Number' })
  @IsOptional()
  @IsString()
  qualityNotificationNo: string;

  @ApiProperty({ description: 'Item code or name' })
  @IsOptional()
  @IsString()
  itemId: string;

  @ApiProperty({ description: 'QLone Number' })
  @IsOptional()
  @IsString()
  qloneNo: string;
}
