import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsBoolean, IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';
import { PageRequest } from '~/@systems/utils';

export class ManhourMonitoringReq extends PageRequest {
  /** Organization Unit level SITE */
  @ApiPropertyOptional({ description: 'Site ID' })
  @IsOptional()
  siteId?: string;

  /** Organization Unit level FACTORY */
  @ApiPropertyOptional({ description: 'Factory ID' })
  @IsOptional()
  factoryId?: string;

  /** Utility meter type */
  @ApiProperty({ description: 'Production Line ID' })
  @IsOptional()
  type?: string;

  /** Utility meter code */
  @ApiProperty({ description: 'Meter Code' })
  @IsOptional()
  meterCode?: Date;

  /** Utility meter name */
  @ApiProperty({ description: 'Meter Name' })
  @IsOptional()
  meterName?: Date;
}

export class ManhourTransactionReq {
  @ApiProperty({ description: '<PERSON><PERSON>y giao dịch' })
  @IsNotEmpty()
  productionDate: Date;

  @ApiProperty({ description: 'Shift ID' })
  @IsNotEmpty()
  shiftId: string;

  @ApiProperty({ description: 'Meter ID' })
  @IsNotEmpty()
  productionAreaId: string;

  @ApiProperty({ description: 'Giờ chấm công' })
  @IsNotEmpty()
  manhour: number;

  @ApiProperty({ description: 'note' })
  @IsOptional()
  Note?: string;
}

export class ListManhourTransaction extends PageRequest {
  @ApiProperty({ description: 'Factory ID' })
  @IsOptional()
  factoryId?: string;

  @ApiProperty({ description: 'Type ID' })
  @IsOptional()
  type?: string;
}
