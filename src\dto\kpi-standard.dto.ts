import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';
import { PageRequest } from '~/@systems/utils';

export class PaginationReq {
  @ApiProperty({ description: 'Set Code' })
  @IsOptional()
  setCodeId: string;
  @ApiProperty({
    example: 10,
  })
  pageSize: number = 50;
  @ApiProperty({
    example: 1,
  })
  pageIndex: number = 1;
}
export class ListKPIStandardReq extends PageRequest {
  @ApiProperty({ description: 'ID Kpi Standard Score' })
  @IsNotEmpty()
  @IsString()
  id: string;

  @ApiProperty({ description: 'Ngày bắt đầu hiệu lực' })
  @IsOptional()
  effectiveDateFrom?: Date;

  @ApiProperty({ description: 'Ngày kết thúc hiệu lực' })
  @IsOptional()
  effectiveDateTo?: Date;
}

export class KPIStandardCreateDTO {
  @ApiProperty({ description: 'ID Kpi Standard Score' })
  @IsNotEmpty()
  @IsString()
  kpiSetId: string;

  @ApiProperty({ description: 'Danh sách điểm đánh giá KPI', required: false })
  @IsOptional()
  kpiScores?: KpiStandardScoreDto[];
}

export class KPIStandardCopyDTO {
  @ApiProperty({ description: 'ID Kpi set Standard Score' })
  @IsNotEmpty()
  @IsString()
  kpiSetId: string;

  @ApiProperty({ description: 'GroupId Standard Score' })
  @IsNotEmpty()
  @IsString()
  groupId: string;

  @ApiProperty({ description: 'Danh sách điểm đánh giá KPI', required: false })
  @IsOptional()
  kpiScores?: KpiStandardScoreDto[];
}
export class KpiStandardScoreDto {
  @ApiProperty({ description: 'KPI SET GROUP', nullable: true })
  @IsString()
  kpiSetGroupId: string;

  @ApiProperty({ description: 'ID KPI Staff Performance' })
  @IsString()
  kpiId: string;

  @ApiProperty({ description: 'ID KPI Set' })
  @IsString()
  kpiSetId: string;

  @ApiProperty({ description: 'level KPI Standard Score' })
  @IsString()
  level: string;

  @ApiProperty({ description: 'Điểm đánh giá xếp loại', example: 80 })
  @IsNumber()
  levelScore: number;

  @ApiProperty({ description: '% nhận thưởng theo xếp loại', example: 10 })
  @IsNumber()
  rewardPercentage: number;

  @ApiProperty({ description: 'Ngày bắt đầu hiệu lực', example: '2024-06-01' })
  effectiveDateFrom: string;

  @ApiProperty({ description: 'Ngày bắt đầu hiệu lực', example: '2024-06-01' })
  effectiveDateTo: string;
}

export class KPIStandardUpdateDTO extends KpiStandardScoreDto {
  @ApiProperty({ description: 'ID Kpi Standard Score' })
  @IsNotEmpty()
  @IsString()
  groupId: string;
}
export class KPISetHeaderFirstDTO {
  @ApiProperty({ description: 'ID Kpi Set' })
  @IsNotEmpty()
  @IsString()
  kpiSetId: string;
}
