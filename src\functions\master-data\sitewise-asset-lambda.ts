import { NestFactory } from '@nestjs/core';
import { Context, Handler } from 'aws-lambda';
import { AppModule } from '~/app.module';
import { SitewiseAssetService } from '~/x-modules/admin/services';
let cachedApp: any = null;

async function bootstrap() {
  if (!cachedApp) {
    const app = await NestFactory.createApplicationContext(AppModule);
    await app.init();
    cachedApp = app;
  }
  return cachedApp;
}

// Helper function to create response
function createResponse(statusCode: number, body: any) {
  return {
    statusCode,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Credentials': true,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(body),
  };
}

// // Create sitewise asset
// export const create: Handler = async (event: any, context: Context) => {
//   try {
//     console.log('Received create request:', event.body);

//     // Parse request body
//     const body = JSON.parse(event.body || '{}');
//     console.log('Parsed request body:', body);

//     // Initialize app and get service
//     const app = await bootstrap();
//     const sitewiseAssetService = app.get(SitewiseAssetService);
//     console.log('Got SitewiseAssetService');

//     // Call service
//     const result = await sitewiseAssetService.create(body);
//     console.log('Service returned result:', result);

//     return createResponse(201, result);
//   } catch (error) {
//     console.error('Error creating sitewise asset:', error);
//     console.error('Error stack:', error.stack);
//     return createResponse(500, { message: error.message || 'Internal server error' });
//   }
// };

// Get all sitewise asset with pagination
export const pagination: Handler = async (event: any, context: Context) => {
  try {
    console.log('Received list request with query params:', event.queryStringParameters);

    // Parse query parameters
    const queryParams = event.queryStringParameters || {};

    const app = await bootstrap();
    const sitewiseAssetService = app.get(SitewiseAssetService);
    console.log('Got SitewiseAssetService');

    const result = await sitewiseAssetService.pagination(queryParams);
    console.log('Service returned result:', result);

    return createResponse(200, result);
  } catch (error) {
    console.error('Error listing sitewise asset:', error);
    console.error('Error stack:', error.stack);
    return createResponse(500, { message: error.message || 'Internal server error' });
  }
};

// Get all sitewise asset without pagination
export const find: Handler = async (event: any, context: Context) => {
  try {
    console.log('Received findAll request with query params:', event.queryStringParameters);

    // Parse query parameters for filtering
    const queryParams = event.queryStringParameters || {};

    const app = await bootstrap();
    const sitewiseAssetService = app.get(SitewiseAssetService);
    console.log('Got SitewiseAssetService');

    // We'll need to add a findAll method to the service
    const result = await sitewiseAssetService.find(queryParams);
    console.log('Service returned result:', result);

    return createResponse(200, result);
  } catch (error) {
    console.error('Error finding all sitewise asset:', error);
    console.error('Error stack:', error.stack);
    return createResponse(500, { message: error.message || 'Internal server error' });
  }
};

// // Get sitewise asset by ID
// export const findOne: Handler = async (event: any, context: Context) => {
//   try {
//     const id = event.pathParameters?.id;
//     console.log('Received findOne request for id:', id);

//     const app = await bootstrap();
//     const sitewiseAssetService = app.get(SitewiseAssetService);
//     console.log('Got SitewiseAssetService');

//     const result = await sitewiseAssetService.findOne(id);
//     console.log('Service returned result:', result);

//     return createResponse(200, result);
//   } catch (error) {
//     console.error('Error getting sitewise asset by id:', error);
//     console.error('Error stack:', error.stack);
//     return createResponse(500, { message: error.message || 'Internal server error' });
//   }
// };


// // Update sitewise asset
// export const update: Handler = async (event: any, context: Context) => {
//   try {
//     const id = event.pathParameters?.id;
//     const body = JSON.parse(event.body || '{}');
//     console.log('Received update request for id:', id, 'with body:', body);

//     const app = await bootstrap();
//     const sitewiseAssetService = app.get(SitewiseAssetService);
//     console.log('Got SitewiseAssetService');

//     // We'll need to update the update method to accept id parameter
//     const result = await sitewiseAssetService.update( id, body );
//     console.log('Service returned result:', result);

//     return createResponse(200, result);
//   } catch (error) {
//     console.error('Error updating sitewise asset:', error);
//     console.error('Error stack:', error.stack);
//     return createResponse(500, { message: error.message || 'Internal server error' });
//   }
// };

// // Create sitewise asset
// export const createDataExcel: Handler = async (event: any, context: Context) => {
//   try {
//     console.log('Received create request:', event.body);

//     // Parse request body
//     const body = JSON.parse(event.body || '{}');
//     console.log('Parsed request body:', body);

//     // Initialize app and get service
//     const app = await bootstrap();
//     const sitewiseAssetService = app.get(SitewiseAssetService);
//     console.log('Got SitewiseAssetService');

//     // Call service
//     const result = await sitewiseAssetService.createDataExcel(body);
//     console.log('Service returned result:', result);

//     return createResponse(201, result);
//   } catch (error) {
//     console.error('Error creating sitewise asset:', error);
//     console.error('Error stack:', error.stack);
//     return createResponse(500, { message: error.message || 'Internal server error' });
//   }
// };

// Handle OPTIONS request for CORS
export const options: Handler = async (event: any, context: Context) => {
  return {
    statusCode: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers':
        'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-Amz-User-Agent',
      'Access-Control-Allow-Methods': 'OPTIONS,POST,GET,PUT,PATCH,DELETE',
      'Access-Control-Allow-Credentials': true,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({}),
  };
};
