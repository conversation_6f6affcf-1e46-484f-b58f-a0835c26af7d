import { Body, Query, UseGuards } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { DefController, DefGet, DefPost } from '~/@core/decorator';
import { MonitorManhourService } from '../../services/production-execution-monitoring/monitor-manhour.service';
import { ListMonitorManhour, MonitoringManhourReq } from '~/dto/monitor-manhour.dto';
import { ManhourTransactionReq } from '~/dto/manhour-transaction.dto';
import { Roles } from '~/@core/decorator/roles.decorator';
import { RoleGuard } from '~/@systems/guard/role.guard';

@ApiTags('Monitor Manhour')
@DefController('monitor-manhour')
export class MonitorManhourController {
  constructor(private readonly service: MonitorManhourService) {}

  @ApiOperation({ summary: 'Danh sách phân trang monitor manhour' })
  @DefGet('')
  @Roles('/production-execution/monitor-manhour', 'View')
  @UseGuards(RoleGuard)
  async list(@Query() params: ListMonitorManhour) {
    return this.service.pagination(params);
  }

  @ApiOperation({ summary: 'Tạo Monitor Manhour' })
  @DefPost('create')
  @Roles('/production-execution/monitor-manhour', 'View')
  @UseGuards(RoleGuard)
  async create(@Body() body: ManhourTransactionReq) {
    return await this.service.create(body);
  }

  @ApiOperation({ summary: 'Danh sách load-shift' })
  @DefGet('load-shift-with-production-date')
  @Roles('/production-execution/monitor-manhour', 'View')
  @UseGuards(RoleGuard)
  async loadShiftWithProductionDate(@Query('productionDate') productionDate: Date) {
    return await this.service.loadShiftWithProductionDate(productionDate);
  }

  @ApiOperation({ summary: 'Danh sách load-shift' })
  @DefGet('load-shift')
  @Roles('/production-execution/monitor-manhour', 'View')
  @UseGuards(RoleGuard)
  async loadShift() {
    return await this.service.loadShift();
  }

  @ApiOperation({ summary: 'Danh sách shift theo factory' })
  @DefGet('load-shift-with-factory')
  @Roles('/production-execution/monitor-manhour', 'View')
  @UseGuards(RoleGuard)
  async loadShiftWithFactory(@Query('factoryId') factoryId: string) {
    return await this.service.loadShiftWithFactory(factoryId);
  }

  @ApiOperation({ summary: 'Danh sách options' })
  @DefGet('get-options')
  @Roles('/production-execution/monitor-manhour', 'View')
  @UseGuards(RoleGuard)
  async getOptions(@Query('levelCode') levelCode: string) {
    return this.service.getOptions(levelCode);
  }

  @ApiOperation({ summary: 'Danh sách options' })
  @DefGet('get-default-options')
  @Roles('/production-execution/monitor-manhour', 'View')
  @UseGuards(RoleGuard)
  async getDefaultOptions(
    @Query('userId') userId: string,
    @Query('formattedDate') productionDate: Date,
  ) {
    return this.service.getDefaultOptions(userId, productionDate);
  }

  @ApiOperation({ summary: 'Danh sách options' })
  @DefGet('get-process-area')
  @Roles('/production-execution/monitor-manhour', 'View')
  @UseGuards(RoleGuard)
  async getProcessArea() {
    return this.service.getProcessArea();
  }

  @ApiOperation({ summary: 'Danh sách production Area theo Factory' })
  @DefGet('get-process-area-with-factory')
  @Roles('/production-execution/monitor-manhour', 'View')
  @UseGuards(RoleGuard)
  async getProcessAreaWithFactory(@Query('factoryId') factoryId: string) {
    return await this.service.getProcessAreaWithFactory(factoryId);
  }
  @ApiOperation({ summary: 'Monitoring Manhour' })
  @DefGet('list-monitoring-manhour')
  @Roles('/production-execution/monitor-manhour', 'View')
  @UseGuards(RoleGuard)
  async getUtilityMonitor(@Query() params: MonitoringManhourReq) {
    return await this.service.getMonitorManhour(params);
  }
}
