import { ApiProperty } from '@nestjs/swagger';
import { <PERSON>ti<PERSON>, Column, ManyToOne, JoinC<PERSON>umn, OneToMany, CreateDateColumn } from 'typeorm';
import { PrimaryBaseEntity } from '../primary-base.entity';

@Entity('monitor_manhour')
export class MonitorManhourEntity extends PrimaryBaseEntity {
  /** Ng<PERSON>y giao dịch */
  @ApiProperty({ description: 'Ngày giao dịch' })
  @CreateDateColumn({ type: 'timestamptz' })
  @Column({
    nullable: false,
  })
  productionDate: Date;

  /** Mã khu vực */
  @ApiProperty({ description: 'Mã khu vực' })
  @Column({
    nullable: false,
  })
  productionAreaId: string;

  /** Mã ca */
  @ApiProperty({ description: 'Mã ca' })
  @Column({
    nullable: false,
  })
  shiftId: string;

  /** Gi<PERSON> làm việc */
  @ApiProperty({ description: '<PERSON><PERSON><PERSON> làm việc' })
  @Column({
    nullable: true,
  })
  manhour: number;

  @ApiProperty({ description: '<PERSON>ô tả' })
  @Column({
    nullable: true,
  })
  note: string;

  /** Posted */
  @ApiProperty({ description: 'Posted' })
  @Column({
    nullable: true,
    default: null,
  })
  posted: number;
}
