import { APIGatewayProxyEvent, APIGatewayProxyResult, Context } from 'aws-lambda';
import { NestFactory } from '@nestjs/core';
import { AppModule } from '../app.module';
import { UserGroupService } from '~/x-modules/admin/services/system-configuration/user-group.service';
import { UserService } from '~/x-modules/admin/services/system-configuration/user.service';

let cachedApp: any;

async function bootstrap() {
  if (!cachedApp) {
    cachedApp = await NestFactory.createApplicationContext(AppModule);
  }
  return cachedApp;
}

// Helper function to create response
function createResponse(statusCode: number, body: any) {
  return {
    statusCode,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Credentials': true,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(body),
  };
}

type Handler = (event: APIGatewayProxyEvent, context: Context) => Promise<APIGatewayProxyResult>;

// Get all user groups
export const getAllUserGroups: Handler = async (event: APIGatewayProxyEvent, context: Context) => {
  try {
    const app = await bootstrap();
    const userGroupService = app.get(UserGroupService);
    const result = await userGroupService.findAll();
    return createResponse(200, result);
  } catch (error) {
    console.error('Error getting user groups:', error);
    return createResponse(500, { message: error.message || 'Internal server error' });
  }
};

// Create new user group
export const createUserGroup: Handler = async (event: APIGatewayProxyEvent, context: Context) => {
  try {
    const app = await bootstrap();
    const userGroupService = app.get(UserGroupService);
    const result = await userGroupService.create(JSON.parse(event.body || '{}'));
    return createResponse(201, result);
  } catch (error) {
    console.error('Error creating user group:', error);
    return createResponse(500, { message: error.message || 'Internal server error' });
  }
};

// Get user group by ID
export const getUserGroupById: Handler = async (event: APIGatewayProxyEvent, context: Context) => {
  try {
    const app = await bootstrap();
    const userGroupService = app.get(UserGroupService);
    const id = event.pathParameters?.id;
    if (!id) {
      return createResponse(400, { message: 'User group ID is required' });
    }
    const result = await userGroupService.findOne(id);
    return createResponse(200, result);
  } catch (error) {
    console.error('Error getting user group:', error);
    return createResponse(500, { message: error.message || 'Internal server error' });
  }
};

// Update user group
export const updateUserGroup: Handler = async (event: APIGatewayProxyEvent, context: Context) => {
  try {
    const app = await bootstrap();
    const userGroupService = app.get(UserGroupService);
    const id = event.pathParameters?.id;
    if (!id) {
      return createResponse(400, { message: 'User group ID is required' });
    }
    const result = await userGroupService.update(id, JSON.parse(event.body || '{}'));
    return createResponse(200, result);
  } catch (error) {
    console.error('Error updating user group:', error);
    return createResponse(500, { message: error.message || 'Internal server error' });
  }
};

// Delete user group
export const deleteUserGroup: Handler = async (event: APIGatewayProxyEvent, context: Context) => {
  try {
    const app = await bootstrap();
    const userGroupService = app.get(UserGroupService);
    const id = event.pathParameters?.id;
    if (!id) {
      return createResponse(400, { message: 'User group ID is required' });
    }
    await userGroupService.remove(id);
    return createResponse(204, {});
  } catch (error) {
    console.error('Error deleting user group:', error);
    return createResponse(500, { message: error.message || 'Internal server error' });
  }
};

// Update user group status
export const updateUserGroupStatus: Handler = async (
  event: APIGatewayProxyEvent,
  context: Context,
) => {
  try {
    const app = await bootstrap();
    const userGroupService = app.get(UserGroupService);
    const id = event.pathParameters?.id;
    if (!id) {
      return createResponse(400, { message: 'User group ID is required' });
    }
    const { active } = JSON.parse(event.body || '{}');
    const result = await userGroupService.updateStatus(id, active);
    return createResponse(200, result);
  } catch (error) {
    console.error('Error updating user group status:', error);
    return createResponse(500, { message: error.message || 'Internal server error' });
  }
};

// Toggle user group status
export const toggleUserGroupStatus: Handler = async (
  event: APIGatewayProxyEvent,
  context: Context,
) => {
  try {
    const app = await bootstrap();
    const userGroupService = app.get(UserGroupService);
    const id = event.pathParameters?.id;
    if (!id) {
      return createResponse(400, { message: 'User group ID is required' });
    }
    const result = await userGroupService.toggleStatus(id);
    return createResponse(200, result);
  } catch (error) {
    console.error('Error toggling user group status:', error);
    return createResponse(500, { message: error.message || 'Internal server error' });
  }
};

// Find active user groups
export const findActiveUserGroups: Handler = async (
  event: APIGatewayProxyEvent,
  context: Context,
) => {
  try {
    const app = await bootstrap();
    const userGroupService = app.get(UserGroupService);
    const result = await userGroupService.findActiveGroups();
    return createResponse(200, result);
  } catch (error) {
    console.error('Error finding active user groups:', error);
    return createResponse(500, { message: error.message || 'Internal server error' });
  }
};

// Find inactive user groups
export const findInactiveUserGroups: Handler = async (
  event: APIGatewayProxyEvent,
  context: Context,
) => {
  try {
    const app = await bootstrap();
    const userGroupService = app.get(UserGroupService);
    const result = await userGroupService.findInactiveGroups();
    return createResponse(200, result);
  } catch (error) {
    console.error('Error finding inactive user groups:', error);
    return createResponse(500, { message: error.message || 'Internal server error' });
  }
};

// Search user groups
export const searchUserGroups: Handler = async (event: APIGatewayProxyEvent, context: Context) => {
  try {
    const app = await bootstrap();
    const userGroupService = app.get(UserGroupService);
    const term = event.queryStringParameters?.term || '';
    const pageIndex = parseInt(event.queryStringParameters?.pageIndex || '1', 10);
    const pageSize = parseInt(event.queryStringParameters?.pageSize || '10', 10);

    const result = await userGroupService.searchByTerm(term, pageIndex, pageSize);
    return createResponse(200, result);
  } catch (error) {
    return createResponse(500, { message: error.message || 'Internal server error' });
  }
};

// Find groups with users
export const findGroupsWithUsers: Handler = async (
  event: APIGatewayProxyEvent,
  context: Context,
) => {
  try {
    const app = await bootstrap();
    const userGroupService = app.get(UserGroupService);
    const result = await userGroupService.findGroupsWithUsers();
    return createResponse(200, result);
  } catch (error) {
    console.error('Error finding groups with users:', error);
    return createResponse(500, { message: error.message || 'Internal server error' });
  }
};

// Find group with users
export const findGroupWithUsers: Handler = async (
  event: APIGatewayProxyEvent,
  context: Context,
) => {
  try {
    const app = await bootstrap();
    const userGroupService = app.get(UserGroupService);
    const id = event.pathParameters?.id;
    if (!id) {
      return createResponse(400, { message: 'User group ID is required' });
    }
    const result = await userGroupService.findGroupWithUsers(id);
    return createResponse(200, result);
  } catch (error) {
    console.error('Error finding group with users:', error);
    return createResponse(500, { message: error.message || 'Internal server error' });
  }
};

// Add users to group
export const addUsersToGroup: Handler = async (event: APIGatewayProxyEvent, context: Context) => {
  try {
    const app = await bootstrap();
    const userGroupService = app.get(UserGroupService);
    const id = event.pathParameters?.id;
    if (!id) {
      return createResponse(400, { message: 'User group ID is required' });
    }
    const { userIds } = JSON.parse(event.body || '{}');
    const result = await userGroupService.addUsers(id, userIds);
    return createResponse(200, result);
  } catch (error) {
    console.error('Error adding users to group:', error);
    return createResponse(500, { message: error.message || 'Internal server error' });
  }
};

// Remove users from group
export const removeUsersFromGroup: Handler = async (
  event: APIGatewayProxyEvent,
  context: Context,
) => {
  try {
    const app = await bootstrap();
    const userGroupService = app.get(UserGroupService);
    const id = event.pathParameters?.id;
    if (!id) {
      return createResponse(400, { message: 'User group ID is required' });
    }
    const { userIds } = JSON.parse(event.body || '{}');
    const result = await userGroupService.removeUsers(id, userIds);
    return createResponse(200, result);
  } catch (error) {
    console.error('Error removing users from group:', error);
    return createResponse(500, { message: error.message || 'Internal server error' });
  }
};

// Get all users
export const getAllUsers: Handler = async (event: APIGatewayProxyEvent, context: Context) => {
  try {
    const app = await bootstrap();
    const userService = app.get(UserService);
    const result = await userService.getUserList();
    return createResponse(200, result);
  } catch (error) {
    console.error('Error getting users:', error);
    return createResponse(500, { message: error.message || 'Internal server error' });
  }
};
