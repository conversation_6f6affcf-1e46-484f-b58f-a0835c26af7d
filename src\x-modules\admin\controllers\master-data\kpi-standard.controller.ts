import { Body, Param, Query, UseGuards } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { DefController, DefDelete, DefGet, DefPatch, DefPost, DefPut } from '~/@core/decorator';
import { KPIStandardService } from '../../services/master-data/kpi-standard.service';
import { KPIStandardCopyDTO, KPIStandardCreateDTO, PaginationReq } from '~/dto/kpi-standard.dto';
import { Roles } from '~/@core/decorator/roles.decorator';
import { RoleGuard } from '~/@systems/guard/role.guard';

@ApiTags('KPIStandard')
@DefController('kpi-standard')
export class KPIStandardController {
  constructor(private readonly kpiStandardService: KPIStandardService) {}

  @ApiOperation({
    summary: 'Pagination Standard Kpi',
    description: 'Get List Kpi set data by ID with filtering options',
  })
  @DefGet('pagination')
  @Roles('/master-data/kpi-standard', 'View')
  @UseGuards(RoleGuard)
  async getGeneralDataList(@Query() params: PaginationReq) {
    return this.kpiStandardService.pagination(params);
  }

  @ApiOperation({
    summary: 'Get all KPI Standard ',
    description: 'Get all KPI Standard data with pagination',
  })
  @DefGet('find/:id')
  @Roles('/master-data/kpi-standard', 'View')
  @UseGuards(RoleGuard)
  async find(@Param('id') id: string) {
    return this.kpiStandardService.find(id);
  }

  @ApiOperation({
    summary: 'Get all KPI Score ',
    description: 'Get all KPI Score',
  })
  @DefPost('findByGroupId')
  @Roles('/master-data/kpi-standard', 'View')
  @UseGuards(RoleGuard)
  async findByGroupId(@Body() body: { kpiSetId: string; groupId: string }) {
    const { kpiSetId, groupId } = body;
    return this.kpiStandardService.findByGroupId(kpiSetId, groupId);
  }

  @ApiOperation({
    summary: 'Find by Id ',
    description: 'Find by Id KPI Standard',
  })
  @DefGet('find-by-id/:id')
  @Roles('/master-data/kpi-standard', 'View')
  @UseGuards(RoleGuard)
  async findById(@Param('id') id: string) {
    return this.kpiStandardService.findById(id);
  }

  @DefGet('load-data-kpi-set')
  @Roles('/master-data/kpi-standard', 'View')
  @UseGuards(RoleGuard)
  async findAll() {
    return await this.kpiStandardService.loadDataKpiSet();
  }

  @DefGet('load-data-kpi-set-first/:kpiSetId')
  @Roles('/master-data/kpi-standard', 'View')
  @UseGuards(RoleGuard)
  async findOne(@Param('kpiSetId') kpiSetId: string) {
    return await this.kpiStandardService.loadDataKpiSetFirst(kpiSetId);
  }
  @DefPost('create')
  @Roles('/master-data/kpi-standard', 'View')
  @UseGuards(RoleGuard)
  async create(@Body() data: KPIStandardCreateDTO) {
    return this.kpiStandardService.create(data);
  }
  @ApiOperation({
    summary: 'Kiểm tra kpi standard score',
    description: 'Kiểm tra xem kpi set detail đã được tạo data trong kpi standard chưa',
  })
  @DefPost('copy')
  @Roles('/master-data/kpi-standard', 'View')
  @UseGuards(RoleGuard)
  async copyKpiTargets(@Body() data: KPIStandardCopyDTO) {
    return this.kpiStandardService.copyKpiTargets(data);
  }
  @ApiOperation({
    summary: 'Kiểm tra kpi standard score',
    description: 'Kiểm tra xem kpi set detail đã được tạo data trong kpi standard chưa',
  })
  @DefPost('update')
  @Roles('/master-data/kpi-standard', 'View')
  @UseGuards(RoleGuard)
  async update(@Body() data: KPIStandardCopyDTO) {
    return this.kpiStandardService.update(data);
  }
  @ApiOperation({
    summary: 'Kiểm tra kpi standard score',
    description: 'Kiểm tra xem kpi set detail đã được tạo data trong kpi standard chưa',
  })
  @DefGet('check-kpi-set-detail/:id')
  @Roles('/master-data/kpi-standard', 'View')
  @UseGuards(RoleGuard)
  async checkTakenKpiScoreByKpiSetDetail(@Param('id') id: string) {
    return this.kpiStandardService.checkTakenKpiScoreByKpiSetDetail(id);
  }

  @ApiOperation({
    summary: 'Kiểm tra kpi standard',
    description: 'Kiểm tra xem kpi set group đã được tạo data trong kpi standard chưa',
  })
  @DefGet('check-kpi-set-group/:id')
  @Roles('/master-data/kpi-standard', 'View')
  @UseGuards(RoleGuard)
  async checkTakenKpiStandardByKpiSetGroup(@Param('id') id: string) {
    return this.kpiStandardService.checkTakenKpiStandardByKpiSetGroup(id);
  }

  @ApiOperation({
    summary: 'Kiểm tra kpi standard',
    description: 'Kiểm tra xem kpi set đã được tạo data trong kpi standard chưa',
  })
  @DefGet('check-kpi-set/:id')
  @Roles('/master-data/kpi-standard', 'View')
  @UseGuards(RoleGuard)
  async checkTakenKpiStandardByKpiSet(@Param('id') id: string) {
    return this.kpiStandardService.checkTakenKpiStandardByKpiSet(id);
  }

  @ApiOperation({
    summary: 'Delete',
    description: 'Kiểm tra xem kpi standard score đã tồn tại chưa',
  })
  @DefDelete('delete/:id')
  @Roles('/master-data/kpi-standard', 'View')
  @UseGuards(RoleGuard)
  delete(@Param('id') id: string) {
    return this.kpiStandardService.delete(id);
  }
}
