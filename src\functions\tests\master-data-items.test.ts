describe('batchCreateOrUpdate', () => {
  it('should throw error due to validate fail', async () => {
    fetch('http://localhost:3000/dev/integration/erp/items/batch', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        items: [
          {
            item_code: 'TEST ITEM',
            item_name: 'Test Item',
            item_type: 'finished_good',
            uom: 'PCS',
            item_group: 'TEST GROUP',
          },
        ],
      }),
    })
      .then(response =>
        response.json().then(data => {
          console.log('Response:', data);
          expect(response.ok).toBe(false);
          expect(data).toHaveProperty('errors');
        }),
      )
      .catch(error => {
        console.error('Error:', error);
      });
  });
  it('should create or update item', async () => {
    fetch('http://localhost:3000/dev/integration/erp/items/batch', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify([
        {
          ItemCode: '1001',
          ItemName: 'Item 4',
          ItemType: 'RM',
          Group: 'Cá',
          Category: '',
          Brand: '',
          BaseUnit: 'Lit',
          InventoryUnit: 'Lit',
          MHUTypes: [
            {
              FromUnit: 'Lit',
              ToUnit: 'Kgs',
              Conversion: '1',
            },
          ],
          ItemStatus: 'Active',
        },
      ]),
    })
      .then(response =>
        response.json().then(data => {
          console.log('Response:', data);
          expect(response.ok).toBe(true);
          expect(data).toHaveProperty('items');
        }),
      )
      .catch(error => {
        console.error('Error:', error);
      });
  });
});
