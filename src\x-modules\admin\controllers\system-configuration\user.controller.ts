import {
  Body,
  Get,
  HttpException,
  Param,
  Post,
  Put,
  Query,
  UploadedFile,
  UseGuards,
  UseInterceptors,
} from '@nestjs/common';
import { FileInterceptor } from '@nestjs/platform-express';
import { ApiConsumes, ApiOperation, ApiTags } from '@nestjs/swagger';
import { DefController } from '~/@core/decorator';
import { ICreateUserRequest, IFilterGetUserList, IUpdateUserRequest } from '~/functions/user';
import { UserService } from '../../services/system-configuration/user.service';
import { Roles } from '~/@core/decorator/roles.decorator';
import { RoleGuard } from '~/@systems/guard/role.guard';

@ApiTags('User')
@DefController('users')
export class UserController {
  constructor(private readonly userService: UserService) {}

  @Get()
  @Roles('/system-configuration/user', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: '<PERSON><PERSON>y danh sách user' })
  async getUserList(@Query() params: IFilterGetUserList) {
    const result = await this.userService.getUserList(params);
    return {
      data: result.items,
      pagination: {
        total: result.total,
        page: result.page,
        limit: result.limit,
        totalPages: result.totalPages,
      },
    };
  }

  @Get(':id')
  @Roles('/system-configuration/user', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Lấy thông tin chi tiết user' })
  async getUser(@Param('id') id: string) {
    const result = await this.userService.getUser(id);
    if (!result) {
      throw new HttpException('User không tồn tại!', 404);
    }
    return { data: result };
  }

  @Post()
  @Roles('/system-configuration/user', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Tạo mới user' })
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('avatarFile'))
  async createUser(
    @Body() user: Omit<ICreateUserRequest, 'avatarFile'>,
    @UploadedFile() avatarFile?: Express.Multer.File,
  ) {
    try {
      await this.userService.createUser(user, avatarFile);
      return { message: 'success' };
    } catch (error) {
      throw new HttpException(error.message, 500);
    }
  }

  @Put(':id')
  @Roles('/system-configuration/user', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Cập nhật thông tin user' })
  @ApiConsumes('multipart/form-data')
  @UseInterceptors(FileInterceptor('avatarFile'))
  async updateUser(
    @Param('id') id: string,
    @Body() updateUser: IUpdateUserRequest,
    @UploadedFile() avatarFile?: Express.Multer.File,
  ) {
    const user = await this.userService.getUser(id);
    if (!user) {
      throw new HttpException('User không tồn tại!', 404);
    }
    try {
      await this.userService.updateUser(user, updateUser, avatarFile);
    } catch (error) {
      throw new HttpException(error.message, 500);
    }
    return { message: 'success' };
  }

  @Post('upload-image')
  @Roles('/system-configuration/user', 'View')
  @UseGuards(RoleGuard)
  @UseInterceptors(FileInterceptor('file'))
  @ApiOperation({ summary: 'Upload image' })
  async uploadImage(@UploadedFile() file: Express.Multer.File) {
    const fileName = file.originalname;
    return await this.userService.uploadImage(file, fileName);
  }
}
