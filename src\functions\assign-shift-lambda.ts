import { NestFactory } from '@nestjs/core';
import { Context, Handler } from 'aws-lambda';
import { AssignShiftService } from '~/x-modules/admin/services';
import { AppModule } from '../app.module';

let cachedApp: any = null;

async function bootstrap() {
  if (!cachedApp) {
    const app = await NestFactory.createApplicationContext(AppModule);
    await app.init();
    cachedApp = app;
  }
  return cachedApp;
}

export const createAssignShift: Handler = async (event: any, context: Context) => {
  const app = await bootstrap();
  const assignShiftService = app.get(AssignShiftService);
  const assignShiftData = JSON.parse(event.body);
  const result = await assignShiftService.create(assignShiftData);
  return {
    statusCode: 201,
    body: JSON.stringify(result),
  };
};

export const updateAssignShift: Handler = async (event: any, context: Context) => {
  const app = await bootstrap();
  const assignShiftService = app.get(AssignShiftService);
  const { id, ...updateData } = JSON.parse(event.body);
  const result = await assignShiftService.update(parseInt(id), updateData);
  return {
    statusCode: 200,
    body: JSON.stringify(result),
  };
};

export const findAssignShiftById: Handler = async (event: any, context: Context) => {
  const app = await bootstrap();
  const assignShiftService = app.get(AssignShiftService);
  const { id } = event.pathParameters;
  const result = await assignShiftService.findOne(parseInt(id));
  return {
    statusCode: result ? 200 : 404,
    body: JSON.stringify(result || { message: 'Assign shift not found' }),
  };
};

export const findAllAssignShifts: Handler = async (event: any, context: Context) => {
  const app = await bootstrap();
  const assignShiftService = app.get(AssignShiftService);
  const result = await assignShiftService.findAll();
  return {
    statusCode: 200,
    body: JSON.stringify(result),
  };
};

export const deleteAssignShift: Handler = async (event: any, context: Context) => {
  const app = await bootstrap();
  const assignShiftService = app.get(AssignShiftService);
  const { id } = event.pathParameters;
  await assignShiftService.delete(parseInt(id));
  return {
    statusCode: 204,
    body: JSON.stringify({ message: 'Assign shift deleted successfully' }),
  };
};
