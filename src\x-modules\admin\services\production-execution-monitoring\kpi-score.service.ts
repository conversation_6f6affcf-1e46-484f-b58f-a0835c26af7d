import { HttpException, HttpStatus, Injectable } from '@nestjs/common';
import { In, Raw, Between, LessThanOrEqual, MoreThanOrEqual } from 'typeorm';
import { BindRepo, DefTransaction } from '~/@core/decorator';
import { NSGeneralData } from '~/common/enums';
import { dayjs, TZ } from '~/common/helpers/date.helper';
import {
  GeneralDataDetailRepo,
  GeneralDataRepo,
  KpiSetHeaderRepo,
  KpiSetProductAreaRepo,
  OrganizationUnitRepo,
  ProductionAreaRepo,
  KpiRepo,
  KPIPeriodRepo,
  KpiSetDetailRepo,
  KpiScoreRepo,
} from '~/repositories/primary';
import { KpiScoreReq } from '~/dto/kpi-score.dto';
import { AssignShiftRepository } from '~/repositories/primary/assign-shift.repo';
import { adminSessionContext } from '~/x-modules/admin/admin-session.context';
import { ShiftRepo } from '~/repositories/primary/shift.repo';
import { AccessRepo } from '~/repositories/primary/access.repo';

@Injectable()
export class KpiScoreService {
  constructor() {}

  @BindRepo(KpiScoreRepo)
  private kpiScoreRepo: KpiScoreRepo;
  @BindRepo(KpiSetHeaderRepo)
  private kpiSetHeaderRepo: KpiSetHeaderRepo;
  @BindRepo(KpiSetDetailRepo)
  private kpiSetDetailRepo: KpiSetDetailRepo;
  @BindRepo(KpiRepo)
  private kpiRepo: KpiRepo;
  @BindRepo(KPIPeriodRepo)
  private kpiPeriodRepo: KPIPeriodRepo;
  @BindRepo(KpiSetProductAreaRepo)
  private kpiSetProductionAreaRepo: KpiSetProductAreaRepo;
  @BindRepo(OrganizationUnitRepo)
  private organizationUnitRepo: OrganizationUnitRepo;
  @BindRepo(ProductionAreaRepo)
  private productionAreaRepo: ProductionAreaRepo;
  @BindRepo(ShiftRepo)
  private shiftRepo: ShiftRepo;
  @BindRepo(AssignShiftRepository)
  private assignShiftRepo: AssignShiftRepository;
  @BindRepo(GeneralDataDetailRepo)
  private generalDataDetailRepo: GeneralDataDetailRepo;
  @BindRepo(GeneralDataRepo)
  private generalDataRepo: GeneralDataRepo;
  @BindRepo(AccessRepo)
  private accessRepo: AccessRepo;

  @DefTransaction()
  async save(body: KpiScoreReq) {
    const { userId: userId } = adminSessionContext;
    const {
      id,
      siteId,
      factoryId,
      productionAreaId,
      kpiSetHeaderId,
      kpiSetGroupId,
      kpiSetDetailId,
      kpiId,
      shiftId,
      scoreDate,
      actualScore,
    } = body;
    const scoreDateFormatted =
      scoreDate && dayjs(scoreDate).isValid()
        ? dayjs(scoreDate).utc().startOf('day').toDate()
        : null;

    // const checkSave = await this.checkKpiScoreSet({
    //   factoryId: body?.factoryId,
    //   productionAreaId: body?.productionAreaId,
    //   kpiSetHeaderId: body?.kpiSetHeaderId,
    //   shiftId: body?.shiftId,
    //   startDate: scoreDate,
    // });

    // if (!checkSave.meta.canInput) {
    //   throw new HttpException(
    //     {
    //       statusCode: HttpStatus.BAD_REQUEST,
    //       message: 'Kỳ KPI chưa mở, không thể nhập điểm',
    //       error: 'Bad Request',
    //     },
    //     HttpStatus.BAD_REQUEST,
    //   );
    // }

    // Kiểm tra xem có bản ghi nào trùng với scoreDate không, ngoại trừ bản ghi hiện tại (nếu có)
    const existingRecord = await this.kpiScoreRepo.findOne({
      where: {
        scoreDate: scoreDateFormatted,
        siteId: siteId,
        factoryId: factoryId,
        productionAreaId: productionAreaId,
        kpiSetHeaderId: kpiSetHeaderId,
        kpiSetGroupId: kpiSetGroupId,
        kpiSetDetailId: kpiSetDetailId,
        kpiId: kpiId,
        shiftId: shiftId,
      },
    });

    if (existingRecord && existingRecord.id !== id) {
      throw new HttpException(
        {
          statusCode: HttpStatus.BAD_REQUEST,
          message: 'Đã có bản ghi với ngày điểm này.',
          error: 'Bad Request',
        },
        HttpStatus.BAD_REQUEST,
      );
    }

    const openPeriod = await this.kpiPeriodRepo
      .createQueryBuilder('kpi')
      .where('"kpi"."status" = :status', { status: 1 })
      .andWhere('"kpi"."startDate"::date <= :scoreDate::date', { scoreDate })
      .andWhere('"kpi"."endDate"::date >= :scoreDate::date', { scoreDate })
      .getOne();

    if (!openPeriod) {
      throw new HttpException(
        {
          statusCode: HttpStatus.BAD_REQUEST,
          message: 'Kỳ KPI chưa mở, không thể nhập điểm',
          error: 'Bad Request',
        },
        HttpStatus.BAD_REQUEST,
      );
    }

    let entity;

    if (id) {
      entity = await this.kpiScoreRepo.findOne({ where: { id: id } });

      if (!entity) {
        throw new Error('Bản ghi không tồn tại');
      }

      entity.shiftId = shiftId;
      entity.scoreDate = scoreDateFormatted;
      entity.actualScore = actualScore;
      entity.kpiPeriodId = openPeriod?.id;
      entity.updatedBy = userId;
    } else {
      entity = this.kpiScoreRepo.create({
        siteId: siteId,
        factoryId: factoryId,
        productionAreaId: productionAreaId,
        kpiSetHeaderId: kpiSetHeaderId,
        kpiSetGroupId: kpiSetGroupId,
        kpiSetDetailId: kpiSetDetailId,
        kpiId: kpiId,
        shiftId: shiftId,
        scoreDate: scoreDateFormatted,
        actualScore: actualScore,
        kpiPeriodId: openPeriod?.id,
      });
      entity.createdBy = userId;
    }

    const saved = await this.kpiScoreRepo.save(entity);

    return {
      data: saved,
      meta: {
        message: id ? 'Cập nhật thành công' : 'Lưu thành công',
        request: body,
        timestamp: new Date(),
      },
    };
  }

  async getAll() {
    const scores = await this.kpiScoreRepo.find();
    return { data: scores, meta: { total: scores.length } };
  }

  async search(body: any) {
    const {
      siteId,
      factoryId,
      productionAreaId,
      kpiSetHeaderId,
      kpiId,
      shiftId,
      startDate,
      endDate,
      userId,
    } = body;

    const formattedStartDate =
      startDate && dayjs(startDate).isValid()
        ? dayjs(startDate).utc().startOf('day').toDate()
        : null;
    const formattedEndDate =
      endDate && dayjs(endDate).isValid() ? dayjs(endDate).utc().endOf('day').toDate() : null;

    if (!userId) return { data: [], meta: { total: 0 } };
    const lstAccess = await this.accessRepo.find({
      where: { user: { id: userId }, status: true },
      relations: ['organizationUnit', 'user'],
    });
    if (lstAccess.length === 0) return { data: [], meta: { total: 0 } };
    const lstFactory = lstAccess.map((item) => item.organizationUnit.id);
    const [scoresResult, kpiSetProductionAreasResult] = await Promise.allSettled([
      this.kpiScoreRepo.find({
        where: {
          ...(siteId && { siteId: siteId }),
          ...(factoryId ? { factoryId: factoryId } : { factoryId: In(lstFactory) }),
          ...(productionAreaId && { productionAreaId: productionAreaId }),
          ...(kpiSetHeaderId && { kpiSetHeaderId: kpiSetHeaderId }),
          ...(kpiId && { kpiId: kpiId }),
          ...(shiftId && { shiftId: shiftId }),
          ...(startDate && endDate
            ? { scoreDate: Between(formattedStartDate, formattedEndDate) }
            : {}),
          ...(startDate && !endDate ? { scoreDate: MoreThanOrEqual(formattedStartDate) } : {}),
          ...(endDate && !startDate ? { scoreDate: LessThanOrEqual(formattedEndDate) } : {}),
        },
        relations: ['kpi', 'kpiSetHeader', 'shift'],
        // order: {
        //   productionAreaId: 'ASC',
        //   kpiSetHeaderId: 'ASC',
        //   shiftId: 'ASC',
        //   kpiId: 'ASC',
        //   scoreDate: 'DESC',
        //   updatedDate: 'DESC',
        // },
      }),
      this.kpiSetProductionAreaRepo.find({
        where: {
          ...(siteId && { siteId: siteId }),
          ...(factoryId && { factoryId: factoryId }),
          ...(productionAreaId && { productionAreaId: productionAreaId }),
          ...(kpiSetHeaderId && { kpiSetHeaderId: kpiSetHeaderId }),
          status: 1,
        },
        relations: [
          'kpiSetHeader',
          'kpiSetHeader.kpiSetGroups',
          'kpiSetHeader.kpiSetGroups.details',
          'kpiSetHeader.kpiSetGroups.details.kpi',
          'site',
          'factory',
          'productionArea',
        ],
      }),
    ]);

    const scores = scoresResult.status === 'fulfilled' ? scoresResult.value : [];
    const kpiSetProductionAreas =
      kpiSetProductionAreasResult.status === 'fulfilled' ? kpiSetProductionAreasResult.value : [];

    // Tạo Map để nhóm điểm số theo từng `kpiSetHeader`, `kpi` và `productionArea`
    const scoreMap = new Map<string, any[]>();

    for (const score of scores) {
      const kpiSetHeader = await score.kpiSetHeader;
      const kpi = await score.kpi;
      const productionAreaId = score.productionAreaId;

      if (!kpiSetHeader || !kpi || !productionAreaId) continue;

      const key = `${kpiSetHeader.id}-${kpi.id}-${productionAreaId}`;

      if (!scoreMap.has(key)) {
        scoreMap.set(key, []);
      }

      scoreMap.get(key)?.push({
        ...score,
        id: score.id,
        shiftId: score.shiftId,
        shiftCode: (await score.shift).code,
        shiftName: (await score.shift).description,
        scoreDate: score.scoreDate,
        actualScore: score.actualScore,
      });
    }

    // Xử lý danh sách KPI cần chấm điểm (details)
    const detailsPromises = kpiSetProductionAreas.flatMap((setArea) => {
      const header = setArea.kpiSetHeader;

      return (
        header?.kpiSetGroups?.flatMap((group) =>
          group?.details?.map((detail) => {
            const key = `${header.id}-${detail.kpi?.id}-${setArea.productionAreaId}`;
            const matchedScores = scoreMap.get(key) || [];

            // Chuẩn bị các thông tin chung cho bản ghi
            const baseRecord = {
              siteId: setArea.siteId,
              site: `${setArea.site?.code ?? null}`,
              factoryId: setArea.factoryId,
              factory: `${setArea.factory?.code ?? null}`,
              productionAreaId: setArea.productionAreaId,
              productionArea: `${setArea.productionArea?.code ?? null}  - ${setArea.productionArea?.name ?? null}`,
              kpiSetHeaderId: header?.id,
              kpiSetHeader: `${header?.code} - ${header?.longName}`,
              kpiSetGroupId: group?.id,
              kpiSetGroup: `${group?.kpiGroupCode} - ${group?.kpiGroupName}`,
              kpiId: detail.kpi?.id ?? null,
              kpiCode: detail.kpi?.code ?? null,
              kpiName: detail.kpi?.longName ?? null,
              unit: detail.kpi?.unit ?? null,
              inputFrequency: detail.kpi?.inputFrequency ?? null,
              kpiMethod: detail.kpi?.kpiMethod ?? null,
              kpiSetDetailId: detail.id,
              kpiSetDetail: detail.kpiSetDetailId,
            };

            // Luôn luôn trả về mảng, bất kể có bài chấm hay không
            let result;

            if (matchedScores.length === 0) {
              result = [];
            } else {
              result = matchedScores.map((score) => ({
                ...baseRecord,
                ...score,
              }));
            }

            return result;
          }),
        ) ?? []
      );
    });

    // Chờ tất cả các Promise hoàn thành rồi làm phẳng mảng
    const details = (await Promise.all(detailsPromises)).flat();
    details.sort((a, b) => {
      const compareProductionArea = (a.productionArea ?? '').localeCompare(b.productionArea ?? '');
      if (compareProductionArea !== 0) return compareProductionArea;

      const compareHeader = (a.kpiSetHeader ?? '').localeCompare(b.kpiSetHeader ?? '');
      if (compareHeader !== 0) return compareHeader;

      const compareKpiCode = (a.kpiCode ?? '').localeCompare(b.kpiCode ?? '');
      if (compareKpiCode !== 0) return compareKpiCode;

      const compareShift = (a.shiftCode ?? '').localeCompare(b.shiftCode ?? '');
      if (compareShift !== 0) return compareShift;

      return new Date(b.scoreDate).getTime() - new Date(a.scoreDate).getTime(); // Mới nhất trước
    });

    return {
      data: details,
      meta: {
        total: details.length,
        sampleItem: details[0] ?? null,
        scores,
        kpiSetProductionAreas,
        request: body,
      },
    };
  }

  async checkKpiScoreSet(body: any) {
    const { factoryId, productionAreaId, kpiSetHeaderId, shiftId, startDate } = body;

    const scores = await this.kpiScoreRepo.find({
      where: {
        ...(factoryId && { factoryId: factoryId }),
        ...(productionAreaId && { productionAreaId: productionAreaId }),
        ...(kpiSetHeaderId && { kpiSetHeaderId: kpiSetHeaderId }),
        ...(shiftId && { shiftId: shiftId }),
      },
      relations: ['kpiPeriod'],
    });

    const inputDate = dayjs(startDate).utc().startOf('day');

    const openScores = (
      await Promise.all(
        scores.map(async (score) => {
          const period = await score.kpiPeriod;
          if (!period || period.status !== 1) return null;

          const periodStart = dayjs(period.startDate).utc().startOf('day');
          const periodEnd = dayjs(period.endDate).utc().endOf('day');

          const isInPeriod =
            inputDate.isSameOrAfter(periodStart) && inputDate.isSameOrBefore(periodEnd);

          return isInPeriod ? score : null;
        }),
      )
    ).filter((s): s is (typeof scores)[number] => s !== null);

    const canInput = openScores.length === 0;

    return {
      data: openScores,
      total: openScores.length,
      meta: {
        canInput,
        total: openScores.length,
        request: body,
      },
    };
  }

  @DefTransaction()
  async saveKpiScoreSet(body: KpiScoreReq[]) {
    const savedItems = [];

    for (const item of body) {
      const saved = await this.save(item);
      savedItems.push(saved);
    }

    return savedItems;
  }

  async setSearch(body: any) {
    const { siteId, factoryId, productionAreaId, kpiSetHeaderId, shiftId, startDate } = body;
    const formattedStartDate =
      startDate && dayjs(startDate).isValid()
        ? dayjs(startDate).utc().startOf('day').toDate()
        : null;
    const shift = shiftId ? await this.shiftRepo.findOne({ where: { id: shiftId } }) : null;

    const [kpiSetProductionAreasResult] = await Promise.allSettled([
      this.kpiSetProductionAreaRepo.find({
        where: {
          ...(siteId && { siteId: siteId }),
          ...(factoryId && { factoryId: factoryId }),
          ...(productionAreaId && { productionAreaId: productionAreaId }),
          ...(kpiSetHeaderId && { kpiSetHeaderId: kpiSetHeaderId }),
          status: 1,
        },
        relations: [
          'kpiSetHeader',
          'kpiSetHeader.kpiSetGroups',
          'kpiSetHeader.kpiSetGroups.details',
          'kpiSetHeader.kpiSetGroups.details.kpi',
          'site',
          'factory',
          'productionArea',
        ],
      }),
    ]);

    const kpiSetProductionAreas =
      kpiSetProductionAreasResult.status === 'fulfilled' ? kpiSetProductionAreasResult.value : [];

    // Xử lý danh sách KPI cần chấm điểm (details)
    const detailsPromises = kpiSetProductionAreas.flatMap((setArea) => {
      const header = setArea.kpiSetHeader;

      return (
        header?.kpiSetGroups?.flatMap((group) =>
          group?.details
            ?.filter((detail) => detail.kpi?.kpiMethod !== 'A')
            .map((detail) => {
              const key = `${header.id}-${detail.kpi?.id}-${setArea.productionAreaId}`;

              const baseRecord = {
                siteId: setArea.siteId,
                site: `${setArea.site?.code ?? null}`,
                factoryId: setArea.factoryId,
                factory: `${setArea.factory?.code ?? null}`,
                productionAreaId: setArea.productionAreaId,
                productionArea: `${setArea.productionArea?.code ?? null}  - ${setArea.productionArea?.name ?? null}`,
                kpiSetHeaderId: header?.id,
                kpiSetHeader: `${header?.code} - ${header?.longName}`,
                kpiSetGroupId: group?.id,
                kpiSetGroup: `${group?.kpiGroupCode} - ${group?.kpiGroupName}`,
                kpiId: detail.kpi?.id ?? null,
                kpiCode: detail.kpi?.code ?? null,
                kpiName: detail.kpi?.longName ?? null,
                unit: detail.kpi?.unit ?? null,
                inputFrequency: detail.kpi?.inputFrequency ?? null,
                kpiMethod: detail.kpi?.kpiMethod ?? null,
                kpiSetDetailId: detail.id,
                kpiSetDetail: detail.kpiSetDetailId,
              };

              return [
                {
                  ...baseRecord,
                  id: null,
                  shiftId: shiftId,
                  shift: shift?.code ?? null,
                  scoreDate: formattedStartDate,
                  actualScore: null,
                },
              ];
            }),
        ) ?? []
      );
    });

    // Chờ tất cả các Promise hoàn thành rồi làm phẳng mảng
    const details = (await Promise.all(detailsPromises)).flat();
    details.sort((a, b) => {
      const compareProductionArea = (a.productionArea ?? '').localeCompare(b.productionArea ?? '');
      if (compareProductionArea !== 0) return compareProductionArea;

      const compareHeader = (a.kpiSetHeader ?? '').localeCompare(b.kpiSetHeader ?? '');
      if (compareHeader !== 0) return compareHeader;

      const compareKpiCode = (a.kpiCode ?? '').localeCompare(b.kpiCode ?? '');
      if (compareKpiCode !== 0) return compareKpiCode;

      const compareShift = (a.shift ?? '').localeCompare(b.shift ?? '');
      if (compareShift !== 0) return compareShift;

      return new Date(b.scoreDate).getTime() - new Date(a.scoreDate).getTime(); // Mới nhất trước
    });

    return {
      data: details,
      meta: {
        total: details.length,
        sampleItem: details[0] ?? null,
        kpiSetProductionAreas,
        request: body,
      },
    };
  }

  async getOrganizationUnitWithProductionAreas() {
    const lstGeneralData = await this.generalDataRepo.find({
      where: { code: In([NSGeneralData.EGeneralDataCode.ORG_LEVEL]) },
    });

    const orgLevel = lstGeneralData.find(
      (gen) => gen.code === NSGeneralData.EGeneralDataCode.ORG_LEVEL,
    );

    const [generalDetails, organizationUnits, productionAreas]: any = await Promise.all([
      this.generalDataDetailRepo.find({
        where: {
          generalId: In([orgLevel?.id]),
          isActive: true,
        },
        select: ['id', 'code', 'name', 'generalId'],
      }),
      this.organizationUnitRepo.find({
        where: { isActive: true },
        select: ['id', 'code', 'name', 'parentId', 'levelId'],
      }),
      this.productionAreaRepo.find({
        where: { isActive: true },
        relations: ['organization'],
        order: { code: 'ASC', createdDate: 'DESC' },
      }),
    ]);

    // Phân loại dữ liệu
    const levelFactory = generalDetails.find(
      (detail) =>
        detail.generalId === orgLevel?.id && detail.code === NSGeneralData.EOrgLevel.FACTORY,
    );
    const levelSite = generalDetails.find(
      (detail) => detail.generalId === orgLevel?.id && detail.code === NSGeneralData.EOrgLevel.SITE,
    );

    // Phân loại factories và sites từ organizationUnits
    const factories = organizationUnits.filter((unit) => unit.levelId === levelFactory?.id);
    const sites = organizationUnits.filter((unit) => unit.levelId === levelSite?.id);
    const mapSite = new Map(sites.map((site) => [site.id, site]));

    // Gán thông tin factory và site cho productionAreas
    for (let item of productionAreas) {
      item.factoryId = item.organization?.id;
      item.factoryCode = item.organization?.code;
      item.factoryName = item.organization?.name;

      const curSite: any = mapSite.get(item.organization?.parentId);
      item.siteId = curSite?.id;
      item.siteCode = curSite?.code;
      item.siteName = curSite?.name;
      delete item.organization;
    }

    return { data: { sites, factories, productionAreas } };
  }

  async getSites({ userId }: { userId?: string }) {
    if (!userId) return { data: [], meta: { total: 0 } };
    const lstAccess = await this.accessRepo.find({
      where: { user: { id: userId }, status: true },
      relations: ['organizationUnit', 'user'],
    });
    if (lstAccess.length === 0) return { data: [], meta: { total: 0 } };
    const lstFactoryId = lstAccess.map((item) => item.organizationUnit.id);
    const lstFactory = await this.organizationUnitRepo.find({
      where: { id: In(lstFactoryId), isActive: true },
    });
    const sites = await this.organizationUnitRepo.find({
      where: {
        id: In(lstFactory.map((i) => i.parentId)),
        levelGeneralDataDetailCode: NSGeneralData.EOrgLevel.SITE,
        isActive: true,
      },
      order: { code: 'ASC', createdDate: 'DESC' },
    });
    return { data: sites, meta: { total: sites.length } };
  }

  async getFactories({ siteId, userId }: { siteId?: string; userId?: string }) {
    if (!userId) return { data: [], meta: { total: 0 } };
    const lstAccess = await this.accessRepo.find({
      where: { user: { id: userId }, status: true },
      relations: ['organizationUnit', 'user'],
    });
    if (lstAccess.length === 0) return { data: [], meta: { total: 0 } };
    const lstFactory = lstAccess.map((item) => item.organizationUnit.id);
    const factories = await this.organizationUnitRepo.find({
      where: {
        id: In(lstFactory),
        levelGeneralDataDetailCode: NSGeneralData.EOrgLevel.FACTORY,
        ...(siteId && { parentId: siteId }),
        isActive: true,
      },
      order: { code: 'ASC', createdDate: 'DESC' },
    });
    return { data: factories, meta: { total: factories.length } };
  }

  async getProductionAreas({
    factoryId,
    kpiSetHeaderId,
    userId,
  }: {
    factoryId?: string;
    kpiSetHeaderId?: string;
    userId?: string;
  }) {
    if (!userId) return { data: [], meta: { total: 0 } };
    const lstAccess = await this.accessRepo.find({
      where: { user: { id: userId }, status: true },
      relations: ['organizationUnit', 'user'],
    });
    if (lstAccess.length === 0) return { data: [], meta: { total: 0 } };
    const lstFactory = lstAccess.map((item) => item.organizationUnit.id);
    const lstArea = await this.kpiSetProductionAreaRepo.find({
      where: {
        ...(kpiSetHeaderId && { kpiSetHeaderId: kpiSetHeaderId }),
        status: 1 || true,
      },
      select: ['productionAreaId'],
    });

    if (lstArea.length === 0) {
      return { data: [], meta: { total: 0 } };
    }

    const productionAreas = await this.productionAreaRepo.find({
      where: {
        id: In(lstArea.map((pro) => pro.productionAreaId)),
        ...(factoryId ? { organizationUnitId: factoryId } : { organizationUnitId: In(lstFactory) }),
        isActive: true,
      },
      relations: ['organization'],
      order: { code: 'ASC', createdDate: 'DESC' },
    });

    const areas = productionAreas.map((area) => ({
      id: area.id,
      code: area.code,
      name: area.name,
      factoryId: area.organization?.id,
      factoryCode: area.organization?.code,
      factoryName: area.organization?.name,
    }));

    return { data: areas, meta: { total: areas.length } };
  }

  async getKpiSetHeaders({
    productionAreaId,
    userId,
  }: {
    productionAreaId?: string;
    userId?: string;
  }) {
    if (!userId) return { data: [], meta: { total: 0 } };
    const lstAccess = await this.accessRepo.find({
      where: { user: { id: userId }, status: true },
      relations: ['organizationUnit', 'user'],
    });
    if (lstAccess.length === 0) return { data: [], meta: { total: 0 } };
    const lstFactory = lstAccess.map((item) => item.organizationUnit.id);
    const lstProductionArea = await this.productionAreaRepo.find({
      where: { organizationUnitId: In(lstFactory) },
    });
    if (lstProductionArea.length === 0) return { data: [], meta: { total: 0 } };
    const lstArea = await this.kpiSetProductionAreaRepo.find({
      where: {
        ...(productionAreaId
          ? { productionAreaId: productionAreaId }
          : { productionAreaId: In(lstProductionArea.map((i) => i.id)) }),
        status: 1 || true,
      },
      select: ['kpiSetHeaderId'],
    });

    if (lstArea.length === 0) {
      return { data: [], total: 0 };
    }

    const kpiSetHeader: any = await this.kpiSetHeaderRepo.find({
      where: { id: In(lstArea.map((pro) => pro.kpiSetHeaderId)), status: 1 || true },
      order: { code: 'ASC', createdDate: 'DESC' },
    });

    return { data: kpiSetHeader, meta: { total: kpiSetHeader.length } };
  }

  async getKpis() {
    const lstKpi = await this.kpiRepo.find({ where: { status: 1 || true } });

    return { data: lstKpi, meta: { total: lstKpi.length } };
  }

  async getShifts({ factoryId }: { factoryId?: string }) {
    const assignShifts = await this.assignShiftRepo.find({
      where: {
        ...(factoryId && { organizationId: factoryId }),
        status: true,
      },
      relations: ['shift'],
    });

    const shifts = assignShifts
      .map(({ shift, organizationId }) => ({
        id: shift?.id,
        code: shift?.code,
        description: shift?.description,
        factoryId: factoryId ? organizationId : null,
      }))
      .filter((a) => a.id);

    const filteredShifts = factoryId
      ? shifts.filter((a) => a.factoryId === factoryId)
      : [...new Map(shifts.map((item) => [item.id, item])).values()];

    return { data: filteredShifts, meta: { total: filteredShifts.length, request: { factoryId } } };
  }
}
