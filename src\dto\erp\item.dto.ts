import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';

export enum ERPItemStatus {
  Active = 'Active',
  Inactive = 'Inactive',
}

/** T<PERSON><PERSON> sản phẩm */
export class ERPCreateItemReq {
  /**
   * Mã sản phẩm/thành phần/nguyên liệu
   * @example 1001
   * */
  @ApiProperty({
    description: 'Mã sản phẩm/thành phần/nguyên liệu',
    example: '1001',
  })
  ItemCode: string;

  /** Tên sản phẩm/thành phần/nguyên liệu */
  @ApiProperty({
    description: 'Tên sản phẩm/thành phần/nguyên liệu',
    example: 'Item 1',
  })
  ItemName: string;

  /** Loại sản phẩm/thành phần/nguyên liệu */
  @ApiProperty({
    description: '<PERSON>ại sản phẩm/thành phần/nguyên liệu',
    example: 'RM',
  })
  ItemType: string;

  /** Nh<PERSON>m sản phẩm/thành phần/nguyên liệu */
  @ApiProperty({
    description: 'Nhóm sản phẩm/thành phần/nguyên liệu',
    example: 'Cá',
  })
  Group: string;

  /** Danh mục sản phẩm/thành phần/nguyên liệu */
  @ApiProperty({
    description: 'Danh mục sản phẩm/thành phần/nguyên liệu',
    example: '',
  })
  Category?: string;

  /** Thương hiệu sản phẩm/thành phần/nguyên liệu */
  @ApiProperty({
    description: 'Thương hiệu sản phẩm/thành phần/nguyên liệu',
    example: '',
  })
  Brand?: string;

  /** Đơn vị tính */
  @ApiProperty({
    description: 'Đơn vị tính cơ bản',
    example: 'Lit',
  })
  BaseUnit: string;

  /** Đơn vị tính của kho */
  @ApiProperty({
    description: 'Đơn vị tính của kho',
    example: 'Lit',
  })
  InventoryUnit: string;

  /** Shelf Life Day */
  @ApiPropertyOptional({
    description: 'Shelf Life Day',
    example: '180',
    nullable: true,
  })
  ShelfLifeDay?: number;

  /** Đơn vị tính */
  @ApiProperty({
    description: 'Loại đơn vị đo lường sản phẩm/thành phần/nguyên liệu',
    type: () => [ERPCreateItemMHUTypesReq],
  })
  // @Type(() => ERPCreateItemMHUTypesReq)
  MHUTypes: ERPCreateItemMHUTypesReq[];

  /** Trạng thái sản phẩm/thành phần/nguyên liệu */
  @ApiProperty({
    description: 'Trạng thái sản phẩm/thành phần/nguyên liệu',
    example: ERPItemStatus.Active,
    enum: ERPItemStatus,
  })
  ItemStatus: ERPItemStatus;
}

/** Đơn vị tính */
export class ERPCreateItemMHUTypesReq {
  /** Đơn vị tính */
  @ApiProperty({
    description: 'Từ đơn vị',
    example: 'Lit',
  })
  FromUnit: string;

  /** Đến đơn vị */
  @ApiProperty({
    description: 'Đến đơn vị',
    example: 'Kgs',
  })
  ToUnit: string;

  /**
   * Hệ số chuyển đổi
   * @example 1
   * */
  @ApiProperty({
    description: 'Tỷ lệ chuyển đổi',
    example: '1',
  })
  Conversion: string;
}

export class ERPCreateItemResponse {
  /**
   * Mã sản phẩm/thành phần/nguyên liệu
   * @example 1001
   * */
  @ApiProperty({
    description: 'Mã sản phẩm/thành phần/nguyên liệu',
    example: '1001',
  })
  item_code: string;
}
