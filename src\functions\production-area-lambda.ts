import { NestFactory } from '@nestjs/core';
import { APIGatewayProxyEvent, APIGatewayProxyResult, Context } from 'aws-lambda';
import { setupTransactionContext } from '~/@core/decorator';
import { AppModule } from '~/app.module';
import { CustomError, ICustomError, LambdaError } from '~/common/constants/Error.constant';
import { ProcessAreaActiveStatus } from '~/dto/production-area.dto';
import { ProductionAreaService } from '~/x-modules/admin/services';
import {
  IProductionAreaFilter,
  IProductionAreaSubmitData,
} from '~/x-modules/admin/services/system-configuration/production-area.service';

// Constants
const CORS_HEADERS = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Credentials': true,
  'Content-Type': 'application/json',
};

const SUCCESS_MESSAGE = 'success';

// Singleton pattern for app instance
class AppInstance {
  private static instance: any = null;

  static async getInstance() {
    if (!AppInstance.instance) {
      try {
        const app = await NestFactory.createApplicationContext(AppModule);
        setupTransactionContext();
        await app.init();
        AppInstance.instance = app;
      } catch (error) {
        console.error('Failed to initialize app:', error);
        throw LambdaError.INTERNAL_ERROR();
      }
    }
    return AppInstance.instance;
  }
}

// Error handler
const handleError = (error: ICustomError): APIGatewayProxyResult => {
  console.error('Error occurred:', error);
  return {
    statusCode: 400,
    headers: CORS_HEADERS,
    body: JSON.stringify(error),
  };
};

// Main handler wrapper
const handler = async <T>(func: () => Promise<T>): Promise<APIGatewayProxyResult> => {
  try {
    const result = await func();
    return {
      statusCode: 200,
      headers: CORS_HEADERS,
      body: JSON.stringify(result),
    };
  } catch (error) {
    if (error instanceof CustomError) {
      return handleError(error);
    }
    return handleError(LambdaError.INTERNAL_ERROR());
  }
};

export const getProductionAreaList = async (
  event: APIGatewayProxyEvent,
  context: Context,
): Promise<APIGatewayProxyResult> => {
  return handler(async () => {
    const app = await AppInstance.getInstance();
    const productionAreaService = app.get(ProductionAreaService) as ProductionAreaService;

    const filter: IProductionAreaFilter = {};
    const queryParams = event.queryStringParameters || {};

    // Parse filter parameters
    if (queryParams.code) filter.code = queryParams.code;
    if (queryParams.factoryId) filter.factoryId = queryParams.factoryId;
    if (queryParams.name) filter.name = queryParams.name;
    if (queryParams.activeStatus) {
      const activeStatus = queryParams.activeStatus as ProcessAreaActiveStatus;
      // Validate activeStatus
      if (!Object.values(ProcessAreaActiveStatus).includes(activeStatus)) {
        throw LambdaError.PARAMETER_ERROR();
      }
      filter.activeStatus = activeStatus;
    }
    if (queryParams.createdDate) filter.createdDate = queryParams.createdDate;
    if (queryParams.updatedDate) filter.updatedDate = queryParams.updatedDate;

    // Parse pagination parameters
    if (queryParams.page) filter.page = parseInt(queryParams.page, 10);
    if (queryParams.limit) filter.limit = parseInt(queryParams.limit, 10);

    // Validate pagination parameters
    if (filter.page !== undefined && filter.page < 1) {
      throw LambdaError.PARAMETER_ERROR();
    }
    if (filter.limit !== undefined && (filter.limit < 1 || filter.limit > 100)) {
      throw LambdaError.PARAMETER_ERROR();
    }

    const result = await productionAreaService.getProductionAreaList(filter);

    return {
      data: result.items,
      pagination: {
        total: result.total,
        page: result.page,
        limit: result.limit,
        totalPages: result.totalPages,
      },
    };
  });
};

export type IGetProductionAreaRequest = {
  id: string;
};
export const getProductionArea = async (
  event: APIGatewayProxyEvent,
  context: Context,
): Promise<APIGatewayProxyResult> => {
  return handler(async () => {
    const app = await AppInstance.getInstance();
    const productionAreaService = app.get(ProductionAreaService) as ProductionAreaService;

    if (!event.pathParameters?.id) {
      throw LambdaError.PARAMETER_ERROR();
    }

    const requestParams = event.pathParameters as IGetProductionAreaRequest;
    const result = await productionAreaService.getProductionArea(requestParams);
    return { data: result };
  });
};

export const getFactoryList = (event: APIGatewayProxyEvent, context: Context) => {
  return handler(async () => {
    const app = await AppInstance.getInstance();
    const productionAreaService = app.get(ProductionAreaService) as ProductionAreaService;
    const result = await productionAreaService.getFactoryList();
    return { data: result, total: result.length };
  });
};

export const getAllGeneralDataDetail = (event: APIGatewayProxyEvent, context: Context) => {
  return handler(async () => {
    const app = await AppInstance.getInstance();
    const productionAreaService = app.get(ProductionAreaService) as ProductionAreaService;
    const result = await productionAreaService.getAllGeneralDataDetail();
    return { data: result, total: result.length };
  });
};

export const getProcessAreaByFactoryId = async (event: APIGatewayProxyEvent, context: Context) => {
  return handler(async () => {
    const app = await AppInstance.getInstance();
    const productionAreaService = app.get(ProductionAreaService) as ProductionAreaService;

    const factoryId = event.pathParameters?.factoryId;
    if (!factoryId) {
      throw LambdaError.PARAMETER_ERROR();
    }

    // Lấy tham số filter từ query string
    const queryParams = event.queryStringParameters || {};
    const lineId = queryParams.lineId;
    const processAreaCode = queryParams.processAreaCode;
    const processAreaName = queryParams.processAreaName;
    const activeStatus = queryParams.activeStatus as ProcessAreaActiveStatus;

    // Validate activeStatus nếu có
    if (activeStatus && !Object.values(ProcessAreaActiveStatus).includes(activeStatus)) {
      throw LambdaError.PARAMETER_ERROR();
    }

    // Lấy tham số phân trang từ query string
    const page = queryParams.page ? parseInt(queryParams.page, 10) : 1;
    const limit = queryParams.limit ? parseInt(queryParams.limit, 10) : 10;

    // Validate tham số phân trang
    if (page < 1) {
      throw LambdaError.PARAMETER_ERROR();
    }
    if (limit < 1 || limit > 100) {
      throw LambdaError.PARAMETER_ERROR();
    }

    const result = await productionAreaService.getProcessAreaByFactoryId(
      factoryId,
      processAreaCode,
      processAreaName,
      lineId,
      activeStatus,
      page,
      limit
    );

    return {
      data: result.items,
      pagination: {
        total: result.total,
        page: result.page,
        limit: result.limit,
        totalPages: result.totalPages
      }
    };
  });
};

export const createProductionArea = async (
  event: APIGatewayProxyEvent,
  context: Context,
): Promise<APIGatewayProxyResult> => {
  return handler(async () => {
    const app = await AppInstance.getInstance();
    const productionAreaService = app.get(ProductionAreaService) as ProductionAreaService;

    if (!event.body) {
      throw LambdaError.PARAMETER_ERROR();
    }

    const requestParams = JSON.parse(event.body) as IProductionAreaSubmitData;
    const result = await productionAreaService.createProductionArea(requestParams);

    return {
      message: SUCCESS_MESSAGE,
      data: result,
    };
  });
};

export const getProductionAreaInfo = async (
  event: APIGatewayProxyEvent,
  context: Context,
): Promise<APIGatewayProxyResult> => {
  return handler(async () => {
    const app = await AppInstance.getInstance();
    const productionAreaService = app.get(ProductionAreaService) as ProductionAreaService;

    if (!event.pathParameters?.id) {
      throw LambdaError.PARAMETER_ERROR();
    }

    const result = await productionAreaService.getProductionAreaInfo(event.pathParameters.id);
    return { data: result };
  });
};

export const getProductionAreaDetails = async (
  event: APIGatewayProxyEvent,
  context: Context,
): Promise<APIGatewayProxyResult> => {
  return handler(async () => {
    const app = await AppInstance.getInstance();
    const productionAreaService = app.get(ProductionAreaService) as ProductionAreaService;

    if (!event.pathParameters?.id) {
      throw LambdaError.PARAMETER_ERROR();
    }

    // Lấy tham số phân trang từ query string
    const queryParams = event.queryStringParameters || {};
    const page = queryParams.page ? parseInt(queryParams.page, 10) : 1;
    const limit = queryParams.limit ? parseInt(queryParams.limit, 10) : 10;

    // Validate tham số phân trang
    if (page < 1) {
      throw LambdaError.PARAMETER_ERROR();
    }
    if (limit < 1 || limit > 100) {
      throw LambdaError.PARAMETER_ERROR();
    }

    const result = await productionAreaService.getProductionAreaDetails(
      event.pathParameters.id,
      page,
      limit,
    );

    return {
      data: result.items,
      pagination: {
        total: result.total,
        page: result.page,
        limit: result.limit,
        totalPages: result.totalPages,
      },
    };
  });
};

export const updateProductionArea = async (
  event: APIGatewayProxyEvent,
  context: Context,
): Promise<APIGatewayProxyResult> => {
  return handler(async () => {
    const app = await AppInstance.getInstance();
    const productionAreaService = app.get(ProductionAreaService) as ProductionAreaService;

    if (!event.pathParameters?.id || !event.body) {
      throw LambdaError.PARAMETER_ERROR();
    }

    const id = event.pathParameters.id;
    const requestParams = JSON.parse(event.body) as IProductionAreaSubmitData;
    const result = await productionAreaService.updateProductionArea(id, requestParams);

    return {
      message: SUCCESS_MESSAGE,
      data: result,
    };
  });
};

export const getLinesByFactoryId = async (event: APIGatewayProxyEvent, context: Context) => {
  return handler(async () => {
    const app = await AppInstance.getInstance();
    const productionAreaService = app.get(ProductionAreaService) as ProductionAreaService;
    const result = await productionAreaService.getLinesByFactoryId(event.pathParameters?.factoryId);
    return { data: result, total: result.length };
  });
};

export const getAllProductionAreas = async (
  event: APIGatewayProxyEvent,
): Promise<APIGatewayProxyResult> => {
  return handler(async () => {
    const app = await AppInstance.getInstance();
    const productionAreaService = app.get(ProductionAreaService);

    const result = await productionAreaService.getAllProductionAreas();
    return {
      data: result.items,
      total: result.total
    };
  });
};
