import { Body, Param, Query } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { DefController, DefGet, DefPost, DefPut } from '~/@core/decorator';
import { ListSitewiseModelReq, SitewiseAssetImportReq, SitewiseAssetReq } from '~/dto/sitewise.dto';
import { SitewiseAssetService } from '../../services';

@ApiTags('Sitewise Asset')
@DefController('sitewise-asset')
export class SitewiseAssetController {
  constructor(private readonly service: SitewiseAssetService) {}

  @DefGet('find')
  async find(@Query() data: any) {
    return await this.service.find(data);
  }

  @DefGet('')
  async pagination(@Query() data: ListSitewiseModelReq) {
    return await this.service.pagination(data);
  }
}
