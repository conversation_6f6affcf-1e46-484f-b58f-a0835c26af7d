import { Column, Entity } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { PrimaryBaseEntity } from '../primary-base.entity';

@Entity('utility_transaction_allocation')
export class UtilityTransactionAllocationEntity extends PrimaryBaseEntity {
  @ApiProperty({ description: '<PERSON><PERSON><PERSON> làm việc dựa trên ca làm và thời điểm check-in' })
  @Column({ type: 'date', nullable: true })
  date: string;

  @ApiProperty({ description: 'shiftId', example: 'b80cef65-afe9-429b-b364-4c83e078f1f6' })
  @Column()
  shiftId: string;

  @ApiProperty({ description: 'resourceCode', example: 'UT044' })
  @Column()
  resourceCode: string;

  @ApiProperty({ description: 'processAreaId', example: 'fc8039b5-26be-4b82-962d-4fb887dad4da' })
  @Column({ type: 'uuid' })
  processAreaId: string;

  @ApiProperty({ description: 'productionAreaId', example: 'fc8039b5-26be-4b82-962d-4fb887dad4da' })
  @Column({ type: 'uuid', nullable: true })
  utilityTransactionId: string;

  @ApiProperty({ description: 'value', example: '17' })
  @Column({ type: 'numeric' })
  value: number;

  @ApiProperty({ description: 'lineId', example: '1234' })
  @Column({ nullable: true, type: 'double precision' })
  lineId: number;

  @ApiProperty({ description: 'planResourceUsage' })
  @Column({ type: 'numeric' })
  planResourceUsage: number;

  @ApiProperty({ description: 'oldActualResourceUsage' })
  @Column({ type: 'numeric' })
  oldActualResourceUsage: number;

  @ApiProperty({ description: 'actualTrxQty' })
  @Column({ type: 'numeric' })
  actualTrxQty: number;

  @ApiProperty({ description: 'planProdQty' })
  @Column({ type: 'numeric' })
  planProdQty: number;

  @ApiProperty({ description: 'poId', example: '02KK00313270325AMIP07' })
  @Column()
  poId: string;

  @ApiProperty({ description: 'contribution' })
  @Column({ type: 'numeric' })
  contribution: number;

  @ApiProperty({ description: 'contrPercent' })
  @Column({ type: 'numeric' })
  contrPercent: number;

  @ApiProperty({ description: 'allocatedValue' })
  @Column({ type: 'numeric' })
  allocatedValue: number;

  @ApiProperty({ description: 'status' })
  @Column()
  status: string;
}
