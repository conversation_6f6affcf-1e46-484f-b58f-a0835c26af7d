import { Injectable, ConflictException, NotFoundException } from '@nestjs/common';
import { In, Like, UpdateResult } from 'typeorm';
import { SitewiseAssetRepo, SitewiseModelRepo } from '~/repositories/primary/sitewise.repo';
import { ListSitewiseAssetReq, SitewiseAssetImportReq, SitewiseAssetReq } from '~/dto/sitewise.dto';
import { adminSessionContext } from '../../admin-session.context';
import { SuccessResponse } from '~/@systems/utils';
import { BindRepo } from '~/@core/decorator';
import { SitewiseAssetEntity } from '~/entities/primary';

@Injectable()
export class SitewiseAssetService {
  constructor() {}
  @BindRepo(SitewiseAssetRepo)
  private readonly repo: SitewiseAssetRepo;

  @BindRepo(SitewiseModelRepo)
  private readonly modelRepo: SitewiseModelRepo;

  async find(data: any) {
    const whereCon: any = { status: 'ACTIVE' };
    if (data.code) whereCon.code = Like(`%${data.code}%`);
    if (data.sitewiseModelId) whereCon.sitewiseModelId = data.sitewiseModelId;
    if (data.lstId?.length > 0) whereCon.id = In(data.lstId);
    if (data.lstCode?.length > 0) whereCon.code = In(data.lstCode);
    return await this.repo.find({ where: whereCon });
  }

  // async findOne(id: string) {
  //   return await this.repo.findOne({ where: { id: id } });
  // }

  // async createData(data: SitewiseAssetReq): Promise<any> {
  //   const checkCodeExist = await this.repo.findOne({ where: { code: data.code } });
  //   if (checkCodeExist)
  //     throw new ConflictException(`Sitewise Asset with code [${data.code}] already exists`);
  //   const checkSitewiseModel = await this.modelRepo.findOne({
  //     where: { id: data.sitewiseModelId, status: 'ACTIVE' },
  //   });
  //   if (!checkSitewiseModel)
  //     throw new NotFoundException(`Sitewise Model with ID [${data.sitewiseModelId}] not found`);
  //   const { userId } = adminSessionContext;

  //   const entity = new SitewiseAssetEntity();
  //   entity.code = data.code;
  //   entity.description = data.description;
  //   entity.awsId = data.awsId;
  //   entity.sitewiseModelId = data.sitewiseModelId;
  //   entity.status = 'ACTIVE';

  //   await this.repo.insert(entity);
  //   return entity;
  // }

  // async updateData(id: string, data: SitewiseAssetReq) {
  //   const entity = await this.repo.findOne({ where: { id: id } });
  //   if (!entity) throw new NotFoundException(`Sitewise model with ID ${id} not found`);
  //   const { userId } = adminSessionContext;
  //   if (data.code != entity.code) {
  //     const checkCodeExist = await this.repo.findOne({ where: { code: data.code } });
  //     if (checkCodeExist)
  //       throw new ConflictException(`Sitewise Model with code [${data.code}] already exists`);
  //   }
  //   const checkSitewiseModel = await this.modelRepo.findOne({
  //     where: { id: data.sitewiseModelId, status: 'ACTIVE' },
  //   });
  //   if (!checkSitewiseModel)
  //     throw new NotFoundException(`Sitewise Model with ID [${data.sitewiseModelId}] not found`);

  //   entity.code = data.code;
  //   entity.description = data.description;
  //   entity.awsId = data.awsId;
  //   entity.sitewiseModelId = data.sitewiseModelId;
  //   const updatedEntity = await this.repo.update(entity.id, entity);
  //   return { message: UpdateResult, data: updatedEntity };
  // }

  async pagination(body: ListSitewiseAssetReq) {
    let whereCon: any = {};
    if (body.awsId) whereCon.awsId = Like(`%${body.awsId}%`);
    if (body.sitewiseModelId) whereCon.sitewiseModelId = body.sitewiseModelId;
    if (body.code) whereCon.code = Like(`%${body.code}%`);
    if (body.status !== undefined) whereCon.status = body.status;
    let res: any = await this.repo.findPagination({}, body);
    const dictSitewiseModel: any = {};
    {
      const lstSitewiseModel = await this.modelRepo.find({
        where: { id: In(res.data.map((item: SitewiseAssetEntity) => item.sitewiseModelId)) },
      });
      lstSitewiseModel.forEach((item) => (dictSitewiseModel[item.id] = item.code));
    }
    res.data.forEach((item) => {
      item['sitewiseModelCode'] = dictSitewiseModel[item.sitewiseModelId] || '';
    });
    return res;
  }

  // async updateIsDelete(id: string) {
  //   const entity = await this.repo.findOne({ where: { id } });
  //   if (!entity) throw new NotFoundException(`Sitewise model with ID ${id} not found`);

  //   entity.status = entity.status === 'ACTIVE' ? 'INACTIVE' : 'ACTIVE';
  //   await this.repo.update(entity.id, entity);
  //   return { message: SuccessResponse };
  // }

  // async createDataExcel(data: SitewiseAssetImportReq[]): Promise<any> {
  //   const existingCodes = new Set(
  //     (await this.repo.find({ where: { status: 'ACTIVE' }, select: ['code'] })).map(c => c.code),
  //   );
  //   const { userId } = adminSessionContext;
  //   const dictSitewiseModel: any = {};
  //   {
  //     const listSitewiseModel = await this.modelRepo.find({
  //       where: { code: data.map(item => item.sitewiseModelCode), status: 'ACTIVE' },
  //       select: ['id', 'code'],
  //     });
  //     listSitewiseModel.forEach(item => (dictSitewiseModel[item.code] = item.id));
  //   }
  //   const newEntityList = data
  //     .filter(
  //       (item, index) =>
  //         !existingCodes.has(item.code) &&
  //         data.findIndex(el => el.code === item.code) === index &&
  //         dictSitewiseModel[item.sitewiseModelCode],
  //     )
  //     .map(({ sitewiseModelCode, ...rest }) => {
  //       const entity = new SitewiseAssetEntity();
  //       entity.code = rest.code;
  //       entity.description = rest.description;
  //       entity.awsId = rest.awsId;
  //       entity.sitewiseModelId = dictSitewiseModel[sitewiseModelCode];
  //       entity.status = 'ACTIVE';
  //       return entity;
  //     });

  //   await this.repo.insert(newEntityList);
  //   return { message: SuccessResponse };
  // }
}
