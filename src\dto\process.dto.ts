import { IsNotEmpty, IsOptional, IsBoolean } from 'class-validator';

export class ProcessReqDto {
  @IsNotEmpty({ message: 'Mã quy trình không được để trống' })
  code: string;

  @IsNotEmpty({ message: 'Tên quy trình không được để trống' })
  name: string;

  @IsOptional()
  description?: string;

  @IsOptional()
  ipAddress?: string;

  @IsOptional()
  gatewayProtocol?: string;

  @IsBoolean({ message: 'Trạng thái phải là boolean' })
  isActive: boolean;

  @IsOptional()
  note?: string;

  @IsOptional()
  organizationUnitId?: string;

  @IsOptional()
  category?: string;
  
  @IsOptional()
  machineIds?: string[];

  @IsOptional()
  outputCalculationMethodCode?: string;
}