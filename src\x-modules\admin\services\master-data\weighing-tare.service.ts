import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { ILike, In, Like, Not } from 'typeorm';
import { BindRepo, DefTransaction } from '~/@core/decorator';
import { ListWeighingTareReq, WeighingTareReq } from '~/dto/weighing-tare.dto';
import { GeneralDataDetailRepo, GeneralDataRepo, ItemRepo } from '~/repositories/primary';
import {
  WeighingTareDetailRepo,
  WeighingTareRepo,
} from '~/repositories/primary/weighing-tare.repo';
import { adminSessionContext } from '~/x-modules/admin/admin-session.context';

@Injectable()
export class WeighingTareService {
  constructor() {}

  @BindRepo(WeighingTareRepo)
  private repo: WeighingTareRepo;

  @BindRepo(WeighingTareDetailRepo)
  private weighingTareDetailRepo: WeighingTareDetailRepo;

  @BindRepo(GeneralDataRepo)
  private generalDataRepo: GeneralDataRepo;

  @BindRepo(GeneralDataDetailRepo)
  private generalDataDetailRepo: GeneralDataDetailRepo;

  @BindRepo(ItemRepo)
  private readonly itemRepo: ItemRepo;

  @DefTransaction()
  async create(body: WeighingTareReq) {
    const { userId: userId } = adminSessionContext;
    const today = new Date();
    const weighingTareTypeCode = await this.getGeneralDataByCode(body.weighingTareTypeDetailCode);
    const uomCode = await this.getGeneralDataByCode(body.uomDetailCode);
    const header: any = await this.repo.save({
      code: body.code,
      weighingTareTypeCode: weighingTareTypeCode.code,
      weighingTareTypeDetailCode: body.weighingTareTypeDetailCode,
      uomCode: uomCode.code,
      uomDetailCode: body.uomDetailCode,
      value: body.value,
      description: body.description,
      note: body.note,
      isActive: body.isActive,
      createdBy: userId,
      updatedBy: userId,
      createdDate: today,
      updatedDate: today,
    });
    const lstDetail = body.lstItem.map((i) => ({
      weighingTareId: header.id,
      itemId: i.id,
      isAssigned: i.isAssigned,
      createdBy: userId,
      updatedBy: userId,
      createdDate: today,
      updatedDate: today,
    }));
    await this.weighingTareDetailRepo.insert(lstDetail);
    header.lstDetail = lstDetail;
    return { message: 'Create Successfully!', data: header };
  }
  async getGeneralDataByCode(code: string) {
    const generalDataDetail = await this.generalDataDetailRepo.findOne({
      where: { code, isActive: true },
    });

    if (!generalDataDetail) {
      throw new Error(`General data detail with code ${code} not found`);
    }

    const generalData = await this.generalDataRepo.findOne({
      where: { id: generalDataDetail.generalId, isActive: true },
    });

    if (!generalData) {
      throw new Error(`General data with generalId ${generalDataDetail.generalId} not found`);
    }

    return {
      generalId: generalData.id,
      code: generalData.code,
    };
  }

  async list(params: ListWeighingTareReq) {
    const { pageIndex = 1, pageSize = 10, code, name, type, isActive, ...rest } = params;

    const where: any = { ...rest };
    if (code) where.code = ILike(`%${code}%`);
    if (type) where.weighingTareTypeDetailCode = ILike(`%${code}%`);
    if (isActive !== undefined) where.isActive = isActive;

    const [data, total] = await this.repo.findAndCount({
      where,
      skip: (pageIndex - 1) * pageSize,
      take: pageSize,
      order: { isActive: 'DESC', createdDate: 'DESC', code: 'DESC' },
    });

    return { data, total };
  }

  async detail(id: string) {
    if (!id) throw new BadRequestException('ID doesn’t exist');

    const weighingTare = await this.repo.findOneOrFail({
      where: { id },
    });

    const weighingTareDetail = await this.weighingTareDetailRepo.find({
      where: { weighingTareId: id },
    });
    const itemIds = weighingTareDetail.map((detail) => detail.itemId);

    const items = await this.itemRepo.find({
      where: { id: In(itemIds) },
    });
    const itemMap = new Map(items.map((item) => [item.id, item]));
    const weighingTareDetailWithItem = weighingTareDetail.map((detail) => ({
      ...detail,
      ...(itemMap.get(detail.itemId) || {}),
    }));

    return {
      weighingTare,
      weighingTareDetailWithItem,
    };
  }

  @DefTransaction()
  async update(id: string, body: WeighingTareReq) {
    if (!id) throw new BadRequestException('ID don’t exist');

    const { userId: userId } = adminSessionContext;
    const today = new Date();

    const existing = await this.repo.findOne({ where: { id } });
    if (!existing) throw new NotFoundException(`Weighing Tare with ID: ${id} not found`);

    const weighingTareTypeCode = await this.getGeneralDataByCode(body.weighingTareTypeDetailCode);
    const uomCode = await this.getGeneralDataByCode(body.uomDetailCode);

    await this.repo.update(id, {
      code: body.code,
      weighingTareTypeCode: weighingTareTypeCode.code,
      weighingTareTypeDetailCode: body.weighingTareTypeDetailCode,
      uomCode: uomCode.code,
      uomDetailCode: body.uomDetailCode,
      value: body.value,
      description: body.description,
      isActive: body.isActive,
      note: body.note,
      updatedBy: userId,
      updatedDate: today,
    });

    await this.weighingTareDetailRepo.delete({ weighingTareId: id });

    const lstDetail = body.lstItem.map((i) => ({
      weighingTareId: id,
      itemId: i.id,
      isAssigned: i.isAssigned,
      createdBy: userId,
      updatedBy: userId,
      createdDate: today,
      updatedDate: today,
    }));
    await this.weighingTareDetailRepo.insert(lstDetail);

    const updatedHeader = await this.repo.findOne({ where: { id } });
    (updatedHeader as any).lstDetail = lstDetail;

    return { message: 'Update successfully', data: updatedHeader };
  }

  @DefTransaction()
  async delete(id: string) {
    if (!id) throw new BadRequestException('ID don’t exist');
    const machine = await this.repo.findOne({ where: { id } });
    if (!machine) throw new NotFoundException('Not found');
    await this.repo.remove(machine);
    return { message: 'Delete successfully' };
  }

  async getLstProductionCategory(code: string): Promise<any[]> {
    let res = [];
    const lstProdCate = await this.generalDataRepo.find({
      where: {
        code: code,
        isActive: true,
      },
    });
    if (lstProdCate.length > 0) {
      const generalIds = lstProdCate.map((item) => item.id);
      const details = await this.generalDataDetailRepo.find({
        where: {
          generalId: In(generalIds),
          isActive: true,
        },
        order: { createdDate: 'DESC' },
      });
      res = details;
    }
    return res;
  }
  async getLstTypeItems(): Promise<any[]> {
    const lstItemTypes = await this.itemRepo.find({
      where: { status: 'ACTIVE' },
      order: { name: 'ASC' },
    });

    return lstItemTypes ?? [];
  }

  async getCode() {
    const result = await this.repo.findOne({
      select: ['code'],
      order: {
        code: 'DESC',
      },
    });

    return result ? parseInt(result.code, 10) + 1 : 100;
  }

  //#endregion
}
