import { CanActivate, ExecutionContext, ForbiddenException, Injectable } from '@nestjs/common';
import { Reflector } from '@nestjs/core';
import { CODE_KEY, PATH_KEY } from '~/@core/decorator/roles.decorator';
import { adminSessionContext } from '~/x-modules/admin/admin-session.context';
import { AuthService } from '~/x-modules/admin/services';

@Injectable()
export class RoleGuard implements CanActivate {
  constructor(
    private readonly reflector: Reflector,
    private readonly authService: AuthService,
  ) {}

  async canActivate(context: ExecutionContext): Promise<boolean> {
    // const request: Request = context.switchToHttp().getRequest();
    const { userId } = adminSessionContext;

    // Lấy metadata phân quyền trên mỗi route
    const requiredPath = this.reflector.get<string>(PATH_KEY, context.getHandler());
    const requiredCode = this.reflector.get<string>(CODE_KEY, context.getHandler());
    if (!requiredPath || !requiredCode) {
      return true;
    }

    const roles = await this.authService.getUserRoles(userId);
    const hasPermission = this.checkPermission(roles, requiredPath, requiredCode);
    if (!hasPermission) {
      throw new ForbiddenException('You do not have permission to access this resource');
    }

    return true;
  }

  private checkPermission(permissions: any[], path: string, code: string): boolean {
    // Hàm đệ quy để kiểm tra quyền
    const checkPermissionRecursive = (nodes: any[]): boolean => {
      for (const node of nodes) {
        // Kiểm tra quyền tại cấp độ này
        if (node.path === path && node.code === code) {
          return true;
        }

        // Kiểm tra quyền trong các node con (children)
        if (node.children && node.children.length > 0) {
          const result = checkPermissionRecursive(node.children);
          if (result) {
            return true;
          }
        }
      }
      return false;
    };

    // Gọi hàm đệ quy để duyệt qua tất cả các quyền
    return checkPermissionRecursive(permissions);
  }
}
