import { RequestContext } from '~/@core/context';
import { KeySessionContext } from '~/common/constants';

export class AdminSessionContext {
  get sessionData() {
    return RequestContext.getAttribute<any>(KeySessionContext.ADMIN_SESSION);
  }
  get accessToken() {
    return this.sessionData?.accessToken;
  }
  get memberId() {
    return this?.sessionData?.sub;
  }
  get username() {
    return this.sessionData?.username;
  }
  get userId() {
    return this.sessionData?.id;
  }

  get userName() {
    return this.sessionData?.fullName;
  }

  get userEmail() {
    return this.sessionData?.email;
  }
}

export const adminSessionContext = new AdminSessionContext();
