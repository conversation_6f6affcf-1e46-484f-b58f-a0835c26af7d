import { Injectable } from '@nestjs/common';
import { BindRepo } from '~/@core/decorator';
import moment from 'moment';
import { JwtService } from '@nestjs/jwt';
import { Connection } from 'typeorm';
import { promises } from 'dns';
import { dateHelper } from '~/common/helpers/date.helper';
import {
  KpiRepo,
  KpiSetDetailRepo,
  KpiSetGroupRepo,
  KpiSetHeaderRepo,
  KpiSetProductAreaRepo,
  KpiScoreRepo,
} from '~/repositories/primary';

@Injectable()

export class ManhourKpi {
  constructor(
    private jwtService: JwtService,
    private readonly connection: Connection
  ) {}
  @BindRepo(KpiRepo)
  private readonly kpiRepo: KpiRepo;
  @BindRepo(KpiScoreRepo)
  private readonly kpiScoreRepo: KpiScoreRepo;
  @BindRepo(KpiSetHeaderRepo)
  private readonly kpiSetHeaderRepo: KpiSetHeaderRepo;
  @BindRepo(KpiSetGroupRepo)
  private readonly kpiSetGroupRepo: KpiSetGroupRepo;
  @BindRepo(KpiSetDetailRepo)
  private readonly kpiSetDetailRepo: KpiSetDetailRepo;
  @BindRepo(KpiSetProductAreaRepo)
  private readonly kpiSetProductAreaRepo: KpiSetProductAreaRepo;

  async onScanManhourTransaction(factoryId: string) {
    if(!factoryId) return [];
    
    const result = await this.connection.query(`
      select mt.*, pa."organizationUnitId" as "factory_id" from manhour_transactions mt 
      join production_area pa on pa."id" = mt."productionAreaId" 
      where pa."organizationUnitId" = $1
      AND mt."productionDate" >= CURRENT_DATE - INTERVAL '2 day'
      ORDER BY mt."productionDate" DESC;
    `,[factoryId]);
    return result || '';
  }

  async getDataByKpi() {
    const result = await this.connection.query(`
      SELECT ksh.* ,'||' "||", ksg."kpiGroupId",ksg."kpiGroupCode", ksg."kpiGroupName", ksg."kpiSetHeaderId", ksg.id as idbanggroup,
      '||' "||", ksd."kpiId", ksd."id" AS "kpiSetDetailId", ksd."kpiSetGroupId" ,'||' "||", k.code, k."shortName", k."utilityType" ,'||' "||", 
      kspa."factoryId" , ou.code , kspa."productionAreaId", pa.code , pa."name" , kspa."siteId", k."inputFrequency", k.unit 
      --kp.kpivalue , kp."shiftId" , kp.podate 
      from kpi_set_header ksh 
      join kpi_set_group ksg  on ksh.id = ksg."kpiSetHeaderId"
      join kpi_set_detail ksd on ksd."kpiSetGroupId" = ksg.id 
      join kpi k  on k.id = ksd."kpiId"
      join kpi_set_production_area kspa  on ksh.id = kspa."kpiSetHeaderId"
      join organization_units ou on kspa."factoryId" = ou.id
      join production_area pa on pa.id = kspa."productionAreaId"
      --left join kpivalue as kp on kp.factory_id = kspa."factoryId" and kp."productionAreaId" =kspa."productionAreaId" and kp.utility_meter_type_detail_code =k."utilityType"
      where ksh.status = 1 and k."kpiMethod" = 'A' and k."autoFunction"  = 'F_KPI_MAN-HOUR'  and kspa.status =1 and k.status =1 
        
    `);
  
    return result;
  }

  onManhourTransaction (manhourTransactionData: any, uniqueManhourTransaction:any) {
    if(!manhourTransactionData || !uniqueManhourTransaction || manhourTransactionData?.length === 0) return 0;

    let result = 0
    manhourTransactionData.forEach((utData:any) => {
      if(
        moment(utData?.productionDate, "YYYY-MM-DD HH:mm:ss.SSS Z").utcOffset(7 * 60).format('YYYY-MM-DD') === moment(uniqueManhourTransaction?.productionDate, "YYYY-MM-DD HH:mm:ss.SSS Z").utcOffset(7 * 60).format('YYYY-MM-DD') &&
        // utData?.productionDate === uniqueManhourTransaction?.productionDate &&
        utData?.shiftId === uniqueManhourTransaction?.shiftId &&
        utData?.factory_id === uniqueManhourTransaction?.factory_id
      ){ 
        
        const getValue = !isNaN(Number(utData?.manhour)) ? Number(utData?.manhour) : utData?.manhour
        result = result + getValue
      }
    })
    return result;
  }

  async onSumProductionOee (uniqueTransaction: any) {
    let result = 0
    const productionOEEData = await this.connection.query(`
      SELECT 
            po."shiftId",
            s.code,
            SUM(po."actualProductQty") - SUM(po."defectiveProducts") AS actualqty,
            ou."parentId" AS factory_id,
            ou_fak.code,
           	DATE(po."productionDate") AS date
      FROM production_oee po
      INNER JOIN organization_units ou ON ou.id = CAST(po."productionLineId" AS uuid)
      INNER JOIN organization_units ou_fak ON ou."parentId" = ou_fak.id
      INNER JOIN shift s ON po."shiftId" = s.id
      WHERE po."shiftId" = $1
        AND ou."parentId" = $2
        AND po."productionDate" = $3
      GROUP BY po."shiftId", ou."parentId", po."productionDate", s.code, ou_fak.code
    `, [uniqueTransaction?.shiftId, uniqueTransaction?.factory_id, uniqueTransaction?.productionDate]);

    if(!productionOEEData || productionOEEData?.length === 0) return 0;
    await Promise.all(
      productionOEEData.map(async (productionOEE:any) => {
        const getActualqty = !isNaN(Number(productionOEE?.actualqty)) ? Number(productionOEE?.actualqty) : productionOEE?.actualqty
        result = result + getActualqty
      })
    )
    return result;
  }

  async onGetKpiPeriodId (date: any) {
    const getDate = moment(date, "YYYY-MM-DD HH:mm:ss.SSS Z").format('YYYY-MM-DD');
    const KpiPeriodData = await this.connection.query(`
      SELECT kp.*
      FROM kpi_period kp
      join kpi_period_rule kpr on kp."kpiPeriodRuleId" = kpr."id"
      WHERE DATE($1) BETWEEN DATE(kp."startDate") AND DATE(kp."endDate")
      and kpr."cycleCode" = '1'
    `, [getDate]);
    return KpiPeriodData[0] ? KpiPeriodData[0]?.id : null;
  }

  async createManhourKpi(data: { fromDate?: string; toDate?: string }) {
    try {
      let getDateFrom = null
      let getDateTo = null
      if(data.fromDate && data.toDate){
        getDateFrom = dateHelper.formatDateByJobManual(data.fromDate)
        getDateTo = dateHelper.formatDateByJobManual(data.toDate)
      }

      // Set-up data
      const dataByKpis = await this.getDataByKpi()
      if(!dataByKpis || dataByKpis?.length === 0) return;

      await Promise.all(
        dataByKpis.map(async (dataByKpi:any) => {
          const manhourTransactionData = await this.onScanManhourTransaction(dataByKpi?.factoryId)
          if(manhourTransactionData?.length === 0) return;

          // Tính toán group by theo factory_id, productionDate, shiftId 
          const uniqueManhourTransactions = Array.from(
            new Map(
              manhourTransactionData
              .map((item:any) => {
                const key = `${item.factory_id}_${item.shiftId}_${item.productionDate}`;
                return [key, item];
              })
            ).values()
          );

          await Promise.all(
            uniqueManhourTransactions.map(async (uniqueManhourTransaction:any) => {
              const sumManhourTransaction = this.onManhourTransaction(manhourTransactionData, uniqueManhourTransaction) * 1000
              const sumProductionOee = await this.onSumProductionOee(uniqueManhourTransaction)
              const getKpiPeriodId = await this.onGetKpiPeriodId(uniqueManhourTransaction?.productionDate) || null;
              if(!getKpiPeriodId) return;

              let actualScoreValue = 0
              if(sumManhourTransaction === 0) actualScoreValue = 0;
              if(sumProductionOee === 0) actualScoreValue = null;
              if(sumManhourTransaction !== 0 && sumProductionOee !== 0) actualScoreValue = sumManhourTransaction/sumProductionOee;
              if(!actualScoreValue) return;

              const kpiScoreObj = {
                kpiSetHeaderId: dataByKpi?.kpiSetHeaderId,
                siteId: dataByKpi?.siteId,
                factoryId: dataByKpi?.factoryId,
                productionAreaId: dataByKpi?.productionAreaId,
                kpiSetGroupId: dataByKpi?.kpiSetGroupId,
                kpiId: dataByKpi?.kpiId,
                shiftId: uniqueManhourTransaction?.shiftId,
                kpiSetDetailId: dataByKpi?.kpiSetDetailId,
                kpiPeriodId: getKpiPeriodId,
                actualScore: actualScoreValue,
                actualValue: sumManhourTransaction,
                actualQty: sumProductionOee,
                scoreDate: uniqueManhourTransaction?.productionDate,
                source: 'Job43',
                createdByUser: "-1",
                updatedByUser: '-1'
              }

              const existing = await this.kpiScoreRepo.findOne({
                where: {
                  productionAreaId: dataByKpi?.productionAreaId,
                  kpiSetHeaderId: dataByKpi?.kpiSetHeaderId,
                  shiftId: uniqueManhourTransaction?.shiftId,
                  scoreDate: uniqueManhourTransaction?.productionDate
                },
              });
              // Nếu đã có record trùng bộ key thì không insert tiếp.
              if(existing){
                await this.kpiScoreRepo.update({ id: existing?.id },{actualScore: kpiScoreObj.actualScore})
              }else{
                await this.kpiScoreRepo.save({...kpiScoreObj})
              }
            })
          )
        })
      )
    } catch (error) {
      console.log(error, 'error');
    }
  }
}
