import { EntityRepository } from 'typeorm';
import { PrimaryRepo } from '../primary.repo';
import { ProductionAreaDetailEntity, ProductionAreaEntity } from '~/entities/primary';

@EntityRepository(ProductionAreaEntity)
export class ProductionAreaRepo extends PrimaryRepo<ProductionAreaEntity> {}

@EntityRepository(ProductionAreaDetailEntity)
export class ProductionAreaDetailRepo extends PrimaryRepo<ProductionAreaDetailEntity> {}