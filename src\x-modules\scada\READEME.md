# Scada Module (`scada`)

## 📋 Tổng quan

Module SCADA phục vụ tích hợp dữ liệu từ hệ thống SCADA vào hệ thống backend để ghi nhận downtime và material consumption một cách tự động.

## 📁 Directory Structure

scada
│
├── controllers
│   ├── index.ts                                 # Export controllers
│   └── scada-data-exchange.controller.ts        # Controller trao đổi dữ liệu với SCADA
│
└── services
    ├── index.ts                                 # Export services
    └── docker-hook.service.ts                   # Service xử lý sự kiện Docker hooks

---

## ⚙️ Sơ đồ luồng xử lý                                        
┌──────────────┐
│ SCADA Device │
└─────┬────────┘
      │  Gửi request đến API
      ▼
┌──────────────────────────────┐
│     ScadaMiddleware          │ ◄── Kiểm tra SQL Injection
└────────────┬─────────────────┘
             ▼
┌──────────────────────────────┐
│ ScadaDataExchangeController  │
└────────────┬─────────────────┘
             ▼
     Xác thực qua ScadaAuthGuard
             ▼
┌────────────────────────────────────────┐
│          DockerHookService             │
│ ┌────────────────────────────────────┐ │
│ │ scadaDataExchange(body)            │◄── `/accept-end-downtime`  
│ │ acceptMaterialConsumption(body)    │◄── `/accept-material-consumption`
│ └────────────────────────────────────┘ │
└────────────────────────────────────────┘
             ▼
        Trả kết quả về SCADA Device

---

### 🛠️. Tổng quan sơ đồ luồng xử lý 
- Khi SCADA Device gửi một yêu cầu (request) đến hệ thống, đầu tiên request đó được xử lý qua lớp ScadaMiddleware – nơi kiểm tra dữ liệu đầu vào để phát hiện các mẫu tấn công SQL Injection. Nếu phát hiện, request sẽ bị chặn và trả lỗi.

- Tiếp theo, request đi qua tầng ScadaAuthGuard, nơi thực hiện việc xác thực định danh. Guard sẽ kiểm tra token SCADA được cấu hình sẵn, đảm bảo request đến từ thiết bị đáng tin cậy.

- Sau khi xác thực thành công, request được chuyển đến ScadaDataExchangeController – nơi cấu hình đến các API tương ứng 

- Controller sau đó gọi đến DockerHookService – module chịu trách nhiệm xử lý logic nghiệp vụ chính như ghi nhận thời gian dừng máy, tiêu thụ vật tư, cập nhật trạng thái thiết bị

- Cuối cùng, sau khi xử lý xong, hệ thống sẽ trả kết quả về cho SCADA Device

