export interface ICustomError {
  message: string;
}

export class CustomError implements ICustomError {
  message: string;

  constructor(message: string) {
    this.message = message;
  }
}

export class LambdaError {
  static PARAMETER_ERROR(): ICustomError {
    return new CustomError('Parameter Error');
  }

  static NOT_FOUND(): ICustomError {
    return new CustomError('Not Found');
  }

  static INTERNAL_ERROR(): ICustomError {
    return new CustomError('Internal Error');
  }
}
