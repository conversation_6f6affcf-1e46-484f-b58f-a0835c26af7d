import { EntityRepository, Repository, getRepository, getCustomRepository } from 'typeorm';

import { PrimaryRepo } from '../primary.repo';
import { PermissionEntity } from '../../entities/primary/permission.entity';
import { PermissionUserEntity } from '../../entities/primary';
import { UserEntity } from '~/entities/primary/user.entity';

@EntityRepository(PermissionEntity)
export class PermissionRepo extends PrimaryRepo<PermissionEntity> {}

@EntityRepository(PermissionUserEntity)
export class PermissionUserRepo extends PrimaryRepo<PermissionUserEntity> {
  async getUserRole(userId: string) {
    const lstPermissionUser: any = await this.find({
      where: { userId: userId },
      relations: ['permission'],
    });
    let userRole = [];
    for (const item of lstPermissionUser) {
      const nodeRole = JSON.parse(item.__permission__.roleStringify);
      const lstCurrentRole = this.getListRole(nodeRole, []);

      if (userRole.length === 0) {
        userRole = lstCurrentRole;
      } else {
        lstCurrentRole.forEach((role, index) => {
          if (role.selected) userRole[index].selected = true;
        });
      }
    }
    return userRole;
  }

  public getListRole(lstRole, lstEnumRole) {
    for (let role of lstRole) {
      const dataRole = role?.[0] || role;
      if (dataRole?.children?.length) {
        this.getListRole(dataRole.children, lstEnumRole);
      } else if (dataRole.isLeaf) {
        lstEnumRole.push({
          ...dataRole,
        });
      }
    }
    return lstEnumRole;
  }
}
