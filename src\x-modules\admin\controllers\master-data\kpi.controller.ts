import { Body, UseGuards } from '@nestjs/common';
import { ApiOperation } from '@nestjs/swagger';
import { DefController, DefPost } from '~/@core/decorator';
import { CreateKPIDto, KpiEditDto, KpiPaginationDto } from '~/dto/kpi.dto';
import { KPIService } from '../../services/master-data/kpi.service';
import { Roles } from '~/@core/decorator/roles.decorator';
import { RoleGuard } from '~/@systems/guard/role.guard';

@DefController('kpi')
export class KpiController {
  constructor(private readonly kpiService: KPIService) {}

  @DefPost('get-select-box-production-category')
  @Roles('/master-data/kpi-list', 'View')
  @UseGuards(RoleGuard)
  async getSelectBoxOrganizationUnit(): Promise<any> {
    return await this.kpiService.getLstProductionCategory();
  }
  @DefPost('get-select-box-material-group')
  @Roles('/master-data/kpi-list', 'View')
  @UseGuards(RoleGuard)
  async getLstMaterialGroup(): Promise<any> {
    return await this.kpiService.getLstMaterialGroup();
  }
  @DefPost('get-select-box-kpi-group')
  @Roles('/master-data/kpi-list', 'View')
  @UseGuards(RoleGuard)
  async getSelectBoxKpiGroup(): Promise<any> {
    return await this.kpiService.getLstKpiGroup();
  }
  @DefPost('get-select-box-unit')
  @Roles('/master-data/kpi-list', 'View')
  @UseGuards(RoleGuard)
  async getSelectBoxUnit(): Promise<any> {
    return await this.kpiService.getLstUnit();
  }
  @DefPost('get-select-box-kpi-method')
  @Roles('/master-data/kpi-list', 'View')
  @UseGuards(RoleGuard)
  async getSelectKpiMethod(): Promise<any> {
    return await this.kpiService.getLstKpiMethod();
  }
  @DefPost('get-select-box-cal-type')
  @Roles('/master-data/kpi-list', 'View')
  @UseGuards(RoleGuard)
  async getSelectCalType(): Promise<any> {
    return await this.kpiService.getLstCalType();
  }
  @DefPost('get-select-input-frequency')
  @Roles('/master-data/kpi-list', 'View')
  @UseGuards(RoleGuard)
  async getSelectInputFrequency(): Promise<any> {
    return await this.kpiService.getLstInputFrequency();
  }
  @DefPost('get-select-box-cal-method')
  @Roles('/master-data/kpi-list', 'View')
  @UseGuards(RoleGuard)
  async getSelectCalMethod(): Promise<any> {
    return await this.kpiService.getLstCalMethod();
  }
  @DefPost('get-select-box-utility-type')
  @Roles('/master-data/kpi-list', 'View')
  @UseGuards(RoleGuard)
  async getSelectUtilityType(): Promise<any> {
    return await this.kpiService.getLstUtilityType();
  }
  @DefPost('get-select-box-kpi-function')
  @Roles('/master-data/kpi-list', 'View')
  @UseGuards(RoleGuard)
  async getSelectKpiFunction(): Promise<any> {
    return await this.kpiService.getLstKpiFunction();
  }
  @ApiOperation({ summary: 'Phân trang danh sách kpi' })
  @DefPost('pagination')
  @Roles('/master-data/kpi-list', 'View')
  @UseGuards(RoleGuard)
  async pagination(@Body() params: KpiPaginationDto) {
    return await this.kpiService.pagination(params);
  }
  @DefPost('delete-kpi')
  @Roles('/master-data/kpi-list', 'View')
  @UseGuards(RoleGuard)
  async deleteKpi(@Body() id: string) {
    return await this.kpiService.delete(id);
  }
  @DefPost('detail-kpi')
  @Roles('/master-data/kpi-list', 'View')
  @UseGuards(RoleGuard)
  async detailKpi(@Body() id: string) {
    return await this.kpiService.findOne(id);
  }
  @DefPost('update-kpi')
  @Roles('/master-data/kpi-list', 'View')
  @UseGuards(RoleGuard)
  async updateKpi(@Body() params: KpiEditDto) {
    return await this.kpiService.update(params);
  }
  @DefPost('create-kpi')
  @Roles('/master-data/kpi-list', 'View')
  @UseGuards(RoleGuard)
  async createKpi(@Body() params: CreateKPIDto) {
    return await this.kpiService.create(params);
  }
  @DefPost('find')
  @Roles('/master-data/kpi-list', 'View')
  @UseGuards(RoleGuard)
  async findKpiList(@Body() params: { kpiGroupCode: string }) {
    return await this.kpiService.findKpiList(params);
  }
}
