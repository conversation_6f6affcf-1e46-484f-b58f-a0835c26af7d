import { ApiProperty } from '@nestjs/swagger';
import { Column, Entity, JoinColumn, ManyToOne } from 'typeorm';
import { PrimaryBaseEntity } from '../primary-base.entity';
import { OrganizationUnitEntity } from './organization-unit.entity';
import { ProductionAreaEntity } from './production-area.entity';

@Entity('production_area_detail')
export class ProductionAreaDetailEntity extends PrimaryBaseEntity {
  /** Trạng thái hoạt động */
  @ApiProperty({ description: 'Trạng thái hoạt động' })
  @Column({ nullable: false, default: true })
  isActive: boolean;

  /** processAreaId  */
  @ApiProperty({ description: 'ProcessAreaId' })
  @Column({ nullable: false })
  organizationUnitId: string;

  /** ID của khu vực sản xuất */
  @ApiProperty({ description: 'ID của khu vực sản xuất' })
  @Column({ nullable: false })
  productionAreaId: string;

  // /** Relation với bảng OrganizationUnit */
  @ManyToOne(() => OrganizationUnitEntity, (org) => org.productionAreaDetails, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'organizationUnitId', referencedColumnName: 'id' })
  organization?: OrganizationUnitEntity;

  // /** Relation với bảng ProductionAreaEntity */
  @ManyToOne(() => ProductionAreaEntity, (productionArea) => productionArea.details, {
    onDelete: 'CASCADE',
  })
  @JoinColumn({ name: 'productionAreaId', referencedColumnName: 'id' })
  productionArea?: ProductionAreaEntity;
}
