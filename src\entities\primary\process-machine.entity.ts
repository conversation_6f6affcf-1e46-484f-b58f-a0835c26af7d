import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>o<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, PrimaryGeneratedC<PERSON>umn, Column } from 'typeorm';
import { MachineEntity } from './machine.entity';
import { ProcessEntity } from './process.entity';
import { PrimaryBaseEntity } from '~/entities/primary-base.entity';

@Entity('process_machine')
export class ProcessMachineEntity extends PrimaryBaseEntity {
  @PrimaryGeneratedColumn('uuid')
  id: string;

  @Column({ type: 'uuid' })
  processId: string;

  @Column({ type: 'uuid' })
  machineId: string;

  @Column({ type: 'boolean', default: true })
  assign: boolean;

  @ManyToOne(() => ProcessEntity, (process) => process.processMachines, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'processId' })
  process: Promise<ProcessEntity>;

  @ManyToOne(() => MachineEntity, (machine) => machine.processMachines, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'machineId' })
  machine: Promise<MachineEntity>;
}
