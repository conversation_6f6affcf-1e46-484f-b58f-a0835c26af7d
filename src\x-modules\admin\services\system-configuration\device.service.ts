import { Body, NotFoundException, Param } from '@nestjs/common';
import { Like } from 'typeorm';
import { BindRepo } from '~/@core/decorator';
import { CreateDeviceDto, ListDeviceReq } from '~/dto/device.dto';
import { DeviceEntity } from '~/entities/primary/device.entity';
import { GeneralDataDetailRepo, GeneralDataRepo } from '~/repositories/primary';
import { DeviceRepo } from '~/repositories/primary/device.repo';

export class DeviceService {
  @BindRepo(DeviceRepo)
  private readonly deviceRepo: DeviceRepo;
  @BindRepo(GeneralDataRepo)
  private readonly generalDataRepo: GeneralDataRepo;
  @BindRepo(GeneralDataDetailRepo)
  private readonly generalDataDetailRepo: GeneralDataDetailRepo;

  async create(body: CreateDeviceDto): Promise<DeviceEntity> {
    try {
      const code = await this.generateCode();
      const newDevice = this.deviceRepo.create({
        ...body,
        code,
      });

      return this.deviceRepo.save(newDevice);
    } catch (error) {
      throw error;
    }
  }

  async update(id: string, data: Partial<DeviceEntity>) {
    try {
      const existingDevice = await this.deviceRepo.findOne({ where: { id } });

      if (!existingDevice) {
        throw new NotFoundException('Thiết bị không tồn tại');
      }

      const res = this.deviceRepo.update(id, data);
      return res;
    } catch (error) {
      throw error;
    }
  }

  async list(params: ListDeviceReq) {
    let whereCon: any = {};

    if (params?.code) {
      whereCon.code = params.code;
    }

    if (params?.description) {
      whereCon.description = Like(`%${params.description.trim()}%`);
    }

    if (params?.deviceTypeDetailCode) {
      whereCon.deviceTypeDetailCode = Like(`%${params.deviceTypeDetailCode.trim()}%`);
    }

    if (params?.isActive) {
      whereCon.isActive = params.isActive;
    }
    return this.deviceRepo.findPagination(
      {
        where: whereCon,
        order: { isActive: 'DESC', createdDate: 'DESC' },
      },
      params,
    );
  }

  async getDeviceTypeCode(deviveType: string) {
    try {
      const generalData = await this.generalDataRepo.findOne({
        where: { code: deviveType },
      });
      if (!generalData) {
        throw new NotFoundException(`General data with type ${deviveType} not found`);
      }

      const res = await this.generalDataDetailRepo.find({
        where: { generalId: generalData.id },
        order: { createdDate: 'DESC' },
      });
      return res;
    } catch (error) {
      throw error;
    }
  }

  private async generateCode(): Promise<number> {
    const existingDevices = await this.deviceRepo.find();
    const code = existingDevices.length;
    return code + 1;
  }
}
