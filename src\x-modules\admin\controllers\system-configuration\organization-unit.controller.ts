import { Body, Param, Put, Query, UseGuards } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { DefController, DefD<PERSON>te, DefGet, DefPatch, DefPost } from '~/@core/decorator';
import { ListOrganizationUnitReq, OrganizationUnitDto } from '~/dto/organization-unit.dto';
import { OrganizationUnitService } from '../../services';
import { Roles } from '~/@core/decorator/roles.decorator';
import { RoleGuard } from '~/@systems/guard/role.guard';

@ApiTags('OrganizationUnit')
@DefController('organization-units')
export class OrganizationUnitController {
  constructor(private readonly organizationUnitService: OrganizationUnitService) {}

  @ApiOperation({
    summary: 'Create organization unit',
    description: 'Create a new organization unit',
  })
  @Roles('/system-configuration/organization-unit', 'View')
  @UseGuards(RoleGuard)
  @DefPost()
  async create(@Body() createDto: OrganizationUnitDto) {
    return this.organizationUnitService.create(createDto);
  }

  @ApiOperation({
    summary: 'Get all organization units',
    description: 'Get all organization units with pagination',
  })
  @DefGet()
  @Roles('/system-configuration/organization-unit', 'View')
  @UseGuards(RoleGuard)
  async findAll(@Query() query: ListOrganizationUnitReq) {
    return this.organizationUnitService.list(query);
  }

  @ApiOperation({
    summary: 'Get all organization units without pagination',
    description: 'Get all organization units without pagination',
  })
  @DefGet('all')
  @Roles('/system-configuration/organization-unit', 'View')
  @UseGuards(RoleGuard)
  async findAllWithoutPagination() {
    return this.organizationUnitService.findAll();
  }

  @ApiOperation({
    summary: 'Get organization unit options',
    description: 'Get all active organization units as options (value-label pairs)',
  })
  @DefGet('options')
  @Roles('/system-configuration/organization-unit', 'View')
  @UseGuards(RoleGuard)
  async getOptions(@Query('levelCode') levelCode?: string) {
    return this.organizationUnitService.getOptions(levelCode);
  }

  @ApiOperation({
    summary: 'Get organization unit by ID',
    description: 'Get organization unit by ID',
  })
  @DefGet(':id')
  @Roles('/system-configuration/organization-unit', 'View')
  @UseGuards(RoleGuard)
  async findOne(@Param('id') id: string) {
    return this.organizationUnitService.findOne(id);
  }

  @ApiOperation({
    summary: 'Update organization unit (PATCH)',
    description: 'Update an existing organization unit using PATCH method',
  })
  @DefPatch(':id')
  @Roles('/system-configuration/organization-unit', 'View')
  @UseGuards(RoleGuard)
  async update(@Param('id') id: string, @Body() updateDto: OrganizationUnitDto) {
    return this.organizationUnitService.update(id, updateDto);
  }

  @ApiOperation({
    summary: 'Update organization unit (PUT)',
    description: 'Update an existing organization unit using PUT method',
  })
  @Put(':id')
  @Roles('/system-configuration/organization-unit', 'View')
  @UseGuards(RoleGuard)
  async updatePut(@Param('id') id: string, @Body() updateDto: OrganizationUnitDto) {
    return this.organizationUnitService.update(id, updateDto);
  }

  @ApiOperation({
    summary: 'Delete organization unit',
    description: 'Delete an organization unit by ID',
  })
  @Roles('/system-configuration/organization-unit', 'View')
  @UseGuards(RoleGuard)
  @DefDelete(':id')
  async remove(@Param('id') id: string) {
    return this.organizationUnitService.remove(id);
  }
}
