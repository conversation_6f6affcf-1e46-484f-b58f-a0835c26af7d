import { ApiProperty } from '@nestjs/swagger';
import { Column, Entity, Join<PERSON>olumn, ManyToOne } from 'typeorm';
import { PrimaryBaseEntity } from '../primary-base.entity';
import { ItemEntity } from './item.entity';
import { OrganizationUnitEntity } from './organization-unit.entity';
import { ProcessEntity } from './process.entity';
import { MachineEntity } from './machine.entity';
import { MachineParameterEntity } from './machine-parameter.entity';

const objColDecimal: any = {
  nullable: true,
  type: 'numeric',
  transformer: {
    to: value => +value || 0,
    from: value => +value || 0,
  },
};

/** Kế hoạch kiểm tra sản phẩm */
@Entity('inspection_plan')
export class InspectionPlanEntity extends PrimaryBaseEntity {
  @ApiProperty({ description: 'ID Đơn vị tổ chức' })
  @Column({ nullable: true })
  organizationUnitId: string;
  @ManyToOne(() => OrganizationUnitEntity, p => p.id)
  @JoinColumn({ name: 'organizationUnitId', referencedColumnName: 'id' })
  organizationUnit: OrganizationUnitEntity;

  @ApiProperty({ description: 'ID tiến trình' })
  @Column({ nullable: true })
  processId: string;
  @ManyToOne(() => ProcessEntity, p => p.id)
  @JoinColumn({ name: 'processId', referencedColumnName: 'id' })
  process: ProcessEntity;

  @ApiProperty({ description: 'ID máy móc' })
  @Column({ nullable: true })
  machineId: string;
  @ManyToOne(() => MachineEntity, p => p.id)
  @JoinColumn({ name: 'machineId', referencedColumnName: 'id' })
  machine: MachineEntity;

  @ApiProperty({ description: 'ID thông số máy móc' })
  @Column({ nullable: true })
  machineParameterId: string;
  @ManyToOne(() => MachineParameterEntity, p => p.id)
  @JoinColumn({ name: 'machineParameterId', referencedColumnName: 'id' })
  machineParameter: MachineEntity;

  @ApiProperty({ description: 'ID nguyên vật liệu' })
  @Column({ nullable: true })
  itemId: string;
  @ManyToOne(() => ItemEntity, p => p.id)
  @JoinColumn({ name: 'itemId', referencedColumnName: 'id' })
  item: ItemEntity;

  @ApiProperty({ description: 'Trạng thái hoạt động' })
  @Column({ nullable: false, default: true })
  isActive: boolean;

  @ApiProperty({ description: 'Mã tự sinh' })
  @Column({ nullable: true })
  code: string;

  @ApiProperty({ description: 'Kế hoạch kiểm tra theo' })
  @Column({ nullable: true })
  frequency: string;

  @ApiProperty({ description: 'Loại kế hoạch' })
  @Column({ nullable: true })
  dataType: string;

  @ApiProperty({ description: 'Số lượng sản phẩm đếm được để kích hoạt Inspection Plan tạo data' })
  @Column(objColDecimal)
  outputQty: number;

  @ApiProperty({ description: 'Lấy từ UOM trong item' })
  @Column({ nullable: true })
  uom: string;

  @ApiProperty({ description: 'Số thời gian đếm để kích hoạt Inspection Plan tạo data' })
  @Column(objColDecimal)
  timeValue: number;

  @ApiProperty({ description: 'Lấy từ master data code: DATETIME_UNIT' })
  @Column({ type: 'uuid', nullable: true })
  timeUnit: string;

  @ApiProperty({ description: 'Số min theo method frequency = BY_VALUE' })
  @Column(objColDecimal)
  minValue: number;

  @ApiProperty({ description: 'Số max theo method frequency = BY_VALUE' })
  @Column(objColDecimal)
  maxValue: number;

  @ApiProperty({ description: 'Lấy từ master data code: QA_SAMPLE_TYPE' })
  @Column({ type: 'uuid', nullable: true })
  sampleType: string;

  @ApiProperty({ description: 'Lấy từ master data code: QA_DEPARTMENT' })
  @Column({ type: 'uuid', nullable: true })
  costCenter: string;

  @ApiProperty({ description: 'Lấy từ master data code: ORG' })
  @Column({ type: 'uuid', nullable: true })
  receivingSite: string;

  @ApiProperty({ description: 'Lấy từ master data code: QA_DEPARTMENT' })
  @Column({ type: 'uuid', nullable: true })
  receivingDepartment: string;

  @ApiProperty({ description: 'Lấy từ master data code: QA_CATEGORY' })
  @Column({ type: 'uuid', nullable: true })
  category: string;

  @ApiProperty({ description: 'Thông tin mã Spec' })
  @Column({ nullable: true })
  specCode: string;

  @ApiProperty({ description: 'version mã Spec' })
  @Column({ nullable: true })
  specVersion: string;

  @ApiProperty({ description: 'Họ tên người tạo data' })
  @Column({ nullable: true })
  qcFullname: string;

  @ApiProperty({ description: '' })
  @Column({ nullable: true })
  aqlCode: string;

  @ApiProperty({ description: 'Lấy từ master data code: QA_INSPECTION_LEVEL' })
  @Column({ type: 'uuid', nullable: true })
  inspectionLevel: string;

  @ApiProperty({ description: 'Lấy từ master data code: QA_SEVERITY' })
  @Column({ type: 'uuid', nullable: true })
  aqlSeverity: string;

  @ApiProperty({ description: 'Ghi chú' })
  @Column({ nullable: true })
  description: string;
}
