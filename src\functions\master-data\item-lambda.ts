import { HttpStatus } from '@nestjs/common';
import { APIGatewayProxyResult, Callback, Context, Handler } from 'aws-lambda';
import { plainToInstance } from 'class-transformer';
import { validate } from 'class-validator';
import { EHttpStatusMessage } from '~/@core/network';
import { ErpApiResponse, ErpBusinessException } from '~/@systems/exceptions/dto/erp-exception';
import { ErpDataResponse } from '~/dto/erp/base.dto';
import { ERPCreateItemResponse } from '~/dto/erp/item.dto';
import { ERPCreateRecipeReq } from '~/dto/erp/recipe.dto';
import { ERPItemIntegrateService } from '~/x-modules/integration/services';
import { appInstanceProvider } from '../common/provider/app-instance.provider';
import withMiddlewares from '../common/middleware/base.middleware';
import { ApiTokenMiddleware } from '../common/middleware/api-token.middleware';

async function bootstrap() {
  return appInstanceProvider.getAppInstance();
}

const formatApiResponse = (body: any, statusCode = HttpStatus.OK): APIGatewayProxyResult => {
  return {
    statusCode,
    body: body?.toString ? body.toString() : JSON.stringify(body),
  };
};

/** 🆕 Hàm validate dữ liệu đầu vào
 * @param dtoClass Class DTO
 * @param data Dữ liệu cần validate
 * @param isArray Kiểu dữ liệu cần validate là mảng hay không
 */
async function validateInput(dtoClass: any, data: any, isArray = false) {
  if (isArray) {
    if (!Array.isArray(data)) {
      throw new ErpBusinessException(
        'Invalid input data',
        [{ property: 'data', constraints: { isArray: 'Data must be an array' } }],
        HttpStatus.UNPROCESSABLE_ENTITY,
      );
    }
    const instances = data.map((item: any) => plainToInstance(dtoClass, item));
    const errors = await Promise.all(instances.map(instance => validate(instance)));
    const errorList = errors.reduce((acc, val) => acc.concat(val), []);
    if (errorList.length > 0) {
      throw new ErpBusinessException(
        'Invalid input data',
        errorList,
        HttpStatus.UNPROCESSABLE_ENTITY,
      );
    }
  } else {
    const instance = plainToInstance(dtoClass, data);
    const errors = await validate(instance);
    if (errors.length > 0) {
      throw new ErpBusinessException('Invalid input data', errors, HttpStatus.UNPROCESSABLE_ENTITY);
    }
  }
  return true;
}

/**
 * Hàm tạo hoặc cập nhật thành phần/nguyên liệu/sản phẩm
 * @param event
 * @param context
 * @returns
 */
const batchCreateOrUpdateHandler: Handler = async (
  event: any,
  context: Context,
  next: Callback,
) => {
  try {
    const app = await bootstrap();
    const itemService = app.get(ERPItemIntegrateService);

    // Parse request body
    let body;
    try {
      body = JSON.parse(event.body);
      console.log('Body:', body);
    } catch (parseError) {
      return formatApiResponse(
        new ErpApiResponse(
          EHttpStatusMessage[HttpStatus.BAD_REQUEST],
          HttpStatus.BAD_REQUEST,
          'Invalid JSON in request body',
        ),
        HttpStatus.BAD_REQUEST,
      );
    }

    // Validate input data
    await validateInput(ERPCreateRecipeReq, body, true);

    // Process the request
    const result = await itemService.batchCreateOrUpdate(body);
    console.log('Result:', result);

    // Return success response
    return formatApiResponse(
      new ErpApiResponse<ErpDataResponse<ERPCreateItemResponse[]>>(
        EHttpStatusMessage[HttpStatus.OK],
        HttpStatus.OK,
        undefined,
        'success',
        {
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          refs: result.data?.map(item => ({
            item_code: item.code,
          })),
        },
      ),
      HttpStatus.OK,
    );
  } catch (error) {
    console.error('Error:', error);
    if (error instanceof ErpBusinessException) {
      return formatApiResponse(error, error.code);
    }
    // Handle unknown errors
    return formatApiResponse(error, HttpStatus.INTERNAL_SERVER_ERROR);
  }
};

export const batchCreateOrUpdate = withMiddlewares(batchCreateOrUpdateHandler, [
  ApiTokenMiddleware(),
]);
