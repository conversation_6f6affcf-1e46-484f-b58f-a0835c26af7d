import { <PERSON>ti<PERSON>, PrimaryColumn, <PERSON>umn, <PERSON>ToOne, Join<PERSON><PERSON>um<PERSON> } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { PrimaryBaseEntity } from '../primary-base.entity';
import { OrganizationUnitEntity } from './organization-unit.entity';
import { ProductionAreaEntity } from './production-area.entity';
import { KpiSetHeaderEntity } from './kpi-set-header.entity';

@Entity('kpi_set_production_area')
export class KpiSetProductAreaEntity extends PrimaryBaseEntity {
  /** id KPI set header */
  @ApiProperty({ description: 'id KPI set header' })
  @Column({ nullable: true })
  kpiSetHeaderId: string;
  @ManyToOne(() => KpiSetHeaderEntity, org => org.id)
  @JoinColumn({ name: 'kpiSetHeaderId', referencedColumnName: 'id' })
  kpiSetHeader?: KpiSetHeaderEntity;

  /** <PERSON>ã nhà máy, chọn từ bảng orgchart - chọn các code có level = 3-Factory */
  @ApiProperty({ description: 'Id nhà máy' })
  @Column({ nullable: true })
  factoryId: string;
  @ManyToOne(() => OrganizationUnitEntity, org => org.id)
  @JoinColumn({ name: 'factoryId', referencedColumnName: 'id' })
  factory?: OrganizationUnitEntity;

  /** Id Site , chọn từ bảng orgchart - chọn các code có level = Line */
  @ApiProperty({ description: 'Id site' })
  @Column({ nullable: true })
  siteId: string;
  @ManyToOne(() => OrganizationUnitEntity, org => org.id)
  @JoinColumn({ name: 'siteId', referencedColumnName: 'id' })
  site?: OrganizationUnitEntity;

  /** Mã khu vực sản xuất, chọn từ bảng Production Area, chỉ hiện những Production Area của Facory đang chọn */
  @ApiProperty({ description: 'Id khu vực sản xuất' })
  @Column({ nullable: true })
  productionAreaId: string;
  @ManyToOne(() => ProductionAreaEntity, org => org.id)
  @JoinColumn({ name: 'productionAreaId', referencedColumnName: 'id' })
  productionArea?: ProductionAreaEntity;

  /** Trạng thái sử dụng: 1-Active 0-Inactive */
  @ApiProperty({ description: 'Trạng thái sử dụng' })
  @Column({ type: 'int', nullable: true, default: 1 })
  status: number;
}
