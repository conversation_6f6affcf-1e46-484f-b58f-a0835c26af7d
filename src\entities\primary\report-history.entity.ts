import { BaseEntity as Base, Column, Entity, JoinColumn, ManyToOne } from 'typeorm';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { PrimaryBaseEntity } from '~/entities/primary-base.entity';
import { GeneralDataDetailEntity } from '~/entities/primary';

@Entity('report_history')
export class ReportHistoryEntity extends PrimaryBaseEntity {
  // @ApiProperty()
  // @PrimaryGeneratedColumn('uuid')
  // @IsUUID(4)
  // id?: string;

  /**  ID của report, liên kết với GeneralDataDetailEntity. */
  @ApiPropertyOptional({ description: 'ID của report' })
  @Column({ nullable: true })
  reportId?: string;
  @ManyToOne(() => GeneralDataDetailEntity)
  @JoinColumn({ name: 'reportId', referencedColumnName: 'id' })
  report?: Promise<GeneralDataDetailEntity>;

  /** Mã GeneralDataDetail của report. */
  @ApiPropertyOptional({ description: 'Mã GeneralDataDetail của report' })
  @Column({ default: '' })
  reportCode?: string;
  /** Tên GeneralDataDetail của report. */
  @ApiPropertyOptional({ description: 'Tên GeneralDataDetail của report' })
  @Column({ default: '' })
  reportName?: string;

  @ApiProperty({ description: 'link download' })
  @Column({ type: 'text', nullable: true })
  linkDownload?: string;

  @ApiProperty({ description: 'Status' })
  @Column({ nullable: true })
  status?: string;

  // @Index()
  // @ApiPropertyOptional()
  // @Column({ type: 'varchar', nullable: true })
  // createdBy?: string | null;

  @ApiPropertyOptional({ description: 'email' })
  @Column({ type: 'varchar', nullable: true })
  email?: string | null;

  @ApiPropertyOptional({ description: 'input' })
  @Column({ type: 'jsonb', nullable: true })
  input?: any;

  @ApiPropertyOptional({ description: 'output' })
  @Column({ type: 'jsonb', nullable: true })
  output?: any;

  @ApiPropertyOptional({ description: 'output total' })
  @Column({ nullable: true })
  outputTotal?: number;

  // @Index()
  // @ApiProperty()
  // @CreateDateColumn({ type: 'timestamptz', nullable: true })
  // createdDate?: Date;

  @ApiPropertyOptional({ description: 'completed date' })
  @Column({ type: 'timestamptz', nullable: true })
  completedDate?: Date;
}
