---
#------------------------ SYSTEM CONFIGURATION ------------------------

# MACHINE
createMachine:
  handler: dist/functions/machine-lambda.create
  events:
    - http:
        path: admin/machines
        method: post

listMachines:
  handler: dist/functions/machine-lambda.list
  events:
    - http:
        path: admin/machines
        method: get

detailMachine:
  handler: dist/functions/machine-lambda.detail
  events:
    - http:
        path: admin/machines/{id}
        method: get

updateMachine:
  handler: dist/functions/machine-lambda.update
  events:
    - http:
        path: admin/machines/{id}
        method: put

deleteMachine:
  handler: dist/functions/machine-lambda.delete
  events:
    - http:
        path: admin/machines/{id}
        method: delete

listMachineParameter:
  handler: dist/functions/machine-lambda.listMachineParameter
  events:
    - http:
        path: admin/machines/{machineId}/parameters
        method: get

batchUpdateMachineParameters:
  handler: dist/functions/machine-lambda.batchUpdateMachineParameters
  events:
    - http:
        path: admin/machines/{machineId}/parameters/batch
        method: put

# ------------------------ MASTER DATA ------------------------
createReasonMaster:
  handler: dist/functions/master-data/reason-master-lambda.createReasonMaster
  events:
    - http:
        path: master-data/reason-master/create
        method: post
loadSelectParent:
  handler: dist/functions/master-data/reason-master-lambda.loadSelectParent
  events:
    - http:
        path: master-data/reason-master/load-data-parent
        method: post
paginationReasonMaster:
  handler: dist/functions/master-data/reason-master-lambda.paginationReasonMaster
  events:
    - http:
        path: master-data/reason-master/pagination
        method: post

findReasonMasterById:
  handler: dist/functions/master-data/reason-master-lambda.findReasonMasterById
  events:
    - http:
        path: master-data/reason-master/detail
        method: post

updateReasonMaster:
  handler: dist/functions/master-data/reason-master-lambda.updateReasonMaster
  events:
    - http:
        path: master-data/reason-master/update
        method: post

deleteReasonMaster:
  handler: dist/functions/master-data/reason-master-lambda.deleteReasonMaster
  events:
    - http:
        path: master-data/reason-master/delete
        method: post

loadEnumForReasonMaster:
  handler: dist/functions/master-data/reason-master-lambda.loadEnumForReasonMaster
  events:
    - http:
        path: master-data/reason-master/load-enum
        method: post

# INSPECTION PLAN
createInspectionPlan:
  handler: dist/functions/master-data/inspection-plan-lambda.createInspectionPlan
  events:
    - http:
        path: admin/inspection-plan
        method: post

updateInspectionPlan:
  handler: dist/functions/master-data/inspection-plan-lambda.updateInspectionPlan
  events:
    - http:
        path: admin/inspection-plan
        method: put

paginationInspectionPlan:
  handler: dist/functions/master-data/inspection-plan-lambda.paginationInspectionPlan
  events:
    - http:
        path: admin/inspection-plan/pagination
        method: post

findInspectionPlanById:
  handler: dist/functions/master-data/inspection-plan-lambda.findInspectionPlanById
  events:
    - http:
        path: admin/inspection-plan/{id}
        method: get

loadDataSelectForCreate:
  handler: dist/functions/master-data/inspection-plan-lambda.loadDataSelectForCreate
  events:
    - http:
        path: admin/reason-master/dataselect-for-create
        method: get

# ------------------------ RECIPE ------------------------

# ------------------------ PRODUCTION EXECUTION MONITORING ------------------------

createCard:
  handler: dist/functions/test-lambda.createCard
  events:
    - http:
        path: cards
        method: post

updateCard:
  handler: dist/functions/test-lambda.updateCard
  events:
    - http:
        path: cards/{id}
        method: put

findCardById:
  handler: dist/functions/test-lambda.findCardById
  events:
    - http:
        path: cards/{id}
        method: get
processList:
  handler: dist/functions/process-lambda.list
  events:
    - http:
        path: process
        method: get
processCreate:
  handler: dist/functions/process-lambda.create
  events:
    - http:
        path: process
        method: post
processUpdate:
  handler: dist/functions/process-lambda.update
  events:
    - http:
        path: process/{id}
        method: put
processDelete:
  handler: dist/functions/process-lambda.deleteHandler
  events:
    - http:
        path: process/{id}
        method: delete
getMachineList:
  handler: dist/functions/process-lambda.getMachineList
  events:
    - http:
        path: process/get/machines
        method: get
processFindById:
  handler: dist/functions/process-lambda.findByIdHandler
  events:
    - http:
        path: process/{id}
        method: get
getForOrganizationUnits:
  handler: dist/functions/process-lambda.getForOrganizationUnits
  events:
    - http:
        path: process/get/organization-units
        method: get

# ------------------------ USER ------------------------
getUserList:
  handler: dist/functions/user.getUserList
  events:
    - http:
        path: admin/users
        method: get

getUser:
  handler: dist/functions/user.getUser
  events:
    - http:
        path: admin/users/{id}
        method: get

createUser:
  handler: dist/functions/user.createUser
  events:
    - http:
        path: admin/users
        method: post

updateUser:
  handler: dist/functions/user.updateUser
  events:
    - http:
        path: admin/users/{id}
        method: put

# ------------------------ User Access ------------------------
# Access APIs
getOrganizationList:
  handler: dist/functions/access-lambda.getOrganizationList
  events:
    - http:
        path: admin/access/organization-units
        method: get

updateAccess:
  handler: dist/functions/access-lambda.updateAccess
  events:
    - http:
        path: admin/access/{userId}
        method: put

getUserAccess:
  handler: dist/functions/access-lambda.getUserAccess
  events:
    - http:
        path: admin/access/{userId}
        method: get

# ------------------------ Production Area ------------------------

getProductionAreaList:
  handler: dist/functions/production-area-lambda.getProductionAreaList
  events:
    - http:
        path: /admin/production-areas
        method: get

getProductionAreaInfo:
  handler: dist/functions/production-area-lambda.getProductionAreaInfo
  events:
    - http:
        path: /admin/production-areas/{id}
        method: get

getProductionAreaDetails:
  handler: dist/functions/production-area-lambda.getProductionAreaDetails
  events:
    - http:
        path: /admin/production-areas/{id}/details
        method: get

getFactoryList:
  handler: dist/functions/production-area-lambda.getFactoryList
  events:
    - http:
        path: admin/production-areas/factories
        method: get

getAllGeneralDataDetail:
  handler: dist/functions/production-area-lambda.getAllGeneralDataDetail
  events:
    - http:
        path: admin/production-areas/general-data-details
        method: get

getProcessAreaByFactoryId:
  handler: dist/functions/production-area-lambda.getProcessAreaByFactoryId
  events:
    - http:
        path: admin/production-areas/process-areas/{factoryId}
        method: get

getAllProductionAreas:
  handler: dist/functions/production-area-lambda.getAllProductionAreas
  events:
    - http:
        path: admin/production-areas/all
        method: get

getLinesByFactoryId:
  handler: dist/functions/production-area-lambda.getLinesByFactoryId
  events:
    - http:
        path: admin/production-areas/lines/{factoryId}
        method: get

createProductionArea:
  handler: dist/functions/production-area-lambda.createProductionArea
  events:
    - http:
        path: admin/production-areas
        method: post

updateProductionArea:
  handler: dist/functions/production-area-lambda.updateProductionArea
  events:
    - http:
        path: admin/production-areas/{id}
        method: put
        cors: true

# Organization Unit Functions
createOrganizationUnit:
  handler: dist/functions/organization-unit.create
  events:
    - http:
        path: admin/organization-units
        method: post

getAllOrganizationUnits:
  handler: dist/functions/organization-unit.list
  events:
    - http:
        path: admin/organization-units
        method: get

getAllOrganizationUnitsAll:
  handler: dist/functions/organization-unit.findAll
  events:
    - http:
        path: admin/organization-units/all
        method: get

getOrganizationUnitById:
  handler: dist/functions/organization-unit.findOne
  events:
    - http:
        path: admin/organization-units/{id}
        method: get

updateOrganizationUnit:
  handler: dist/functions/organization-unit.update
  events:
    - http:
        path: admin/organization-units/{id}
        method: patch

deleteOrganizationUnit:
  handler: dist/functions/organization-unit.remove
  events:
    - http:
        path: admin/organization-units/{id}
        method: delete

# Shift Functions
getAllShifts:
  handler: dist/functions/shift-lambda.findAll
  events:
    - http:
        path: admin/shifts
        method: get

getShiftById:
  handler: dist/functions/shift-lambda.findOne
  events:
    - http:
        path: admin/shifts/{id}
        method: get
createShift:
  handler: dist/functions/shift-lambda.create
  events:
    - http:
        path: admin/shifts
        method: post

updateShift:
  handler: dist/functions/shift-lambda.update
  events:
    - http:
        path: admin/shifts/{id}
        method: put

optionsShift:
  handler: dist/functions/shift-lambda.options
  events:
    - http:
        path: admin/shifts/{proxy+}
        method: options

# Holiday Functions
getAllHolidays:
  handler: dist/functions/holiday-lambda.findAll
  events:
    - http:
        path: admin/holidays
        method: get

getHolidayById:
  handler: dist/functions/holiday-lambda.findOne
  events:
    - http:
        path: admin/holidays/{id}
        method: get
createHoliday:
  handler: dist/functions/holiday-lambda.create
  events:
    - http:
        path: admin/holidays
        method: post

updateHoliday:
  handler: dist/functions/holiday-lambda.update
  events:
    - http:
        path: admin/holidays/{id}
        method: put

optionsHoliday:
  handler: dist/functions/holiday-lambda.options
  events:
    - http:
        path: admin/holidays/{proxy+}
        method: options

# GENERAL DATA
createGeneralData:
  handler: dist/functions/general-data.create
  events:
    - http:
        path: admin/general-data
        method: post

listGeneralData:
  handler: dist/functions/general-data.list
  events:
    - http:
        path: admin/general-data
        method: get

findAllGeneralData:
  handler: dist/functions/general-data.findAll
  events:
    - http:
        path: admin/general-data/all
        method: get

getGeneralDataById:
  handler: dist/functions/general-data.findOne
  events:
    - http:
        path: admin/general-data/{id}
        method: get

updateGeneralData:
  handler: dist/functions/general-data.update
  events:
    - http:
        path: admin/general-data/{id}
        method: patch

deleteGeneralData:
  handler: dist/functions/general-data.remove
  events:
    - http:
        path: admin/general-data/{id}
        method: delete

listGeneralDataDetails:
  handler: dist/functions/general-data.listDetails
  events:
    - http:
        path: admin/general-data/{id}/details
        method: get

createGeneralDataDetails:
  handler: dist/functions/general-data.createDetails
  events:
    - http:
        path: admin/general-data/{id}/details
        method: post

getGeneralDataDetailById:
  handler: dist/functions/general-data.findDetailById
  events:
    - http:
        path: admin/general-data/{generalId}/details/{detailId}
        method: get

updateGeneralDataDetails:
  handler: dist/functions/general-data.updateDetails
  events:
    - http:
        path: admin/general-data/{generalId}/details/{detailId}
        method: patch

deleteGeneralDataDetail:
  handler: dist/functions/general-data.removeDetail
  events:
    - http:
        path: admin/general-data/{generalId}/details/{detailId}
        method: delete
utility-meter-type:
  handler: dist/functions/utility-meter-type.findAll
  events:
    - http:
        path: admin/v1/utility-meter-types
        method: get
        cors: true

utility-meter-type-find-one:
  handler: dist/functions/utility-meter-type.findOne
  events:
    - http:
        path: admin/v1/utility-meter-types/{id}
        method: get
        cors: true

utility-meter-type-create:
  handler: dist/functions/utility-meter-type.create
  events:
    - http:
        path: admin/v1/utility-meter-types
        method: post
        cors: true

utility-meter-type-update:
  handler: dist/functions/utility-meter-type.update
  events:
    - http:
        path: admin/v1/utility-meter-types/{id}
        method: put
        cors: true

utility-meter-type-remove:
  handler: dist/functions/utility-meter-type.remove
  events:
    - http:
        path: admin/v1/utility-meter-types/{id}
        method: delete
        cors: true

utility-meter-type-update-status:
  handler: dist/functions/utility-meter-type.updateStatus
  events:
    - http:
        path: admin/v1/utility-meter-types/{id}/status
        method: patch
        cors: true

utility-meter-type-find-active:
  handler: dist/functions/utility-meter-type.findActiveTypes
  events:
    - http:
        path: admin/v1/utility-meter-types/active
        method: get
        cors: true

utility-meter-type-find-inactive:
  handler: dist/functions/utility-meter-type.findInactiveTypes
  events:
    - http:
        path: admin/v1/utility-meter-types/inactive
        method: get
        cors: true

utility-meter-type-search:
  handler: dist/functions/utility-meter-type.searchByTerm
  events:
    - http:
        path: admin/v1/utility-meter-types/search
        method: get
        cors: true

# Utility Meter Type APIs
getAllUtilityMeterTypes:
  handler: dist/functions/utility-meter-type.findAll
  events:
    - http:
        path: admin/utility-meter-types
        method: get
        cors: true

getUtilityMeterTypeById:
  handler: dist/functions/utility-meter-type.findOne
  events:
    - http:
        path: admin/utility-meter-types/{id}
        method: get
        cors: true

createUtilityMeterType:
  handler: dist/functions/utility-meter-type.create
  events:
    - http:
        path: admin/utility-meter-types
        method: post
        cors: true

updateUtilityMeterType:
  handler: dist/functions/utility-meter-type.update
  events:
    - http:
        path: admin/utility-meter-types/{id}
        method: patch
        cors: true

deleteUtilityMeterType:
  handler: dist/functions/utility-meter-type.remove
  events:
    - http:
        path: admin/utility-meter-types/{id}
        method: delete
        cors: true

updateUtilityMeterTypeStatus:
  handler: dist/functions/utility-meter-type.updateStatus
  events:
    - http:
        path: admin/utility-meter-types/{id}/status
        method: patch
        cors: true

# UOM APIs
getAllUOMs:
  handler: dist/functions/uom.findAll
  events:
    - http:
        path: admin/uoms
        method: get
        cors: true

getUOMById:
  handler: dist/functions/uom.findOne
  events:
    - http:
        path: admin/uoms/{id}
        method: get
        cors: true

createUOM:
  handler: dist/functions/uom.create
  events:
    - http:
        path: admin/uoms
        method: post
        cors: true

updateUOM:
  handler: dist/functions/uom.update
  events:
    - http:
        path: admin/uoms/{id}
        method: patch
        cors: true

deleteUOM:
  handler: dist/functions/uom.remove
  events:
    - http:
        path: admin/uoms/{id}
        method: delete
        cors: true

updateUOMStatus:
  handler: dist/functions/uom.updateStatus
  events:
    - http:
        path: admin/uoms/{id}/status
        method: patch
        cors: true

# Data Type APIs
getAllDataTypes:
  handler: dist/functions/data-type.findAll
  events:
    - http:
        path: admin/data-types
        method: get
        cors: true

getDataTypeById:
  handler: dist/functions/data-type.findOne
  events:
    - http:
        path: admin/data-types/{id}
        method: get
        cors: true

createDataType:
  handler: dist/functions/data-type.create
  events:
    - http:
        path: admin/data-types
        method: post
        cors: true

updateDataType:
  handler: dist/functions/data-type.update
  events:
    - http:
        path: admin/data-types/{id}
        method: patch
        cors: true

deleteDataType:
  handler: dist/functions/data-type.remove
  events:
    - http:
        path: admin/data-types/{id}
        method: delete
        cors: true

updateDataTypeStatus:
  handler: dist/functions/data-type.updateStatus
  events:
    - http:
        path: admin/data-types/{id}/status
        method: patch
        cors: true

# Utility Meters APIs
getAllUtilityMeters:
  handler: dist/functions/utility-meters.findAll
  events:
    - http:
        path: admin/utility-meters
        method: get
        cors: true

getUtilityMeterById:
  handler: dist/functions/utility-meters.findOne
  events:
    - http:
        path: admin/utility-meters/{id}
        method: get
        cors: true

createUtilityMeter:
  handler: dist/functions/utility-meters.create
  events:
    - http:
        path: admin/utility-meters
        method: post
        cors: true

updateUtilityMeter:
  handler: dist/functions/utility-meters.update
  events:
    - http:
        path: admin/utility-meters/{id}
        method: patch
        cors: true

deleteUtilityMeter:
  handler: dist/functions/utility-meters.remove
  events:
    - http:
        path: admin/utility-meters/{id}
        method: delete
        cors: true

updateUtilityMeterStatus:
  handler: dist/functions/utility-meters.updateStatus
  events:
    - http:
        path: admin/utility-meters/{id}/status
        method: patch
        cors: true

findActiveUtilityMeters:
  handler: dist/functions/utility-meters.findActiveMeters
  events:
    - http:
        path: admin/utility-meters/active
        method: get
        cors: true

findInactiveUtilityMeters:
  handler: dist/functions/utility-meters.findInactiveMeters
  events:
    - http:
        path: admin/utility-meters/inactive
        method: get
        cors: true

searchUtilityMeters:
  handler: dist/functions/utility-meters.searchByTerm
  events:
    - http:
        path: admin/utility-meters/search
        method: get
        cors: true

findMetersByProcessArea:
  handler: dist/functions/utility-meters.findMetersByProcessArea
  events:
    - http:
        path: admin/utility-meters/process-area/{processAreaId}
        method: get
        cors: true

findMetersByType:
  handler: dist/functions/utility-meters.findMetersByType
  events:
    - http:
        path: admin/utility-meters/type/{typeId}
        method: get
        cors: true

findMetersByUOM:
  handler: dist/functions/utility-meters.findMetersByUOM
  events:
    - http:
        path: admin/utility-meters/uom/{uomId}
        method: get
        cors: true

findMetersByDataType:
  handler: dist/functions/utility-meters.findMetersByDataType
  events:
    - http:
        path: admin/utility-meters/data-type/{dataTypeId}
        method: get
        cors: true

# User Group Functions
getAllUserGroups:
  handler: dist/functions/user-group.getAllUserGroups
  events:
    - http:
        path: admin/user-groups
        method: get

createUserGroup:
  handler: dist/functions/user-group.createUserGroup
  events:
    - http:
        path: admin/user-groups
        method: post

getUserGroupById:
  handler: dist/functions/user-group.getUserGroupById
  events:
    - http:
        path: admin/user-groups/{id}
        method: get

updateUserGroup:
  handler: dist/functions/user-group.updateUserGroup
  events:
    - http:
        path: admin/user-groups/{id}
        method: patch

deleteUserGroup:
  handler: dist/functions/user-group.deleteUserGroup
  events:
    - http:
        path: admin/user-groups/{id}
        method: delete

updateUserGroupStatus:
  handler: dist/functions/user-group.updateUserGroupStatus
  events:
    - http:
        path: admin/user-groups/{id}/status
        method: patch

toggleUserGroupStatus:
  handler: dist/functions/user-group.toggleUserGroupStatus
  events:
    - http:
        path: admin/user-groups/{id}/toggle-status
        method: patch

findActiveUserGroups:
  handler: dist/functions/user-group.findActiveUserGroups
  events:
    - http:
        path: admin/user-groups/active
        method: get

findInactiveUserGroups:
  handler: dist/functions/user-group.findInactiveUserGroups
  events:
    - http:
        path: admin/user-groups/inactive
        method: get

searchUserGroups:
  handler: dist/functions/user-group.searchUserGroups
  events:
    - http:
        path: admin/user-groups/search
        method: get

findGroupsWithUsers:
  handler: dist/functions/user-group.findGroupsWithUsers
  events:
    - http:
        path: admin/user-groups/with-users
        method: get

findGroupWithUsers:
  handler: dist/functions/user-group.findGroupWithUsers
  events:
    - http:
        path: admin/user-groups/{id}/with-users
        method: get

addUsersToGroup:
  handler: dist/functions/user-group.addUsersToGroup
  events:
    - http:
        path: admin/user-groups/{id}/users
        method: post

removeUsersFromGroup:
  handler: dist/functions/user-group.removeUsersFromGroup
  events:
    - http:
        path: admin/user-groups/{id}/users
        method: delete

# Auth APIs
validateToken:
  handler: dist/functions/auth-lambda.validateToken
  events:
    - http:
        path: auth/validate-token
        method: post
        cors: true
