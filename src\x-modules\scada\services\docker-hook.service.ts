import { Injectable } from '@nestjs/common';
import { BindRepo } from '~/@core/decorator';
import { AcceptMaterialConsumptionReq, ScadaDataExchangeReq } from '~/dto/scada.dto';
import {
  MachineRepo,
  ProductionDowntimeRepo,
  ProcessMachineRepo,
  ProductionOrderRepo,
  ProductionBatchRepo,
  RecipeProcessItemRepo,
  ItemRepo,
  ProcessRepo,
  MaterialConsumptionScadaRepo,
  UomConventionRepo,
} from '~/repositories/primary';
import { MaterialConsumptionScadaEntity, ProductionDowntimeEntity } from '~/entities/primary';
import { SQSService } from '~/x-modules/sqs/sqs.service';
import { NSSQSS } from '~/common/enums';
import { SnsService } from '~/x-modules/sqs/sns.service';
@Injectable()
export class DockerHookService {
  constructor(
    private readonly sqsService: SQSService,
    private readonly snsService: SnsService,
  ) {}

  @BindRepo(ProductionDowntimeRepo)
  private productionDowntimeRepo: ProductionDowntimeRepo;
  @BindRepo(MachineRepo)
  private machineRepo: MachineRepo;
  @BindRepo(ProcessMachineRepo)
  private processMachineRepo: ProcessMachineRepo;
  @BindRepo(ProductionOrderRepo)
  private productionOrderRepo: ProductionOrderRepo;
  @BindRepo(ProductionBatchRepo)
  private productionBatchRepo: ProductionBatchRepo;
  @BindRepo(RecipeProcessItemRepo)
  private recipeProcessItemRepo: RecipeProcessItemRepo;
  @BindRepo(ItemRepo)
  private itemRepo: ItemRepo;
  @BindRepo(ProcessRepo)
  private processRepo: ProcessRepo;
  @BindRepo(MaterialConsumptionScadaRepo)
  private materialConsumptionScadaRepo: MaterialConsumptionScadaRepo;
  @BindRepo(UomConventionRepo)
  private uomConventionRepo: UomConventionRepo;
  /**
   * Xử lý dữ liệu từ SCADA
   * @param data
   * @returns
   */
  async scadaDataExchange(data: ScadaDataExchangeReq) {
    const downtimeSearch = await this.productionDowntimeRepo.findOne({
      where: {
        scadaDowntimeId: data.downtimeId,
      },
    });
    const machine = await this.machineRepo.findOne({
      where: {
        code: data.machineCode,
      },
    });

    if (!machine) {
      throw new Error(`Machine not found: ${data.machineCode}`);
    }

    const machineProcess = await this.processMachineRepo.find({
      where: {
        machineId: machine.id,
      },
    });
    if (machineProcess.length > 0) {
      const processId = machineProcess[0]?.processId;
      const process = await this.processRepo.findOne({
        where: {
          id: processId,
        },
      });
      const processAreaId = process?.organizationUnitId;

      const downtime = new ProductionDowntimeEntity();
      downtime.scadaDowntimeId = data.downtimeId;
      downtime.machineCode = data.machineCode;
      downtime.startTime = new Date(data.startTime);
      downtime.endTime = new Date(data.endTime);
      downtime.reasonCode = data.reasonCode;
      downtime.reasonDescription = data.reasonDesc;
      downtime.machineId = machine?.id;
      downtime.processAreaId = processAreaId;
      downtime.cancelledFlag = 0;

      if (downtimeSearch) {
        downtime.id = downtimeSearch.id;
        downtime.updatedByUser = 'Server';
      } else {
        downtime.createdByUser = 'Server';
      }

      const result = await this.productionDowntimeRepo.save(downtime);
      return {
        created_at: result.createdDate,
        updated_at: result.updatedDate,
        refs: [
          {
            downtimeId: data.downtimeId,
            machineCode: data.machineCode,
          },
        ],
      };
    }
  }

  async acceptMaterialConsumption(data: AcceptMaterialConsumptionReq) {
    // console.log('acceptMaterialConsumption', data);

    const order = await this.productionOrderRepo.findOne({
      where: {
        orderNo: data.ProductionOrderNumber,
      },
    });

    if (!order) {
      throw new Error(`Order not found: ${data.ProductionOrderNumber}`);
    }

    const batch = await this.productionBatchRepo.findOne({
      where: {
        batchNumber: Number(data.BatchCode),
        orderId: order.id,
      },
    });
    if (batch) {
      for (const consumption of data.Consumptions) {
        const item = await this.itemRepo.findOne({
          where: {
            code: consumption.IngredientCode,
          },
        });

        const recipeProcessItem = await this.recipeProcessItemRepo.findOne({
          where: {
            itemId: item.id,
          },
        });
        let listUnit = [];

        if (item) {
          listUnit.push(item.baseUnit);
          listUnit.push(item.inventoryUnit);
          const uomConventions = await this.uomConventionRepo.find({
            where: {
              itemId: item.id,
            },
          });
          for (const uomConvention of uomConventions) {
            listUnit.push(uomConvention.fromUnit);
            listUnit.push(uomConvention.toUnit);
          }
          if (recipeProcessItem) {
            // Kiểm tra consumption.UnitOfMeasurment có trong listUnit không
            if (listUnit.includes(consumption.UnitOfMeasurment)) {
              const materialConsumption = new MaterialConsumptionScadaEntity();
              materialConsumption.orderNumber = data.ProductionOrderNumber;
              materialConsumption.orderId = order.id;
              materialConsumption.batchCode = data.BatchCode;
              materialConsumption.batchId = batch.id;
              materialConsumption.ingredientCode = consumption.IngredientCode;
              materialConsumption.ingredientId = item.id;
              materialConsumption.lotNumber = consumption.Lot;
              materialConsumption.qty = Number(consumption.Quantity);
              materialConsumption.uom = consumption.UnitOfMeasurment;
              materialConsumption.datetime = new Date(consumption.Datetime);
              materialConsumption.operatorId = consumption.Operator_ID;
              materialConsumption.transactionType = 'Consumption';
              materialConsumption.isProcessed = false;
              await this.materialConsumptionScadaRepo.save(materialConsumption);
            } else {
              throw new Error('UOM not exist for item');
            }
          } else {
            throw new Error('Recipe process item not found');
          }
        } else {
          throw new Error('Item not found');
        }
      }
    } else {
      throw new Error('Batch not found');
    }

    return 'Success';
  }
}
