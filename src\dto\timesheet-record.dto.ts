import { Optional } from '@nestjs/common';
import { ApiProperty } from '@nestjs/swagger';
import { IsEnum, IsNotEmpty, IsN<PERSON>ber, IsOptional, IsString } from 'class-validator';

/**
 * DTO list production area
 */
export class RequestListProductioAreaTimeSheetDTO {
  factoryId: string | 'All';
}

/**
 * DTO list shift
 */
export class RequestListShiftTimeSheetDTO {
  productionAreaId: string | 'All';
}

/**
 * DTO user timesheet
 */
export class RequestUserTimeSheetDTO {
  // @ApiProperty({
  //   description: 'ID khu vực sản xuất',
  //   example: '550e8400-e29b-41d4-a716-446655440002',
  // })
  // @IsNotEmpty()
  // productionAreaId: string;

  // @ApiProperty({
  //   description: 'ID của ca làm việc',
  //   example: '550e8400-e29b-41d4-a716-446655440001',
  // })
  // @Optional()
  // shiftId?: string;

  @ApiProperty({
    description: 'ID của người dùng',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @IsNotEmpty()
  userId: string;
}

/**
 * DTO check in
 */
export class RequestCheckInDTO {
  @ApiProperty({
    description: 'ID khu vực sản xuất',
    example: '550e8400-e29b-41d4-a716-446655440002',
  })
  @IsNotEmpty()
  productionAreaId: string;

  @ApiProperty({
    description: 'ID của ca làm việc',
    example: '550e8400-e29b-41d4-a716-446655440001',
  })
  @Optional()
  shiftId?: string;

  @ApiProperty({
    description: 'ID của người dùng',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @IsNotEmpty()
  userId: string;

  @ApiProperty({
    description: 'user no',
    example: 1,
  })
  @Optional()
  userNo?: number;

  @ApiProperty({
    description: 'Vị trí user lang',
    example: '550e8400-e29b-41d4-a716-446655440002',
  })
  @IsNotEmpty()
  userLocationX: number;

  @ApiProperty({
    description: 'Vị trí user long',
    example: '550e8400-e29b-41d4-a716-446655440002',
  })
  @IsNotEmpty()
  userLocationY: number;

  @ApiProperty({
    description: 'Khoảng cách của user check in so với production area',
    example: '550e8400-e29b-41d4-a716-446655440002',
  })
  @IsNotEmpty()
  distance: number;
}

/**
 * DTO check out
 */
export class RequestCheckOutDTO {
  @ApiProperty({
    description: 'ID timesheet',
    example: '550e8400-e29b-41d4-a716-446655440002',
  })
  @IsOptional()
  id: string;

  @ApiProperty({
    description: 'ID khu vực sản xuất',
    example: '550e8400-e29b-41d4-a716-446655440002',
  })
  @IsNotEmpty()
  productionAreaId: string;

  @ApiProperty({
    description: 'ID của ca làm việc',
    example: '550e8400-e29b-41d4-a716-446655440001',
  })
  @Optional()
  shiftId?: string;

  @ApiProperty({
    description: 'ID của người dùng',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @IsNotEmpty()
  userId: string;

  @ApiProperty({
    description: 'ID của người dùng',
    example: 1,
  })
  @Optional()
  userNo: number;

  @ApiProperty({
    description: 'Vị trí user lang',
    example: 1,
  })
  @IsNotEmpty()
  userLocationX: number;

  @ApiProperty({
    description: 'Vị trí user long',
    example: 1,
  })
  @IsNotEmpty()
  userLocationY: number;

  @ApiProperty({
    description: 'Khoảng cách của user check out so với production area',
    example: 1,
  })
  @IsNotEmpty()
  distance: number;

  @ApiProperty({
    description: 'Check in time dùng để test',
    example: '2025-03-18T06:53:14.589Z',
  })
  @IsOptional()
  checkInTime?: Date;

  @ApiProperty({
    description: 'Check out time dùng để test',
    example: '2025-03-18T06:53:14.589Z',
  })
  @IsOptional()
  checkOutTime?: Date;
}

/**
 * DTO dùng để load thông tin chấm công
 */
export class RequestGetUserTimeSheetDTO {
  @ApiProperty({
    description: 'ID khu vực sản xuất',
    example: '550e8400-e29b-41d4-a716-446655440002',
  })
  @IsNotEmpty()
  productionAreaId: string;

  @ApiProperty({
    description: 'ID của ca làm việc',
    example: '550e8400-e29b-41d4-a716-446655440001',
  })
  @Optional()
  shiftId?: string;

  @ApiProperty({
    description: 'ID của người dùng',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @IsNotEmpty()
  userId: string;
}

/**
 * DTO để chọn bảng chấm công dựa trên tiêu chí người dùng.
 */

export class RequestCheckInCheckOutMobileDTO {
  @ApiProperty({
    description: 'Id của ca hiện tại của user',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @IsOptional()
  id?: string;

  @ApiProperty({
    description: 'Loại thao tác: IN (check-in) hoặc OUT (check-out)',
    enum: ['IN', 'OUT', 'EXTRA'],
  })
  @IsNotEmpty()
  @IsEnum(['IN', 'OUT'])
  type: 'IN' | 'OUT' | 'EXTRA';

  @ApiProperty({
    description: 'ID của người dùng',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @IsNotEmpty()
  userId: string;

  @ApiProperty({
    description: 'ID của ca làm việc',
    example: '550e8400-e29b-41d4-a716-446655440001',
  })
  @IsNotEmpty()
  shiftId: string;

  @ApiProperty({
    description: 'ID khu vực sản xuất',
    example: '550e8400-e29b-41d4-a716-446655440002',
  })
  @IsNotEmpty()
  productionAreaId: string;

  @ApiProperty({
    description: 'Vị trí X của người dùng khi check-in/check-out',
    example: 105.854444,
  })
  @IsNotEmpty()
  @IsNumber()
  userLocationX: number;

  @ApiProperty({
    description: 'Vị trí Y của người dùng khi check-in/check-out',
    example: 21.028511,
  })
  @IsNotEmpty()
  @IsNumber()
  userLocationY: number;

  @ApiProperty({
    description: 'Độ chênh lệch cho phép (tolerance) khi check-in/check-out',
    example: 5,
  })
  @IsNotEmpty()
  @IsNumber()
  tolerance: number;
}

/**
 * DTO để chọn bảng chấm công dựa trên tiêu chí người dùng.
 */
export class RequestUserSelectionTimeSheetDTO {}

/**
 * DTO để chọn bảng chấm công dựa trên tiêu chí khu vực sản xuất.
 */
export class RequestProductionAreaSelectionTimeSheetDTO {
  factoryId: string | 'All';
  userId?: string;
}

/**
 * DTO để chọn bảng chấm công dựa trên tiêu chí ca làm việc.
 */
export class RequestShiftSelectionSheetTimeDTO {
  productionAreaId: string;
}

/**
 * DTO đại diện cho bản ghi chấm công khi check-in.
 * Bao gồm thông tin về người dùng, khu vực sản xuất, ca làm việc,
 * thời gian check-in/check-out, ngày làm việc và tọa độ vị trí.
 */
export class RequestCheckInTimeSheetDTO {
  type: 'IN' | 'OUT';
  userId: string;
  productionAreaId: string;
  shiftId: string;
  checkinTime: string;
  checkoutTime: string;
  workDate: string;
  checkinLocationX: string;
  checkinLocationY: string;
  checkoutLocationX: string;
  checkoutLocationY: string;
}

/**
 * DTO đại diện cho bản ghi chấm công khi check-out.
 * Có cấu trúc tương tự CheckInTimeSheetRecordDTO.
 */
export class RequestCheckOutTimeSheetDTO {
  type: 'IN' | 'OUT';
  userId: string;
  productionAreaId: string;
  shiftId: string;
  checkinTime: string;
  checkoutTime: string;
  workDate: string;
  checkinLocationX: string;
  checkinLocationY: string;
  checkoutLocationX: string;
  checkoutLocationY: string;
}

/**
 * DTO để yêu cầu danh sách bảng chấm công có phân trang.
 */
export class RequestPaginationTimeSheetDTO {
  pageSize: number;
  currentPage: number;
  userId: string;
  productionAreaId: string;
  shiftId: string;
  startDate: string | Date;
  endDate: string | Date;
}

/**
 * DTO để yêu cầu chi tiết một bảng chấm công theo ID.
 */
export class RequestDetailTimeSheetDTO {
  id: number;
}

/**
 * DTO để tạo một bảng chấm công mới.
 */
export class RequestCreateTimeSheetDTO {
  @ApiProperty({
    description: 'ID của người dùng',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @IsNotEmpty()
  userId: string;

  @ApiProperty({
    description: 'ID của khu vực sản xuất',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @IsNotEmpty()
  productionAreaId: string;

  @ApiProperty({
    description: 'ID của ca làm việc',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @IsNotEmpty()
  shiftId: string;

  @ApiProperty({
    description: 'Id của site',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @IsOptional()
  siteId: string;

  @ApiProperty({
    description: 'Id của factory',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @IsOptional()
  factoryId: string;

  @ApiProperty({
    description: 'Thời gian check-in (ISO 8601 format)',
    example: '2025-03-18T08:00:00.000Z',
  })
  @IsNotEmpty()
  checkInTime: string;

  @ApiProperty({
    description: 'Thời gian check-out (ISO 8601 format)',
    example: '2025-03-18T17:00:00.000Z',
  })
  @IsOptional()
  checkOutTime?: string;

  @ApiProperty({
    description: 'ID của người tạo',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @IsNotEmpty()
  createdBy: string;

  @ApiProperty({ example: 0 })
  @IsOptional()
  duration?: number | string;

  @ApiProperty({ example: 0 })
  @IsOptional()
  earlyTime?: number | string;

  @ApiProperty({ example: 0 })
  @IsOptional()
  lateTime?: number | string;

  @ApiProperty({ example: 0 })
  @IsOptional()
  salaryTime?: number | string;

  @ApiProperty({ example: 0 })
  @IsOptional()
  nightTime?: number | string;

  @ApiProperty({ example: 0 })
  @IsOptional()
  diffTime?: number | string;

  @ApiProperty({ example: 0 })
  @IsOptional()
  otTime?: number | string;

  @ApiProperty({ example: 0 })
  @IsOptional()
  otHolidayTime?: number | string;

  @ApiProperty({ example: 0 })
  @IsOptional()
  otNightTime?: number | string;

  @ApiProperty({ example: 0 })
  @IsOptional()
  otHolidayNightTime?: number | string;
}

/**
 * DTO để cập nhật một bảng chấm công hiện có.
 */
export class RequestUpdateTimeSheetDTO {
  @ApiProperty({
    description: 'Id của TimeSheet',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @IsNotEmpty()
  id: string;

  @ApiProperty({
    description: 'Id của user',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @IsNotEmpty()
  userId: string;

  @ApiProperty({
    description: 'Id của khu vực sản xuất (production area)',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @IsNotEmpty()
  productionAreaId: string;

  @ApiProperty({
    description: 'Id của ca làm việc (shift)',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @IsNotEmpty()
  shiftId: string;

  @ApiProperty({
    description: 'Id của site',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @IsNotEmpty()
  siteId: string;

  @ApiProperty({
    description: 'Id của factory',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @IsNotEmpty()
  factoryId: string;

  @ApiProperty({
    description: 'Thời gian check-in của user (ISO 8601 format)',
    example: '2025-03-18T08:00:00.000Z',
  })
  @IsNotEmpty()
  checkInTime: string;

  @ApiProperty({
    description: 'Thời gian check-out của user (ISO 8601 format)',
    example: '2025-03-18T17:00:00.000Z',
  })
  @IsNotEmpty()
  @IsOptional()
  checkOutTime?: string;

  @ApiProperty({
    description: 'Người thực hiện cập nhật',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @IsNotEmpty()
  updatedBy: string;

  @ApiProperty({ example: 0 })
  @IsOptional()
  duration?: number | string;

  @ApiProperty({ example: 0 })
  @IsOptional()
  earlyTime?: number | string;

  @ApiProperty({ example: 0 })
  @IsOptional()
  lateTime?: number | string;

  @ApiProperty({ example: 0 })
  @IsOptional()
  salaryTime?: number | string;

  @ApiProperty({ example: 0 })
  @IsOptional()
  nightTime?: number | string;

  @ApiProperty({ example: 0 })
  @IsOptional()
  diffTime?: number | string;

  @ApiProperty({ example: 0 })
  @IsOptional()
  otTime?: number | string;

  @ApiProperty({ example: 0 })
  @IsOptional()
  otHolidayTime?: number | string;

  @ApiProperty({ example: 0 })
  @IsOptional()
  otNightTime?: number | string;

  @ApiProperty({ example: 0 })
  @IsOptional()
  otHolidayNightTime?: number | string;
}

export class RequestListFactoryMobileTimeSheetDTO {
  @ApiProperty({
    description: 'Id của TimeSheet',
    example: '550e8400-e29b-41d4-a716-446655440000',
  })
  @IsNotEmpty()
  siteId: string;
}

/**
 * DTO để xóa một bảng chấm công.
 */
export class RequestDeleteTimeSheetDTO {
  id: string;
}

/**
 * DTO để yêu cầu check-in từ thiết bị di động.
 */
export class RequestCheckInMobileTimeSheetDTO {
  id: string;
}

/**
 * DTO để yêu cầu check-out từ thiết bị di động.
 */
export class RequestCheckOutMobileTimeSheetDTO {
  id: string;
}

export class ListSiteTimeSheetDTO {
  userId: string;
}

export class ListFactoryTimeSheetDTO {}

export class RequestListUserTimeSheetDTO {
  productionAreaId: string | 'All';
}

export class RequestListHoliadyDTO {}

export class RequestListPlace {}

export class RequestListDevice {}

export class RequestSyncListUserZKT {
  SN: string;
}

export class RequestFetchEmitDevice {}

export class RequestGetListStation {}

export type ResponseGetListStation = Array<{
  stationId: string;
  stationCode: number;
  stationDescription: string;
  ipcIP?: string;
  clientIP?: string;
  isAcceptClientIP: boolean;
  listDevice: Array<{
    deviceId: string;
    deviceCode: number;
    deviceDescription: string;
    ipAddress: string;
    serialNumber: string;
    deviceTypeDetailCode: string;
  }>;
}>;
