import { Injectable, ConflictException, NotFoundException } from '@nestjs/common';
import { In, Like, UpdateResult } from 'typeorm';
import { SitewiseModelRepo } from '~/repositories/primary/sitewise.repo';
import { ListSitewiseModelReq, SitewiseModelReq } from '~/dto/sitewise.dto';
import { adminSessionContext } from '../../admin-session.context';
import { SuccessResponse } from '~/@systems/utils';
import { BindRepo } from '~/@core/decorator';

@Injectable()
export class SitewiseModelService {
  constructor() {}
  @BindRepo(SitewiseModelRepo)
  private readonly repo: SitewiseModelRepo;

  async find(data: any) {
    const whereCon: any = { isActive: true };
    if (data.code) whereCon.code = Like(`%${data.code}%`);
    if (data.lstId?.length > 0) whereCon.id = In(data.lstId);
    if (data.lstCode?.length > 0) whereCon.code = In(data.lstCode);
    return await this.repo.find({ where: whereCon });
  }

  async findOne(id: string) {
    return await this.repo.findOne({ where: { id: id } });
  }

  async createData(data: SitewiseModelReq): Promise<any> {
    const checkCodeExist = await this.repo.findOne({ where: { code: data.code } });
    const { userId: userId } = adminSessionContext;
    if (checkCodeExist)
      throw new ConflictException(`Sitewise Model with code [${data.code}] already exists`);

    const entity = this.repo.create({ ...data, createdBy: userId, isActive: true });
    await this.repo.insert(entity);

    return entity;
  }

  async updateData(id: string, data: SitewiseModelReq) {
    const entity = await this.repo.findOne({ where: { id: id } });
    if (!entity) throw new NotFoundException(`Sitewise model with ID ${id} not found`);
    const { userId: userId } = adminSessionContext;
    if (data.code != entity.code) {
      const checkCodeExist = await this.repo.findOne({ where: { code: data.code } });
      if (checkCodeExist)
        throw new ConflictException(`Sitewise Model with code [${data.code}] already exists`);
    }

    entity.code = data.code;
    entity.description = data.description;
    entity.awsId = data.awsId;
    entity.updatedBy = data?.updatedBy || userId;
    const updatedEntity = await this.repo.update(entity.id, entity);
    return { message: UpdateResult, data: updatedEntity };
  }

  async pagination(body: ListSitewiseModelReq) {
    let whereCon: any = {};
    if (body.awsId) whereCon.awsId = Like(`%${body.awsId}%`);
    if (body.code) whereCon.code = Like(`%${body.code}%`);
    if (body.isActive) whereCon.isActive = body.isActive;
    let res: any = await this.repo.findPagination({ where: whereCon }, body);
    if (res.length == 0) return [[], 0];
    return res;
  }

  async updateIsDelete(id: string) {
    const entity = await this.repo.findOne({ where: { id } });
    if (!entity) throw new NotFoundException(`Sitewise model with ID ${id} not found`);

    entity.isActive = !entity.isActive;
    await this.repo.update(entity.id, entity);
    return { message: SuccessResponse };
  }

  async createDataExcel(data: SitewiseModelReq[]): Promise<any> {
    const existingCodes = new Set(
      (await this.repo.find({ where: { isActive: true }, select: ['code'] })).map((c) => c.code),
    );
    const { userId: userId } = adminSessionContext;
    const newEntityList = data
      .filter(
        (item, index) =>
          !existingCodes.has(item.code) && data.findIndex((el) => el.code === item.code) === index,
      )
      .map((item) => this.repo.create({ ...item, createdBy: userId }));

    await this.repo.insert(newEntityList);
    return { message: SuccessResponse };
  }
}
