import { forwardRef, Inject, Injectable } from '@nestjs/common';
import axios from 'axios';
import dayjs from 'dayjs';
import { EntityManager, In, LessThanOrEqual, Like, MoreThanOrEqual } from 'typeorm';
import { configEnv } from '~/@config/env';
import { BindRepo } from '~/@core/decorator';
import { BusinessException } from '~/@systems/exceptions';
import { NSSetting } from '~/common/enums/NSSetting.enum';
import {
  GeneralDataDetailRepo,
  InspectionPlanRepo,
  ItemRepo,
  MachineParameterRepo,
  OrganizationUnitRepo,
  ProcessRepo,
  ProductionAreaDetailRepo,
  ProductionBatchRepo,
  ProductionOrderRepo,
  QualityNotificationRepo,
  SettingStringRepo,
  ShiftRepo,
} from '~/repositories/primary';
import { SitewiseLibService } from '~/x-modules/admin/services';
const { QLONE_CREATE_SAMPLE_URL, QLONE_VISUAL_INSPECTION_URL } = configEnv();

@Injectable()
export class QualityNotificationService {
  constructor(
    @Inject(forwardRef(() => SitewiseLibService))
    private readonly sitewiseService: SitewiseLibService,
    private readonly entityManager: EntityManager,
  ) {}
  @BindRepo(InspectionPlanRepo)
  private readonly inspectionPlanRepo: InspectionPlanRepo;

  @BindRepo(MachineParameterRepo)
  private readonly machineParameterRepo: MachineParameterRepo;

  @BindRepo(ProductionOrderRepo)
  private readonly productionOrderRepo: ProductionOrderRepo;

  @BindRepo(OrganizationUnitRepo)
  private readonly organizationUnitRepo: OrganizationUnitRepo;

  @BindRepo(ProductionAreaDetailRepo)
  private readonly productionAreaDetailRepo: ProductionAreaDetailRepo;

  @BindRepo(ProcessRepo)
  private readonly processRepo: ProcessRepo;

  @BindRepo(SettingStringRepo)
  private readonly settingStringRepo: SettingStringRepo;

  @BindRepo(ShiftRepo)
  private readonly shiftRepo: ShiftRepo;

  @BindRepo(ProductionBatchRepo)
  private readonly productionBatchRepo: ProductionBatchRepo;

  @BindRepo(QualityNotificationRepo)
  private readonly qualityNotificationRepo: QualityNotificationRepo;

  @BindRepo(ItemRepo)
  private readonly itemRepo: ItemRepo;

  @BindRepo(GeneralDataDetailRepo)
  private readonly generalDataDetailRepo: GeneralDataDetailRepo;

  async createSampleQLONE() {
    const { IdToken } = await this.getTokenQloneCognito();
    const whereCon: any = {};
    const timeNow = dayjs().tz('Asia/Ho_Chi_Minh').format('HH:mm:ss');
    whereCon.startTime = LessThanOrEqual(timeNow);
    whereCon.endTime = MoreThanOrEqual(timeNow);
    const shift = await this.shiftRepo.findOne({
      where: whereCon,
      order: { startTime: 'DESC' },
    });
    // Thời gian test
    const now = dayjs.utc().subtract(1, 'minute').startOf('minute');

    // #region Lấy danh sách machine parameter từ inspection plan
    const lstInspection: any = await this.inspectionPlanRepo.find({ where: { isActive: true } });
    const dictMachineParam: any = {};
    const dictMachineParameterForBatch: any = {};
    {
      // Danh sách machine parameter từ inspection plan
      const lstMachineParamId = lstInspection.map((i) => i.machineParameterId);
      const lstMachineParam = await this.machineParameterRepo.find({
        where: { id: In(lstMachineParamId) },
      });
      lstMachineParam.forEach((i) => (dictMachineParam[i.id] = i));

      // Danh sách machine parameter từ assetId của machineParam trên có typeCode = 7
      const machineParameter = await this.machineParameterRepo.find({
        where: {
          iotsitewiseAssetId: In(lstMachineParam.map((i) => i.iotsitewiseAssetId)),
          machineId: In(lstMachineParam.map((i) => i.machineId)),
          typeCode: '7',
        },
      });
      machineParameter.forEach((i: any) => {
        const machineParamId = lstMachineParam.find(
          (o) => o.iotsitewiseAssetId === i.iotsitewiseAssetId,
        );
        i.machineParameterId = machineParamId?.id;
        if (!dictMachineParameterForBatch[i.iotsitewiseAssetId]) {
          dictMachineParameterForBatch[i.iotsitewiseAssetId] = [];
        }
        dictMachineParameterForBatch[i.iotsitewiseAssetId].push(i);
      });
    }
    // #endregion

    const dictPO: any = {};
    const dictTransforms: any = {};
    const dictTransformStart: any = {};
    {
      const baseSql2 = `
  SELECT DISTINCT ON (value) *
  FROM scada_mes_dx_transforms
  WHERE "transformId" = ANY($1)
    AND datetime >= $2::timestamptz
    AND datetime < ($2::timestamptz + interval '1 minute')
  ORDER BY value, datetime DESC;
`;

      const lstId = Object.values(dictMachineParameterForBatch).flatMap((arr: any[]) =>
        arr.map((item) => item.iotsitewisePropertyId),
      );
      const lstTransforms = await this.entityManager.query(baseSql2, [lstId, now]);

      const foundTransformIds = new Set(lstTransforms.map((i) => i.transformId));
      for (const key in dictMachineParameterForBatch) {
        dictMachineParameterForBatch[key] = dictMachineParameterForBatch[key].filter((item: any) =>
          foundTransformIds.has(item.iotsitewisePropertyId),
        );
        if (dictMachineParameterForBatch[key]?.length === 0) {
          delete dictMachineParameterForBatch[key];
        }
      }
      const result = lstTransforms
        .map((i) => {
          const [batchNo, orderNo] = i.value.split('|');
          if (!batchNo || !orderNo || batchNo.length === 0 || orderNo.length === 0) {
            return null;
          }
          return {
            transformId: i.transformId,
            batchNo,
            orderNo,
          };
        })
        .filter(Boolean);
      if (result.length === 0) {
        return [];
      }

      const oneDayAgo = now.subtract(1, 'day').toDate();

      const baseSqlLike = `
        SELECT split_part(value, '|', 2) AS value_sub, MIN(datetime) AS earliest_datetime, "transformId"
        FROM scada_mes_dx_transforms
        WHERE "transformId" = ANY($1)
          AND datetime >= $2
          AND split_part(value, '|', 2) = ANY($3)
        GROUP BY value_sub, "transformId";
      `;

      const queryParams = [lstId, oneDayAgo, result.map((v) => v.orderNo)];
      const resultStartTransform = await this.entityManager.query(baseSqlLike, queryParams);

      resultStartTransform.forEach((i) => {
        if (i.value_sub.length > 0) {
          dictTransformStart[i.transformId] = i.earliest_datetime;
        }
      });

      const lstPO = await this.productionOrderRepo.find({
        where: { orderNo: In(result.map((i) => i.orderNo)) },
      });
      lstPO.forEach((i) => (dictPO[i.orderNo] = i));

      const dictBatch: any = {};
      {
        const lstBatch = await this.productionBatchRepo.find({
          where: {
            orderId: In(lstPO.map((i) => i.id)),
            batchNumber: In(result.filter((i) => i.batchNo.length > 0).map((i) => i.batchNo)),
          },
          select: ['id', 'batchNumber'],
        });
        lstBatch.forEach((i) => (dictBatch[i.batchNumber] = i));
      }

      result.forEach((i) => {
        if (dictPO[i.orderNo] && dictBatch[i.batchNo]) {
          const machineParamId = (
            Object.values(dictMachineParameterForBatch)
              .flat()
              .find((item: any) => item.iotsitewisePropertyId === i.transformId) as any
          )?.machineParameterId;
          const lstInspectionPlanId = lstInspection
            .filter((o) => o.machineParameterId === machineParamId)
            ?.map((o) => o.id);
          dictTransforms[i.transformId] = {
            transformId: i.transformId,
            batchId: dictBatch[i.batchNo]?.id,
            orderId: dictPO[i.orderNo]?.id,
            batchNo: i.batchNo,
            orderNo: i.orderNo,
            lstInspectionPlanId: lstInspectionPlanId,
          };
        }
      });
    }

    // #region Lấy thông tin org unit của inspection plan
    const dictProcess: any = {};
    const dictFactory: any = {};
    const dictProductionLine: any = {};
    const dictProductionArea: any = {};
    {
      const lstProcessId = lstInspection.map((i) => i.processId);
      const lstProcess = await this.processRepo.find({ where: { id: In(lstProcessId) } });
      const lstProcessOrg = await this.organizationUnitRepo.find({
        where: { id: In(lstProcess.map((i) => i.organizationUnitId)) },
        select: ['id', 'code', 'name', 'parentId'],
      });
      const lstLine = await this.organizationUnitRepo.find({
        where: { id: In(lstProcessOrg.map((i) => i.parentId)) },
        select: ['id', 'code', 'name', 'parentId'],
      });
      const lstFactory = await this.organizationUnitRepo.find({
        where: { id: In(lstLine.map((i) => i.parentId)) },
        select: ['id', 'code', 'name', 'parentId'],
      });
      const lstProductionArea = await this.productionAreaDetailRepo.find({
        where: { organizationUnitId: In(lstProcessOrg.map((i) => i.id)) },
        select: ['id', 'organizationUnitId', 'productionAreaId'],
      });
      lstProcess.forEach((i: any) => {
        const orgUnit = lstProcessOrg.find((o) => o.id === i.organizationUnitId);
        i.parentId = orgUnit.parentId;
        i.orgId = orgUnit.id;
      });
      lstProcess.forEach((i) => (dictProcess[i.id] = i));
      lstLine.forEach((i) => (dictProductionLine[i.id] = i));
      lstFactory.forEach((i) => (dictFactory[i.id] = i));
      lstProductionArea.forEach((i) => (dictProductionArea[i.organizationUnitId] = i));
    }
    // #endregion

    // Áp điều kiện Bước 2
    {
      lstInspection.forEach((i) => {
        i.iotsitewisePropertyId = dictMachineParam[i.machineParameterId]?.iotsitewisePropertyId;
        i.iotsitewiseAssetId = dictMachineParam[i.machineParameterId]?.iotsitewiseAssetId;
        i.iotsitewisePropertyIdTransform =
          dictMachineParameterForBatch[i.iotsitewiseAssetId]?.[0]?.iotsitewisePropertyId;
      });
      const dictValueMeasurement: any = {};
      {
        const lstMeasureId = lstInspection.filter((i) => i.frequency === 'BY_VALUE');
        if (lstMeasureId.length === 0) return;
        const tupleSql = lstMeasureId
          .map((_, idx) => `($${idx * 2 + 1}, $${idx * 2 + 2})`)
          .join(', ');

        const baseSql = `
        SELECT DISTINCT ON (m."measurementId") m.*
        FROM scada_mes_dx_measurements m
        WHERE 
          (m."measurementId", m."assetId") IN (${tupleSql})
          AND m.datetime >= $${lstMeasureId.length * 2 + 1}::timestamptz
          AND m.datetime < ($${lstMeasureId.length * 2 + 1}::timestamptz + interval '1 minute')
        ORDER BY m."measurementId", m.datetime DESC;
      `;

        const params = lstMeasureId.flatMap(({ iotsitewisePropertyId, iotsitewiseAssetId }) => [
          iotsitewisePropertyId,
          iotsitewiseAssetId,
        ]);
        params.push(now);
        const results = await this.entityManager.query(baseSql, params);
        results.forEach((i) => {
          dictValueMeasurement[i.measurementId] = i.value;
        });
      }
      const dictValueMetric: any = {};
      const dictValueMetricStart: any = {};
      {
        const lstMetricId = lstInspection
          .filter((i) => i.frequency === 'PRODUCTION_QUANTITY')
          .map((i) => i.iotsitewisePropertyId);
        if (lstMetricId.length > 0) {
          const baseSqlMetric = `
          SELECT DISTINCT ON ("metricId") * FROM scada_mes_dx_metrics m  WHERE "metricId" = ANY($1)
          AND m.datetime >= $2::timestamptz
          AND m.datetime < ($2::timestamptz + interval '1 minute') ORDER BY "metricId", datetime DESC;
          `;
          const lstValueMetric = await this.entityManager.query(baseSqlMetric, [lstMetricId, now]);
          lstValueMetric.forEach((i) => (dictValueMetric[i.metricId] = i.value));

          const lstConditions = Object.entries(dictTransformStart).flatMap(
            ([transformId, min_time]) => {
              const lstFilter = lstInspection.filter(
                (o) => o.iotsitewisePropertyIdTransform === transformId,
              );
              return lstFilter.map((inspection) => ({
                metricId: inspection.iotsitewisePropertyId,
                min_time,
              }));
            },
          );
          if (lstConditions.length > 0) {
            const valuesPlaceholders = lstConditions
              .map((_, idx) => `($${idx * 2 + 1}::text, $${idx * 2 + 2}::timestamptz)`)
              .join(', ');

            const baseSqlMetric2 = `
          WITH min_times(metricId, min_time) AS (
            VALUES ${valuesPlaceholders}
          )
          SELECT DISTINCT ON (m."metricId") m.*
          FROM scada_mes_dx_metrics m
          JOIN min_times t ON m."metricId" = t.metricId
          WHERE m.datetime >= t.min_time
          ORDER BY m."metricId", m.datetime ASC;
        `;

            const queryParams = lstConditions.flatMap(({ metricId, min_time }) => [
              metricId,
              min_time,
            ]);

            const result = await this.entityManager.query(baseSqlMetric2, queryParams);
            result.forEach((i) => (dictValueMetricStart[i.metricId] = i.value));
          }
        }
      }
      const dictNotificationIsNull: any = {};
      {
        const lstNotification = await this.qualityNotificationRepo.find({
          where: {
            machineParameterId: In(lstInspection.map((i) => i.machineParameterId)),
            result: null,
          },
          select: ['id', 'machineParameterId', 'result'],
        });
        lstNotification.forEach((i) => (dictNotificationIsNull[i.machineParameterId] = true));
      }
      const dictNotification: any = {};
      {
        const lstNotification = await this.qualityNotificationRepo.find({
          where: {
            productionOrderId: In(Object.values(dictPO).map((i: any) => i.id)),
            inspectionPlanId: In(lstInspection.map((i) => i.id)),
          },
          select: ['productionOrderId', 'inspectionPlanId', 'id'],
        });
        lstNotification.forEach((i) => {
          if (!dictNotification[i.inspectionPlanId]) {
            dictNotification[i.inspectionPlanId] = [];
          }
          dictNotification[i.inspectionPlanId].push(i);
        });
      }
      lstInspection.forEach((i) => {
        switch (i.frequency) {
          case 'BY_VALUE':
            i.isCreate = false;
            const valueMeasure = dictValueMeasurement[i.iotsitewisePropertyId];
            if (!valueMeasure) break;
            if (
              (Number(valueMeasure) < Number(i.minValue) ||
                Number(valueMeasure) > Number(i.maxValue)) &&
              !dictNotificationIsNull[i.machineParameterId]
            ) {
              i.isCreate = true;
            }
            break;
          case 'PRODUCTION_QUANTITY': {
            const lstNoti = dictNotification[i.id] || [];
            i.isCreate = false;
            const value = dictValueMetric[i.iotsitewisePropertyId];
            const startValue = dictValueMetricStart[i.iotsitewisePropertyId];
            if (!startValue || !value) break;
            const num = Math.floor((Number(value) - Number(startValue)) / Number(i.outputQty));
            if (lstNoti && lstNoti.length < num) i.isCreate = true;
            break;
          }

          case 'BY_TIME': {
            const totalMinutes =
              now.tz('Asia/Ho_Chi_Minh').hour() * 60 + now.tz('Asia/Ho_Chi_Minh').minute();
            const [hours, minutes] = shift.startTime.split(':').map(Number);
            const shiftStartMinutes = hours * 60 + minutes;
            const num = (totalMinutes - shiftStartMinutes) % Number(i.timeValue);
            i.isCreate = false;
            if (num === 0) i.isCreate = true;
            break;
          }
        }
      });
    }

    const lstEntity = [];
    for (const i of lstInspection.filter((i) => i.isCreate === true)) {
      const machineParam = dictMachineParam[i.machineParameterId];
      const lstMachineParameterForBatch =
        dictMachineParameterForBatch[machineParam.iotsitewiseAssetId];
      if (!lstMachineParameterForBatch || lstMachineParameterForBatch.length === 0) continue;

      const process = dictProcess[i.processId];
      const line = dictProductionLine[process?.parentId] || null;
      const factory = dictFactory[line?.parentId] || null;
      const productionAreaId = dictProductionArea[process?.orgId]?.productionAreaId || null;

      for (const y of lstMachineParameterForBatch) {
        const transform = dictTransforms[y.iotsitewisePropertyId];
        const PO = dictPO[transform?.orderNo] || null;

        if (!transform || !PO) continue;

        const newEntity: any = {
          inspectionPlanId: i.id,
          inspectionPlanType: i.dataType,
          sampleNo: null,
          qualityNotificationNo: await this.codeDefault(PO?.orderNo),
          productionOrderId: PO.id,
          productionBatchId: transform?.batchId || null,
          status: 1,
          siteId: i.organizationUnitId,
          factoryId: factory?.id || null,
          productionLineId: line?.id || null,
          productionAreaId: productionAreaId || null,
          processId: i.processId,
          machineId: i.machineId,
          machineParameterId: i.machineParameterId,
          tagAddress: dictMachineParam[i.machineParameterId]?.tagAddress || null,
          itemId: i.itemId,
          createdBy: 'MES_SYS',
          updatedBy: 'MES_SYS',
        };
        const entity = await this.qualityNotificationRepo.save(newEntity);
        lstEntity.push({ ...entity, orderNo: PO.orderNo });
      }
    }
    const lstInspectionFilter = lstInspection.filter((i) =>
      lstEntity.some((e) => e.inspectionPlanId === i.id),
    );
    const dictItem: any = {};
    {
      const lstItem = await this.itemRepo.find({
        where: { id: In(lstInspectionFilter.map((i) => i.itemId)) },
        select: ['id', 'code'],
      });
      lstItem.forEach((i) => (dictItem[i.id] = i.code));
    }
    const dictGeneralDataDetail: any = {};
    {
      const lstGeneralData = await this.generalDataDetailRepo.find({
        where: {
          id: In(
            lstInspectionFilter.flatMap((i) => [
              i.sampleType,
              i.costCenter,
              i.receivingSite,
              i.receivingDepartment,
              i.category,
              i.inspectionLevel,
              i.aqlSeverity,
            ]),
          ),
        },
      });
      lstGeneralData.forEach((i) => (dictGeneralDataDetail[i.id] = i));
    }

    lstEntity.forEach(async (i: any) => {
      const inspection = lstInspection.find((x) => x.id === i.inspectionPlanId);

      if (i.inspectionPlanType === 'SAMPLE') {
        const data = {
          ItemCode: dictItem[inspection.itemId],
          LotNumber: dictPO[i.orderNo]?.lotNumber,
          ProductionOrderNumber: dictPO[i.orderNo]?.orderNo,
          SampleName: dictGeneralDataDetail[inspection.sampleType]?.name,
          Cost_Center: dictGeneralDataDetail[inspection.costCenter]?.name,
          ReceivingSite: dictGeneralDataDetail[inspection.receivingSite]?.name,
          ReceivingDept: dictGeneralDataDetail[inspection.receivingDepartment]?.name,
          Category: dictGeneralDataDetail[inspection.category]?.name,
          QC: inspection.qcFullname,
          SpecCode: inspection.specCode,
          SpecVersion: inspection.specVersion,
          NotificationNumber: i.qualityNotificationNo,
          CreationTimestamp: now,
          CreatedBy: 'MES_SYS',
          'User Name': 'MES_SYS',
        };
        try {
          const res = await axios.post(QLONE_CREATE_SAMPLE_URL, data, {
            headers: {
              Authorization: `Bearer ${IdToken}`,
            },
          });
          if (res?.data?.code === 202) {
            await this.qualityNotificationRepo.update(
              { id: i.id },
              { sampleNo: res.data?.data[0]?.sample_id },
            );
          }
        } catch (err) {}
        await this.qualityNotificationRepo.update({ id: i.id }, { reqBody: JSON.stringify(data) });
      } else if (i.inspectionPlanType === 'VISUAL_INSPECTION') {
        const data = {
          Site: inspection.organizationUnitId,
          Item: inspection.itemId,
          LotNumber: dictPO[i.orderNo]?.lotNumber,
          LotQty: dictPO[i.orderNo]?.lotQty,
          'AQL code': inspection.aqlCode,
          'Sample type': dictGeneralDataDetail[inspection.sampleType]?.name,
          'Inspection level': dictGeneralDataDetail[inspection.inspectionLevel]?.name,
          'AQL severity': dictGeneralDataDetail[inspection.aqlSeverity]?.name,
          NotificationNumber: i.qualityNotificationNo,
          ProductionOrderNumber: dictPO[i.orderNo]?.orderNo,
          'User Name': 'MES_SYS',
        };
        try {
          const res = await axios.post(QLONE_VISUAL_INSPECTION_URL, data, {
            headers: {
              Authorization: `Bearer ${IdToken}`,
            },
          });
          if (res?.data?.code === 202) {
            await this.qualityNotificationRepo.update(
              { id: i.id },
              { sampleNo: res.data?.data[0]?.sample_id },
            );
          }
        } catch (err) {}
        await this.qualityNotificationRepo.update({ id: i.id }, { reqBody: JSON.stringify(data) });
      }
    });
    return lstEntity;
  }

  /** Lấy thông tin token để xác thực qua hệ thống QLONE */
  async getTokenQloneCognito() {
    const checkSetting = await this.settingStringRepo.findOne({
      where: { code: NSSetting.ESettingCode.COGNITO_TOKEN },
    });

    // Nếu token lưu trong db còn hạn thì lấy từ db ra
    if (checkSetting?.value) {
      const objValue = JSON.parse(checkSetting.value);
      const { expiredTime, IdToken } = objValue;
      const curTime = new Date().getTime();
      if (expiredTime > curTime && IdToken) {
        return { IdToken };
      }
    }

    const payload = {
      AuthFlow: 'USER_PASSWORD_AUTH',
      ClientId: process.env.CLIENT_ID,
      AuthParameters: {
        USERNAME: process.env.USERNAME_COGNITO,
        PASSWORD: process.env.PASSWORD_COGNITO,
      },
    };
    const cognitoEndpoint = `https://cognito-idp.${process.env.REGION}.amazonaws.com/`;

    try {
      const res = await axios.post(cognitoEndpoint, payload, {
        headers: {
          'X-Amz-Target': 'AWSCognitoIdentityProviderService.InitiateAuth',
          'Content-Type': 'application/x-amz-json-1.1',
        },
      });
      const { AccessToken, IdToken, RefreshToken, ExpiresIn } = res?.data?.AuthenticationResult;
      if (!IdToken) throw new BusinessException('Hệ thống QLONE không trả về IdToken');
      const expiredTime = new Date().getTime() + (ExpiresIn - 5 * 60) * 1000; // 5 phút trước khi hết hạn

      // Lưu lại token vào db sử dụng cho lần gọi sau
      if (checkSetting) {
        await this.settingStringRepo.update(checkSetting.id, {
          value: JSON.stringify({ AccessToken, IdToken, RefreshToken, expiredTime }),
          updatedDate: new Date(),
        });
      } else {
        await this.settingStringRepo.insert({
          code: NSSetting.ESettingCode.COGNITO_TOKEN,
          type: NSSetting.EDataType.STRING,
          value: JSON.stringify({ AccessToken, IdToken, RefreshToken, expiredTime }),
          createdBy: '-1',
        });
      }
      return { IdToken };
    } catch (error) {
      throw new BusinessException(
        'Lỗi khi lấy token từ hệ thống QLONE, chi tiết: ' + error.toString(),
      );
    }
  }

  async codeDefault(orderNo: string) {
    const objData = await this.qualityNotificationRepo.findOne({
      where: { qualityNotificationNo: Like(`${orderNo}_%`) },
      order: { qualityNotificationNo: 'DESC' },
    });
    let number = 1;
    if (!objData) {
      const formattedNumber = number.toString().padStart(3, '0');
      return orderNo + '_' + formattedNumber;
    }
    const [orderNumber, value] = objData.qualityNotificationNo.split('_');
    number = Number.parseInt(value) + 1;
    const formattedNumber = number.toString().padStart(3, '0');
    return orderNo + '_' + formattedNumber;
  }
}
