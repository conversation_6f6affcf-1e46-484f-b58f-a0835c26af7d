// src/x-modules/admin/controllers/system-configuration/utility-meter.controller.ts
import {
  BadRequestException,
  Body,
  Controller,
  Get,
  Param,
  Post,
  Put,
  Query,
  UseGuards,
} from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { UtilityMetersService } from '../../services/system-configuration/utility-meters.service';
import {
  UtilityMeterQueryDto,
  UtilityMeterReq,
  ProcessAreaReq,
} from '../../../../dto/utility-meter.dto';
import { ProcessAreaActiveStatus } from '../../../../dto/production-area.dto';
import { DefPost } from '~/@core/decorator';
import { Roles } from '~/@core/decorator/roles.decorator';
import { RoleGuard } from '~/@systems/guard/role.guard';

@ApiTags('Utility Meters')
@Controller()
export class UtilityMeterController {
  constructor(private readonly utilityMetersService: UtilityMetersService) {}

  @Get('/utility-meters')
  @Roles('/system-configuration/utility-meters', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Lấy danh sách đồng hồ tiện ích' })
  async findAll(@Query() query: UtilityMeterQueryDto) {
    try {
      const result = await this.utilityMetersService.findAll(query);
      return result;
    } catch (error) {
      throw new BadRequestException({
        message: error.message || 'Có lỗi xảy ra khi lấy danh sách đồng hồ tiện ích',
        errors: error,
      });
    }
  }

  @Get('/utility-meters/:id')
  @Roles('/system-configuration/utility-meters', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Lấy thông tin chi tiết đồng hồ tiện ích' })
  async findOne(@Param('id') id: string) {
    try {
      const result = await this.utilityMetersService.findOne(id);
      return {
        data: result,
      };
    } catch (error) {
      throw new BadRequestException({
        message: error.message || 'Có lỗi xảy ra khi lấy thông tin đồng hồ tiện ích',
        errors: error,
      });
    }
  }

  @Get('/utility-meters/:id/all-data')
  @Roles('/system-configuration/utility-meters', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Lấy tất cả dữ liệu của đồng hồ tiện ích' })
  async getAllData(@Param('id') id: string) {
    try {
      const result = await this.utilityMetersService.getAllData(id);
      return {
        data: result,
      };
    } catch (error) {
      throw new BadRequestException({
        message: error.message || 'Có lỗi xảy ra khi lấy dữ liệu đồng hồ tiện ích',
        errors: error,
      });
    }
  }

  @Get('/utility-meters/:id/details')
  @Roles('/system-configuration/utility-meters', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Lấy thông tin chi tiết đồng hồ tiện ích với phân trang' })
  async findOneWithDetails(
    @Param('id') id: string,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
  ) {
    try {
      const result = await this.utilityMetersService.findOneWithDetails(id, page, limit);
      return result;
    } catch (error) {
      throw new BadRequestException({
        message: error.message || 'Có lỗi xảy ra khi lấy thông tin chi tiết đồng hồ tiện ích',
        errors: error,
      });
    }
  }

  @DefPost('/utility-meters/save')
  @Roles('/system-configuration/utility-meters', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Lưu thông tin đồng hồ tiện ích' })
  async save(@Body() body: UtilityMeterReq) {
    try {
      const result = await this.utilityMetersService.save(body);
      return {
        message: 'success',
        data: result,
      };
    } catch (error) {
      throw new BadRequestException({
        message: error.message || 'Có lỗi xảy ra khi lưu đồng hồ tiện ích',
        errors: error,
      });
    }
  }

  // Options APIs
  @Get('/utility-meters-dump/factories')
  @Roles('/system-configuration/utility-meters', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Lấy danh sách nhà máy' })
  async getFactoryList() {
    try {
      const result = await this.utilityMetersService.getFactoryList();
      return {
        data: result,
        total: result.length,
      };
    } catch (error) {
      throw new BadRequestException({
        message: error.message || 'Có lỗi xảy ra khi lấy danh sách nhà máy',
        errors: error,
      });
    }
  }

  @Get('/utility-meters-dump/uoms')
  @Roles('/system-configuration/utility-meters', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Lấy danh sách đơn vị đo' })
  async getUOMList() {
    try {
      const result = await this.utilityMetersService.getUOMList();
      return {
        data: result,
        total: result.length,
      };
    } catch (error) {
      throw new BadRequestException({
        message: error.message || 'Có lỗi xảy ra khi lấy danh sách đơn vị đo',
        errors: error,
      });
    }
  }

  @Get('/utility-meters-dump/types')
  @Roles('/system-configuration/utility-meters', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Lấy danh sách loại đồng hồ tiện ích' })
  async getUtilityMeterTypeList() {
    try {
      const result = await this.utilityMetersService.getUtilityMeterTypeList();
      return {
        data: result,
        total: result.length,
      };
    } catch (error) {
      throw new BadRequestException({
        message: error.message || 'Có lỗi xảy ra khi lấy danh sách loại đồng hồ',
        errors: error,
      });
    }
  }

  @Get('/utility-meters-dump/data-types')
  @Roles('/system-configuration/utility-meters', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Lấy danh sách kiểu dữ liệu' })
  async getDataTypeList() {
    try {
      const result = await this.utilityMetersService.getDataTypeList();
      return {
        data: result,
        total: result.length,
      };
    } catch (error) {
      throw new BadRequestException({
        message: error.message || 'Có lỗi xảy ra khi lấy danh sách kiểu dữ liệu',
        errors: error,
      });
    }
  }

  @Get('/utility-meters-dump/datetime-units')
  @Roles('/system-configuration/utility-meters', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Lấy danh sách đơn vị thời gian' })
  async getDateTimeUnitList() {
    try {
      const result = await this.utilityMetersService.getDateTimeUnitList();
      return {
        data: result,
        total: result.length,
      };
    } catch (error) {
      throw new BadRequestException({
        message: error.message || 'Có lỗi xảy ra khi lấy danh sách đơn vị thời gian',
        errors: error,
      });
    }
  }

  @Get('/utility-meters-dump/calculation-methods')
  @Roles('/system-configuration/utility-meters', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Lấy danh sách phương thức tính toán' })
  async getCalculationMethodList() {
    try {
      const result = await this.utilityMetersService.getCalculationMethodList();
      return {
        data: result,
        total: result.length,
      };
    } catch (error) {
      throw new BadRequestException({
        message: error.message || 'Có lỗi xảy ra khi lấy danh sách phương thức tính toán',
        errors: error,
      });
    }
  }

  @Get('/utility-meters-dump/uom-calculation-methods')
  @Roles('/system-configuration/utility-meters', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Lấy danh sách đơn vị tính toán' })
  async getUOMCalculationMethodList() {
    try {
      const result = await this.utilityMetersService.getUOMCalculationMethodList();
      return {
        data: result,
        total: result.length,
      };
    } catch (error) {
      throw new BadRequestException({
        message: error.message || 'Có lỗi xảy ra khi lấy danh sách đơn vị tính toán',
        errors: error,
      });
    }
  }

  @Get('/utility-meters-dump/general-data-options')
  @Roles('/system-configuration/utility-meters', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Lấy danh sách các option từ General Data' })
  async getGeneralDataOptions() {
    try {
      const result = await this.utilityMetersService.getGeneralDataOptions();
      return {
        data: result,
      };
    } catch (error) {
      throw new BadRequestException({
        message: error.message || 'Có lỗi xảy ra khi lấy danh sách option từ General Data',
        errors: error,
      });
    }
  }

  @Get('/utility-meters-dump/lines/:factoryId')
  @Roles('/system-configuration/utility-meters', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Lấy danh sách line theo nhà máy' })
  async getLinesByFactoryId(@Param('factoryId') factoryId: string) {
    try {
      const result = await this.utilityMetersService.getLinesByFactoryId(factoryId);
      return { data: result, total: result.length };
    } catch (error) {
      throw new BadRequestException({
        message: error.message || 'Có lỗi xảy ra khi lấy danh sách lines',
        errors: error,
      });
    }
  }

  @Get('/utility-meters-dump/process-areas/:factoryId')
  @Roles('/system-configuration/utility-meters', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Lấy danh sách khu vực sản xuất theo nhà máy' })
  async getProcessAreasByFactoryId(
    @Param('factoryId') factoryId: string,
    @Query('page') page: number = 1,
    @Query('limit') limit: number = 10,
    @Query('processAreaCode') processAreaCode?: string,
    @Query('processAreaName') processAreaName?: string,
    @Query('lineId') lineId?: string,
    @Query('activeStatus') activeStatus?: ProcessAreaActiveStatus,
  ) {
    try {
      const result = await this.utilityMetersService.getProcessAreasByFactoryId(
        factoryId,
        page,
        limit,
        processAreaCode,
        processAreaName,
        lineId,
        activeStatus,
      );
      return {
        data: result.items,
        pagination: {
          total: result.total,
          page: result.page,
          limit: result.limit,
          totalPages: result.totalPages,
        },
      };
    } catch (error) {
      throw new BadRequestException({
        message: error.message || 'Có lỗi xảy ra khi lấy danh sách process areas',
        errors: error,
      });
    }
  }

  @Get('/utility-meters-dump/last-code')
  @Roles('/system-configuration/utility-meters', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Lấy mã code cuối cùng của Utility Meter' })
  async getLastCode() {
    try {
      const lastCode = await this.utilityMetersService.getLastCode();
      return {
        data: lastCode,
      };
    } catch (error) {
      throw new BadRequestException({
        message: error.message || 'Có lỗi xảy ra khi lấy mã code cuối cùng',
        errors: error,
      });
    }
  }
}
