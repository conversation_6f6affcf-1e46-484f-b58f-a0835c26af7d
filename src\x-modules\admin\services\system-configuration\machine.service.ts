import { BadRequestException, Injectable, NotFoundException } from '@nestjs/common';
import { isUUID } from 'class-validator';
import { ILike, In, Like, Not } from 'typeorm';
import { BindRepo, DefTransaction } from '~/@core/decorator';
import { NSGeneralData } from '~/common/enums';
import { MachineReq, ListMachineReq, ListParameterReq, ParameterReq } from '~/dto/machine.dto';
import { MachineParameterEntity } from '~/entities/primary';
import {
  GeneralDataDetailRepo,
  GeneralDataRepo,
  MachineParameterRepo,
  MachineRepo,
  ProcessMachineRepo,
} from '~/repositories/primary';

@Injectable()
export class MachineService {
  constructor() {}

  @BindRepo(MachineRepo)
  private machineRepo: MachineRepo;

  @BindRepo(ProcessMachineRepo)
  private processMachineRepo: ProcessMachineRepo;

  @BindRepo(MachineParameterRepo)
  private machineParameterRepo: MachineParameterRepo;

  @BindRepo(GeneralDataRepo)
  private generalDataRepo: GeneralDataRepo;

  @BindRepo(GeneralDataDetailRepo)
  private generalDataDetailRepo: GeneralDataDetailRepo;

  @DefTransaction()
  async create(body: MachineReq) {
    return this.machineRepo.save({
      ...body,
    });
  }

  async list(params: ListMachineReq) {
    const { pageIndex = 1, pageSize = 10, code, name, isActive, ...rest } = params;

    const where: any = { ...rest };
    if (code) where.code = ILike(`%${code}%`);
    if (name) where.name = ILike(`%${name}%`);
    if (isActive !== undefined) where.isActive = isActive;

    const [data, total] = await this.machineRepo.findAndCount({
      where,
      skip: (pageIndex - 1) * pageSize,
      take: pageSize,
      order: { isActive: 'DESC', createdDate: 'DESC', code: 'DESC' },
    });

    return { data, total };
  }

  async detail(id: string) {
    if (!id) throw new BadRequestException('ID don’t exist');
    return this.machineRepo.findOneOrFail({ where: { id } });
  }

  @DefTransaction()
  async update(id: string, body: MachineReq) {
    if (!id) throw new BadRequestException('ID don’t exist');
    const { affected } = await this.machineRepo.update({ id }, body);
    if (!affected) throw new NotFoundException(`Machine with ID: ${id} not found`);
    return { message: 'Update successfully' };
  }

  @DefTransaction()
  async delete(id: string) {
    if (!id) throw new BadRequestException('ID don’t exist');
    const machine = await this.machineRepo.findOne({ where: { id } });
    if (!machine) throw new NotFoundException('Not found machine');
    await this.machineRepo.remove(machine);
    return { message: 'Delete successfully' };
  }

  //#region MachineParameter

  async listParameters(machineId: string, params: ListParameterReq) {
    const {
      pageIndex = 1,
      pageSize = 10,
      tagName,
      iotsitewisePropertyId,
      iotsitewiseAssetId,
      ...otherParams
    } = params;

    const where: any = {
      machineId,
      ...otherParams,
    };

    if (tagName) {
      where.tagName = Like(`%${tagName}%`);
    }

    if (iotsitewisePropertyId) {
      where.iotsitewisePropertyId = Like(`%${iotsitewisePropertyId}%`);
    }

    if (iotsitewiseAssetId) {
      where.iotsitewiseAssetId = Like(`%${iotsitewiseAssetId}%`);
    }

    const [data, total] = await this.machineParameterRepo.findAndCount({
      where,
      order: { isActive: 'DESC', createdDate: 'DESC', code: 'DESC' },
      skip: (pageIndex - 1) * pageSize,
      take: pageSize,
    });

    return { data, total };
  }

  // 🔄 Batch update parameters (chỉ thêm mới hoặc sửa, không xóa)
  @DefTransaction()
  async batchUpdateParameters(machineId: string, parameters: ParameterReq[]) {
    const machine = await this.machineRepo.findOne({ where: { id: machineId } });
    if (!machine) {
      throw new NotFoundException(`Machine with ID ${machineId} not exist`);
    }

    const existingParams = await this.machineParameterRepo.find({ where: { machineId } });
    const existingParamMap = new Map(existingParams.map((p) => [p.id, p]));

    // Lấy `code` lớn nhất toàn hệ thống để đảm bảo tăng đúng
    const lastParam = await this.machineParameterRepo.findOne({
      order: { code: 'DESC' },
      select: ['code'],
    });
    let maxCode = lastParam ? lastParam.code : 1000000;

    const updateParams = [];
    const newParams = [];

    parameters.forEach((param) => {
      if (param.id && existingParamMap.has(param.id) && isUUID(param.id)) {
        const existing = existingParamMap.get(param.id);
        const isChanged = Object.keys(param).some((key) => param[key] !== existing[key]);
        if (isChanged) {
          updateParams.push({
            ...existing,
            ...param,
            id: existing.id,
            machineId: existing.machineId,
            code: existing.code,
          });
        }
      } else {
        maxCode++;
        newParams.push({ machineId, code: maxCode, ...param });
      }
    });

    if (updateParams.length > 0) {
      await this.machineParameterRepo.save(updateParams);
    }
    if (newParams.length > 0) {
      await this.machineParameterRepo.insert(newParams);
    }

    return { message: 'Batch update parameters successfully' };
  }

  // Phương thức để lưu MachineParameter và lưu thông tin vào bảng mapping (tạo mới hoặc cập nhật)
  @DefTransaction()
  async saveParameter(machineId: string, parameterDto: ParameterReq) {
    const machine = await this.machineRepo.findOne({ where: { id: machineId } });
    if (!machine) {
      throw new NotFoundException(`Machine with ID ${machineId} does not exist`);
    }

    const {
      id,
      typeId,
      dataTypeId,
      uomId,
      datetimeUnitId,
      iotsitewisePropertyTypeId,
      ...parameter
    } = parameterDto;

    let parameterEntity: MachineParameterEntity;
    const codeMap: any = await this.generalDataDetailRepo.find({
      where: {
        id: In([typeId, dataTypeId, uomId, datetimeUnitId, iotsitewisePropertyTypeId]),
      },
      relations: ['general'],
      select: ['id', 'code'],
    });

    const getCode = (targetId?: string) => codeMap.find(({ id }) => id === targetId)?.code;
    const getGeneralDataCode = (targetId?: string) =>
      codeMap.find(({ id }) => id === targetId)?.__general__?.code;

    // Kiểm tra tagName đã tồn tại trong cùng machineId chưa
    const existingParameter = await this.machineParameterRepo.findOne({
      where: { tagName: parameterDto.tagName, machineId, ...(id ? { id: Not(id) } : {}) }, // Nếu cập nhật thì loại trừ chính nó
    });

    if (existingParameter) {
      throw new BadRequestException(
        `TagName "${parameterDto.tagName}" existed in Machine ID ${machineId}.`,
      );
    }

    if (!id) {
      // Tạo mới MachineParameter
      const lastParam = await this.machineParameterRepo.findOne({
        order: { code: 'DESC' },
        select: ['code'],
      });

      let maxCode = lastParam ? lastParam.code : 1000000;
      maxCode++;

      parameterEntity = this.machineParameterRepo.create({
        machineId,
        code: maxCode,
        typeId,
        dataTypeId,
        uomId,
        datetimeUnitId,
        iotsitewisePropertyTypeId,
        typeCode: getCode(typeId),
        dataTypeCode: getCode(dataTypeId),
        uomCode: getCode(uomId),
        datetimeUnitCode: getCode(datetimeUnitId),
        iotsitewisePropertyTypeCode: getCode(iotsitewisePropertyTypeId),
        typeGeneralCode: NSGeneralData.EGeneralDataCode.MACHINE_PARAMETER_TYPE,
        dataTypeGeneralCode: NSGeneralData.EGeneralDataCode.DATA_TYPE,
        uomGeneralCode: NSGeneralData.EGeneralDataCode.UOM,
        datetimeUnitGeneralCode: NSGeneralData.EGeneralDataCode.DATETIME_UNIT,
        iotsitewisePropertyTypeGeneralCode:
          NSGeneralData.EGeneralDataCode.IOTSITEWISE_MODEL_PROPERTY_TYPE,
        ...parameter,
      });
    } else {
      // Cập nhật MachineParameter
      parameterEntity = await this.machineParameterRepo.findOne({ where: { id } });
      if (!parameterEntity) {
        throw new NotFoundException('MachineParameter not found');
      }

      this.machineParameterRepo.merge(parameterEntity, {
        typeId,
        dataTypeId,
        uomId,
        datetimeUnitId,
        iotsitewisePropertyTypeId,
        typeCode: getCode(typeId),
        dataTypeCode: getCode(dataTypeId),
        uomCode: getCode(uomId),
        datetimeUnitCode: getCode(datetimeUnitId),
        iotsitewisePropertyTypeCode: getCode(iotsitewisePropertyTypeId),
        typeGeneralCode: NSGeneralData.EGeneralDataCode.MACHINE_PARAMETER_TYPE,
        dataTypeGeneralCode: NSGeneralData.EGeneralDataCode.DATA_TYPE,
        uomGeneralCode: NSGeneralData.EGeneralDataCode.UOM,
        datetimeUnitGeneralCode: NSGeneralData.EGeneralDataCode.DATETIME_UNIT,
        iotsitewisePropertyTypeGeneralCode:
          NSGeneralData.EGeneralDataCode.IOTSITEWISE_MODEL_PROPERTY_TYPE,
        ...parameter,
      });
    }

    // Lưu MachineParameter vào DB
    await this.machineParameterRepo.save(parameterEntity);

    return {
      message: 'Machine parameter saved successfully',
      parameterEntity,
    };
  }

  async loadParameterDataSelect() {
    const genCodes = [
      NSGeneralData.EGeneralDataCode.MACHINE_PARAMETER_TYPE,
      NSGeneralData.EGeneralDataCode.DATA_TYPE,
      NSGeneralData.EGeneralDataCode.UOM,
      NSGeneralData.EGeneralDataCode.DATETIME_UNIT,
      NSGeneralData.EGeneralDataCode.IOTSITEWISE_MODEL_PROPERTY_TYPE,
    ];

    const genData = await this.generalDataRepo.find({
      where: { code: In(genCodes) },
      select: ['id', 'code'],
    });

    // Lấy toàn bộ chi tiết theo các generalId
    const details = await this.generalDataDetailRepo.find({
      where: { generalId: In(genData.map((item) => item.id)) },
      select: ['id', 'code', 'name', 'generalId'],
    });

    const result = genData.reduce(
      (acc, item) => {
        acc[item.code] = details
          .filter((detail) => detail.generalId === item.id)
          .map(({ id, code, name }) => ({ id, code, name, genCode: item.code }));
        return acc;
      },
      {} as Record<string, { id: string; code: string; name: string; genCode: string }[]>,
    );

    return result;
  }

  async find(data: { processId: string }) {
    const whereCon: any = { isActive: true };
    if (data.processId) {
      const lstMachineProcess = await this.processMachineRepo.find({
        where: { processId: data.processId },
        select: ['id', 'machineId'],
      });
      whereCon.id = In(lstMachineProcess.map((item) => item.machineId));
    }
    return await this.machineRepo.find({
      where: whereCon,
      order: { name: 'ASC' },
    });
  }

  async findMachineParameter(data: { machineId: string }) {
    return await this.machineParameterRepo.find({
      where: { machineId: data.machineId },
      order: { code: 'ASC' },
    });
  }

  //#endregion
}
