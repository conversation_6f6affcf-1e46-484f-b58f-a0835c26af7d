import { BadRequestException } from '@nestjs/common';
import { IsString, isUUID } from 'class-validator';
import dayjs from 'dayjs';
import { In } from 'typeorm';
import { BindRepo } from '~/@core/decorator';
import { NSRecipe } from '~/common/enums';
import { MaterialConsumptionScadaEntity } from '~/entities/primary';
import {
  ItemRepo,
  MaterialConsumptionScadaRepo,
  ProcessRepo,
  ProductionBatchRepo,
  ProductionOrderRepo,
  ProductionWeightRepo,
  RecipeProcessItemRepo,
  RecipeProcessRepo,
} from '~/repositories/primary';

export class ScanService {
  @BindRepo(ProcessRepo)
  private processRepo: ProcessRepo;

  @BindRepo(ProductionOrderRepo)
  private readonly productionOrderRepo: ProductionOrderRepo;

  @BindRepo(ProductionBatchRepo)
  private readonly productionBatchRepo: ProductionBatchRepo;

  @BindRepo(ItemRepo)
  private readonly itemRepo: ItemRepo;

  @BindRepo(RecipeProcessRepo)
  private readonly recipeProcessRepo: RecipeProcessRepo;

  @BindRepo(RecipeProcessItemRepo)
  private readonly recipeProcessItemRepo: RecipeProcessItemRepo;

  @BindRepo(MaterialConsumptionScadaRepo)
  private readonly materialConsumptionScadaRepo: MaterialConsumptionScadaRepo;

  @BindRepo(ProductionWeightRepo)
  private readonly productionWeightRepo: ProductionWeightRepo;

  async getItem(data: any) {
    const productionOrder = await this.productionOrderRepo.findOne({
      where: { id: data.productionOrderId },
    });
    if (!productionOrder) return [];
    const process = await this.processRepo.findOne({
      where: { organizationUnitId: productionOrder.processAreaId },
    });
    if (!process) return [];
    const lstRecipeProcess = await this.recipeProcessRepo.find({
      where: { recipeId: productionOrder.recipeId, processId: process.id },
    });
    const lstRecipeProcessItem = await this.recipeProcessItemRepo.find({
      where: {
        recipeProcessId: In(lstRecipeProcess.map((i) => i.id)),
        typeCode: NSRecipe.RecipeProcessItemTypeCode.Ingredient,
      },
    });
    const dictActualQty: any = {};
    {
      const lstMaterialConsumption = await this.materialConsumptionScadaRepo.find({
        where: {
          batchId: data.batchId,
          ingredientId: In(lstRecipeProcessItem.map((i) => i.itemId)),
        },
      });
      lstMaterialConsumption.forEach((i) => {
        if (!dictActualQty[i.ingredientId]) {
          dictActualQty[i.ingredientId] = 0;
        }
        dictActualQty[i.ingredientId] += Number(i.qty);
      });
    }
    const dictItem: any = {};
    {
      const lstItem = await this.itemRepo.find({
        where: { id: In(lstRecipeProcessItem.map((i) => i.itemId)) },
      });
      lstItem.forEach((i) => (dictItem[i.id] = i));
    }
    lstRecipeProcessItem.forEach((i: any) => {
      i.actualQty = dictActualQty[i.itemId] || 0;
      i.itemCode = dictItem[i.itemId]?.code;
      i.itemName = dictItem[i.itemId]?.name;
      i.weighingTolerance = dictItem[i.itemId]?.weighingTolerance || 0;
    });
    return lstRecipeProcessItem;
  }

  async getProductionWeightByBatchId(data: any) {
    const productionOrder = await this.productionOrderRepo.findOne({
      where: { id: data.productionOrderId },
    });
    if (!productionOrder) return [];
    const process = await this.processRepo.findOne({
      where: { organizationUnitId: productionOrder.processAreaId },
    });
    if (!process) return [];
    const lstRecipeProcess = await this.recipeProcessRepo.find({
      where: { recipeId: productionOrder.recipeId, processId: process.id },
    });
    const lstRecipeProcessItem = await this.recipeProcessItemRepo.find({
      where: {
        recipeProcessId: In(lstRecipeProcess.map((i) => i.id)),
        typeCode: NSRecipe.RecipeProcessItemTypeCode.Ingredient,
      },
    });
    const lstProductionWeight = await this.productionWeightRepo.find({
      where: {
        batchId: data.batchId,
        orderId: data.productionOrderId,
        itemId: In(lstRecipeProcessItem.map((i) => i.itemId)),
      },
    });
    return lstProductionWeight;
  }

  async scanProductionWeight(data: { weightId: any; batchId: any; productionOrderId: any }) {
    if (!isUUID(data.weightId)) {
      return {
        message: `Mã hàng ${data.weightId} không hợp lệ`,
        color: 'red',
      };
    }
    const weight = await this.productionWeightRepo.findOne({ where: { id: data.weightId } });
    if (!weight || weight.batchId !== data.batchId || weight.orderId !== data.productionOrderId) {
      return {
        message: `Mã hàng ${data.weightId} không có trong BOM`,
        color: 'red',
      };
    }
    if (weight.status == 1) {
      return {
        message: `QRCode ${weight.id} đã được sử dụng`,
        color: 'red',
      };
    }
    const productionOrder = await this.productionOrderRepo.findOne({
      where: { id: data.productionOrderId },
      select: ['id', 'processAreaId', 'recipeId'],
    });
    if (!productionOrder) return [];
    const process = await this.processRepo.findOne({
      where: { organizationUnitId: productionOrder.processAreaId },
      select: ['id', 'organizationUnitId'],
    });
    if (!process) return [];
    const lstRecipeProcess = await this.recipeProcessRepo.find({
      where: { recipeId: productionOrder.recipeId, processId: process.id },
      select: ['id', 'recipeId', 'processId'],
    });
    const lstRecipeProcessItem = await this.recipeProcessItemRepo.find({
      where: {
        recipeProcessId: In(lstRecipeProcess.map((i) => i.id)),
        typeCode: NSRecipe.RecipeProcessItemTypeCode.Ingredient,
      },
      select: ['id', 'itemId', 'quantity'],
    });
    const dictActualQty: any = {};
    {
      const lstMaterialConsumption = await this.materialConsumptionScadaRepo.find({
        where: {
          batchId: data.batchId,
          ingredientId: In(lstRecipeProcessItem.map((i) => i.itemId)),
        },
        select: ['ingredientId', 'qty'],
      });
      lstMaterialConsumption.forEach((i) => {
        if (!dictActualQty[i.ingredientId]) {
          dictActualQty[i.ingredientId] = 0;
        }
        dictActualQty[i.ingredientId] += Number(i.qty);
      });
    }

    const dictItem: any = {};
    {
      const lstItem = await this.itemRepo.find({
        where: { id: In(lstRecipeProcessItem.map((i) => i.itemId)) },
        select: ['id', 'weighingTolerance'],
      });
      lstItem.forEach((i) => (dictItem[i.id] = i.weighingTolerance));
    }

    const item = lstRecipeProcessItem.find((i) => i.itemId === weight.itemId);
    if (!item) {
      return {
        message: `Mã hàng ${weight.id} không có trong BOM`,
        color: 'red',
      };
    }
    const actualQty = dictActualQty[weight.itemId] || 0;
    const weighedQty =
      typeof weight.weighedQty === 'string'
        ? Number.parseFloat(weight.weighedQty)
        : weight.weighedQty;
    const quantity =
      typeof item.quantity === 'string' ? Number.parseFloat(item.quantity) : item.quantity;
    if (
      weighedQty + Number.parseFloat(actualQty) >
      quantity + Number.parseFloat(dictItem[weight.itemId])
    ) {
      return {
        message: `Số lượng vượt định mức BOM`,
        color: 'red',
      };
    }

    const newMaterialConsumption: Partial<MaterialConsumptionScadaEntity> = {
      orderNumber: weight.orderNo,
      orderId: weight.orderId,
      batchCode: weight.batchNo,
      batchId: weight.batchId,
      ingredientCode: weight.itemCode,
      ingredientId: weight.itemId,
      lotNumber: weight.lotNumber,
      qty: weighedQty,
      uom: weight.weightedUom,
      datetime: new Date(),
      operatorId: null,
      transactionType: 'Consumption',
      isProcessed: false,
      createdDate: new Date(),
    };
    const newMaterialConsumptionEntity =
      this.materialConsumptionScadaRepo.create(newMaterialConsumption);

    await this.materialConsumptionScadaRepo.insert(newMaterialConsumptionEntity);
    await this.productionWeightRepo.update(
      { id: weight.id },
      { status: 1, updatedDate: new Date() },
    );
    return {
      message: 'Scan QRCode thành công',
      color: 'blue',
      status: 'Success',
      itemId: weight.itemId,
      actualQty: weighedQty + Number.parseFloat(actualQty),
    };
  }

  async completeBatch(batchId: string) {
    return await this.productionBatchRepo.update(
      { id: batchId },
      { weighStatus: 2, updatedDate: new Date() },
    );
  }
}
