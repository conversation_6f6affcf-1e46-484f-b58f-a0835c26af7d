import { ApiProperty } from '@nestjs/swagger';
import { Column, Entity, OneToMany } from 'typeorm';
import { PrimaryBaseEntity } from '~/entities/primary-base.entity';
import { GeneralDataDetailEntity } from './general-data-detail.entity';

// General Master Data entity
@Entity('general_data')
export class GeneralDataEntity extends PrimaryBaseEntity {
  /** Mã General master data */
  @ApiProperty({ description: 'Mã General master data' })
  @Column({ unique: true, length: 50 })
  code: string;

  /** Tên General master data */
  @ApiProperty({ description: 'Tên General master data' })
  @Column({ length: 255 })
  name: string;

  /** Mô tả */
  @ApiProperty({ description: '<PERSON>ô tả' })
  @Column({ type: 'text', nullable: true })
  description?: string;

  /** <PERSON>hi chú thêm */
  @ApiProperty({ description: 'Ghi chú thêm' })
  @Column({ type: 'text', nullable: true })
  note?: string;

  /** Trạng thái hoạt động */
  @ApiProperty({ description: 'Trạng thái hoạt động' })
  @Column({ default: true, nullable: true })
  isActive: boolean;

  @OneToMany(() => GeneralDataDetailEntity, e => e.general)
  details: Promise<GeneralDataDetailEntity[]>;
}
