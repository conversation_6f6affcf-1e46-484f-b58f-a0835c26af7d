import {
  FindConditions,
  ObjectLiteral,
  Repository,
  FindOneOptions,
  FindOptionsUtils,
  DeepPartial,
  SaveOptions,
  FindManyOptions,
  InsertResult,
  UpdateResult,
} from 'typeorm';
import { QueryDeepPartialEntity } from 'typeorm/query-builder/QueryPartialEntity';
import { PageRequest, PageResponse } from '~/@systems/utils/page.utils';
import { adminSessionContext } from '~/x-modules/admin/admin-session.context';

interface ISumFindOptions<Entity = any> {
  sumSelect: (keyof Entity)[];
  where?: FindConditions<Entity>[] | FindConditions<Entity> | ObjectLiteral | string;
}
type TSumResult<Entity = any> = {
  [key in keyof Entity]: number;
};

const pageSizeDefault = 10;

/**
 * Utility: lấy đúng kiểu criteria của Repository.update()
 */
// type UpdateCriteria<Entity> = Parameters<Repository<Entity>['update']>[0];
type UpdateCriteria<Entity> =
  | string
  | number
  | Date
  | string[]
  | number[]
  | Date[]
  | FindConditions<Entity>
  | FindConditions<Entity>[];

/**
 * BaseRepository với:
 * - findOrCreate, findPagination, saves()
 * - override insert/update/save để auto gán audit
 * - update criteria chuẩn TypeORM 0.2
 */
export class BaseRepository<Entity extends ObjectLiteral> extends Repository<Entity> {
  // ========================
  // findOrCreate
  // ========================
  async findOrCreate(
    value: Partial<Entity>,
    findOptions?: FindOneOptions<Entity>,
  ): Promise<Entity> {
    const where = findOptions?.where ?? (value as any);
    const existing = await this.findOne({ where });
    if (existing) return existing;

    const now = new Date();
    const audit = this.buildAuditFields(true, now) as Record<string, any>;
    // Merge safe: chỉ override khi value[key] ≠ null/undefined
    const merged: Record<string, any> = { ...audit };
    Object.entries(value).forEach(([k, v]) => {
      if (v != null) merged[k] = v;
    });

    return this.save(merged as DeepPartial<Entity>);
  }

  // ========================
  // findPagination
  // ========================
  async findPagination(
    options: FindOneOptions<Entity> = {},
    pageRequest: PageRequest,
  ): Promise<{ data: Entity[]; total: number }> {
    const { pageIndex = 1, pageSize = 20 } = pageRequest;
    const [data, total] = await this.findAndCount({
      ...options,
      skip: (pageIndex - 1) * pageSize,
      take: pageSize,
    });
    return { data, total };
  }

  // ========================
  // insert override
  // ========================
  override async insert(
    input: QueryDeepPartialEntity<Entity> | QueryDeepPartialEntity<Entity>[],
  ): Promise<InsertResult> {
    const now = new Date();
    const audit = this.buildAuditFields(true, now) as QueryDeepPartialEntity<Entity>;

    const patch = (d: QueryDeepPartialEntity<Entity>) => {
      const merged: Record<string, any> = { ...audit };
      Object.entries(d).forEach(([k, v]) => {
        if (v != null) merged[k] = v;
      });
      return merged as QueryDeepPartialEntity<Entity>;
    };

    const payload = Array.isArray(input) ? input.map(patch) : patch(input as any);

    return super.insert(payload);
  }

  // ========================
  // update override
  // ========================
  override async update(
    criteria: UpdateCriteria<Entity>,
    partialEntity: QueryDeepPartialEntity<Entity>,
  ): Promise<UpdateResult> {
    const auditPE = this.buildAuditFields(false, new Date()) as QueryDeepPartialEntity<Entity>;

    const merged: Record<string, any> = { ...auditPE };
    Object.entries(partialEntity).forEach(([k, v]) => {
      if (v != null) merged[k] = v;
    });

    return super.update(criteria as any, merged as QueryDeepPartialEntity<Entity>);
  }

  // ========================
  // save override (single & array)
  // ========================
  override async save(entity: DeepPartial<Entity>, options?: SaveOptions): Promise<Entity>;
  override async save(entities: DeepPartial<Entity>[], options?: SaveOptions): Promise<Entity[]>;
  override async save(
    input: DeepPartial<Entity> | DeepPartial<Entity>[],
    options?: SaveOptions,
  ): Promise<Entity | Entity[]> {
    const now = new Date();

    const patch = (e: DeepPartial<Entity>): DeepPartial<Entity> => {
      const isNew = !(e as any).id;
      const audit = this.buildAuditFields(isNew, now) as Record<string, any>;
      // chỉ lấy những field thực sự có value
      const nonNull = Object.fromEntries(
        Object.entries(e as Record<string, any>).filter(([, v]) => v != null),
      );
      return { ...audit, ...nonNull } as DeepPartial<Entity>;
    };

    const toSave = Array.isArray(input) ? input.map(patch) : patch(input);

    return super.save(toSave as any, options);
  }

  // ========================
  // saves batch
  // ========================
  async saves(
    entities: readonly DeepPartial<Entity>[],
    options: SaveOptions = {},
  ): Promise<Entity[]> {
    // Ép overload save(array) để TS chọn đúng kiểu trả về Entity[]
    const saveMany = this.save as (e: DeepPartial<Entity>[], o?: SaveOptions) => Promise<Entity[]>;

    return saveMany(entities as DeepPartial<Entity>[], {
      reload: true,
      chunk: 10000,
      ...options,
    });
  }

  // ========================
  // buildAuditFields
  // ========================
  protected buildAuditFields(isCreate = false, now = new Date()): Record<string, any> {
    const { userId, userName } = adminSessionContext;
    const base: Record<string, any> = {
      updatedBy: userId,
      updatedByUser: userName,
      updatedDate: now,
    };
    if (isCreate) {
      base.createdBy = userId;
      base.createdByUser = userName;
      base.createdDate = now;
    }
    return base;
  }
}

export class BaseRepoPostgreSql<Entity> extends BaseRepository<Entity> {
  constructor() {
    super();
  }

  // SQL injection
  // async queryPagination<T = any, F = any>(
  //   sqlRoot: string,
  //   pageRequest: PageRequest,
  // ): Promise<PageResponse<T>> {
  //   let { pageIndex = 1, pageSize = pageSizeDefault } = pageRequest;
  //   if (!pageIndex) {
  //     pageIndex = 1;
  //   }
  //   if (!pageSize) {
  //     pageSize = pageSizeDefault;
  //   }
  //   let sqlData = ` select * from ( ${sqlRoot} ) temp  `;
  //   const sqlTotal = ` select CAST( count(*) as integer ) as total from ( ${sqlRoot} ) temp  `;
  //   sqlData = sqlData + ` limit ${pageSize} offset  ${(pageIndex - 1) * pageSize}  `;
  //   const res = await Promise.all([this.query(sqlData), this.query(sqlTotal)]);
  //   return {
  //     data: res[0],
  //     total: res[1][0].total,
  //   };
  // }

  // SQL injection
  // async pageQueryMaxTotal<T = any, F = any>(
  //   sqlRoot: string,
  //   pageRequest: PageRequest,
  //   maxTotal = 1000,
  // ): Promise<PageResponse<T>> {
  //   let { pageIndex = 1, pageSize = pageSizeDefault } = pageRequest;
  //   if (!pageIndex) {
  //     pageIndex = 1;
  //   }
  //   if (!pageSize) {
  //     pageSize = pageSizeDefault;
  //   }
  //   const offset = (pageIndex - 1) * pageSize;
  //   if (offset > maxTotal) {
  //     return {
  //       data: [],
  //       total: maxTotal,
  //     };
  //   }
  //   let sqlData = ` select * from ( ${sqlRoot} ) temp  `;
  //   sqlData = sqlData + ` limit ${pageSize} offset  ${offset}  `;
  //   const data = (await this.query(sqlData)) as T[];
  //   return {
  //     data,
  //     total: data.length >= pageSize ? maxTotal : offset + data.length,
  //   };
  // }

  // async sum<T = TSumResult<Entity>>(sumFindOptions: ISumFindOptions<Entity>) {
  //   const { sumSelect, ...findManyOptions } = sumFindOptions;
  //   const { ownColumns = [] } = this.metadata;
  //   const keyMaps = ownColumns.reduce((prev, current) => {
  //     const { propertyName, givenDatabaseName, databaseName, databaseNameWithoutPrefixes } =
  //       current;

  //     const proDbName = databaseNameWithoutPrefixes || databaseName || givenDatabaseName;

  //     const fieldSelect = sumSelect.find((v) => v === propertyName);
  //     if (fieldSelect) {
  //       prev[propertyName] = proDbName;
  //     }
  //     return prev;
  //   }, {});

  //   const queryBuilder = FindOptionsUtils.applyOptionsToQueryBuilder(
  //     this.createQueryBuilder(),
  //     findManyOptions,
  //   );

  //   for (const [idx, keyMap] of Object.keys(keyMaps).entries()) {
  //     if (idx === 0) {
  //       queryBuilder.select(
  //         `COALESCE ( SUM ( CASE WHEN "${keyMaps[keyMap]}" IS  NULL THEN 0 ELSE "${keyMaps[keyMap]}" END )) `,
  //         keyMap,
  //       );
  //     } else {
  //       queryBuilder.addSelect(
  //         `COALESCE ( SUM ( CASE WHEN "${keyMaps[keyMap]}" IS  NULL THEN 0 ELSE "${keyMaps[keyMap]}" END )) `,
  //         keyMap,
  //       );
  //     }
  //   }
  //   const res = await queryBuilder.getRawOne<T>();
  //   Object.keys(res).forEach((key) => {
  //     res[key] = Number(res[key]);
  //   });

  //   return res;
  // }

  async queryOne<T = any>(query: string, parameters?: any[]): Promise<T> {
    try {
      const res = await this.query(query, parameters);
      if (!res || !(res instanceof Array) || res.length === 0) {
        return undefined;
      }
      return res[0] as T;
    } catch (error) {
      return undefined;
    }
  }

  async queryOneOrFail<T = any>(query: string, parameters?: any[]): Promise<T> {
    const res = await this.query(query, parameters);
    if (!res || !(res instanceof Array) || res.length === 0) {
      throw new Error('Empty record');
    }
    return res[0] as T;
  }
}
