import { Bind, Inject, Injectable } from '@nestjs/common';
import { configEnv } from '~/@config/env';
import { BindRepo, DefTransaction } from '~/@core/decorator';
import { ApiTokenRepo } from '~/repositories/primary';
import crypto from 'crypto';
import { HttpMethod } from '~/@core/network';
import { ILike, Like, MoreThanOrEqual } from 'typeorm';
import dayjs from 'dayjs';
import { BusinessException } from '~/@systems/exceptions';

const { API_TOKEN_SECRET, API_TOKEN_CACHE_INTERVAL } = configEnv();

@Injectable()
export class ApiTokenService {
  @BindRepo(ApiTokenRepo)
  private apiTokenRepo: ApiTokenRepo;
  private apiTokenCache: Record<string, Record<string, boolean>> = {};
  private readonly CACHE_INTERVAL = 1000 * 60 * 60 * (API_TOKEN_CACHE_INTERVAL ?? 1); // 1 hour
  private readonly API_TOKEN_SECRET: string;
  private cacheInterval: NodeJS.Timeout;
  constructor() {
    this.API_TOKEN_SECRET = API_TOKEN_SECRET;
  }

  async pagination(params: any) {
    const whereCon: any = {};
    if (params.path) whereCon.path = Like(`%${params.path}%`);
    if (params.method) whereCon.method = params.method;
    return await this.apiTokenRepo.findPagination(
      { where: whereCon },
      { pageIndex: params.pageIndex, pageSize: params.pageSize },
    );
  }

  @DefTransaction()
  async create(
    paths: Array<{ url: string; method: HttpMethod }>,
    expiredAt?: Date,
    key = this.API_TOKEN_SECRET,
  ) {
    const textToHash = `${key}_${JSON.stringify(paths)}_${Date.now()}`;
    const token = this.hash(textToHash, 'sha256');
    const hash = this.hash(token);
    const promises = paths.map(async (path) => {
      const exist = await this.apiTokenRepo.find({
        where: { path: path.url, method: path.method, expiredAt: null },
      });
      const exist2 = await this.apiTokenRepo.find({
        where: { path: path.url, method: path.method, expiredAt: MoreThanOrEqual(dayjs()) },
      });
      if (exist.length > 0 || exist2.length > 0) throw new BusinessException('Api đã tồn tại');
      return this.apiTokenRepo.save({
        path: path.url,
        method: path.method,
        key: hash,
        expiredAt,
      });
    });
    await Promise.all(promises);
    await this.cache();
    return token;
  }

  hash(key: string, algorithm = 'sha512') {
    const hash = crypto
      .createHmac(algorithm, this.API_TOKEN_SECRET ?? '')
      .update(key)
      .digest('hex');
    return hash;
  }

  async check(path: string, method: HttpMethod, key: string) {
    await this.refreshCache();

    // if (!this.cacheInterval) {
    await this.initCache();
    // }

    const hashedKey = this.hash(key);

    return this.apiTokenCache[`$${path}_${method}`]?.[hashedKey] ?? false;
  }

  async initCache() {
    console.log('[ApiTokenService] Init cache, interval:', this.CACHE_INTERVAL);
    await this.cache();
    clearInterval(this.cacheInterval);
    this.cacheInterval = setInterval(async () => {
      await this.cache();
    }, this.CACHE_INTERVAL);
  }

  async invalidateCache(path: string, method: HttpMethod, key: string) {
    delete this.apiTokenCache[`$${path}_${method}`]?.[key];
  }

  async refreshCache() {
    this.apiTokenCache = {};
    await this.cache();
  }

  async deleteToken(key: string, path?: string, method?: string) {
    if (path && method) {
      await this.apiTokenRepo.delete({ key, path, method });
    } else {
      await this.apiTokenRepo.delete({ key });
    }
    await this.refreshCache();
  }

  // TODO: Chuyển sang cơ ché cache redis nếu dữ liệu lớn hơn
  async cache() {
    const apiTokens = await this.apiTokenRepo.find();

    for await (const apiToken of apiTokens) {
      if (!this.apiTokenCache[apiToken.path]) {
        this.apiTokenCache[`$${apiToken.path}_${apiToken.method}`] = {};
      }
      this.apiTokenCache[`$${apiToken.path}_${apiToken.method}`][apiToken.key] = true;
    }
  }
}
