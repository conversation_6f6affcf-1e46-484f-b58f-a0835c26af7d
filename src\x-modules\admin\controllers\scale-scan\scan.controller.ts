import { Body, Query, UseGuards } from '@nestjs/common';
import { DefController, DefGet, DefPost, DefPut } from '~/@core/decorator';
import { Roles } from '~/@core/decorator/roles.decorator';
import { RoleGuard } from '~/@systems/guard/role.guard';
import { ScanService } from '~/x-modules/admin/services';

@DefController('scan')
export class ScanController {
  constructor(private readonly service: ScanService) {}

  @DefGet('get-item')
  @Roles('/production-execution/weighing-scan', 'View')
  @UseGuards(RoleGuard)
  async getItem(@Query() param: any) {
    return this.service.getItem(param);
  }

  @DefGet('get-production-weight')
  @Roles('/production-execution/weighing-scan', 'View')
  @UseGuards(RoleGuard)
  async getProductionWeightByBatchId(@Query() param: any) {
    return this.service.getProductionWeightByBatchId(param);
  }

  @DefPost('scan-production-weight')
  @Roles('/production-execution/weighing-scan', 'View')
  @UseGuards(RoleGuard)
  async scanProductionWeight(@Body() data: any) {
    return this.service.scanProductionWeight(data);
  }

  @DefPut('complete-scan')
  @Roles('/production-execution/weighing-scan', 'View')
  @UseGuards(RoleGuard)
  async completeScanProductionBatch(@Body() data: any) {
    return this.service.completeBatch(data.batchId);
  }
}
