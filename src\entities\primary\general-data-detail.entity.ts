import { ApiProperty } from '@nestjs/swagger';
import { Column, Entity, Index, JoinColumn, ManyToOne, OneToMany } from 'typeorm';
import { GeneralDataEntity } from '~/entities/primary';
import { PrimaryBaseEntity } from '~/entities/primary-base.entity';

@Entity('general_data_details')
export class GeneralDataDetailEntity extends PrimaryBaseEntity {
  /** Mã General Master Data Detail */
  @ApiProperty({ description: 'Mã General Master Data Detail' })
  @Column({ length: 50 })
  @Index()
  code: string;

  /** Tên General Master Data Detail */
  @ApiProperty({ description: 'Tên General Master Data Detail' })
  @Column({ length: 255 })
  name: string;

  /** <PERSON>ô tả */
  @ApiProperty({ description: 'Mô tả' })
  @Column({ type: 'text', nullable: true })
  description?: string;

  /** Ghi chú thêm */
  @ApiProperty({ description: '<PERSON><PERSON> chú thêm' })
  @Column({ type: 'text', nullable: true })
  note?: string;

  /** Trạng thái hoạt động */
  @ApiProperty({ description: 'Trạng thái hoạt động' })
  @Column({ default: true })
  isActive: boolean;

  @Column({})
  generalId: string;

  @ManyToOne(() => GeneralDataEntity, p => p.details)
  @JoinColumn({ name: 'generalId', referencedColumnName: 'id' })
  general: Promise<GeneralDataEntity>;
}
