import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { PageRequest } from '~/@systems/utils';

export class ListKPIPeriodReq extends PageRequest {
  @ApiProperty({ description: 'Tên kỳ K<PERSON>' })
  @IsOptional()
  periodName?: string;

  @ApiProperty({ description: 'Ngày bắt đầu kỳ <PERSON>' })
  @IsOptional()
  startDate?: Date;

  @ApiProperty({ description: '<PERSON><PERSON><PERSON> kết thúc kỳ K<PERSON>' })
  @IsOptional()
  endDate?: Date;

  @ApiProperty({ description: 'Trạng thái kỳ <PERSON>' })
  @IsOptional()
  status?: number;

  @ApiProperty({ description: 'Người khóa kỳ' })
  @IsOptional()
  lockedBy?: string;

  @ApiProperty({ description: '<PERSON><PERSON><PERSON> <PERSON>h<PERSON><PERSON> kỳ KPI' })
  @IsOptional()
  lockedDate?: Date;

  @ApiProperty({ description: '<PERSON><PERSON>' })
  @IsOptional()
  cycleType?: string;

  @ApiProperty({ description: '<PERSON>ỳ KPI' })
  @IsOptional()
  cycleTypeName?: string;

  @ApiProperty({ description: 'Mã site áp dụng kỳ KPI' })
  @IsOptional()
  siteId?: number;
}

export class ListKPIPeriodRuleReq extends PageRequest {
  @ApiPropertyOptional({description: 'Tên rule kỳ KPI'})
  @IsOptional()
  periodRuleName?: string;

  @ApiPropertyOptional({ description: 'Ngày bắt đầu kỳ KPI' })
  @IsOptional()
  startDate?: Date;

  @ApiPropertyOptional({ description: 'Ngày kết thúc kỳ KPI' })
  @IsOptional()
  endDate?: Date;

  @ApiPropertyOptional({ description: 'Ca bắt đầu kỳ KPI' })
  @IsOptional()
  startShift?: Date;

  @ApiPropertyOptional({ description: 'Ca kết thúc kỳ KPI' })
  @IsOptional()
  endShift?: Date;

  @ApiPropertyOptional({ description: 'Tháng bắt đầu kỳ KPI' })
  @IsOptional()
  startMonth?: number;

  @ApiPropertyOptional({ description: 'Chu kỳ KPI' })
  @IsOptional()
  cycleType?: string;

  @ApiProperty({ description: 'Trạng thái rule kỳ KPI' })
  @IsOptional()
  status?: number;
}

export class KPIPeriodRuleReq {
  @ApiProperty({description: 'Tên rule kỳ KPI'})
  @IsNotEmpty()
  periodRuleName: string;

  @ApiProperty({description: 'Ngày bắt đầu kỳ KPI'})
  @IsNotEmpty()
  @Type(() => Number)
  startDate: number;

  @ApiProperty({description: 'Ngày kết thúc kỳ KPI'})
  @IsNotEmpty()
  @Type(() => Number)
  endDate: number;

  @ApiProperty({description: 'Ca bắt đầu kỳ KPI'})
  @IsNotEmpty()
  startShift: string;

  @ApiProperty({description: 'Ca kết thúc kỳ KPI'})
  @IsNotEmpty()
  endShift: string;

  @ApiProperty({description: 'Tháng bắt đầu kỳ KPI', nullable: true})
  @IsOptional()
  @Type(() => Number)
  startMonth: number;

  @ApiProperty({description: 'Chu kỳ KPI'})
  @IsNotEmpty()
  cycleType: string;


  @ApiProperty({ description: 'Trạng thái rule kỳ KPI' })
  @IsOptional()
  status?: boolean;
}

export class KPIPeriodUpdateStatusDTO {
  @ApiPropertyOptional({description: 'Danh sách site'})
  @IsOptional()
  lstSiteId?: string[]

  @ApiPropertyOptional({description: 'Áp dụng tất cả site'})
  @IsOptional()
  isAll?: boolean

  @ApiProperty({description: 'Tên kỳ'})
  @IsString()
  periodName: string

  @ApiProperty({description: 'Trạng thái'})
  @IsNotEmpty()
  status: number
}

export class ListKPIPeriodMonthDTO {
  @ApiPropertyOptional({description: 'Danh sách site'})
  @IsOptional()
  lstSiteId?: string[]

  @ApiProperty({description: 'Năm áp dụng kpi'})
  year: number
}