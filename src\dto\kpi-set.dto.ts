import {
  IsString,
  Is<PERSON><PERSON>ber,
  IsOptional,
  IsArray,
  ValidateNested,
  IsNotEmpty,
} from 'class-validator';
import { Type } from 'class-transformer';
import { ApiProperty } from '@nestjs/swagger';
import { PageRequest } from '~/@systems/utils';

class FactoryProcessAreaDto {
  @ApiProperty({ description: 'Id site' })
  @IsOptional()
  siteId: string;

  @ApiProperty({ description: 'Id khu vực sản xuất' })
  @IsOptional()
  productionAreaId: string;

  @ApiProperty({ description: 'Id khu vực sản xuất' })
  @IsOptional()
  status: string;
}

class KpiSetGroupDto {
  @ApiProperty({ description: 'Mã nhóm KPI' })
  @IsString()
  @IsOptional()
  kpiGroupId: string;

  @ApiProperty({ description: 'Tỷ trọng chia thưởng' })
  @IsOptional()
  weighTarget: number;

  @ApiProperty({ description: '<PERSON><PERSON> tiền thưởng cho nhóm' })
  @IsOptional()
  budget: number;

  details: KpiSetDetailDto[];
}

class KpiSetDetailDto {
  @ApiProperty({ description: 'Mã KPI (liên kết đến bảng KPI list)' })
  @IsString()
  @IsOptional()
  kpiId: string;

  @ApiProperty({ description: 'Tỷ trọng chia thưởng cho KPI chi tiết' })
  @IsOptional()
  weighTarget: number;

  @ApiProperty({ description: 'Số tiền thưởng cho KPI chi tiết' })
  @IsOptional()
  budget: number;
}

export class CreateKpiSetDto {
  @ApiProperty({ description: 'Mã bộ KPI' })
  @IsString()
  code: string;

  @ApiProperty({ description: 'Tên ngắn (tên viết tắt) của KPI' })
  @IsOptional()
  shortName: string;

  @ApiProperty({ description: 'Tên KPI đầy đủ' })
  @IsString()
  @IsOptional()
  longName: string;

  @ApiProperty({ description: 'Ngân sách thưởng hàng tháng' })
  @IsNumber()
  @IsOptional()
  budget: number;

  @ApiProperty({ description: 'Trạng thái sử dụng' })
  @IsOptional()
  status: number;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => FactoryProcessAreaDto)
  factoryProcessAreas: FactoryProcessAreaDto[];

  @IsArray()
  kpiSetGroups: KpiSetGroupDto[];
}

export class UpdateKpiSetDto extends CreateKpiSetDto {
  @ApiProperty({ description: 'ID' })
  @IsNotEmpty()
  @IsString()
  id: string;
}

export class KpiSetPageReq extends PageRequest {
  @ApiProperty({ description: 'Id nhà máy' })
  @IsOptional()
  factoryId: string;

  @ApiProperty({ description: 'Id khu vực sản xuất' })
  @IsOptional()
  productionAreaId: string;

  @ApiProperty({ description: 'Id Site' })
  @IsOptional()
  siteId: string;

  @ApiProperty({ description: 'Tên' })
  @IsOptional()
  longName: string;

  @ApiProperty({ description: 'Tên' })
  @IsOptional()
  shortName: string;

  @ApiProperty({ description: 'Mã' })
  @IsOptional()
  code: string;

  @ApiProperty({ description: 'Trạng thái' })
  @IsOptional()
  status: number;
}
