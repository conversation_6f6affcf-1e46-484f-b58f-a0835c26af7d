import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import { BindRepo } from '~/@core/decorator';
import { UOMEntity } from '~/entities/primary/uom.entity';
import { UOMRepo } from '~/repositories/primary/uom.repo';

@Injectable()
export class UOMService {
  constructor() {}

  @BindRepo(UOMRepo)
  private readonly uomRepo: UOMRepo;

  async findAll(): Promise<UOMEntity[]> {
    return this.uomRepo.find();
  }

  async findOne(id: string): Promise<UOMEntity> {
    const uom = await this.uomRepo.findOne({ where: { id } });
    if (!uom) {
      throw new NotFoundException(`UOM with ID ${id} not found`);
    }
    return uom;
  }

  async create(data: Partial<UOMEntity>): Promise<UOMEntity> {
    const existingUOM = await this.uomRepo.findByCode(data.code);
    if (existingUOM) {
      throw new BadRequestException(`UOM with code ${data.code} already exists`);
    }
    return this.uomRepo.save(data);
  }

  async update(id: string, data: Partial<UOMEntity>): Promise<UOMEntity> {
    const uom = await this.findOne(id);
    if (data.code && data.code !== uom.code) {
      const existingUOM = await this.uomRepo.findByCode(data.code);
      if (existingUOM) {
        throw new BadRequestException(`UOM with code ${data.code} already exists`);
      }
    }
    return this.uomRepo.save({ ...uom, ...data });
  }

  async remove(id: string): Promise<void> {
    const uom = await this.findOne(id);
    await this.uomRepo.remove(uom);
  }

  async updateStatus(id: string, isActive: boolean): Promise<UOMEntity> {
    const uom = await this.findOne(id);
    return this.uomRepo.save({ ...uom, isActive });
  }

  async findActiveUOMs(): Promise<UOMEntity[]> {
    return this.uomRepo.findActiveUOMs();
  }

  async findInactiveUOMs(): Promise<UOMEntity[]> {
    return this.uomRepo.findInactiveUOMs();
  }

  async searchByTerm(term: string): Promise<UOMEntity[]> {
    return this.uomRepo.searchByTerm(term);
  }
} 