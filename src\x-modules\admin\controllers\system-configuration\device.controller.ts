import { Body, Controller, Get, Param, Post, Query, UseGuards } from '@nestjs/common';
import { DefController, DefGet } from '~/@core/decorator';
import { Roles } from '~/@core/decorator/roles.decorator';
import { RoleGuard } from '~/@systems/guard/role.guard';
import { CreateDeviceDto, ListDeviceReq, UpdateDeviceDto } from '~/dto/device.dto';
import { DeviceService } from '~/x-modules/admin/services/system-configuration/device.service';

@DefController('device')
export class DeviceController {
  constructor(private readonly deviceService: DeviceService) {}

  @Post('create')
  @Roles('/system-configuration/device', 'View')
  @UseGuards(RoleGuard)
  create(@Body() data: CreateDeviceDto) {
    return this.deviceService.create(data);
  }

  @Get('list')
  @Roles('/system-configuration/device', 'View')
  @UseGuards(RoleGuard)
  list(@Query() params: ListDeviceReq) {
    return this.deviceService.list(params);
  }

  @Get('get-list-device-type')
  @Roles('/system-configuration/device', 'View')
  @UseGuards(RoleGuard)
  listDeviceType(@Query('deviceType') deviceType: string) {
    return this.deviceService.getDeviceTypeCode(deviceType);
  }

  @Post('update/:id')
  @Roles('/system-configuration/device', 'View')
  @UseGuards(RoleGuard)
  update(@Param('id') id: string, @Body() data: UpdateDeviceDto) {
    return this.deviceService.update(id, data);
  }
}
