import { Entity, PrimaryColumn, Column, PrimaryGeneratedColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { PrimaryBaseEntity } from '~/entities/primary-base.entity';

@Entity('production_order_resource')
export class ProductionOrderResourceEntity extends PrimaryBaseEntity {
  /** Mỗi Production Order được tạo, thông tin detail của Production Order được lưu vào bảng Material. Order id link từ bảng Production Order. */
  @ApiProperty({ description: 'ID lệnh sản xuất (liên kết đến ProductionOrder)' })
  @Column({ type: 'uuid', nullable: true })
  orderId: string;

  /** Polulated theo Order id từ bảng Production Order. */
  @ApiProperty({ description: 'Mã lệnh sản xuất (lấy từ ProductionOrder)' })
  @Column({ type: 'varchar', length: 250 })
  orderNo: string;

  /** Polulated theo Order id từ bảng Production Order. */
  @ApiProperty({ description: '<PERSON><PERSON> công thức (lấy từ ProductionOrder)' })
  @Column({ type: 'uuid', nullable: true })
  recipeId: string;

  /** Polulated theo Order id từ bảng Production Order. */
  @ApiProperty({ description: 'Phiên bản công thức (lấy từ ProductionOrder)' })
  @Column({ nullable: true, type: 'numeric', precision: 20, scale: 2 })
  recipeVersion: number;

  /** Hệ thống tự sinh theo số tự nhiên, để mỗi dòng material là 1 id duy nhất. */
  @ApiProperty({ description: 'ID dòng vật liệu' })
  @PrimaryGeneratedColumn()
  lineId: number;

  /** Process Area Id, thông tin populate tir Recipe */
  @ApiProperty({ description: 'Mã khu vực sản xuất (liên kết đến Recipe)' })
  @Column({ type: 'uuid', nullable: true })
  processId: string;

  /** Material Code, thông tin populate từ Recipe */
  @ApiProperty({ description: 'Mã code vật liệu (liên kết đến Recipe)' })
  @PrimaryColumn({ type: 'varchar', length: 50 })
  resourceCode: string;

  @ApiProperty({ description: 'Id recipe_resource' })
  @Column({ type: 'uuid', nullable: true })
  receiptResourceId: string;

  /** Id Đơn vị tính */
  @ApiProperty({ description: 'Đơn vị tính vật liệu' })
  @Column({ type: 'varchar', length: 36, nullable: true })
  uom: string;

  /** Mã đơn vị tính */
  @ApiProperty({ description: 'Đơn vị giao dịch' })
  @Column({ type: 'varchar', length: 50, nullable: true })
  uomCode: string;

  /** Số lượng sản phẩm cân chạy resource theo kế hoạch */
  @ApiProperty({ description: 'Số lượng sản phẩm theo kế hoạch' })
  @Column({ type: 'numeric', nullable: true })
  planProdQty: number;

  /** Số lượng sản phẩm chạy resource thực tế. Hệ thống tự update theo ghi nhận thực tế từ Scada và update vào bảng Số lượng resource sử dụng theo kế hoạch (Có tỉnh toàn để ra số lượng theo mô tả quy trình nhập ở bước 5 bên dưới) */
  @ApiProperty({ description: 'Số lượng sản phẩm thực tế' })
  @Column({ type: 'numeric', nullable: true })
  actualProdQty: number;

  /** Số lượng resource sử dụng theo kế hoạch (Có tỉnh toàn để ra số lượng theo mô tả quy trình nhập ở bước 5 bên dưới) */
  @ApiProperty({ description: 'Số lượng vật liệu sử dụng theo kế hoạch' })
  @Column({ type: 'numeric', nullable: true })
  planResourceUsage: number;

  /** Số lượng resource sử dụng thực tế */
  @ApiProperty({ description: 'Số lượng vật liệu sử dụng thực tế' })
  @Column({ type: 'numeric', nullable: true })
  actualResourceUsage: number;

  /** Hệ thống tự sinh theo số thứ tự tăng dần, 1 order_id có 1 order_step_id. */
  @ApiProperty({ description: 'ID bước lệnh sản xuất' })
  @PrimaryGeneratedColumn()
  orderStepId: number;

  //   // Mối quan hệ với ProductionOrder
  //   @ManyToOne(() => ProductionOrder, productionOrder => productionOrder.productionOrderResources)
  //   @JoinColumn({ name: 'orderId' })
  //   productionOrder: ProductionOrder;
}
