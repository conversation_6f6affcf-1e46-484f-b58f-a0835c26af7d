import { Column, Entity, OneToMany } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { PrimaryBaseEntity } from '~/entities/primary-base.entity';
import { MaterialGroupDetailEntity } from '~/entities/primary/material-group-detail.entity';

/** Nhóm nguyên vật liệu header */
@Entity('material_group_header')
export class MaterialGroupHeaderEntity extends PrimaryBaseEntity {
  /** Tên nhớm nguyên vật liệu */
  @ApiProperty()
  @Column({ nullable: true })
  materialGroupCode: string;

  /** Mô tả nhớm nguyên vật liệu */
  @ApiProperty()
  @Column({ nullable: true })
  materialGroupDescription: string;

  /** Trạng thái nhóm nguyên vật liệu header */
  @Column({ default: true })
  active?: boolean;

  /** Danh sách chi tiết nhóm nguyên vật liệu */
  @OneToMany(() => MaterialGroupDetailEntity, (e) => e.headerId)
  details: Promise<MaterialGroupDetailEntity[]>;
}
