import { ConflictException, Injectable, NotFoundException } from '@nestjs/common';
import moment from 'moment';
import { In, Raw } from 'typeorm';
import { BindRepo } from '~/@core/decorator';
import {
  CreateReasonMasterDto,
  ReasonMasterPaginationDto,
  ReasonMasterResponseDto,
  UpdateReasonMasterDto,
} from '~/dto/reason-master.dto';
import { ReasonMasterEntity } from '~/entities/primary/reason-master.entity';
import { GeneralDataDetailRepo, GeneralDataRepo, ReasonMasterRepo } from '~/repositories/primary';

@Injectable()
export class ReasonMasterService {
  constructor() {}

  @BindRepo(ReasonMasterRepo)
  private readonly repo: ReasonMasterRepo;

  @BindRepo(GeneralDataDetailRepo)
  private readonly generalDataDetailRepo: GeneralDataDetailRepo;

  @BindRepo(GeneralDataRepo)
  private readonly generalDataRepo: GeneralDataRepo;

  /**
   * lấy danh sách dữ liệu select box cho lý do
   * @param loadSelectBoxDataForReasonMaster lấy danh sách dữ liệu select box cho lý do
   * @returns lấy danh sách dữ liệu select box cho lý do
   */
  async loadSelectBoxDataForReasonMaster() {
    //     1. Group = REASON
    // 2. Sub Group = REASON_SUB_GROUP
    // 3. Category = PRODUCTION_CATEGORY
    const GROUP_CODE = 'REASON';
    const SUB_GROUP_CODE = 'REASON_SUB_GROUP';
    const CATEGORY_CODE = 'PRODUCTION_CATEGORY';

    const lstGeneralData: any = await this.generalDataRepo.find({
      where: { code: In([GROUP_CODE, SUB_GROUP_CODE, CATEGORY_CODE]), isActive: true },
      relations: ['details'],
    });

    const groupOptions = lstGeneralData.find((item) => item.code === GROUP_CODE)?.__details__;
    const subGroupOptions = lstGeneralData.find(
      (item) => item.code === SUB_GROUP_CODE,
    )?.__details__;
    const cateOptions = lstGeneralData.find((item) => item.code === CATEGORY_CODE)?.__details__;

    return {
      groups: groupOptions?.map((item) => {
        return {
          label: item.code,
          value: item.id,
          name: item.name,
        };
      }),
      subGroups: subGroupOptions?.map((item) => {
        return {
          label: item.code,
          value: item.id,
          name: item.name,
        };
      }),
      categories: cateOptions?.map((item) => {
        return {
          label: item.code,
          value: item.id,
          name: item.name,
        };
      }),
    };
  }

  /**
   * Tạo một lý do mới
   * @param createReasonMasterDto Dữ liệu để tạo lý do mới
   * @returns Thực thể lý do đã được tạo
   */
  async create(createReasonMasterDto: CreateReasonMasterDto): Promise<ReasonMasterResponseDto> {
    // Kiểm tra xem mã đã tồn tại chưa
    const existingReason = await this.repo.findOne({
      where: { code: createReasonMasterDto.code },
    });

    if (existingReason) {
      throw new ConflictException(`Reason with code ${createReasonMasterDto.code} already exists`);
    }

    const [category, group, subGroup] = await Promise.all([
      this.generalDataDetailRepo.findOne({
        where: {
          id: createReasonMasterDto.categoryId,
        },
      }),
      this.generalDataDetailRepo.findOne({
        where: {
          id: createReasonMasterDto.groupId,
        },
      }),
      this.generalDataDetailRepo.findOne({
        where: {
          id: createReasonMasterDto.subGroupId,
        },
      }),
    ]);

    const reasonMaster: ReasonMasterEntity = this.repo.create(createReasonMasterDto);

    if (!category) {
      throw new NotFoundException(`Category with ID ${createReasonMasterDto.categoryId} not found`);
    }

    if (!group) {
      throw new NotFoundException(`Group with ID ${createReasonMasterDto.groupId} not found`);
    }

    // Xử lý mối quan hệ cha nếu có parentId
    if (createReasonMasterDto.parentId) {
      const parent = await this.repo.findOne({
        where: { id: createReasonMasterDto.parentId },
      });

      if (!parent) {
        throw new NotFoundException(
          `Parent reason with ID ${createReasonMasterDto.parentId} not found`,
        );
      }

      reasonMaster.parentId = parent.id;
    }

    // reasonMaster.createdBy = '-1';
    reasonMaster.createdDate = new Date();
    reasonMaster.category = category.name;
    reasonMaster.categoryCode = category.code;
    reasonMaster.categoryId = category.id;
    reasonMaster.group = group.name;
    reasonMaster.groupCode = group.code;
    reasonMaster.groupId = group.id;
    reasonMaster.subGroup = subGroup ? subGroup.name : null;
    reasonMaster.subGroupCode = subGroup ? subGroup.code : null;
    reasonMaster.subGroupId = subGroup ? subGroup.id : null;

    const savedEntity = await this.repo.save(reasonMaster);
    return new ReasonMasterResponseDto(savedEntity);
  }

  async loadSelectParent() {
    const res = await this.repo.find({
      order: { isActive: 'DESC', name: 'DESC' },
      select: ['id', 'name', 'code'],
    });
    return res?.map((item) => {
      return {
        label: item.code,
        value: item.id,
        name: item.name,
      };
    });
  }

  /**
   * Tìm tất cả lý do với bộ lọc tùy chọn
   * @param filterDto Tiêu chí lọc tùy chọn
   * @returns Danh sách lý do có phân trang
   */
  async pagination(params: ReasonMasterPaginationDto) {
    const { pageIndex, pageSize, where } = params;
    const whereCon: any = {};
    whereCon.parentId = null;

    if (where?.name) {
      whereCon.name = Raw((alias) => `LOWER(${alias}) LIKE LOWER(:name)`, {
        name: `%${where.name}%`,
      });
    }
    if (where?.code) {
      whereCon.code = Raw((alias) => `LOWER(${alias}) LIKE LOWER(:code)`, {
        code: `%${where.code}%`,
      });
    }
    if (where?.isActive !== undefined) {
      whereCon.isActive = where.isActive;
    }

    if (where?.category) {
      whereCon.category = where.category;
    }
    if (where?.group) {
      whereCon.groupId = where.group;
    }
    if (where?.subGroup) {
      whereCon.subGroup = where.subGroup;
    }

    if (where?.startDate && where.endDate) {
      const ds = new Date(new Date(where?.startDate).setHours(0, 0, 0, 0));
      const dsStr = moment(ds).format('YYYY-MM-DD HH:mm:ss');
      const de = new Date(where.endDate).setHours(23, 59, 59, 99);
      const deStr = moment(de).format('YYYY-MM-DD HH:mm:ss');
      whereCon.createdDate = Raw((alias) => `(${alias}) BETWEEN :startDate AND :endDate`, {
        startDate: dsStr,
        endDate: deStr,
      });
    }

    // ===> Tìm kiếm bản ghi cấp cha
    let [data, total]: any = await this.repo.findAndCount({
      where: whereCon,
      order: { isActive: 'DESC', createdDate: 'DESC' },
      skip: (pageIndex - 1) * pageSize,
      take: pageSize,
    });

    // ===> Nếu không tìm thấy kết quả cấp cha, tìm kiếm cấp con
    if (!data.length) {
      const childWhere = { ...whereCon };
      delete childWhere.parentId;
      const childResults = await this.repo.find({
        where: {
          ...childWhere,
        },
        relations: ['parent'],
      });
      // Lấy các bản ghi cha của các bản ghi con tìm được
      const parentIds = childResults.map((child) => child.parentId).filter(Boolean);
      // Truy vấn bản ghi cha từ danh sách ID
      if (parentIds.length) {
        [data, total] = await this.repo.findAndCount({
          where: { id: In(parentIds) },
          order: { isActive: 'DESC', createdDate: 'DESC' },
          skip: (pageIndex - 1) * pageSize,
          take: pageSize,
        });
      }
    }

    // Đệ quy để lấy toàn bộ cây con
    for (const item of data) {
      item.children = await this.fetchChildren(item.id, item.code, item.name);
    }

    return {
      data,
      total,
      pageIndex,
      pageSize,
    };
  }

  // Hàm đệ quy lấy tất cả các cấp con
  private async fetchChildren(parentId: string, parentCode: string, parentName): Promise<any[]> {
    const children: any = await this.repo.find({
      where: { parent: { id: parentId } },
    });

    for (const child of children) {
      child.parentCode = parentCode;
      child.parentName = parentName;
      child.children = await this.fetchChildren(child.id, child.code, child.name);
    }

    return children;
  }

  /**
   * Tìm một lý do theo ID
   * @param id ID của lý do cần tìm
   * @returns Thực thể lý do tìm thấy
   */
  async findOne(id: string): Promise<ReasonMasterResponseDto> {
    const reasonMaster = await this.repo.findOne({
      where: { id },
      relations: ['parent', 'children'],
    });

    if (!reasonMaster) {
      throw new NotFoundException(`Reason master with ID ${id} not found`);
    }

    return new ReasonMasterResponseDto(reasonMaster);
  }

  /**
   * Cập nhật lý do hiện có
   * @param id ID của lý do cần cập nhật
   * @param updateReasonMasterDto Dữ liệu cập nhật lý do
   * @returns Thực thể lý do đã cập nhật
   */
  async update(updateReasonMasterDto: UpdateReasonMasterDto): Promise<ReasonMasterResponseDto> {
    const reasonMaster = await this.repo.findOne({
      where: { id: updateReasonMasterDto.id },
      relations: ['parent'],
    });

    if (!reasonMaster) {
      throw new NotFoundException(`Reason master with ID ${updateReasonMasterDto.id} not found`);
    }

    if (updateReasonMasterDto.parentId === updateReasonMasterDto.id) {
      throw new ConflictException('A reason cannot be its own parent');
    }

    if (updateReasonMasterDto.parentId) {
      const parent = await this.repo.findOne({
        where: { id: updateReasonMasterDto.parentId },
      });

      if (!parent) {
        throw new NotFoundException(
          `Parent reason with ID ${updateReasonMasterDto.parentId} not found`,
        );
      }

      if (parent.id === updateReasonMasterDto.id) {
        throw new ConflictException('A reason cannot be it"s own parent');
      }

      reasonMaster.parentId = updateReasonMasterDto.parentId;
    } else {
      delete reasonMaster.parentId;
    }

    const [category, group, subGroup] = await Promise.all([
      this.generalDataDetailRepo.findOne({
        where: {
          id: updateReasonMasterDto.categoryId,
        },
      }),
      this.generalDataDetailRepo.findOne({
        where: {
          id: updateReasonMasterDto.groupId,
        },
      }),
      this.generalDataDetailRepo.findOne({
        where: {
          id: updateReasonMasterDto.subGroupId,
        },
      }),
    ]);

    if (!category) {
      throw new NotFoundException(`Category with ID ${updateReasonMasterDto.categoryId} not found`);
    }

    if (!group) {
      throw new NotFoundException(`Group with ID ${updateReasonMasterDto.groupId} not found`);
    }

    // reasonMaster.updatedBy = '-1';
    reasonMaster.updatedDate = new Date();
    reasonMaster.code = updateReasonMasterDto.code;
    reasonMaster.name = updateReasonMasterDto.name;
    reasonMaster.isActive = updateReasonMasterDto.isActive;
    reasonMaster.description = updateReasonMasterDto.description;
    reasonMaster.note = updateReasonMasterDto.note;
    reasonMaster.code = updateReasonMasterDto.code;
    reasonMaster.category = category.name;
    reasonMaster.categoryCode = category.code;
    reasonMaster.categoryId = category.id;
    reasonMaster.group = group.name;
    reasonMaster.groupCode = group.code;
    reasonMaster.groupId = group.id;
    reasonMaster.subGroup = subGroup ? subGroup.name : null;
    reasonMaster.subGroupCode = subGroup ? subGroup.code : null;
    reasonMaster.subGroupId = subGroup ? subGroup.id : null;
    reasonMaster.parentId = updateReasonMasterDto.parentId;

    delete reasonMaster.parent;

    await this.repo.update(reasonMaster.id, reasonMaster);
    return new ReasonMasterResponseDto(updateReasonMasterDto as any);
  }

  /**
   * Xóa một lý do
   * @param id ID của lý do cần xóa
   * @returns Thực thể lý do đã xóa
   */
  async remove(id: string): Promise<ReasonMasterResponseDto> {
    const reasonMaster = await this.repo.findOne({
      where: { id },
      relations: ['parent'],
    });

    if (!reasonMaster) {
      throw new NotFoundException(`Reason master with ID ${id} not found`);
    }

    // Kiểm tra xem lý do có con không
    const childrenCount = await this.repo.count({
      where: { parent: { id } },
    });

    if (childrenCount > 0) {
      throw new ConflictException(
        `Cannot delete reason with ID ${id} because it has ${childrenCount} child reasons. \nPlease delete or reassign the children first.`,
      );
    }

    const response = new ReasonMasterResponseDto(reasonMaster);
    await this.repo.remove(reasonMaster);
    return response;
  }
}
