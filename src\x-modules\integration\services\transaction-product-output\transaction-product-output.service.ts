import { Injectable } from '@nestjs/common';
import { BindRepo } from '~/@core/decorator';
import { MesDxTransformRepo } from '~/repositories/scada';
import moment from 'moment';
import { JwtService } from '@nestjs/jwt';
import { Connection } from 'typeorm';
import { promises } from 'dns';
import { dateHelper } from '~/common/helpers/date.helper';

@Injectable()

export class TransactionProductOutput {
  constructor(
    private jwtService: JwtService,
    private readonly connection: Connection
  ) {}

  async createTransactionProductOutput(data: { fromDate?: string; toDate?: string }) {
    try {
      let getDateFrom = null
      let getDateTo = null
      if(data.fromDate && data.toDate){
        getDateFrom = dateHelper.formatDateByJobManual(data.fromDate)
        getDateTo = dateHelper.formatDateByJobManual(data.toDate)
      }
      const result = await this.connection.query(`CALL prod_signal_counter_36_5($1, $2, $3)`, [getDateFrom, getDateTo, null]);
    } catch (error) {
      console.log(error, 'error');
    }
  }
}
