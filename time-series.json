[{"table_schema": "public", "table_name": "mes_dx_measurements", "table_type": "BASE TABLE", "columns": [{"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "NO", "column_default": "now()"}, {"column_name": "gatewayName", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "gatewayId", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "datasourceName", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "datasourceId", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "modelName", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "modelId", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "assetName", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "assetId", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "measurementName", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "measurementId", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "value", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "datetime", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}]}, {"table_schema": "public", "table_name": "mes_dx_metrics", "table_type": "BASE TABLE", "columns": [{"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "NO", "column_default": "now()"}, {"column_name": "gatewayName", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "gatewayId", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "datasourceName", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "datasourceId", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "modelName", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "modelId", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "assetName", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "assetId", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "metricName", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "metricId", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "value", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "datetime", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}]}, {"table_schema": "public", "table_name": "mes_dx_transforms", "table_type": "BASE TABLE", "columns": [{"column_name": "id", "data_type": "uuid", "is_nullable": "NO", "column_default": "uuid_generate_v4()"}, {"column_name": "createdDate", "data_type": "timestamp with time zone", "is_nullable": "NO", "column_default": "now()"}, {"column_name": "gatewayName", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "gatewayId", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "datasourceName", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "datasourceId", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "modelName", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "modelId", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "assetName", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "assetId", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "transformName", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "transformId", "data_type": "character varying", "is_nullable": "NO", "column_default": null}, {"column_name": "value", "data_type": "character varying", "is_nullable": "YES", "column_default": null}, {"column_name": "datetime", "data_type": "timestamp with time zone", "is_nullable": "YES", "column_default": null}]}]