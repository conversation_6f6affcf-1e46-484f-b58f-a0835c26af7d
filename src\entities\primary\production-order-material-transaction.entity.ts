import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
} from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { IsUUID } from 'class-validator';
import { PrimaryBaseEntity } from '../primary-base.entity';
@Entity('production_order_material_transaction')
export class ProductionOrderMaterialTransactionEntity extends PrimaryBaseEntity {
  @ApiProperty()
  @PrimaryGeneratedColumn('uuid')
  @IsUUID(4)
  materialTransactionId?: string;

  // @Index()
  // @ApiProperty()
  // @CreateDateColumn()
  // creationDate?: Date;

  @ApiProperty({ description: 'orderId của production_batch_status', example: '1234' })
  @Column({ type: 'uuid' })
  orderId: string;

  @ApiProperty({ description: '<PERSON><PERSON>y mà mẻ sản xuất chạy qua', example: '6789' })
  @Column({ type: 'uuid', nullable: true })
  batchId?: string;

  @ApiProperty({ description: 'Batch id của mẻ sản xuất', example: '1234' })
  @Column({ type: 'uuid', nullable: true })
  machineId: string;

  @ApiProperty({ description: 'metricId của production_batch_status', example: '1234' })
  @Column({ type: 'uuid', nullable: true })
  metricId: string;

  @ApiProperty({
    description: 'production_order_material_id từ bảng production_order_material_id',
    example: '1234',
  })
  @Column({ type: 'uuid', nullable: true })
  productionOrderMaterialId: string;

  @ApiProperty({ description: 'material_id từ bảng production_order_material_id', example: '1234' })
  @Column({ type: 'uuid' })
  materialId: string;

  @ApiProperty({
    description: 'datetime của dòng dữ liệu tìm thấy trong material_consumption_scada',
    example: '2024-03-06T14:30:00Z',
  })
  @ApiProperty({
    description: 'datetime của dòng dữ liệu tìm thấy trong material_consumption_scada',
    example: '2024-03-06',
  })
  @Column({ type: 'timestamptz', nullable: true })
  transactionDate?: Date | null;

  @ApiProperty({ description: 'WIP Issue', example: '1234' })
  @Column()
  transactionType: string;

  @ApiProperty({
    description: 'lot_number của dòng dữ liệu tìm thấy trong material_consumption_scada',
    example: '1234',
  })
  @Column()
  lotNumber: string;

  @ApiProperty({
    description: 'qty của dòng dữ liệu tìm thấy trong material_consumption_scada',
    example: 1,
  })
  @Column({ nullable: true, type: 'numeric' })
  transactionQty?: number;

  @ApiProperty({
    description: 'uom của dòng dữ liệu tìm thấy trong material_consumption_scada',
    example: 'uom',
  })
  @Column()
  transactionUom: string;

  // @ApiProperty({ description: '', example: '-1' })
  // @Column()
  // createdBy?: string | null;

  // @ApiProperty({
  //   description: 'Thời gian cập nhật cuối của dòng dữ liệu',
  //   example: '2024-03-06T14:30:00Z',
  // })
  // @UpdateDateColumn({ type: 'timestamptz' })
  // lastUpdate: Date | null;

  @ApiProperty({ description: '', example: '-1' })
  @Column({ nullable: true })
  lastUpdateBy: number;

  @ApiProperty({ description: '', example: '1' })
  @Column({ nullable: true })
  oeeCal: number | null;

  @ApiProperty({ description: '', example: '1' })
  @Column({ type: 'uuid', nullable: true })
  scadaTransactionId: number | null;

  @ApiProperty({ description: '' })
  @Column({ type: 'timestamptz', nullable: true })
  fromtransactionDate: Date;

  @ApiProperty({ description: '' })
  @Column({ type: 'timestamptz', nullable: true })
  totransactionDate: Date;

  @ApiProperty({ description: '' })
  @Column({ type: 'timestamptz', nullable: true })
  fromUpdatedDate: Date;

  @ApiProperty({ description: '' })
  @Column({ type: 'timestamptz', nullable: true })
  toUpdatedDate: Date;
}
