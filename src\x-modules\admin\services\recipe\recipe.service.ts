import { Injectable } from '@nestjs/common';
import { FindManyOptions } from 'typeorm';
import { BindRepo } from '~/@core/decorator';
import { NSRecipe } from '~/common/enums';
import { ListRecipeReq } from '~/dto/recipe.dto';
import { RecipeEntity } from '~/entities/primary';
import { ItemRepo, OrganizationUnitRepo, RecipeRepo } from '~/repositories/primary';
import { Between, Like, ILike, In } from 'typeorm';
import dayjs from 'dayjs';
@Injectable()
export class RecipeService {
  @BindRepo(RecipeRepo)
  private readonly recipeRepo: RecipeRepo;

  @BindRepo(OrganizationUnitRepo)
  private readonly OrganizationUnitRepo: OrganizationUnitRepo;

  @BindRepo(ItemRepo)
  private readonly ItemRepo: ItemRepo;

  constructor() {}

  async findAll(query: ListRecipeReq): Promise<{ data: RecipeEntity[]; total: number }> {
    return await this.recipeRepo.findPagination(
      {
        relations: typeof query.relations === 'string' ? [query.relations] : query.relations,
      },
      query,
    );
  }

  async pagination(query: ListRecipeReq): Promise<{ data: RecipeEntity[]; total: number }> {
    const qb = this.recipeRepo
      .createQueryBuilder('recipe')
      .leftJoinAndSelect('recipe.product', 'product')
      .leftJoinAndSelect('recipe.organizationUnit', 'organizationUnit');

    if (query?.productCode) {
      qb.andWhere('product.code ILIKE :productCode', {
        productCode: `%${query.productCode.trim()}%`,
      });
    }

    if (query?.productName) {
      qb.andWhere('product.name ILIKE :productName', {
        productName: `%${query.productName.trim()}%`,
      });
    }

    if (query?.processArea || query?.organizationUnit) {
      const orgUnitQb = this.OrganizationUnitRepo.createQueryBuilder('ou');
      if (query.processArea) {
        orgUnitQb.andWhere('ou.code ILIKE :processArea', {
          processArea: `%${query.processArea.trim()}%`,
        });
      }
      if (query.organizationUnit) {
        orgUnitQb.andWhere('ou.name ILIKE :orgName', {
          orgName: `%${query.organizationUnit.trim()}%`,
        });
      }

      const orgs = await orgUnitQb.getMany();
      const orgIds = orgs.map((o) => o.id);
      if (orgIds.length > 0) {
        qb.andWhere('recipe.processAreaId IN (:...orgIds)', { orgIds });
      } else {
        return { data: [], total: 0 };
      }
    }

    if (query?.organizationCode) {
      qb.andWhere('recipe.organizationCode ILIKE :organizationCode', {
        organizationCode: `%${query.organizationCode.trim()}%`,
      });
    }

    if (query?.recipeNo) {
      qb.andWhere('recipe.recipeNo ILIKE :recipeNo', {
        recipeNo: `%${query.recipeNo.trim()}%`,
      });
    }

    if (query?.recipeVer) {
      qb.andWhere('recipe.recipeVer = :recipeVer', { recipeVer: Number(query.recipeVer) });
    }

    if (query?.recipeDescription) {
      qb.andWhere('recipe.recipeDescription ILIKE :recipeDescription', {
        recipeDescription: `%${query.recipeDescription.trim()}%`,
      });
    }

    if (query?.formulaNo) {
      qb.andWhere('recipe.formulaNo ILIKE :formulaNo', {
        formulaNo: `%${query.formulaNo.trim()}%`,
      });
    }

    if (query?.formulaVer) {
      qb.andWhere('recipe.formulaVer = :formulaVer', {
        formulaVer: Number(query.formulaVer),
      });
    }

    if (query?.routingNo) {
      qb.andWhere('recipe.routingNo ILIKE :routingNo', {
        routingNo: `%${query.routingNo.trim()}%`,
      });
    }

    if (query?.routingVer) {
      qb.andWhere('recipe.routingVer = :routingVer', {
        routingVer: Number(query.routingVer),
      });
    }

    if (query?.startDate && query?.endDate) {
      qb.andWhere('recipe.updatedDate BETWEEN :startDate AND :endDate', {
        startDate: dayjs(query.startDate).startOf('day').toDate(),
        endDate: dayjs(query.endDate).endOf('day').toDate(),
      });
    }

    if (query?.recipeStatus) {
      qb.andWhere('recipe.recipeStatus = :recipeStatus', {
        recipeStatus: query.recipeStatus,
      });
    }

    if (query?.startSyncDate || query?.endSyncDate) {
      const startSyncDate = query.startSyncDate
        ? dayjs(query.startSyncDate).startOf('day').toDate()
        : dayjs('1900-01-01').toDate();

      const endSyncDate = query.endSyncDate
        ? dayjs(query.endSyncDate).endOf('day').toDate()
        : dayjs().endOf('day').toDate();

      qb.andWhere('recipe.dateSyncScada BETWEEN :startSyncDate AND :endSyncDate', {
        startSyncDate,
        endSyncDate,
      });
    }

    if (query?.isSyncScada && query.isSyncScada != undefined) {
      qb.andWhere('recipe.isSyncScada = :isSyncScada', {
        isSyncScada: query.isSyncScada == 'true' ? true : false,
      });
    }

    // Pagination
    const { pageIndex = 1, pageSize = 20 } = query;
    qb.skip((pageIndex - 1) * pageSize).take(pageSize);
    qb.orderBy('recipe.createdDate', 'DESC');

    const [data, total] = await qb.getManyAndCount();
    return { data, total };
  }

  async find(data: { itemId: string; processAreaId: string }) {
    const whereCon: any = {
      recipeStatus: NSRecipe.RecipeStatus.ApprovedForGeneralUse,
    };
    if (data.itemId) whereCon.productId = data.itemId;
    if (data.processAreaId) whereCon.processAreaId = data.processAreaId;
    return await this.recipeRepo.find({
      where: whereCon,
      select: [
        'id',
        'organizationCode',
        'organizationUnit',
        'version',
        'recipeNo',
        'recipeVer',
        'recipeDescription',
        'productId',
        'batchUom',
      ],
    });
  }

  async loadDataSelect(itemId: string) {
    const lstRecipe = await this.recipeRepo.find({
      where: {
        productId: itemId ? itemId : null,
        recipeStatus: NSRecipe.RecipeStatus.ApprovedForGeneralUse,
      },
      select: [
        'id',
        'organizationCode',
        'organizationUnit',
        'version',
        'recipeNo',
        'recipeVer',
        'recipeDescription',
        'productId',
        'batchUom',
      ],
    });

    const mapRcp = new Map();
    for (let rcp of lstRecipe) {
      let curItem = mapRcp.get(rcp.recipeNo) || { ...rcp, details: [] };
      curItem.details.push(rcp);
      mapRcp.set(rcp.recipeNo, curItem);
    }
    const lstData = [];
    mapRcp.forEach((val) => {
      lstData.push(val);
    });
    return lstData;
  }

  async findById(id: string): Promise<RecipeEntity> {
    return await this.recipeRepo
      .createQueryBuilder('recipe')
      .leftJoinAndSelect('recipe.product', 'product')
      .leftJoinAndSelect('recipe.organizationUnit', 'organizationUnit')
      .leftJoinAndSelect('recipe.recipeProcesses', 'recipeProcesses')
      .leftJoinAndSelect('recipeProcesses.process', 'process')
      .leftJoinAndSelect('recipeProcesses.recipeResources', 'recipeResources')
      .leftJoinAndSelect('recipeProcesses.recipeProcessItems', 'recipeProcessItems')
      .leftJoinAndSelect('recipeProcessItems.item', 'item')
      .where('recipe.id = :id', { id })
      .orderBy('recipeProcesses.operationCode', 'ASC')
      .addOrderBy('recipeResources.resource', 'ASC')
      .addOrderBy('item.code', 'ASC')
      .getOne();
  }

  async create(data: RecipeEntity): Promise<RecipeEntity> {
    return await this.recipeRepo.save(data);
  }

  async update(id: string, data: RecipeEntity): Promise<RecipeEntity> {
    const updatedRecipe = { id, ...data };
    return await this.recipeRepo.save(updatedRecipe);
  }

  async remove(id: string): Promise<void> {
    await this.recipeRepo.delete(id);
  }
}
