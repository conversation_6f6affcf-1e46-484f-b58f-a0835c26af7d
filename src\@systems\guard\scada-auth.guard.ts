import { CanActivate, ExecutionContext, Injectable, UnauthorizedException } from '@nestjs/common';
/**
 * Guard dùng để kiểm tra xem request có đúng secret key hay không
 * Dù<PERSON> cho các request từ SCADA
 */
@Injectable()
export class ScadaAuthGuard implements CanActivate {
  async canActivate(context: ExecutionContext): Promise<boolean> {
    const request = context.switchToHttp().getRequest();
    const key_secret = request.headers['x-header'];

    if (!key_secret)
      throw new UnauthorizedException('Không có quyền truy cập! (code: KEY_SECRET_ERROR_1)');

    if (!process.env.API_SCADA_SECRET)
      throw new UnauthorizedException('Không có quyền truy cập! (code: KEY_SECRET_ERROR_2)');
    if (key_secret !== process.env.API_SCADA_SECRET)
      throw new UnauthorizedException('<PERSON>hông có quyền truy cập! (code: KEY_SECRET_ERROR_3)');

    return true;
  }
}
