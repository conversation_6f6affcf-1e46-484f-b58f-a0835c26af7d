import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Column, Entity, Index, JoinColumn, ManyToOne, OneToMany } from 'typeorm';
import { ProcessEntity } from '.';
import { PrimaryBaseEntity } from '../primary-base.entity';
import { AssignShiftEntity } from './assign-shift.entity';
import { GeneralDataDetailEntity } from './general-data-detail.entity';
import { ProductionAreaDetailEntity } from './production-area-detail.entity';
import { ProductionAreaEntity } from './production-area.entity';
import { RecipeEntity } from './recipe.entity';
import { SetPointEntity } from './set-point.entity';
import { UtilityMetersEntity } from './utility-meters.entity';
import { UtilityMeterDetailEntity } from './utility-meter-detail.entity';

/**
 * Đơn vị tổ chức trong hệ thống
 */
@Entity('organization_units')
export class OrganizationUnitEntity extends PrimaryBaseEntity {
  /** Mã định danh duy nhất của đơn vị tổ chức */
  @ApiProperty({ description: 'Organization unit code', example: 'ORG-001' })
  @Column({ type: 'varchar', length: 50 })
  @Index()
  code: string;

  /** Tên hiển thị của đơn vị tổ chức */
  @ApiProperty({ description: 'Organization unit name', example: 'Marketing Department' })
  @Column({ type: 'varchar', length: 255 })
  name: string;

  /** Mô tả chi tiết về mục đích và trách nhiệm của đơn vị */
  @ApiPropertyOptional({
    description: 'Organization unit description',
    example: 'Marketing department for product promotion',
  })
  @Column({ type: 'varchar', length: 500, nullable: true })
  description?: string;

  /** ID của level trong GeneralDataDetail */
  @ApiPropertyOptional({ description: 'Organization unit level ID' })
  @Column({ type: 'uuid', nullable: true })
  levelId?: string;

  /** Level general data parent code */
  @ApiPropertyOptional({ description: 'Level general data parent code' })
  @Column({ type: 'varchar', length: 50, nullable: true })
  levelGeneralDataParentCode?: string;

  /** Level general data detail code */
  @ApiPropertyOptional({ description: 'Level general data detail code' })
  @Column({ type: 'varchar', length: 50, nullable: true })
  levelGeneralDataDetailCode?: string;

  /** Liên kết với level trong GeneralDataDetail */
  @ManyToOne(() => GeneralDataDetailEntity)
  @JoinColumn({ name: 'levelId' })
  level?: GeneralDataDetailEntity;

  /** ID của category trong GeneralDataDetail */
  @ApiPropertyOptional({ description: 'Organization unit category ID' })
  @Column({ type: 'uuid', nullable: true })
  categoryId?: string;

  /** Category general data parent code */
  @ApiPropertyOptional({ description: 'Category general data parent code' })
  @Column({ type: 'varchar', length: 50, nullable: true })
  categoryGeneralDataParentCode?: string;

  /** Category general data detail code */
  @ApiPropertyOptional({ description: 'Category general data detail code' })
  @Column({ type: 'varchar', length: 50, nullable: true })
  categoryGeneralDataDetailCode?: string;

  /** Liên kết với category trong GeneralDataDetail */
  @ManyToOne(() => GeneralDataDetailEntity)
  @JoinColumn({ name: 'categoryId' })
  category?: GeneralDataDetailEntity;

  /** Sức chứa tối đa hoặc quy mô của đơn vị */
  @ApiPropertyOptional({ description: 'Organization unit capacity', example: 100.5 })
  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  capacity?: number;

  /** Hệ số phân bổ tài nguyên */
  @ApiPropertyOptional({ description: 'Weighting distribution', example: 0.75 })
  @Column({ type: 'decimal', precision: 5, scale: 2, nullable: true })
  weightingDistribution?: number;

  /** Vị trí lưu trữ nguyên liệu */
  @ApiPropertyOptional({ description: 'Ingredient locator', example: 'SECTION-A' })
  @Column({ type: 'varchar', length: 50, nullable: true })
  ingredientLocator?: string;

  /** Vị trí lưu trữ sản phẩm */
  @ApiPropertyOptional({ description: 'Products locator', example: 'SECTION-B' })
  @Column({ type: 'varchar', length: 50, nullable: true })
  productsLocator?: string;

  /** Vị trí lưu trữ (Storage Location) */
  @ApiPropertyOptional({ description: 'Storage Location', example: 'Warehouse A, Shelf B3' })
  @Column({ type: 'text', nullable: true })
  storageLocation?: string;

  /** ID của đơn vị tổ chức cha */
  @ApiPropertyOptional({ description: 'Parent organization unit ID', example: 'uuid' })
  @Column({ type: 'uuid', nullable: true })
  parentId?: string;

  /** IP Scada */
  @ApiPropertyOptional({ description: 'IP Scada' })
  @Column({ nullable: true })
  ipScada?: string;

  /** Trạng thái hoạt động của đơn vị */
  @ApiPropertyOptional({
    description: 'Is the organization unit active',
    example: true,
    default: true,
  })
  @Column({ type: 'boolean', default: true })
  isActive: boolean;

  /** Ghi chú bổ sung về đơn vị */
  @ApiPropertyOptional({ description: 'Additional notes', example: 'Special handling required' })
  @Column({ type: 'text', nullable: true })
  note?: string;

  /** Material Handle Type */
  @ApiPropertyOptional({ description: 'Material Handle Type' })
  @Column({ type: 'text', nullable: true })
  materialHandleType?: string;

  /** Thứ tự sắp xếp */
  @ApiPropertyOptional({ description: 'Order number for sorting', example: 1 })
  @Column({ type: 'int', nullable: true, default: 0 })
  orderNo: number;

  /** Liên kết với đơn vị tổ chức cha */
  @ManyToOne(() => OrganizationUnitEntity, (orgUnit) => orgUnit.children)
  @JoinColumn({ name: 'parentId' })
  parent?: OrganizationUnitEntity;

  /** Liên kết với các đơn vị tổ chức con */
  @OneToMany(() => OrganizationUnitEntity, (orgUnit) => orgUnit.parent)
  children?: OrganizationUnitEntity[];

  /** Liên kết với ca làm */
  @OneToMany(() => AssignShiftEntity, (assignShift) => assignShift.organization)
  assignShifts?: AssignShiftEntity[];

  /** Liên kết với công thức */
  @OneToMany(() => RecipeEntity, (recipe) => recipe.organizationUnit)
  recipes?: RecipeEntity[];

  /** Liên kết với khu vực sản xuất */
  @OneToMany(() => ProductionAreaEntity, (productionArea) => productionArea.organization)
  productionAreas?: ProductionAreaEntity[];

  /** Liên kết với chi tiết khu vực sản xuất */
  @OneToMany(
    () => ProductionAreaDetailEntity,
    (productionAreaDetail) => productionAreaDetail.organization,
  )
  productionAreaDetails?: ProductionAreaDetailEntity[];

  @OneToMany(() => ProcessEntity, (process) => process.organization)
  processes: ProcessEntity[];

  @OneToMany(() => SetPointEntity, (setPoint) => setPoint.organizeUnit)
  setPoints: SetPointEntity[];

  /** Relation với bảng UtilityMeters */
  @OneToMany(() => UtilityMetersEntity, (meter) => meter.factory)
  utilityMeters: UtilityMetersEntity[];

  /** Relation với bảng UtilityMeterDetail */
  @OneToMany(() => UtilityMeterDetailEntity, (detail) => detail.organization)
  utilityMeterDetails: UtilityMeterDetailEntity[];
}
