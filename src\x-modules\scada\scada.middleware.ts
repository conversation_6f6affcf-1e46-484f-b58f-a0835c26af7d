import {
  BadRequestException,
  Injectable,
  NestMiddleware,
  UnauthorizedException,
} from '@nestjs/common';
import { JwtService } from '@nestjs/jwt';
import { Request, Response } from 'express';
import { SystemValue } from '~/common/constants/SystemValue';

@Injectable()
export class ScadaMiddleware implements NestMiddleware {
  constructor(private jwtService: JwtService) {}
  async use(req: Request, res: Response, next: Function) {
    try {
      const checkObject = (obj: any): boolean => {
        if (!obj || typeof obj !== 'object') return false;

        for (const value of Object.values(obj)) {
          if (typeof value === 'string' && SystemValue.FORBIDDEN_PATTERN.test(value)) {
            return true;
          } else if (typeof value === 'object') {
            if (checkObject(value)) return true; // đệ quy nếu nested
          }
        }

        return false;
      };

      if (checkObject(req.query) || checkObject(req.body)) {
        throw new BadRequestException('SQL Injection detected!');
      }
      next();
    } catch (error) {
      next(new UnauthorizedException('Unauthorized'));
    }
  }
}
