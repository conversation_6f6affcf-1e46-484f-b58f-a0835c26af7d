import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { Between, EntityManager, ILike, In } from 'typeorm';
import * as ExcelJS from 'exceljs';
import * as path from 'path';
import * as os from 'os';
import * as fs from 'fs';
import { BindRepo } from '~/@core/decorator';
import { PageResponse } from '~/@systems/utils/page.utils';
import { NSGeneralData } from '~/common/enums';
import { dayjs, TZ } from '~/common/helpers/date.helper';
import { adminSessionContext } from '~/x-modules/admin/admin-session.context';
import { S3PrivateService } from '~/x-modules/@global/services';
import { MachineParameterEntity, ProcessEntity } from '~/entities/primary';
import {
  GeneralDataDetailRepo,
  GeneralDataRepo,
  MachineParameterRepo,
  MachineRepo,
  MesDxProdBatchParamatersRepo,
  MesDxProdBatchStatusRepo,
  OrganizationUnitRepo,
  ProcessMachineRepo,
  ProcessRepo,
  ProductionBatchRepo,
  ProductionOrderRepo,
  ReportHistoryRepo,
  ShiftRepo,
  UserRepo,
} from '~/repositories/primary';
import { CreateReportDto, ListFilterReportDto } from '~/dto/report-history.dto';

const REPORT = { reportCode: '3', reportName: 'Production Batch and Process Parameter' };

@Injectable()
export class ProductionBatchAndProcessParameterReportService {
  constructor(
    private readonly entityManager: EntityManager,
    private readonly s3PrivateService: S3PrivateService,
  ) {}

  @BindRepo(ReportHistoryRepo)
  private reportHistoryRepo: ReportHistoryRepo;
  @BindRepo(ProductionOrderRepo)
  private productionOrderRepo: ProductionOrderRepo;
  @BindRepo(ProductionBatchRepo)
  private productionBatchRepo: ProductionBatchRepo;
  @BindRepo(MesDxProdBatchParamatersRepo)
  private productionBatchParamRepo: MesDxProdBatchParamatersRepo;
  @BindRepo(MesDxProdBatchStatusRepo)
  private productionBatchStatusRepo: MesDxProdBatchStatusRepo;
  @BindRepo(ShiftRepo)
  private shiftRepo: ShiftRepo;
  @BindRepo(OrganizationUnitRepo)
  private organizationUnitRepo: OrganizationUnitRepo;
  @BindRepo(GeneralDataRepo)
  private generalDataRepo: GeneralDataRepo;
  @BindRepo(ProcessRepo)
  private processRepo: ProcessRepo;
  @BindRepo(MachineRepo)
  private machineRepo: MachineRepo;
  @BindRepo(ProcessMachineRepo)
  private processMachineRepo: ProcessMachineRepo;
  @BindRepo(MachineParameterRepo)
  private machineParamRepo: MachineParameterRepo;
  @BindRepo(GeneralDataDetailRepo)
  private generalDataDetailRepo: GeneralDataDetailRepo;
  @BindRepo(UserRepo)
  private userRepo: UserRepo;

  async search(req: ListFilterReportDto): Promise<PageResponse> {
    const { createdByUser, email, fromDate, toDate, pageIndex, pageSize } = req;
    // Parse date dạng ISO -> Date object để truy vấn chuẩn
    const start = fromDate ? dayjs(fromDate).toDate() : undefined;
    const end = toDate ? dayjs(toDate).toDate() : undefined;

    const [data, total] = await this.reportHistoryRepo.findAndCount({
      where: {
        reportCode: REPORT.reportCode,
        ...(createdByUser && { createdByUser: ILike(`%${createdByUser}%`) }),
        ...(email && { email: ILike(`%${email}%`) }),
        ...(start && end && { createdDate: Between(start, end) }),
      },
      order: { createdDate: 'DESC' },
      skip: pageIndex && pageSize ? (pageIndex - 1) * pageSize : undefined,
      take: pageIndex && pageSize ? pageSize : undefined,
    });

    const signedUrls = await Promise.all(
      data.map(async (item) => {
        const signedUrl = await this.s3PrivateService.getS3Link(item.linkDownload ?? ''); // 1h
        return signedUrl;
      }),
    );
    data.forEach((item, index) => {
      item.linkDownload = signedUrls[index];
    });

    return { data: data, meta: { totalRecords: total, request: req } };
  }

  async submitReport(req: CreateReportDto) {
    const beginAt = dayjs.utc(Date.now()).tz(TZ).format('YYYYMMDDHHmmss');
    const { userId, userName, userEmail } = adminSessionContext;

    const report = this.reportHistoryRepo.create({
      reportCode: REPORT.reportCode,
      reportName: REPORT.reportName,
      status: NSGeneralData.EReportStatus.RUNNING,
      input: req,
      createdBy: userId,
      createdByUser: userName,
      email: userEmail,
    });
    const reportSaved = await this.reportHistoryRepo.save(report);

    try {
      let result: any[] = [];

      const data = await this.buildReportData2(req);

      // Xuất dữ liệu ra file excel (giả sử return đường dẫn file tạm local)
      const localFilePath = await this.exportExcel(data);

      // Upload file lên S3
      const fileName = `report_${reportSaved.reportCode}_${beginAt}`;

      const { fileUrl } = await this.s3PrivateService.uploadS3(
        {
          // Giả sử exportExcel trả về Buffer hoặc Stream,
          // Nếu exportExcel trả về path, bạn có thể đọc file thành Buffer ở đây
          buffer: await fs.promises.readFile(localFilePath),
          originalname: fileName + '.xlsx',
        } as Express.Multer.File,
        fileName,
        'private',
      );

      const reportCompleted = await this.reportHistoryRepo.save({
        ...reportSaved,
        linkDownload: fileUrl,
        output: result,
        outputTotal: result.length,
        status: NSGeneralData.EReportStatus.COMPLETED,
        completedDate: new Date(),
      });

      return { data: reportCompleted, meta: { request: req } };
    } catch (err) {
      Logger.error(err);
      await this.reportHistoryRepo.save({
        ...reportSaved,
        status: NSGeneralData.EReportStatus.ERROR,
        output: {
          errorMessage: err?.message ?? 'Unknown Error',
        },
        completedDate: new Date(),
      });
      throw new BadRequestException(`Submit report failed: ${err.message}`);
    }
  }

  private async buildReportData(
    req: CreateReportDto,
  ): Promise<ProductionBatchAndProcessParameterReportOutput[]> {
    const outputs: ProductionBatchAndProcessParameterReportOutput[] = [];

    const start = req.fromDate ? dayjs(req.fromDate).toDate() : undefined;
    const end = req.toDate ? dayjs(req.toDate).toDate() : undefined;

    const productionOrders = await this.productionOrderRepo.find({
      where: {
        ...(req.siteIdLst?.length && { siteId: In(req.siteIdLst) }),
        ...(req.factoryIdLst?.length && { factoryId: In(req.factoryIdLst) }),
        ...(req.lineIdLst?.length && { lineId: In(req.lineIdLst) }),
        ...(req.orderNo && { orderNo: ILike(`%${req.orderNo}%`) }),
        ...(req.statusIdLst?.length && { orderStatus: In(req.statusIdLst) }),
        ...(start && end && { planStartDate: Between(start, end) }),
      },
    });

    for (const order of productionOrders) {
      const [site, factory, line, processArea, shift, status, operator] = await Promise.all([
        this.organizationUnitRepo.findOne({ where: { id: order.siteId } }),
        this.organizationUnitRepo.findOne({ where: { id: order.factoryId } }),
        this.organizationUnitRepo.findOne({ where: { id: order.lineId } }),
        this.organizationUnitRepo.findOne({ where: { id: order.processAreaId } }),
        this.shiftRepo.findOne({ where: { id: order.shiftId } }),
        this.generalDataDetailRepo.findOne({ where: { id: order.orderStatus } }),
        this.userRepo.findOne({ where: { id: order.operatorUserId } }),
      ]);

      const batches = await this.productionBatchRepo.find({
        where: {
          orderId: order.id,
          ...(req.batchNumber && { batchNumber: req.batchNumber }),
        },
      });

      if (!batches.length) continue;

      const batchIds = batches.map((b) => b.id);
      const [batchStatuses, batchParams] = await Promise.all([
        this.productionBatchStatusRepo.find({ where: { batchId: In(batchIds) } }),
        this.productionBatchParamRepo.find({ where: { batchId: In(batchIds) } }),
      ]);

      const machineIds = [...new Set(batchParams.map((bp) => bp.machineId))];
      const [machineParams, machines, processMachines] = await Promise.all([
        this.machineParamRepo.find({ where: { machineId: In(machineIds) } }),
        this.machineRepo.find({ where: { id: In(machineIds) } }),
        this.processMachineRepo.find({ where: { machineId: In(machineIds) } }),
      ]);

      const processIds = [...new Set(processMachines.map((pm) => pm.processId))];
      const processes = await this.processRepo.find({ where: { id: In(processIds) } });

      const batchStatusMap = new Map(batchStatuses.map((bs) => [bs.batchId, bs]));
      const machineMap = new Map(machines.map((m) => [m.id, m]));

      const machineParamMap = new Map<string, MachineParameterEntity[]>();
      for (const mp of machineParams) {
        if (!machineParamMap.has(mp.machineId)) machineParamMap.set(mp.machineId, []);
        machineParamMap.get(mp.machineId)!.push(mp);
      }

      const processMap = new Map(processes.map((p) => [p.id, p]));
      const processByMachineId = new Map<string, ProcessEntity[]>();
      for (const pm of processMachines) {
        const process = processMap.get(pm.processId);
        if (!process) continue;
        if (!processByMachineId.has(pm.machineId)) processByMachineId.set(pm.machineId, []);
        processByMachineId.get(pm.machineId)!.push(process);
      }

      const uniqueStatusCodes = [...new Set(batchStatuses.map((bs) => bs.status))];
      const generalStatusList = await this.generalDataDetailRepo.find({
        where: { code: In(uniqueStatusCodes) },
      });
      const statusCodeMap = new Map(generalStatusList.map((s) => [s.code, s.name]));

      for (const batch of batches) {
        const currentStatus = batchStatusMap.get(batch.id);
        const genStatusName = currentStatus ? statusCodeMap.get(currentStatus.status) : undefined;
        const currentBatchParams = batchParams.filter((bp) => bp.batchId === batch.id);

        for (const bp of currentBatchParams) {
          const relatedMachineParams = machineParamMap.get(bp.machineId) ?? [];
          const machine = machineMap.get(bp.machineId);
          const relatedProcesses = processByMachineId.get(bp.machineId) ?? [];

          for (const mp of relatedMachineParams) {
            const baseOutput = {
              site: `${site?.code} - ${site?.name}`,
              factory: `${factory?.code} - ${factory?.name}`,
              line: `${line?.code} - ${line?.name}`,
              processArea: `${processArea?.code} - ${processArea?.name}`,
              productionOrderNo: order.orderNo,
              recipeCode: order.recipeCode,
              recipeVer: order.recipeVersion,
              orderQty: order.quantity,
              orderUom: order.trxUom,
              productionDate: dayjs(order.planStartDate).tz(TZ).toDate(),
              shift: `${shift?.code} - ${shift?.description}`,
              orderStatus: `${status?.code} - ${status?.name}`,
              createdBy: order.createdBy,
              creationDate: order.createdDate,
              operator: `${operator?.employeeCode} - ${operator?.fullName}`,
              machineCode: machine?.code,
              machineName: machine?.name,
              batchNumber: batch.batchNumber,
              batchStatus: genStatusName,
              startTime: currentStatus?.startTime,
              endTime: currentStatus?.endTime,
              processParameterCode: mp.code,
              processParameterName: mp.desc,
              value: bp.value,
              dateTime: bp.dateTime,
            };

            if (relatedProcesses.length) {
              for (const process of relatedProcesses) {
                outputs.push({
                  ...baseOutput,
                  processCode: process.code ?? '',
                  processName: process.name ?? '',
                });
              }
            } else {
              outputs.push(baseOutput);
            }
          }
        }
      }
    }

    return outputs;
  }

  private async exportExcel(
    data: ProductionBatchAndProcessParameterReportOutput[],
  ): Promise<string> {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Report');

    // Định nghĩa header
    worksheet.columns = [
      { header: 'No', key: 'index', width: 5 },
      { header: 'Site', key: 'site', width: 20 },
      { header: 'Factory', key: 'factory', width: 20 },
      { header: 'Line', key: 'line', width: 20 },
      { header: 'Process Area', key: 'processArea', width: 20 },
      { header: 'Production Order No', key: 'productionOrderNo', width: 25 },
      { header: 'Recipe Code', key: 'recipeCode', width: 20 },
      { header: 'Recipe Version', key: 'recipeVer', width: 15 },
      { header: 'Order Quantity', key: 'orderQty', width: 15 },
      { header: 'Order UOM', key: 'orderUom', width: 10 },
      { header: 'Production Date', key: 'productionDate', width: 20 },
      { header: 'Shift', key: 'shift', width: 15 },
      { header: 'Order Status', key: 'orderStatus', width: 20 },
      { header: 'Created By', key: 'createdBy', width: 20 },
      { header: 'Creation Date', key: 'creationDate', width: 20 },
      { header: 'Operator', key: 'operator', width: 25 },
      { header: 'Machine Code', key: 'machineCode', width: 15 },
      { header: 'Machine Name', key: 'machineName', width: 25 },
      { header: 'Batch Number', key: 'batchNumber', width: 20 },
      { header: 'Batch Status', key: 'batchStatus', width: 15 },
      { header: 'Start Time', key: 'startTime', width: 20 },
      { header: 'End Time', key: 'endTime', width: 20 },
      { header: 'Process Code', key: 'processCode', width: 20 },
      { header: 'Process Name', key: 'processName', width: 25 },
      { header: 'Parameter Code', key: 'processParameterCode', width: 20 },
      { header: 'Parameter Name', key: 'processParameterName', width: 25 },
      { header: 'Value', key: 'value', width: 15 },
      { header: 'Datetime', key: 'dateTime', width: 20 },
    ];

    // Thêm dữ liệu
    data.forEach((item, index) => {
      worksheet.addRow({
        index: index + 1,
        site: item.site,
        factory: item.factory,
        line: item.line,
        processArea: item.processArea,
        productionOrderNo: item.productionOrderNo,
        recipeCode: item.recipeCode,
        recipeVer: item.recipeVer,
        orderQty: item.orderQty,
        orderUom: item.orderUom,
        productionDate: dayjs.utc(item.productionDate).tz(TZ).format('DD-MM-YYYY HH:mm:ss'),
        shift: item.shift,
        orderStatus: item.orderStatus,
        createdBy: item.createdBy,
        creationDate: dayjs.utc(item.creationDate).tz(TZ).format('DD-MM-YYYY HH:mm:ss'),
        operator: item.operator,
        machineCode: item.machineCode,
        machineName: item.machineName,
        batchNumber: item.batchNumber,
        batchStatus: item.batchStatus,
        startTime: dayjs.utc(item.startTime).tz(TZ).format('DD-MM-YYYY HH:mm:ss'),
        endTime: dayjs.utc(item.endTime).tz(TZ).format('DD-MM-YYYY HH:mm:ss'),
        processCode: item.processCode,
        processName: item.processName,
        processParameterCode: item.processParameterCode,
        processParameterName: item.processParameterName,
        value: item.value,
        dateTime: dayjs.utc(item.dateTime).tz(TZ).format('DD-MM-YYYY HH:mm:ss'),
      });
    });

    // Tạo đường dẫn file tạm thời
    const filePath = path.join(os.tmpdir(), `report-${Date.now()}.xlsx`);

    // Ghi file
    await workbook.xlsx.writeFile(filePath);

    return filePath;
  }

  private async buildReportData2(req: CreateReportDto) {
    const {
      siteIdLst,
      factoryIdLst,
      lineIdLst,
      orderNo,
      statusIdLst,
      fromDate,
      toDate,
      batchNumber,
    } = req;

    // Chuẩn bị query SQL với tham số
    const baseSql = `
      WITH raw_data AS (
        SELECT DISTINCT ON (
          pb."batchNumber",
          pbs."machineId",
          mp."code",
          pbp."dateTime"
        )
          COALESCE(s.code, '') || ' - ' || COALESCE(s.name, '') AS "site",    
          COALESCE(f.code, '') || ' - ' || COALESCE(f.name, '') AS "factory",    
          COALESCE(l.code, '') || ' - ' || COALESCE(l.name, '') AS "line",    
          COALESCE(ou_p.code, '') || ' - ' || COALESCE(ou_p.name, '') AS "processArea",
          pb."batchNumber",
          po."orderNo" AS "productionOrderNo",
          po."quantity" AS "orderQty",
          po."trxUom" AS "orderUom",
          po."planStartDate" AS "productionDate",
          po."orderStatus",
          po."createdDate" AS "creationDate",
          po."recipeCode" AS "recipeCode",
          po."recipeVersion" AS "recipeVer",

          p.code AS "processCode",
          p."name" AS "processName",

          pbs."machineId",
          m.code AS "machineCode",
          m."name" AS "machineName",
          gdd."name" AS "batchStatus",
          pbs."startTime",
          pbs."endTime",

          mp.code AS "processParameterCode",
          mp."desc" AS "processParameterName",
          pbp.value,
          pbp."dateTime",
          sft."code" AS "shift",
          COALESCE(uop."employeeCode", '') || ' - ' || COALESCE(uop."fullName", '') AS "operator",
          po."createdBy" AS "createdBy"

        FROM production_batch_status pbs
        LEFT JOIN production_batch_paramaters pbp 
          ON pbs."batchId" = pbp."batchId" AND pbs."machineId" = pbp."machineId"
        LEFT JOIN machine m 
          ON m.id = CAST(pbs."machineId" AS uuid)
        LEFT JOIN process_machine pm 
          ON m.id = pm."machineId" AND pm.assign = true
        LEFT JOIN process p 
          ON p.id = pm."processId"
        LEFT JOIN production_batch pb 
          ON pb.id = CAST(pbs."batchId" AS uuid)
        LEFT JOIN machine_parameter mp 
          ON mp."iotsitewisePropertyId" = pbp."measurementId"
        LEFT JOIN production_order po 
          ON po.id = pb."orderId"
        LEFT JOIN general_data_details gdd
          ON gdd.code = pbs."status" 
          AND gdd."generalId" = (
            SELECT id FROM general_data WHERE code = 'ORDER_STATUS'
          )
        LEFT JOIN organization_units s
          ON s.id = po."siteId"
        LEFT JOIN organization_units f
          ON f.id = po."factoryId"
        LEFT JOIN organization_units l
          ON l.id = po."lineId"
        LEFT JOIN organization_units ou_p
          ON ou_p.id = po."processAreaId"
        LEFT JOIN "user" uop
          ON uop.id = po."operatorUserId"
        LEFT JOIN shift sft
          ON sft.id = po."shiftId"

        WHERE 1=1
          AND ($1 = '' OR po."orderNo" ILIKE '%' || $1 || '%')
          AND (array_length($2::uuid[], 1) IS NULL OR po."siteId" = ANY($2::uuid[]))
          AND (array_length($3::uuid[], 1) IS NULL OR po."factoryId" = ANY($3::uuid[]))
          AND (array_length($4::uuid[], 1) IS NULL OR po."lineId" = ANY($4::uuid[]))
          AND pbs."startTime" BETWEEN $5 AND $6
          AND ($7::numeric IS NULL OR pb."batchNumber" = $7::numeric)
          --AND (array_length($8::text[], 1) IS NULL OR pbs."status" = ANY($8::text[]))
      )
      SELECT * FROM raw_data
      --ORDER BY "batchNumber", "machineId", "processParameterCode", "dateTime";
    `;

    const queryParams = [
      orderNo,
      siteIdLst?.length ? siteIdLst : null,
      factoryIdLst?.length ? factoryIdLst : null,
      lineIdLst?.length ? lineIdLst : null,
      fromDate,
      toDate,
      batchNumber ?? null,
      // statusIdLst?.length ? statusIdLst : null,
    ];

    // Thực thi query raw SQL
    const data = await this.entityManager.query(baseSql, queryParams);

    return data;
  }

  async getMetaLines(req: { factoryIdLst?: string[] }): Promise<PageResponse<any>> {
    const lines = await this.organizationUnitRepo.find({
      where: {
        isActive: true,
        levelGeneralDataDetailCode: NSGeneralData.EOrgLevel.LINE,
        ...(req?.factoryIdLst && { parentId: In(req.factoryIdLst) }),
      },
      order: { code: 'ASC', createdDate: 'DESC' },
    });
    return { data: lines };
  }

  async getMetaOrderStatus(): Promise<PageResponse<any>> {
    const genData = await this.generalDataRepo.find({
      where: {
        isActive: true,
        code: NSGeneralData.EGeneralDataCode.ORDER_STATUS,
      },
      select: ['id', 'code'],
    });

    const details = await this.generalDataDetailRepo.find({
      where: {
        isActive: true,
        generalId: In(genData.map((item) => item.id)),
      },
      order: { code: 'ASC' },
      select: ['id', 'code', 'name'],
    });

    return { data: details, total: details.length };
  }
}

class ProductionBatchAndProcessParameterReportOutput {
  site: string;
  factory: string;
  line: string;
  processArea: string;
  // Production Order Details
  productionOrderNo: string;
  recipeCode: string;
  recipeVer: number | string;
  orderQty: number;
  orderUom: string;
  productionDate: Date | string;
  shift: string;
  orderStatus: string;
  // Created By Information
  createdBy: string;
  creationDate: Date;
  // Operator Information
  operator: string;
  // Batch Information
  batchNumber: number | string;
  batchStatus: string;
  startTime: Date;
  endTime: Date;
  // Process Information
  processCode?: number | string;
  processName?: string;
  // Machine Information
  machineCode: string;
  machineName: string;
  // Process Parameter
  processParameterCode: number | string;
  processParameterName: string;
  value: string;
  dateTime: Date | string;
}
