export enum EOrganizationLevel {
  Group = 'GROUP',
  Site = 'SITE',
  Factory = 'FACTORY',
  Line = 'LINE',
  Process = 'PROCESS',
}

export interface IOrganizationLevelConfig {
  code: EOrganizationLevel;
  level: number;
  parentCode: EOrganizationLevel | null;
}

export const ORGANIZATION_LEVEL_CONFIG: IOrganizationLevelConfig[] = [
  { code: EOrganizationLevel.Group, level: 1, parentCode: EOrganizationLevel.Group },
  { code: EOrganizationLevel.Site, level: 2, parentCode: EOrganizationLevel.Group },
  { code: EOrganizationLevel.Factory, level: 3, parentCode: EOrganizationLevel.Site },
  { code: EOrganizationLevel.Line, level: 4, parentCode: EOrganizationLevel.Factory },
  { code: EOrganizationLevel.Process, level: 5, parentCode: EOrganizationLevel.Line },
];

export function getParentLevelCode(levelCode: EOrganizationLevel): EOrganizationLevel | null {
  const config = ORGANIZATION_LEVEL_CONFIG.find(c => c.code === levelCode);
  return config ? config.parentCode : null;
}

export function getLevelNumber(levelCode: EOrganizationLevel): number | null {
  const config = ORGANIZATION_LEVEL_CONFIG.find(c => c.code === levelCode);
  return config ? config.level : null;
}
