import { Entity, Column } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { PrimaryBaseEntity } from '../primary-base.entity';
@Entity('production_downtime')
export class ProductionDowntimeEntity extends PrimaryBaseEntity {
  //   /** [PK] sinh tự động */
  //   @ApiProperty({ description: 'ID giao dịch dừng máy' })
  //   @PrimaryColumn({ type: 'uuid' })
  //   downtimeTransactionId: string;

  /** Theo dữ liệu từ API */
  @ApiProperty({ description: 'ID dừng máy SCADA' })
  @Column({ type: 'text', nullable: true })
  scadaDowntimeId: string;

  /** Theo dữ liệu từ API */
  @ApiProperty({ description: 'Mã máy' })
  @Column({ type: 'text' })
  machineCode: string;

  /** Thời gian bắt đầu downtime */
  @ApiProperty({ description: 'Thời gian bắt đầu downtime' })
  @Column({ nullable: true, type: 'timestamptz' })
  startTime: Date;

  /** Thời gian kết thúc downtime */
  @ApiProperty({ description: 'Thời gian kết thúc downtime' })
  @Column({ nullable: true, type: 'timestamptz' })
  endTime: Date;

  /** Mã lý do */
  @ApiProperty({ description: 'Mã lý do' })
  @Column({ nullable: true })
  reasonCode: string; //NSReason.EReasonGroup;

  /** Mô tả lý do */
  @ApiProperty({ description: 'Mô tả lý do' })
  @Column({ type: 'text', nullable: true })
  reasonDescription: string;

  /** Từ machine code, link qua bảng machine để lưu machine id */
  @ApiProperty({ description: 'ID máy' })
  @Column({ nullable: true, type: 'uuid' })
  machineId: string;

  /** Link qua process mà machine được gắn để lưu */
  @ApiProperty({ description: 'ID khu vực sản xuất' })
  @Column({ nullable: true, type: 'uuid' })
  processAreaId: string;

  /** 0 */
  @ApiProperty({ description: 'Cờ hủy' })
  @Column({ type: 'int', default: 0 })
  cancelledFlag: number;

  /** Mô tả giao dịch */
  @ApiProperty({ description: 'Mô tả giao dịch' })
  @Column({ type: 'text', nullable: true })
  description: string;

  /** is_processed */
  @ApiProperty({ description: 'Đã xử lý' })
  @Column({ type: 'boolean', default: false })
  isProcessed: boolean;
}
