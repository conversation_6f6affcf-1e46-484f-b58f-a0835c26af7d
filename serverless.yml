service: mes-lambda

provider:
  name: aws
  runtime: nodejs18.x
  region: ap-southeast-1
  timeout: 29
  # apiGateway:
  #   endpointType: REGIONAL
  # ipAddressType: IPV4
  environment:
    NODE_ENV: ${env.NODE_ENV}
    PORT: ${env.PORT}
    TZ: ${env.TZ}
    REQUEST_TIMEOUT: ${env.REQUEST_TIMEOUT}
    SWAGGER_TITLE: ${env.SWAGGER_TITLE}
    SWAGGER_DESCRIPTION: ${env.SWAGGER_DESCRIPTION}
    SWAGGER_VERSION: ${env.SWAGGER_VERSION}
    DB_PRIMARY_HOST: ${env.DB_PRIMARY_HOST}
    DB_PRIMARY_PORT: ${env.DB_PRIMARY_PORT}
    DB_PRIMARY_USERNAME: ${env.DB_PRIMARY_USERNAME}
    DB_PRIMARY_PASSWORD: ${env.DB_PRIMARY_PASSWORD}
    DB_PRIMARY_DATABASE: ${env.DB_PRIMARY_DATABASE}
    DB_PRIMARY_SYNCHRONIZE: ${env.DB_PRIMARY_SYNCHRONIZE}
    DB_PRIMARY_SSL: ${env.DB_PRIMARY_SSL}
    DB_PRIMARY_SSL_REJECT_UNAUTHORIZED: ${env.DB_PRIMARY_SSL_REJECT_UNAUTHORIZED}
    DB_SCADA_HOST: ${env.DB_SCADA_HOST}
    DB_SCADA_PORT: ${env.DB_SCADA_PORT}
    DB_SCADA_USERNAME: ${env.DB_SCADA_USERNAME}
    DB_SCADA_PASSWORD: ${env.DB_SCADA_PASSWORD}
    DB_SCADA_DATABASE: ${env.DB_SCADA_DATABASE}
    DB_SCADA_SYNCHRONIZE: ${env.DB_SCADA_SYNCHRONIZE}
    DB_SCADA_SSL: ${env.DB_SCADA_SSL}
    DB_SCADA_SSL_REJECT_UNAUTHORIZED: ${env.DB_SCADA_SSL_REJECT_UNAUTHORIZED}
    JWT_SECRET: ${env.JWT_SECRET}
    JWT_EXPIRY: ${env.JWT_EXPIRY}
    # AWS_ACCESS_KEY_ID: ${env.AWS_ACCESS_KEY_ID}
    # AWS_SECRET_ACCESS_KEY: ${env.AWS_SECRET_ACCESS_KEY}
    # AWS_REGION: ${env.AWS_REGION}
    API_TOKEN_CACHE_INTERVAL: ${env.API_TOKEN_CACHE_INTERVAL}
    API_TOKEN_SECRET: ${env.API_TOKEN_SECRET}
    DOCKER_SERVER_IP: ${env.DOCKER_SERVER_IP}
    TOKEN_ERP_URL: ${env.TOKEN_ERP_URL}
    ERP_INVENTORY: ${env.ERP_INVENTORY}
    EBS_MES_USERNAME: ${env.EBS_MES_USERNAME}
    EBS_MES_PASSWORD: ${env.EBS_MES_PASSWORD}
    IS_DOCKER_SERVER: ${env.IS_DOCKER_SERVER}
    AWS_SQS_URL: ${env.AWS_SQS_URL}
    AWS_SQS_URL_CUSTOM: ${env.AWS_SQS_URL_CUSTOM}
    AWS_SQS_REGION: ${env.AWS_SQS_REGION}
    AWS_SQS_ACCESS_KEY_ID: ${env.AWS_SQS_ACCESS_KEY_ID}
    AWS_SQS_SECRET_ACCESS_KEY: ${env.AWS_SQS_SECRET_ACCESS_KEY}
    AWS_API_VERSION: ${env.AWS_API_VERSION}
    AWS_SNS_ARN: ${env.AWS_SNS_ARN}
    REDIS_URL: ${env.REDIS_URL}
    ACCEPT_PUBLIC_IP: ${env.ACCEPT_PUBLIC_IP}

functions: ${file(./lambdas/serverless-erp.yaml)}

package:
  individually: true

custom:
  jetpack:
    concurrency: 1

plugins:
  - serverless-offline
  - serverless-plugin-optimize
  - serverless-jetpack
