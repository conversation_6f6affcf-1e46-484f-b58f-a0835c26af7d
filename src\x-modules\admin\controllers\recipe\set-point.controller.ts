import { Body, Param, UseGuards, UsePipes, ValidationPipe } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { DefController, DefGet, DefPost } from '~/@core/decorator';
import {
  DuplicateByProcessDTO,
  DuplicateByProductDTO,
  DuplicateDTO,
  SetPointCreateByProcessDTO,
  SetPointCreateByProductDTO,
  SetPointCreateDTO,
  SetPointPaginationDTO,
  UpdateSetPointDTO,
} from '~/dto/set-point.dto';
import { SetPointService } from '../../services';
import { Roles } from '~/@core/decorator/roles.decorator';
import { RoleGuard } from '~/@systems/guard/role.guard';

@ApiTags('SetPoint')
@DefController('set-point')
@UsePipes(new ValidationPipe())
export class SetPointController {
  constructor(private readonly service: SetPointService) {}

  @DefGet('recipe-load-select-box')
  @Roles('/recipe/set-point', 'View')
  @UseGuards(RoleGuard)
  async recipeLoadSelectBox() {
    return await this.service.recipeLoadSelectBox();
  }

  @DefGet('process-load-select-box')
  @Roles('/recipe/set-point', 'View')
  @UseGuards(RoleGuard)
  async processLoadSelectBox() {
    return await this.service.processLoadSelectBox();
  }

  @DefGet('product-load-select-box')
  @Roles('/recipe/set-point', 'View')
  @UseGuards(RoleGuard)
  async productLoadSelectBox() {
    return await this.service.productLoadSelectBox();
  }

  @DefGet('machine-parameter-load-select-box/:recipeId')
  @Roles('/recipe/set-point', 'View')
  @UseGuards(RoleGuard)
  async machineParameterLoadSelectBox(@Param('recipeId') recipeId: string) {
    return await this.service.machineParameterLoadSelectBox(recipeId);
  }

  @DefGet('machine-parameter-load-select-box-by-process/:processId')
  @Roles('/recipe/set-point', 'View')
  @UseGuards(RoleGuard)
  async machineParameterLoadSelectBoxByProcessId(@Param('processId') processId: string) {
    return await this.service.machineParameterLoadSelectBoxByProcessId(processId);
  }

  @DefGet('machine-parameter-load-select-box-by-product/:productId')
  @Roles('/recipe/set-point', 'View')
  @UseGuards(RoleGuard)
  async machineParameterLoadSelectBoxByProductId(@Param('productId') productId: string) {
    return await this.service.machineParameterLoadSelectBoxByProductId(productId);
  }

  @DefPost('pagination')
  @Roles('/recipe/set-point', 'View')
  @UseGuards(RoleGuard)
  async pagination(@Body() body: SetPointPaginationDTO) {
    return await this.service.pagination(body);
  }

  @DefPost('create')
  @Roles('/recipe/set-point', 'View')
  @UseGuards(RoleGuard)
  async create(@Body() data: SetPointCreateDTO) {
    return await this.service.create(data);
  }

  @DefPost('create-by-process')
  @Roles('/recipe/set-point', 'View')
  @UseGuards(RoleGuard)
  async createByProcess(@Body() data: SetPointCreateByProcessDTO) {
    return await this.service.createByProcess(data);
  }

  @DefPost('create-by-product')
  @Roles('/recipe/set-point', 'View')
  @UseGuards(RoleGuard)
  async createByProduct(@Body() data: SetPointCreateByProductDTO) {
    return await this.service.createByProduct(data);
  }

  @DefPost('update')
  @Roles('/recipe/set-point', 'View')
  @UseGuards(RoleGuard)
  async update(@Body() data: UpdateSetPointDTO) {
    return await this.service.update(data);
  }

  @DefPost('duplicate')
  @Roles('/recipe/set-point', 'View')
  @UseGuards(RoleGuard)
  async duplicate(@Body() data: DuplicateDTO) {
    return await this.service.duplicate(data);
  }

  @DefPost('duplicate-by-process')
  @Roles('/recipe/set-point', 'View')
  @UseGuards(RoleGuard)
  async duplicateByProcess(@Body() data: DuplicateByProcessDTO) {
    return await this.service.duplicateByProcess(data);
  }

  @DefPost('duplicate-by-product')
  @Roles('/recipe/set-point', 'View')
  @UseGuards(RoleGuard)
  async duplicateByProduct(@Body() data: DuplicateByProductDTO) {
    return await this.service.duplicateByProduct(data);
  }
}
