import { Module, DynamicModule } from '@nestjs/common';
import { SQSHandlerService } from './sqsHandler.service';
import { SQSService } from './sqs.service';
import { SnsService } from './sns.service';
import {
  SitewiseInService,
  ProdBatchParametersService,
  ProductOutputByRejection,
  ProdOrderMaterialTransactionService,
  CalculateUtilityUsage,
  ProductionOeeService,
  CalculateKansuiAndSeasoningService,
  TransactionProductOutput,
  CallProcedureService,
  UtilityConsumptionKpi,
  ManhourKpi,
  RejectionKpi,
  LossPowderKpi,
} from '~/x-modules/integration/services';
import { LogJobService } from './log-job.service';
import { LogScheduleService } from '~/x-modules/sqs/log-schedule.service';
const externalProviders = [
  SQSHandlerService,
  SQSService,
  SnsService,
  SitewiseInService,
  ProdBatchParametersService,
  ProductOutputByRejection,
  ProdOrderMaterialTransactionService,
  CalculateUtilityUsage,
  ProductionOeeService,
  CalculateKansuiAndSeasoningService,
  LogJobService,
  TransactionProductOutput,
  LogScheduleService,
  CallProcedureService,
  UtilityConsumptionKpi,
  ManhourKpi,
  RejectionKpi,
  LossPowderKpi,
];
@Module({
  providers: [...Object.values(externalProviders)],
  exports: [SQSService, SnsService, LogJobService, LogScheduleService],
})
export class SQSModule {}
