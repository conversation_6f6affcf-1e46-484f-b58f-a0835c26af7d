export namespace NSSitewise {
  export enum ESitewisePropertyType {
    ATTRIBUTE = 'ATTRIBUTE',
    MEASUREMENT = 'MEASUREMENT',
    METRIC = 'METRIC',
    TRANSFORM = 'TRANSFORM',
  }

  export enum ESitewiseDashboartLabelType {
    DEFAULT = 'DEFAULT', //done
    TOTAL_COUNT = 'TOTAL_COUNT', //done
    MIXING_TIME = 'MIXING_TIME', //done
    MATERIAL_CONSUMPTION = 'MATERIAL_CONSUMPTION',
    ELECTRICITY_SHIFT = 'ELECTRICITY_SHIFT', //done
    GOOD_COUNT = 'GOOD_COUNT', //done
    REJECTION_COUNT = 'REJECTION_COUNT', //done
    PRODUCT_CODE_PRODUCT_NAME = 'PRODUCT_CODE_PRODUCT_NAME',
    SUM_TOTAL_COUNT = 'SUM_TOTAL_COUNT', //done
    SUM_TOTAL_GOOD = 'SUM_TOTAL_GOOD', //done
    SUM_TOTAL_REJECTION = 'SUM_TOTAL_REJECTION', //done
    FLOUR_LOSS_SHIFT = 'FLOUR_LOSS_SHIFT',
    STEAM_SHIFT = 'STEAM_SHIFT', // FLYING && Steaming
    SHORT_SHIFT = 'SHORT_SHIFT',
    SHORT_HOUR = 'SHORT_HOUR',
    BROKEN_NOODLES_SHIFT = 'BROKEN_NOODLES_SHIFT',
    AVERAGE_10_BOX = 'AVERAGE_10_BOX', // Trọng lượng 10 thùng gần nhất
  }

  export enum ESitewiseDataType {
    STRING = 'string',
    INTEGER = 'integer',
    DOUBLE = 'double',
    BOOLEAN = 'boolean',
    DATE_TIME = 'DateTime',
  }
}
