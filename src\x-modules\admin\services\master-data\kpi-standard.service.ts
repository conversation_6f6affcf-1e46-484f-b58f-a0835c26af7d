import { ConflictException, Injectable, NotFoundException } from '@nestjs/common';
import { In, Not } from 'typeorm';
import { BindRepo, DefTransaction } from '~/@core/decorator';
import {
  KPIStandardCopyDTO,
  KPIStandardCreateDTO,
  KpiStandardScoreDto,
  PaginationReq,
} from '~/dto/kpi-standard.dto';
import { KpiStandardEntity, KpiStandardScoreEntity } from '~/entities/primary';
import {
  KpiSetDetailRepo,
  KpiSetGroupRepo,
  KpiSetHeaderRepo,
  KpiStandardRepo,
  KpiStandardScoreRepo,
  KpiScoreRepo,
} from '~/repositories/primary';
import { KpiRepo } from '~/repositories/primary/kpi.repo';
import { v4 as uuidv4 } from 'uuid';
import { BusinessException } from '~/@systems/exceptions';

@Injectable()
export class KPIStandardService {
  constructor() {}

  @BindRepo(KpiStandardRepo)
  private repo: KpiStandardRepo;

  @BindRepo(KpiSetHeaderRepo)
  private kpiSetHeaderRepo: KpiSetHeaderRepo;

  @BindRepo(KpiSetGroupRepo)
  private kpiSetGroupRepo: KpiSetGroupRepo;

  @BindRepo(KpiSetDetailRepo)
  private kpiSetDetailRepo: KpiSetDetailRepo;

  @BindRepo(KpiRepo)
  private kpiRepo: KpiRepo;

  @BindRepo(KpiStandardScoreRepo)
  private kpiStandardScoreRepo: KpiStandardScoreRepo;

  @BindRepo(KpiScoreRepo)
  private kpiScoreRepo: KpiScoreRepo;

  @DefTransaction()
  async create(body: KPIStandardCreateDTO) {
    const kpiSetHeader = await this.kpiSetHeaderRepo.findOne({ where: { id: body.kpiSetId } });

    if (!kpiSetHeader) {
      throw new NotFoundException('KPI Set Header not found');
    }

    const dictKpiStandardBySet: any = {};
    {
      const lstKpiStandard = await this.repo.find({ where: { kpiSetId: body.kpiSetId } });
      const lstKpiScore = await this.kpiStandardScoreRepo.find({
        where: { groupId: In(lstKpiStandard.map((i) => i.groupId)) },
      });
      if (lstKpiScore.find((i) => i.effectiveDateTo === null))
        throw new ConflictException(
          'Please enter the end date for the current KPI Target before creating a new one',
        );
    }
    // Infor table KPI Set Group
    const dictKpiSetGroup = {};
    {
      const kpiSetGroup = await this.kpiSetGroupRepo.find({
        where: { kpiSetHeaderId: body.kpiSetId },
      });
      if (kpiSetGroup.length === 0) {
        throw new NotFoundException('KPI Set Group not found');
      }
      Object.assign(
        dictKpiSetGroup,
        Object.fromEntries(kpiSetGroup.map((item) => [item.id, item])),
      );
    }

    const kpiSetDetails = await this.kpiSetDetailRepo.find({
      where: { kpiSetGroupId: In(Object.keys(dictKpiSetGroup)) },
    });
    if (kpiSetDetails.length === 0) {
      throw new NotFoundException('KPI Set Detail not found');
    }

    const kpiIds = kpiSetDetails.map((detail) => detail.kpiId);
    const kpiList = await this.kpiRepo.find({ where: { id: In(kpiIds) } });
    if (kpiList.length === 0) {
      throw new NotFoundException('KPI Code không tồn tại');
    }
    const existStand: any = await this.repo.find({ where: { kpiSetId: body.kpiSetId } });
    const existStandScore = await this.kpiStandardScoreRepo.find({
      where: { groupId: In(existStand.map((i) => i.groupId)) },
      select: ['effectiveDateFrom', 'effectiveDateTo'],
    });
    existStand.forEach((i) => {
      i.effectiveDateFrom =
        existStandScore.find((j) => j.groupId === i.groupId)?.effectiveDateFrom || null;
      i.effectiveDateTo =
        existStandScore.find((j) => j.groupId === i.groupId)?.effectiveDateTo || null;
    });
    const newKpiStandards = [];
    const groupId = uuidv4();
    for (const kpi of kpiList) {
      const kpiSetDetail = kpiSetDetails.find((detail) => detail.kpiId === kpi.id);
      if (!kpiSetDetail) continue;

      const kpiSetGroup = dictKpiSetGroup[kpiSetDetail.kpiSetGroupId];
      const effectiveDateFrom = body.kpiScores[0].effectiveDateFrom
        ? new Date(body.kpiScores[0].effectiveDateFrom)
        : null;
      const effectiveDateTo = body.kpiScores[0].effectiveDateTo
        ? new Date(body.kpiScores[0].effectiveDateTo)
        : null;
      if (
        existStand.filter(
          (item) =>
            item.kpiCodeId === kpiSetDetail.kpiId &&
            item.kpiSetGroupId === kpiSetGroup.id &&
            item.effectiveDateFrom?.getTime() == effectiveDateFrom?.getTime() &&
            item.effectiveDateTo?.getTime() == effectiveDateTo?.getTime(),
        ).length > 0
      )
        continue;
      const newKpiStandard: KpiStandardEntity = this.repo.create({
        kpiSetId: kpiSetHeader.id,
        kpiSetGroupId: kpiSetGroup.id,
        kpiCodeId: kpiSetDetail.kpiId,
        unit: kpi.unit,
        weightageKpi: kpiSetDetail.weighTarget,
        // updatedBy: 'admin',
        // createdBy: 'admin',
        createdDate: new Date(),
        updatedDate: new Date(),
        groupId,
      });

      newKpiStandards.push(newKpiStandard);
    }

    await this.repo.insert(newKpiStandards);

    // Process KPI Standard Score Table
    if (body.kpiScores && body.kpiScores.length > 0) {
      const dictExistKpiScore: Record<string, any> = {};
      const dictKpiScore: Record<string, number> = {};

      // Chuẩn hóa ngày về dạng YYYY-MM-DD
      const normalizeDate = (date: Date | null): string => {
        return date instanceof Date && !isNaN(date.getTime())
          ? date.toISOString().split('T')[0]
          : '';
      };

      {
        const lstKpiScore = await this.kpiStandardScoreRepo.find({
          where: { kpiCodeId: In(body.kpiScores.map((i) => i.kpiId)) },
          order: { kpiCodeId: 'DESC' },
        });

        lstKpiScore.forEach((i) => {
          const effectiveDateFrom = i.effectiveDateFrom ? new Date(i.effectiveDateFrom) : null;
          const effectiveDateTo = i.effectiveDateTo ? new Date(i.effectiveDateTo) : null;

          const key =
            i.kpiCodeId +
            i.level +
            normalizeDate(effectiveDateFrom) +
            normalizeDate(effectiveDateTo);
          dictExistKpiScore[key] = i;
        });

        lstKpiScore.forEach((i) => {
          if (!dictKpiScore[i.kpiCodeId]) {
            dictKpiScore[i.kpiCodeId] = lstKpiScore.filter(
              (y) => y.kpiCodeId === i.kpiCodeId,
            ).length;
          }
        });
      }

      for (const item of body.kpiScores) {
        const effectiveDateFrom = item.effectiveDateFrom ? new Date(item.effectiveDateFrom) : null;
        const effectiveDateTo = item.effectiveDateTo ? new Date(item.effectiveDateTo) : null;

        const key =
          item.kpiId +
          item.kpiSetId +
          item.level +
          normalizeDate(effectiveDateFrom) +
          normalizeDate(effectiveDateTo);

        if (
          item.level != null &&
          item.levelScore != null &&
          item.rewardPercentage &&
          dictExistKpiScore[key]
        ) {
          throw new ConflictException('KPI Score already exists');
        }
      }
      const kpiScores = body.kpiScores.map((item: KpiStandardScoreDto) => {
        if (
          item.level.length > 0 &&
          item.levelScore !== undefined &&
          item.rewardPercentage !== undefined &&
          dictExistKpiScore[item.kpiId + item.level + item.effectiveDateFrom + item.effectiveDateTo]
        )
          return;

        if (
          item.level.length > 0 &&
          item.levelScore !== undefined &&
          item.rewardPercentage !== undefined &&
          (newKpiStandards.find(
            (i: KpiStandardEntity) =>
              i.kpiCodeId === item.kpiId &&
              i.kpiSetId === body.kpiSetId &&
              i.kpiSetGroupId === item.kpiSetGroupId,
          ) ||
            existStand.find(
              (i) =>
                i.kpiCodeId === item.kpiId &&
                i.kpiSetId === body.kpiSetId &&
                i.kpiSetGroupId === item.kpiSetGroupId,
            ))
        ) {
          return {
            groupId:
              newKpiStandards.find(
                (i: KpiStandardEntity) =>
                  i.kpiCodeId === item.kpiId &&
                  i.kpiSetId === body.kpiSetId &&
                  i.kpiSetGroupId === item.kpiSetGroupId,
              )?.groupId ||
              existStand.find(
                (i) =>
                  i.kpiCodeId === item.kpiId &&
                  i.kpiSetId === body.kpiSetId &&
                  i.kpiSetGroupId === item.kpiSetGroupId,
              ).groupId,
            kpiSetId: body.kpiSetId,
            kpiScoreId: dictKpiScore[item.kpiId] || 1,
            kpiCodeId: item.kpiId,
            level: item.level,
            levelScore: item.levelScore,
            rewardPercentage: item.rewardPercentage,
            effectiveDateFrom: item.effectiveDateFrom ? new Date(item.effectiveDateFrom) : null,
            effectiveDateTo: item.effectiveDateTo ? new Date(item.effectiveDateTo) : null,
          } as KpiStandardScoreEntity;
        }
      });

      await this.kpiStandardScoreRepo.insert(kpiScores.filter((i) => i !== undefined));
    }

    return { message: 'Create success' };
  }

  async copyKpiTargets(body: KPIStandardCopyDTO) {
    const kpiStandardScore = await this.kpiStandardScoreRepo.find({
      where: { groupId: body.groupId },
      select: ['groupId', 'effectiveDateTo', 'effectiveDateFrom'],
    });

    // Nếu tồn tại bản ghi có effectiveDateTo là null, ném lỗi
    if (kpiStandardScore.some((score) => score.effectiveDateTo === null)) {
      throw new ConflictException(
        ` Please enter the end date for the current KPI Target before creating a new one`,
      );
    }
    if (body.kpiScores && body.kpiScores.length > 0) {
      const existingDates = new Set(
        kpiStandardScore
          .filter((score) => score.effectiveDateFrom)
          .map((score) => new Date(score.effectiveDateFrom).getTime()),
      );

      for (const kpiScore of body.kpiScores) {
        if (kpiScore.effectiveDateFrom) {
          const inputDate = new Date(kpiScore.effectiveDateFrom).getTime();
          if (existingDates.has(inputDate)) {
            throw new ConflictException(`KPI Score already exists`);
          }
        }
      }
    }
    const kpiSetHeader = await this.kpiSetHeaderRepo.findOne({ where: { id: body.kpiSetId } });

    if (!kpiSetHeader) {
      throw new NotFoundException('KPI Set Header not found');
    }

    const dictKpiSetGroup = {};
    {
      const kpiSetGroup = await this.kpiSetGroupRepo.find({
        where: { kpiSetHeaderId: body.kpiSetId },
      });
      if (kpiSetGroup.length === 0) {
        throw new NotFoundException('KPI Set Group not found');
      }
      Object.assign(
        dictKpiSetGroup,
        Object.fromEntries(kpiSetGroup.map((item) => [item.id, item])),
      );
    }

    // Infor table KPI Set Detail
    const kpiSetDetails = await this.kpiSetDetailRepo.find({
      where: { kpiSetGroupId: In(Object.keys(dictKpiSetGroup)) },
    });
    if (kpiSetDetails.length === 0) {
      throw new NotFoundException('KPI Set Detail not found');
    }

    // Infor table KPI List (kpiCodeId)
    const kpiIds = kpiSetDetails.map((detail) => detail.kpiId);
    const kpiList = await this.kpiRepo.find({ where: { id: In(kpiIds) } });
    if (kpiList.length === 0) {
      throw new NotFoundException('KPI Code không tồn tại');
    }
    const existStand: any = await this.repo.find({ where: { kpiSetId: body.kpiSetId } });
    const existStandScore = await this.kpiStandardScoreRepo.find({
      where: { groupId: In(existStand.map((i) => i.groupId)) },
      select: ['effectiveDateFrom', 'effectiveDateTo'],
    });
    if (existStandScore.some((score) => score.effectiveDateTo === null)) {
      throw new ConflictException('KPI Score has effectiveDateTo null');
    }
    existStand.forEach((i) => {
      i.effectiveDateFrom =
        existStandScore.find((j) => j.groupId === i.groupId)?.effectiveDateFrom || null;
      i.effectiveDateTo =
        existStandScore.find((j) => j.groupId === i.groupId)?.effectiveDateTo || null;
    });
    const newKpiStandards = [];
    const groupId = uuidv4();
    for (const kpi of kpiList) {
      const kpiSetDetail = kpiSetDetails.find((detail) => detail.kpiId === kpi.id);
      if (!kpiSetDetail) continue;

      const kpiSetGroup = dictKpiSetGroup[kpiSetDetail.kpiSetGroupId];
      const effectiveDateFrom = body.kpiScores[0].effectiveDateFrom
        ? new Date(body.kpiScores[0].effectiveDateFrom)
        : null;
      const effectiveDateTo = body.kpiScores[0].effectiveDateTo
        ? new Date(body.kpiScores[0].effectiveDateTo)
        : null;
      if (
        existStand.filter(
          (item) =>
            item.kpiCodeId === kpiSetDetail.kpiId &&
            item.kpiSetGroupId === kpiSetGroup.id &&
            item.effectiveDateFrom?.getTime() == effectiveDateFrom?.getTime() &&
            item.effectiveDateTo?.getTime() == effectiveDateTo?.getTime(),
        ).length > 0
      )
        continue;
      const newKpiStandard: KpiStandardEntity = this.repo.create({
        kpiSetId: kpiSetHeader.id,
        kpiSetGroupId: kpiSetGroup.id,
        kpiCodeId: kpiSetDetail.kpiId,
        unit: kpi.unit,
        weightageKpi: kpiSetDetail.weighTarget,
        // updatedBy: 'admin',
        // createdBy: 'admin',
        createdDate: new Date(),
        updatedDate: new Date(),
        groupId,
      });

      newKpiStandards.push(newKpiStandard);
    }

    await this.repo.insert(newKpiStandards);

    // Process KPI Standard Score Table
    if (body.kpiScores && body.kpiScores.length > 0) {
      const dictExistKpiScore: any = {};
      const dictKpiScore: any = {};
      {
        const lstKpiScore = await this.kpiStandardScoreRepo.find({
          where: { kpiCodeId: In(body.kpiScores.map((i) => i.kpiId)) },
          order: { kpiCodeId: 'DESC' },
        });
        lstKpiScore.forEach(
          (i) =>
            (dictExistKpiScore[
              i.kpiCodeId + i.level + i.effectiveDateFrom + (i.effectiveDateTo || '')
            ] = i),
        );
        lstKpiScore.forEach((i) => {
          if (!dictKpiScore[i.kpiCodeId]) {
            dictKpiScore[i.kpiCodeId] = lstKpiScore.filter(
              (y: any) => y.kpiCodeId === i.kpiCodeId,
            ).length;
          }
        });
      }
      for (const item of body.kpiScores) {
        if (
          item.level != null &&
          item.levelScore != null &&
          item.rewardPercentage &&
          dictExistKpiScore[item.kpiId + item.level + item.effectiveDateFrom + item.effectiveDateTo]
        ) {
          throw new ConflictException('KPI Score already exists');
        }
      }
      const kpiScores = body.kpiScores.map((item: KpiStandardScoreDto) => {
        if (
          item.level.length > 0 &&
          item.levelScore !== undefined &&
          item.rewardPercentage !== undefined &&
          dictExistKpiScore[item.kpiId + item.level + item.effectiveDateFrom + item.effectiveDateTo]
        )
          return;

        if (
          item.level.length > 0 &&
          item.levelScore !== undefined &&
          item.rewardPercentage !== undefined &&
          (newKpiStandards.find(
            (i: KpiStandardEntity) =>
              i.kpiCodeId === item.kpiId &&
              i.kpiSetId === body.kpiSetId &&
              i.kpiSetGroupId === item.kpiSetGroupId,
          ) ||
            existStand.find(
              (i) =>
                i.kpiCodeId === item.kpiId &&
                i.kpiSetId === body.kpiSetId &&
                i.kpiSetGroupId === item.kpiSetGroupId,
            ))
        ) {
          return {
            groupId:
              newKpiStandards.find(
                (i: KpiStandardEntity) =>
                  i.kpiCodeId === item.kpiId &&
                  i.kpiSetId === body.kpiSetId &&
                  i.kpiSetGroupId === item.kpiSetGroupId,
              )?.groupId ||
              existStand.find(
                (i) =>
                  i.kpiCodeId === item.kpiId &&
                  i.kpiSetId === body.kpiSetId &&
                  i.kpiSetGroupId === item.kpiSetGroupId,
              ).groupId,
            kpiSetId: body.kpiSetId,
            kpiScoreId: dictKpiScore[item.kpiId] || 1,
            kpiCodeId: item.kpiId,
            level: item.level,
            levelScore: item.levelScore,
            rewardPercentage: item.rewardPercentage,
            effectiveDateFrom: item.effectiveDateFrom ? new Date(item.effectiveDateFrom) : null,
            effectiveDateTo: item.effectiveDateTo ? new Date(item.effectiveDateTo) : null,
          } as KpiStandardScoreEntity;
        }
      });

      await this.kpiStandardScoreRepo.insert(kpiScores.filter((i) => i !== undefined));
    }

    return { message: 'Create success' };
  }
  async update(body: KPIStandardCopyDTO) {
    const kpiSetHeader = await this.kpiSetHeaderRepo.findOne({ where: { id: body.kpiSetId } });

    if (!kpiSetHeader) {
      throw new NotFoundException('KPI Set Header not found');
    }
    // Infor table KPI Set Group

    const dictKpiSetGroup = {};
    {
      const kpiSetGroup = await this.kpiSetGroupRepo.find({
        where: { kpiSetHeaderId: body.kpiSetId },
      });
      if (kpiSetGroup.length === 0) {
        throw new NotFoundException('KPI Set Group not found');
      }
      Object.assign(
        dictKpiSetGroup,
        Object.fromEntries(kpiSetGroup.map((item) => [item.id, item])),
      );
    }

    // Infor table KPI Set Detail
    const kpiSetDetails = await this.kpiSetDetailRepo.find({
      where: { kpiSetGroupId: In(Object.keys(dictKpiSetGroup)) },
    });
    if (kpiSetDetails.length === 0) {
      throw new NotFoundException('KPI Set Detail not found');
    }

    // Infor table KPI List (kpiCodeId)
    const kpiIds = kpiSetDetails.map((detail) => detail.kpiId);
    const kpiList = await this.kpiRepo.find({ where: { id: In(kpiIds) } });
    if (kpiList.length === 0) {
      throw new NotFoundException('KPI Code không tồn tại');
    }
    const existStand = await this.repo.find({ where: { kpiSetId: body.kpiSetId } });
    const newKpiStandards = [];
    for (const kpi of kpiList) {
      const kpiSetDetail = kpiSetDetails.find((detail) => detail.kpiId === kpi.id);
      if (!kpiSetDetail) continue;

      const kpiSetGroup = dictKpiSetGroup[kpiSetDetail.kpiSetGroupId];
      if (
        existStand.filter(
          (item) => item.kpiCodeId === kpiSetDetail.kpiId && item.kpiSetGroupId === kpiSetGroup.id,
        ).length > 0
      )
        continue;
      const newKpiStandard: KpiStandardEntity = this.repo.create({
        kpiSetId: kpiSetHeader.id,
        kpiSetGroupId: kpiSetGroup.id,
        kpiCodeId: kpiSetDetail.kpiId,
        unit: kpi.unit,
        weightageKpi: kpiSetDetail.weighTarget,
        // updatedBy: 'admin',
        // createdBy: 'admin',
        createdDate: new Date(),
        updatedDate: new Date(),
      });

      newKpiStandards.push(newKpiStandard);
    }

    await this.repo.insert(newKpiStandards);

    const lstKpiScore = await this.kpiStandardScoreRepo.find({
      where: { groupId: body.groupId },
    });
    const formatDate = (date: Date | string): string => {
      return new Date(date).toISOString().split('T')[0];
    };
    for (const item of body.kpiScores) {
      const kpiScoreToUpdate = lstKpiScore.find(
        (score) => score.kpiCodeId === item.kpiId && score.level === item.level,
      );

      if (kpiScoreToUpdate) {
        const effectiveDateFrom = item.effectiveDateFrom || null;
        const effectiveDateTo = item.effectiveDateTo || null;

        // Tìm các bản ghi khác groupId nhưng cùng kpiSetId, kpiCodeId, level
        const conflictScores = await this.kpiStandardScoreRepo.find({
          where: {
            kpiSetId: body.kpiSetId,
            kpiCodeId: item.kpiId,
            level: item.level,
            groupId: Not(body.groupId),
          },
        });

        for (const conflict of conflictScores) {
          // Check effectiveDateTo = null
          if (conflict.effectiveDateTo === null) {
            throw new NotFoundException(
              `Please enter the end date for the current KPI Target before creating a new one`,
            );
          }

          // check effectiveDateFrom
          if (
            effectiveDateFrom &&
            conflict.effectiveDateFrom &&
            formatDate(conflict.effectiveDateFrom) === formatDate(effectiveDateFrom)
          ) {
            throw new NotFoundException(`Exists KPI Target with duplicate effective date from`);
          }
        }

        await this.kpiStandardScoreRepo.update(
          { kpiScoreLineId: kpiScoreToUpdate.kpiScoreLineId },
          {
            levelScore: item.levelScore,
            rewardPercentage: item.rewardPercentage,
            effectiveDateFrom: effectiveDateFrom,
            effectiveDateTo: effectiveDateTo,
          },
        );
      }
    }
    return { message: 'Create success' };
  }

  async loadDataKpiSet() {
    const data = await this.kpiSetHeaderRepo.find({
      order: { createdDate: 'DESC' },
    });
    data.forEach((item: any) => {
      item.kpiSetName = `${item.code} - ${item.longName}` || '';
    });

    return {
      message: 'Success',
      data,
    };
  }
  async loadDataKpiSetFirst(kpiSetId: string) {
    const data = await this.kpiSetHeaderRepo.findOne({
      where: { id: kpiSetId },
    });

    if (data) {
      (data as any).kpiSetName = `${data.code} - ${data.longName}` || '';
    }

    return {
      message: 'Success',
      data,
    };
  }

  // Find KPI Set Header
  async find(kpiSetId: string) {
    const kpiSetHeader = await this.kpiSetHeaderRepo.findOne({ where: { id: kpiSetId } });

    if (!kpiSetHeader) {
      throw new NotFoundException('KPI Set Header not found');
    }

    // Infor KPI Set Group
    const dictKpiSetGroup = {};
    {
      const kpiSetGroup = await this.kpiSetGroupRepo.find({ where: { kpiSetHeaderId: kpiSetId } });
      if (kpiSetGroup.length === 0) {
        throw new NotFoundException('KPI Set Group not found');
      }
      Object.assign(
        dictKpiSetGroup,
        Object.fromEntries(kpiSetGroup.map((item) => [item.id, item])),
      );
    }

    // Infor KPI Set Detail
    const kpiSetDetails = await this.kpiSetDetailRepo.find({
      where: { kpiSetGroupId: In(Object.keys(dictKpiSetGroup)) },
    });
    if (kpiSetDetails.length === 0) {
      throw new NotFoundException('KPI Set Detail not found');
    }

    // Infor KPI List (kpiCodeId)
    const kpiIds = kpiSetDetails.map((detail) => detail.kpiId);
    const kpiList = await this.kpiRepo.find({ where: { id: In(kpiIds) } });
    if (kpiList.length === 0) {
      throw new NotFoundException('KPI Code không tồn tại');
    }

    const kpiStandScore: KpiStandardScoreEntity[] = await this.kpiStandardScoreRepo.find({
      where: { kpiSetId: kpiSetId },
    });
    const data = [];
    for (const kpi of kpiList) {
      const kpiSetDetail = kpiSetDetails.find((detail) => detail.kpiId === kpi.id);
      if (!kpiSetDetail) continue;

      const kpiSetGroup = dictKpiSetGroup[kpiSetDetail.kpiSetGroupId];
      const lstKpiScore = kpiStandScore.filter((i) => i.kpiCodeId === kpi.id);
      // if (lstKpiScore.length > 0) {
      //   const lstEffectiveDate = lstKpiScore.reduce((acc, item) => {
      //     if (
      //       !acc.some(
      //         e =>
      //           e.effectiveDateFrom?.getTime() === item.effectiveDateFrom?.getTime() &&
      //           e.effectiveDateTo?.getTime() === item.effectiveDateTo?.getTime(),
      //       )
      //     ) {
      //       acc.push(item);
      //     }
      //     return acc;
      //   }, []);
      //   for (const effectiveDate of lstEffectiveDate) {
      //     const item = {
      //       kpiSetId: kpiSetHeader.id,
      //       kpiSetName: kpiSetHeader.shortName,
      //       kpiSetGroupId: kpiSetGroup.id,
      //       kpiSetGroupName: `${kpiSetGroup?.kpiGroupCode} - ${kpiSetGroup?.kpiGroupName}`,
      //       kpiId: kpi.id,
      //       kpiCode: kpi.code,
      //       kpiName: kpi.longName,
      //       unit: kpi.unit,
      //       calMethod: kpi.calMethod,
      //       weightageKpi: kpiSetDetail.weighTarget,
      //       effectiveDateFrom: effectiveDate.effectiveDateFrom,
      //       effectiveDateTo: effectiveDate.effectiveDateTo,
      //     };
      //     const lstScore = lstKpiScore.filter(i => {
      //       if (
      //         i.effectiveDateTo?.getTime() === effectiveDate.effectiveDateTo?.getTime() &&
      //         i.effectiveDateFrom?.getTime() === effectiveDate.effectiveDateFrom?.getTime()
      //       )
      //         return true;
      //       else return false;
      //     });
      //     for (const score of lstScore) {
      //       item['Level - ' + score.level] = score.levelScore;
      //       item['Reward % - ' + score.level] = score.rewardPercentage;
      //     }
      //     data.push(item);
      //   }
      // } else {
      data.push({
        kpiSetId: kpiSetHeader.id,
        kpiSetName: `${kpiSetHeader.code} - ${kpiSetHeader.longName}`,
        kpiSetGroupId: kpiSetGroup.id,
        kpiSetGroupName: `${kpiSetGroup?.kpiGroupCode} - ${kpiSetGroup?.kpiGroupName}`,
        kpiId: kpi.id,
        kpiCode: kpi.code,

        kpiName: kpi.longName,
        unit: kpi.unit,
        calMethod: kpi.calMethod,
        weightageKpi: kpiSetDetail.weighTarget,
        effectiveDateFrom: new Date(),
      });
      //}
    }
    return data.sort((a, b) => a.kpiSetGroupName.localeCompare(b.kpiSetGroupName));
  }

  async findByGroupId(kpiSetId: string, groupId: string) {
    const kpiSetHeader = await this.kpiSetHeaderRepo.findOne({ where: { id: kpiSetId } });

    if (!kpiSetHeader) {
      throw new NotFoundException('KPI Set Header not found');
    }

    // Infor KPI Set Group
    const dictKpiSetGroup = {};
    {
      const kpiSetGroup = await this.kpiSetGroupRepo.find({ where: { kpiSetHeaderId: kpiSetId } });
      if (kpiSetGroup.length === 0) {
        throw new NotFoundException('KPI Set Group not found');
      }
      Object.assign(
        dictKpiSetGroup,
        Object.fromEntries(kpiSetGroup.map((item) => [item.id, item])),
      );
    }

    // Infor KPI Set Detail
    const kpiSetDetails = await this.kpiSetDetailRepo.find({
      where: { kpiSetGroupId: In(Object.keys(dictKpiSetGroup)) },
    });
    if (kpiSetDetails.length === 0) {
      throw new NotFoundException('KPI Set Detail not found');
    }

    // Infor KPI List (kpiCodeId)
    const kpiIds = kpiSetDetails.map((detail) => detail.kpiId);
    const kpiList = await this.kpiRepo.find({ where: { id: In(kpiIds) } });
    if (kpiList.length === 0) {
      throw new NotFoundException('KPI Code không tồn tại');
    }

    const kpiStandScore: KpiStandardScoreEntity[] = await this.kpiStandardScoreRepo.find({
      where: { groupId: groupId },
    });
    const data = [];
    for (const kpi of kpiList) {
      const kpiSetDetail = kpiSetDetails.find((detail) => detail.kpiId === kpi.id);
      if (!kpiSetDetail) continue;

      const kpiSetGroup = dictKpiSetGroup[kpiSetDetail.kpiSetGroupId];
      const lstKpiScore = kpiStandScore.filter((i) => i.kpiCodeId === kpi.id);
      if (lstKpiScore.length > 0) {
        const lstEffectiveDate = lstKpiScore.reduce((acc, item) => {
          const effectiveDateFrom =
            item.effectiveDateFrom instanceof Date ? item.effectiveDateFrom.getTime() : null;
          const effectiveDateTo =
            item.effectiveDateTo instanceof Date ? item.effectiveDateTo.getTime() : null;
          if (
            !acc.some(
              (e) =>
                (e.effectiveDateFrom instanceof Date ? e.effectiveDateFrom.getTime() : null) ===
                  effectiveDateFrom &&
                (e.effectiveDateTo instanceof Date ? e.effectiveDateTo.getTime() : null) ===
                  effectiveDateTo,
            )
          ) {
            acc.push(item);
          }
          return acc;
        }, []);
        for (const effectiveDate of lstEffectiveDate) {
          const item = {
            kpiSetId: kpiSetHeader.id,
            kpiSetName: `${kpiSetHeader.code} - ${kpiSetHeader.longName}`,
            kpiSetGroupId: kpiSetGroup.id,
            kpiSetGroupName: `${kpiSetGroup?.kpiGroupCode} - ${kpiSetGroup?.kpiGroupName}`,
            kpiId: kpi.id,
            kpiCode: kpi.code,
            kpiName: kpi.longName,
            unit: kpi.unit,
            calMethod: kpi.calMethod,
            weightageKpi: kpiSetDetail.weighTarget,
            effectiveDateFrom: effectiveDate.effectiveDateFrom,
            effectiveDateTo: effectiveDate.effectiveDateTo,
          };
          const lstScore = lstKpiScore.filter((i) => {
            const effectiveFrom = i.effectiveDateFrom
              ? new Date(i.effectiveDateFrom).getTime()
              : null;
            const effectiveTo = i.effectiveDateTo ? new Date(i.effectiveDateTo).getTime() : null;
            const compareFrom = effectiveDate.effectiveDateFrom
              ? new Date(effectiveDate.effectiveDateFrom).getTime()
              : null;
            const compareTo = effectiveDate.effectiveDateTo
              ? new Date(effectiveDate.effectiveDateTo).getTime()
              : null;

            return effectiveFrom === compareFrom && effectiveTo === compareTo;
          });

          for (const score of lstScore) {
            item['Level - ' + score.level] = score.levelScore;
            item['Reward % - ' + score.level] = score.rewardPercentage;
          }
          data.push(item);
        }
      } else {
        data.push({
          kpiSetId: kpiSetHeader.id,
          kpiSetName: kpiSetHeader.shortName,
          kpiSetGroupId: kpiSetGroup.id,
          kpiSetGroupName: `${kpiSetGroup?.kpiGroupCode} - ${kpiSetGroup?.kpiGroupName}`,
          kpiId: kpi.id,
          kpiCode: kpi.code,
          kpiName: kpi.longName,
          unit: kpi.unit,
          calMethod: kpi.calMethod,
          weightageKpi: kpiSetDetail.weighTarget,
          effectiveDateFrom: new Date(),
        });
      }
    }
    return data.sort((a, b) => a.kpiSetGroupName.localeCompare(b.kpiSetGroupName));
  }

  async findById(id: string) {
    try {
      const kpiStandardScores = await this.kpiStandardScoreRepo.find({
        where: { kpiStandardId: id },
      });

      if (!kpiStandardScores.length) {
        throw new Error('KPI Standard Score Not Found!');
      }
      for (const item of kpiStandardScores) {
        if (item.kpiSetId) {
          const kpiSetHeader = await this.kpiSetHeaderRepo.findOne({
            where: { id: item.kpiSetId },
          });
          (item as any).name = kpiSetHeader ? kpiSetHeader.longName : null;
        }
      }

      return kpiStandardScores;
    } catch (error) {
      throw new Error(error.message || '');
    }
  }

  async pagination(params: PaginationReq) {
    const whereSet: any = {};
    if (params.setCodeId) whereSet.id = params.setCodeId;

    const lstSetHeader = await this.kpiSetHeaderRepo.find({ where: whereSet });
    const whereCon: any = {};
    if (lstSetHeader.length > 0) whereCon.kpiSetId = In(lstSetHeader.map((i) => i.id));

    let { data, total } = await this.repo.findPagination(
      { where: whereCon, order: { createdDate: 'DESC' } },
      params,
    );

    if (!data.length) {
      return { data, total };
    }

    const uniqueData = Array.from(new Map(data.map((kpi) => [kpi.groupId, kpi])).values());

    const kpiSetIds = uniqueData.map((kpi) => kpi.kpiSetId).filter(Boolean);
    const kpiSetHeaders = kpiSetIds.length
      ? await this.kpiSetHeaderRepo.find({ where: { id: In(kpiSetIds) } })
      : [];
    const kpiSetHeaderMap = new Map(kpiSetHeaders.map((header) => [header.id, header]));

    const kpiStandardIds = uniqueData.map((kpi) => kpi.groupId).filter(Boolean);
    let minKpiScores: Record<string, any> = {};
    let maxKpiScores: Record<string, any> = {};

    if (kpiStandardIds.length) {
      const minScores = await this.kpiStandardScoreRepo.find({
        where: { groupId: In(kpiStandardIds) },
        order: { effectiveDateFrom: 'ASC' },
      });

      const maxScores = await this.kpiStandardScoreRepo.find({
        where: { groupId: In(kpiStandardIds) },
        order: { effectiveDateTo: 'DESC' },
      });

      minKpiScores = Object.fromEntries(minScores.map((score) => [score.groupId, score]));
      const groupedMaxScores: Record<string, (Date | null)[]> = {};
      for (const score of maxScores) {
        if (!groupedMaxScores[score.groupId]) {
          groupedMaxScores[score.groupId] = [];
        }
        groupedMaxScores[score.groupId].push(score.effectiveDateTo);
      }

      maxKpiScores = Object.fromEntries(
        Object.entries(groupedMaxScores).map(([kpiStandardId, dates]) => {
          const validDates = dates
            .filter((d) => d !== null)
            .map((d) => new Date(d as unknown as string)); // Chuyển string thành Date

          return [
            kpiStandardId,
            validDates.length ? new Date(Math.max(...validDates.map((d) => d.getTime()))) : null,
          ];
        }),
      );
    }

    for (const kpi of uniqueData) {
      if (kpi.kpiSetId) {
        const kpiSetHeader = kpiSetHeaderMap.get(kpi.kpiSetId);
        (kpi as any).setCode = kpiSetHeader
          ? `${kpiSetHeader.code} - ${kpiSetHeader.longName}`
          : null;
        (kpi as any).shortName = kpiSetHeader ? kpiSetHeader.shortName : null;
      }

      if (kpi.groupId) {
        (kpi as any).effectiveDateFrom = minKpiScores[kpi.groupId]?.effectiveDateFrom || null;
        (kpi as any).effectiveDateTo = maxKpiScores[kpi.groupId] || null;
      }
    }

    return { data: uniqueData, total: uniqueData.length };
  }

  async delete(groupId: string) {
    const count = await this.kpiStandardScoreRepo.count({
      where: { groupId },
    });

    if (count > 0) {
      throw new BusinessException('KPI Standard Score exists you cannot delete');
    }

    await this.kpiStandardScoreRepo.delete({ groupId: groupId });
    await this.repo.delete({ groupId });

    return { message: 'Create success' };
  }

  /** Kiểm tra xem kpi set detail đã được tạo data trong kpi standard chưa */
  async checkTakenKpiScoreByKpiSetDetail(id: string) {
    const [checkKpiStandScore, checkKpiScore] = await Promise.all([
      this.kpiStandardScoreRepo.count({ where: { kpiCodeId: id } }),
      this.kpiScoreRepo.count({ where: { kpiSetDetailId: id } }), //check thêm kpiScore
    ]);

    return { isTaken: checkKpiStandScore > 0 || checkKpiScore > 0 };
  }

  /** Kiểm tra xem kpi set group đã được tạo data trong kpi standard chưa */
  async checkTakenKpiStandardByKpiSetGroup(id: string) {
    const [checkKpi, checkKpiScore] = await Promise.all([
      this.repo.count({ where: { kpiSetGroupId: id } }),
      this.kpiScoreRepo.count({ where: { kpiSetGroupId: id } }), //check thêm kpiScore
    ]);

    return { isTaken: checkKpi > 0 || checkKpiScore > 0 };
  }

  /** Kiểm tra xem kpi set đã được tạo data trong kpi standard chưa */
  async checkTakenKpiStandardByKpiSet(id: string) {
    const [checkKpi, checkKpiScore] = await Promise.all([
      this.repo.count({ where: { kpiSetId: id } }),
      this.kpiScoreRepo.count({ where: { kpiSetHeaderId: id } }), //check thêm kpiScore
    ]);

    return { isTaken: checkKpi > 0 || checkKpiScore > 0 };
  }
}
