import { <PERSON>ti<PERSON>, Column, <PERSON><PERSON><PERSON><PERSON>ne, Join<PERSON><PERSON>umn, Index } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { ProductionOrderEntity } from './production-order.entity'; // Import ProductionOrder entity
import { PrimaryBaseEntity } from '../primary-base.entity';
import { UrlEntity } from './url.entity';

@Index(['uniqueCode'], { unique: true })
@Entity('production_order_unique_code_detail')
export class ProductionOrderUniqueCodeDetailEntity extends PrimaryBaseEntity {
  /** Mỗi Production Order được tạo, thông tin detail của Production Order được lưu vào bảng Material. Order id link từ bảng Production Order. */
  @ApiProperty({ description: 'ID lệnh sản xuất (liên kết đến ProductionOrder)' })
  @Column({ type: 'uuid', nullable: true })
  orderId: string;

  @ApiProperty({ description: 'Id bảng url' })
  @Column({ type: 'uuid', nullable: true })
  urlId: string;

  @ApiProperty({ description: 'File name được upload' })
  @Column({ nullable: true })
  fileName: string;

  /** Polulated theo Order id từ bảng Production Order. */
  @ApiProperty({ description: 'Phiên bản công thức (lấy từ ProductionOrder)' })
  @Column()
  uniqueCode: string;

  // /** Relation với bảng GeneralDataDetail */
  @ManyToOne(() => ProductionOrderEntity)
  @JoinColumn({ name: 'orderId', referencedColumnName: 'id' })
  productionOrder: ProductionOrderEntity;

  @ManyToOne(() => UrlEntity)
  @JoinColumn({ name: 'urlId', referencedColumnName: 'id' })
  url: UrlEntity;
}
