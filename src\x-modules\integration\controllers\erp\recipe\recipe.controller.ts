import { ApiTags } from '@nestjs/swagger';
import { DefController, DefGet, DefPost } from '~/@core/decorator';
import { Body, Inject, UseGuards } from '@nestjs/common';
import { ERPCreateRecipeReq } from '~/dto/erp/recipe.dto';
import { ERPRecipeIntegrateService } from '~/x-modules/integration/services';
import { ApiTokenGuard } from '~/@core/decorator/api-token.decorator';

@ApiTags('ERPRecipe')
@UseGuards(ApiTokenGuard)
@DefController('erp/recipe')
export class ERPRecipeController {
  constructor(
    @Inject(ERPRecipeIntegrateService)
    private readonly integrateService: ERPRecipeIntegrateService,
  ) {}

  @DefPost('', { summary: 'Tạo/cập nhật công thức' })
  async create(@Body() data: ERPCreateRecipeReq) {
    return this.integrateService.createOrUpdate(data);
  }
}
