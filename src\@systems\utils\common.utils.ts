import dayjs, { Dayjs } from 'dayjs';

const getKeyEnumByValue = <T = any>(targetEnum: T, valueFind: any) => {
  return Object.keys(targetEnum)[Object.values(targetEnum).indexOf(valueFind)] || '';
};

const validateVietNamese = (str: string): boolean => {
  const vietnameseText =
    /[A-Za-zÀÁÂÃÈÉÊÌÍÒÓÔÕÙÚĂĐĨŨƠàáâãèéêìíòóôõùúăđĩũơƯĂẠẢẤẦẨẪẬẮẰẲẴẶẸẺẼỀỀỂưăạảấầẩẫậắằẳẵặẹẻẽềềểỄỆỈỊỌỎỐỒỔỖỘỚỜỞỠỢỤỦỨỪễệỉịọỏốồổỗộớờởỡợụủứừỬỮỰỲỴÝỶỸửữựỳỵýỷỹ0-9.,!?""''()…\s-]+/;
  const isMatch = str.match(vietnameseText);
  return isMatch ? true : false;
};

const convertToDisplayTimezone = (date?: Date | string, offset = 7): Dayjs => {
  return dayjs(date).add(offset, 'hour');
};

const getFirstDayTz = (date: Date): any => {
  const fd = new Date(new Date(date).setHours(0, 0, 0, 0));
  // const fdTz = convertToDisplayTimezone(fd, -7);
  return fd;
};

const getLastDayTz = (date: Date): any => {
  const ld = new Date(new Date(date).setHours(23, 59, 59));
  // const ldTz = convertToDisplayTimezone(ld, -7);
  return ld;
};

export {
  getKeyEnumByValue,
  validateVietNamese,
  convertToDisplayTimezone,
  getFirstDayTz,
  getLastDayTz,
};
