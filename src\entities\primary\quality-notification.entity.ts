import { ApiProperty } from '@nestjs/swagger';
import { Column, Entity } from 'typeorm';
import { PrimaryBaseEntity } from '../primary-base.entity';

@Entity('quality_notification')
export class QualityNotificationEntity extends PrimaryBaseEntity {
  @ApiProperty({ description: 'Quality Notification No' })
  @Column({ nullable: false })
  qualityNotificationNo: string;

  @ApiProperty({ description: 'ID inspection plan' })
  @Column({ nullable: false, type: 'uuid' })
  inspectionPlanId: string;

  @ApiProperty({ description: 'Inspection plan type' })
  @Column({ nullable: true })
  inspectionPlanType: string;

  @ApiProperty({ description: 'Sample No' })
  @Column({ nullable: true })
  sampleNo: string;

  @ApiProperty({ description: 'ID Production Order' })
  @Column({ nullable: false, type: 'uuid' })
  productionOrderId: string;

  @ApiProperty({ description: 'ID Production Batch' })
  @Column({ nullable: true, type: 'uuid' })
  productionBatchId: string;

  @ApiProperty({ description: 'Result' })
  @Column({ nullable: true })
  result: string;

  @ApiProperty({ description: 'QLONE Update Date' })
  @Column({ nullable: true, type: 'timestamptz' })
  qloneUpdateDate: Date;

  @ApiProperty({ description: 'QLONE Update By' })
  @Column({ nullable: true })
  qloneUpdateBy: string;

  @ApiProperty({ description: 'Status' })
  @Column({ nullable: true, type: 'numeric', default: 1 })
  status: number;

  @ApiProperty({ description: 'ID Site' })
  @Column({ nullable: true, type: 'uuid' })
  siteId: string;

  @ApiProperty({ description: 'ID Factory' })
  @Column({ nullable: true, type: 'uuid' })
  factoryId: string;

  @ApiProperty({ description: 'ID Production Line' })
  @Column({ nullable: true, type: 'uuid' })
  productionLineId: string;

  @ApiProperty({ description: 'ID Production Area' })
  @Column({ nullable: true, type: 'uuid' })
  productionAreaId: string;

  @ApiProperty({ description: 'ID Process' })
  @Column({ nullable: true, type: 'uuid' })
  processId: string;

  @ApiProperty({ description: 'ID Machine' })
  @Column({ nullable: true, type: 'uuid' })
  machineId: string;

  @ApiProperty({ description: 'ID Machine Parameter' })
  @Column({ nullable: true, type: 'uuid' })
  machineParameterId: string;

  @ApiProperty({ description: 'Tag Address' })
  @Column({ nullable: true })
  tagAddress: string;

  @ApiProperty({ description: 'ID Item' })
  @Column({ nullable: true, type: 'uuid' })
  itemId: string;

  @ApiProperty({ description: 'Request Body call QLONE' })
  @Column({ nullable: true, type: 'text' })
  reqBody: string;
}
