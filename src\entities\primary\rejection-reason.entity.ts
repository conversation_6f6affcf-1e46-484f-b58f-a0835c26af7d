import { ApiProperty } from '@nestjs/swagger';
import { Column, Entity } from 'typeorm';
import { PrimaryBaseEntity } from '../primary-base.entity';

/** <PERSON><PERSON> do từ chối tích hợp từ QLONE */
@Entity('rejection_reason')
export class RejectionReasonEntity extends PrimaryBaseEntity {
  @ApiProperty({ description: 'Mã' })
  @Column({ type: 'varchar', length: 100, nullable: true })
  code: string;

  @ApiProperty({ description: 'Tên' })
  @Column({ type: 'text', nullable: true })
  name: string;

  /** Trạng thái sử dụng: 1-Active 0-Inactive */
  @ApiProperty({ description: 'Trạng thái sử dụng' })
  @Column({ type: 'int', nullable: true })
  status: number;
}
