import { Column, Entity } from 'typeorm';
import { PrimaryBaseEntity } from '~/entities/primary-base.entity';
import { ApiProperty } from '@nestjs/swagger';

@Entity('sitewise_model')
export class SitewiseModelEntity extends PrimaryBaseEntity {
  /** Mã sitewise model */
  @Column({ type: 'text', unique: true })
  code: string;

  /** Mô tả sitewise model */
  @Column({ name: 'description' })
  description: string;

  /** Mã AWS */
  @Column({ name: 'awsId' })
  awsId: string;

  /** Trạng thái hoạt động sitewise model */
  @ApiProperty({ description: 'Trạng thái hoạt động', default: true })
  @Column({ default: true })
  isActive: boolean;
}
