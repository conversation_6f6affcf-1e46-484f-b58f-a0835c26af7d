{"version": "0.2.0", "js/ts.implicitProjectConfig.experimentalDecorators": true, "editor.formatOnSave": true, "debug.javascript.autoAttachFilter": "disabled", "typescript.preferences.importModuleSpecifier": "relative", "editor.tabSize": 2, "prettier.printWidth": 150, "jest.autoRun": "off", "configurations": [{"name": "Debug ngay trong tiến trình đang chạy", "type": "node", "request": "attach", "address": "localhost", "processId": "${command:PickProcess}", "port": "${env:DEBUG_PORT:9229}", "restart": true}], "cSpell.words": ["ILIKE"]}