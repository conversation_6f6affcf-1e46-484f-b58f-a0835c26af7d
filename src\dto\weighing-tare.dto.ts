import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsArray,
  IsBoolean,
  IsIP,
  IsNotEmpty,
  IsNumber,
  IsOptional,
  IsString,
  IsUUID,
  MaxLength,
  ValidateNested,
} from 'class-validator';
import { PageRequest } from '~/@systems/utils';
import { NSGeneralData } from '~/common/enums';

export class ListWeighingTareReq extends PageRequest {
  code?: string;
  name?: string;
  type?: string;
  isActive?: boolean;
}

export class WeighingTareReq {
  @ApiProperty({ description: 'Mã của weighing tare, dùng để định danh', maxLength: 50 })
  @IsString()
  @MaxLength(50)
  code: string;

  @ApiProperty({ description: 'General data detail', maxLength: 50 })
  @IsString()
  @MaxLength(50)
  weighingTareTypeDetailCode: string;

  @ApiProperty({ description: 'General data detail', maxLength: 50 })
  @IsString()
  @MaxLength(50)
  uomDetailCode: string;

  @ApiPropertyOptional({ description: '<PERSON><PERSON> tả (có thể null)', type: String })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({ description: 'Ghi chú thêm ', type: String })
  @IsOptional()
  @IsString()
  note?: string;

  @ApiPropertyOptional({ description: 'Value', type: Number })
  @IsOptional()
  value?: number;

  @ApiProperty({ description: 'Danh sách sản phẩm nguyên vật liệu' })
  lstItem: ItemDto[];

  @ApiPropertyOptional({
    description: 'Trạng thái hoạt động, mặc định true',
    type: Boolean,
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  isActive: boolean = true;
}
export class ItemDto {
  /** Mã sản phẩm */
  @ApiProperty({ description: 'Mã sản phẩm' })
  id: string;

  /** Trạng thái sản phẩm */
  @ApiProperty({ description: 'Trạng thái sản phẩm' })
  isAssigned: boolean;

  /** Người tạo */
  @ApiPropertyOptional({ description: 'Người tạo' })
  @IsOptional()
  createdBy?: string;

  /** Ngày tạo */
  @ApiPropertyOptional({ description: 'Ngày tạo' })
  @IsOptional()
  createdDate?: string;
}
