import { HttpStatus } from '@nestjs/common';

export const EHttpStatusMessage: Record<HttpStatus, string> = {
  [HttpStatus.OK]: 'OK',
  [HttpStatus.CREATED]: 'CREATED',
  [HttpStatus.ACCEPTED]: 'ACCEPTED',
  [HttpStatus.NO_CONTENT]: 'NO_CONTENT',
  [HttpStatus.RESET_CONTENT]: 'RESET_CONTENT',
  [HttpStatus.PARTIAL_CONTENT]: 'PARTIAL_CONTENT',
  [HttpStatus.BAD_REQUEST]: 'BAD_REQUEST',
  [HttpStatus.UNAUTHORIZED]: 'UNAUTHORIZED',
  [HttpStatus.PAYMENT_REQUIRED]: 'PAYMENT_REQUIRED',
  [HttpStatus.FORBIDDEN]: 'FORBIDDEN',
  [HttpStatus.NOT_FOUND]: 'NOT_FOUND',
  [HttpStatus.METHOD_NOT_ALLOWED]: 'METHOD_NOT_ALLOWED',
  [HttpStatus.NOT_ACCEPTABLE]: 'NOT_ACCEPTABLE',
  [HttpStatus.PROXY_AUTHENTICATION_REQUIRED]: 'PROXY_AUTHENTICATION_REQUIRED',
  [HttpStatus.REQUEST_TIMEOUT]: 'REQUEST_TIMEOUT',
  [HttpStatus.CONFLICT]: 'CONFLICT',
  [HttpStatus.GONE]: 'GONE',
  [HttpStatus.LENGTH_REQUIRED]: 'LENGTH_REQUIRED',
  [HttpStatus.PRECONDITION_FAILED]: 'PRECONDITION_FAILED',
  [HttpStatus.PAYLOAD_TOO_LARGE]: 'PAYLOAD_TOO_LARGE',
  [HttpStatus.URI_TOO_LONG]: 'URI_TOO_LONG',
  [HttpStatus.UNSUPPORTED_MEDIA_TYPE]: 'UNSUPPORTED_MEDIA_TYPE',
  [HttpStatus.EXPECTATION_FAILED]: 'EXPECTATION_FAILED',
  [HttpStatus.I_AM_A_TEAPOT]: 'I_AM_A_TEAPOT',
  [HttpStatus.UNPROCESSABLE_ENTITY]: 'UNPROCESSABLE_ENTITY',
  [HttpStatus.TOO_MANY_REQUESTS]: 'TOO_MANY_REQUESTS',
  [HttpStatus.INTERNAL_SERVER_ERROR]: 'INTERNAL_SERVER_ERROR',
  [HttpStatus.NOT_IMPLEMENTED]: 'NOT_IMPLEMENTED',
  [HttpStatus.BAD_GATEWAY]: 'BAD_GATEWAY',
  [HttpStatus.SERVICE_UNAVAILABLE]: 'SERVICE_UNAVAILABLE',
  [HttpStatus.GATEWAY_TIMEOUT]: 'GATEWAY_TIMEOUT',
  [HttpStatus.HTTP_VERSION_NOT_SUPPORTED]: 'HTTP_VERSION_NOT_SUPPORTED',
  [HttpStatus.NON_AUTHORITATIVE_INFORMATION]: 'NON_AUTHORITATIVE_INFORMATION',
  [HttpStatus.CONTINUE]: 'CONTINUE',
  [HttpStatus.SWITCHING_PROTOCOLS]: 'SWITCHING_PROTOCOLS',
  [HttpStatus.PROCESSING]: 'PROCESSING',
  [HttpStatus.EARLYHINTS]: 'EARLY_HINTS',
  [HttpStatus.AMBIGUOUS]: 'AMBIGUOUS',
  [HttpStatus.MOVED_PERMANENTLY]: 'MOVED_PERMANENTLY',
  [HttpStatus.FOUND]: 'FOUND',
  [HttpStatus.SEE_OTHER]: 'SEE_OTHER',
  [HttpStatus.NOT_MODIFIED]: 'NOT_MODIFIED',
  [HttpStatus.TEMPORARY_REDIRECT]: 'TEMPORARY_REDIRECT',
  [HttpStatus.PERMANENT_REDIRECT]: 'PERMANENT_REDIRECT',
  [HttpStatus.REQUESTED_RANGE_NOT_SATISFIABLE]: 'REQUESTED_RANGE_NOT_SATISFIABLE',
  [HttpStatus.MISDIRECTED]: 'MISDIRECTED',
  [HttpStatus.FAILED_DEPENDENCY]: 'FAILED_DEPENDENCY',
  [HttpStatus.PRECONDITION_REQUIRED]: 'PRECONDITION_REQUIRED',
};
