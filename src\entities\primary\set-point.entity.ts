import { ApiProperty } from '@nestjs/swagger';
import { Column, Entity, Join<PERSON><PERSON>umn, ManyToOne } from 'typeorm';
import {
  ItemEntity,
  MachineEntity,
  MachineParameterEntity,
  OrganizationUnitEntity,
  ProcessEntity,
  RecipeEntity,
} from '.';
import { PrimaryBaseEntity } from '../primary-base.entity';

@Entity('set_point')
export class SetPointEntity extends PrimaryBaseEntity {
  @ApiProperty({ description: 'ID của organize unit' })
  @Column({ nullable: true })
  organizeUnitId: string;

  @ManyToOne(() => OrganizationUnitEntity, (p) => p.setPoints)
  @JoinColumn({ name: 'organizeUnitId', referencedColumnName: 'id' })
  organizeUnit: Promise<OrganizationUnitEntity>;

  @ApiProperty({ description: 'Id cua recipe' })
  @Column({ nullable: true })
  recipeId: string;

  @ManyToOne(() => RecipeEntity, (p) => p.id)
  @JoinColumn({ name: 'recipeId', referencedColumnName: 'id' })
  recipe: Promise<RecipeEntity>;

  @ApiProperty({ description: 'Id cua item' })
  @Column({ nullable: true })
  itemId: string;

  @ManyToOne(() => ItemEntity, (p) => p.id)
  @JoinColumn({ name: 'itemId', referencedColumnName: 'id' })
  item: Promise<ItemEntity>;

  @ApiProperty({ description: 'Id cua process' })
  @Column()
  processId: string;

  @ManyToOne(() => ProcessEntity, (p) => p.id)
  @JoinColumn({ name: 'processId', referencedColumnName: 'id' })
  process: Promise<ProcessEntity>;

  @ApiProperty({ description: 'Id cua machine' })
  @Column()
  machineId: string;

  @ManyToOne(() => MachineEntity, (p) => p.id)
  @JoinColumn({ name: 'machineId', referencedColumnName: 'id' })
  machine: Promise<MachineEntity>;

  @ApiProperty({ description: 'Id cua machine parameter' })
  @Column()
  machineParameterId: string;

  @ManyToOne(() => MachineParameterEntity, (p) => p.id)
  @JoinColumn({ name: 'machineParameterId', referencedColumnName: 'id' })
  machineParameter: Promise<MachineParameterEntity>;

  @ApiProperty({ description: 'Giá trị mục tiêu' })
  @Column({ type: 'decimal' })
  targetValue: number;

  @ApiProperty({ description: 'Giá trị tối thiểu' })
  @Column({ type: 'decimal' })
  min: number;

  @ApiProperty({ description: 'Giá trị tối đa' })
  @Column({ type: 'decimal' })
  max: number;

  @ApiProperty({ description: 'Trạng thái hoạt động', default: true })
  @Column({ default: true })
  isActive: boolean;

  @ApiProperty({ description: 'Type khi tạo set point' })
  @Column({ default: 'RECIPE' })
  type: string;
}
