import { Column, Entity, Index, OneToMany } from 'typeorm';
import { PrimaryBaseEntity } from '~/entities/primary-base.entity';
import { ApiProperty } from '@nestjs/swagger';
import { WeighingTareDetailEntity } from '~/entities/primary/weighing-tare-detail.entity';

// Weighing Tare entity
@Entity('weighing_tare')
export class WeighingTareEntity extends PrimaryBaseEntity {
  @ApiProperty({ description: 'Mã Weighing Tare' })
  @Column({ unique: true, length: 50 })
  @Index()
  code: string;

  @ApiProperty({ description: 'Mã General master data' })
  @Column({ length: 50 })
  weighingTareTypeCode: string;

  @ApiProperty({ description: 'Mã General master data detail' })
  @Column({ length: 50 })
  weighingTareTypeDetailCode: string;

  @ApiProperty({ description: 'UOM code' })
  @Column({ length: 50 })
  uomCode: string;

  @ApiProperty({ description: 'UOM detail code' })
  @Column({ length: 50 })
  uomDetailCode: string;

  @Column({ type: 'decimal' })
  value: number;
  /** <PERSON><PERSON> tả (có thể null) */
  @Column({ type: 'text', default: '', nullable: true })
  description?: string;

  @ApiProperty({ description: 'Ghi chú' })
  @Column({ type: 'text', default: '', nullable: true })
  note?: string;

  @ApiProperty({ description: 'Trạng thái hoạt động' })
  @Column({ default: true })
  isActive: boolean;

  @OneToMany(() => WeighingTareDetailEntity, (e) => e.weighingTare)
  details: Promise<WeighingTareDetailEntity[]>;
}
