import { Injectable } from '@nestjs/common';
import { v4 as uuidv4 } from 'uuid';
import { In, Like, Not, Raw } from 'typeorm';
import { BindRepo, DefTransaction } from '~/@core/decorator';
import { BusinessException } from '~/@systems/exceptions';
import { CreateKpiSetDto, KpiSetPageReq, UpdateKpiSetDto } from '~/dto/kpi-set.dto';
import {
  KpiSetDetailEntity,
  KpiSetGroupEntity,
  KpiSetHeaderEntity,
  KpiSetProductAreaEntity,
} from '~/entities/primary';
import {
  AccessRepo,
  GeneralDataDetailRepo,
  GeneralDataRepo,
  KpiScoreRepo,
  KpiSetDetailRepo,
  KpiSetGroupRepo,
  KpiSetHeaderRepo,
  KpiSetProductAreaRepo,
  KpiStandardRepo,
  OrganizationUnitRepo,
  ProductionAreaRepo,
} from '~/repositories/primary';
import { NSGeneralData } from '~/common/enums';
@Injectable()
export class KPISetService {
  constructor() {}

  @BindRepo(KpiSetHeaderRepo)
  private repo: KpiSetHeaderRepo;
  @BindRepo(KpiSetGroupRepo)
  private kpiSetGroupRepo: KpiSetGroupRepo;
  @BindRepo(KpiSetDetailRepo)
  private kpiSetDetailRepo: KpiSetDetailRepo;
  @BindRepo(KpiSetProductAreaRepo)
  private kpiSetProductAreaRepo: KpiSetProductAreaRepo;

  @BindRepo(OrganizationUnitRepo)
  private organizationUnitRepo: OrganizationUnitRepo;

  @BindRepo(ProductionAreaRepo)
  private productionAreaRepo: ProductionAreaRepo;

  @BindRepo(GeneralDataRepo)
  private generalDataRepo: GeneralDataRepo;
  @BindRepo(GeneralDataDetailRepo)
  private generalDataDetailRepo: GeneralDataDetailRepo;

  @BindRepo(KpiStandardRepo)
  private kpiStandardRepo: KpiStandardRepo;

  @BindRepo(KpiScoreRepo)
  private kpiScoreRepo: KpiScoreRepo;

  @BindRepo(AccessRepo)
  private accessRepo: AccessRepo;

  @DefTransaction()
  async create(data: CreateKpiSetDto) {
    let lstProductionAreaId = [];

    const checkCode = await this.repo.findOne({
      where: { code: data.code },
    });
    if (checkCode) throw new BusinessException('KPI Set Code đã tồn tại');

    // Validation và Tạo dict production area
    for (let group of data.factoryProcessAreas) {
      lstProductionAreaId.push(group.productionAreaId);
    }
    lstProductionAreaId = [...new Set(lstProductionAreaId)];
    const lstProArea = await this.productionAreaRepo.find({
      where: { id: In(lstProductionAreaId), isActive: true },
    });
    if (lstProductionAreaId.length !== lstProArea.length)
      throw new BusinessException('Not found production area');
    const mapProductionArea = new Map(lstProArea.map((pro) => [pro.id, pro]));

    // Validation và Tạo dict general data
    let lstKpiGroupId = [];
    for (let group of data.kpiSetGroups) {
      lstKpiGroupId.push(group.kpiGroupId);
    }
    lstKpiGroupId = [...new Set(lstKpiGroupId)];
    const lstKpiGroup = await this.generalDataDetailRepo.find({
      where: { id: In(lstKpiGroupId), isActive: true },
    });
    if (lstKpiGroupId.length !== lstKpiGroup.length)
      throw new BusinessException('Not found Kpi Group');
    const mapKpiGroup = new Map(lstKpiGroup.map((group) => [group.id, group]));

    const newKpiSet = new KpiSetHeaderEntity();
    newKpiSet.code = data.code;
    newKpiSet.shortName = data.shortName;
    newKpiSet.longName = data.longName;
    newKpiSet.budget = data.budget;
    newKpiSet.status = data.status ? 1 : 0;
    newKpiSet.id = uuidv4();
    // newKpiSet.createdBy = 'admin';
    await this.repo.insert(newKpiSet);

    const lstProAreaInsert = [];
    for (let group of data.factoryProcessAreas) {
      const newProArea = new KpiSetProductAreaEntity();
      newProArea.kpiSetHeaderId = newKpiSet.id;
      newProArea.productionAreaId = group.productionAreaId;
      newProArea.status = group.status ? 1 : 0;
      newProArea.factoryId = mapProductionArea.get(group.productionAreaId)?.organizationUnitId;
      newProArea.siteId = group.siteId;
      // newProArea.createdBy = 'admin';
      lstProAreaInsert.push(newProArea);
    }
    await this.kpiSetProductAreaRepo.insert(lstProAreaInsert);

    const lstKpiGroupInsert = [];
    const lstKpiDetailInsert = [];
    let totalPercent = 0;
    for (let group of data.kpiSetGroups) {
      const curKpiGroup = mapKpiGroup.get(group.kpiGroupId);
      const newKpiGroup = new KpiSetGroupEntity();
      newKpiGroup.kpiSetHeaderId = newKpiSet.id;
      newKpiGroup.kpiGroupId = group.kpiGroupId;
      newKpiGroup.kpiGroupCode = curKpiGroup?.code;
      newKpiGroup.kpiGroupName = curKpiGroup?.name;
      newKpiGroup.budget = group.budget;
      newKpiGroup.weighTarget = +group.weighTarget || 0;
      newKpiGroup.budget = +group.budget || 0;
      newKpiGroup.id = uuidv4();
      // newKpiGroup.createdBy = 'admin';
      totalPercent += newKpiGroup.weighTarget;
      lstKpiGroupInsert.push(newKpiGroup);

      let totalPercentDetail = 0;
      for (let detail of group.details) {
        const newDetail = new KpiSetDetailEntity();
        newDetail.kpiSetGroupId = newKpiGroup.id;
        newDetail.weighTarget = +detail.weighTarget || 0;
        newDetail.budget = +detail.budget || 0;
        newDetail.kpiId = detail.kpiId;
        // newDetail.createdBy = 'admin';
        totalPercentDetail += newDetail.weighTarget;
        lstKpiDetailInsert.push(newDetail);
      }

      if (totalPercentDetail !== newKpiGroup.weighTarget && group.details?.length) {
        throw new BusinessException(
          'Tỷ trọng chia thưởng và số tiền chia thưởng cho KPI chưa đúng',
        );
      }
    }

    if (totalPercent !== 100 && data.kpiSetGroups?.length) {
      throw new BusinessException(
        'Tỷ trọng chia thưởng và số tiền chia thưởng cho nhóm KPI chưa đúng',
      );
    }

    await this.kpiSetGroupRepo.insert(lstKpiGroupInsert);
    await this.kpiSetDetailRepo.insert(lstKpiDetailInsert);

    return { message: `Create KPI Set ${data.code} successfully` };
  }

  @DefTransaction()
  async update1(data: UpdateKpiSetDto) {
    const foundKpiSet = await this.repo.findOne({ where: { id: data.id } });
    if (!foundKpiSet) throw new BusinessException('Not found Kpi set');

    const checkCode = await this.repo.findOne({
      where: { code: data.code, id: Not(foundKpiSet.id) },
    });
    if (checkCode) throw new BusinessException('KPI Set Code đã tồn tại');

    // Xóa thông tin cũ
    await this.kpiSetProductAreaRepo.delete({ kpiSetHeaderId: foundKpiSet.id });
    // const checkLstKpiGroup = await this.kpiSetGroupRepo.find({
    //   where: { kpiSetHeaderId: foundKpiSet.id },
    // });
    // await this.kpiSetDetailRepo.delete({ kpiSetGroupId: In(checkLstKpiGroup.map((gr) => gr.id)) });
    // await this.kpiSetGroupRepo.delete({ kpiSetHeaderId: foundKpiSet.id });

    let lstProductionAreaId = [];
    // Validation và Tạo dict production area
    for (let group of data.factoryProcessAreas) {
      lstProductionAreaId.push(group.productionAreaId);
    }
    lstProductionAreaId = [...new Set(lstProductionAreaId)];
    const lstProArea = await this.productionAreaRepo.find({
      where: { id: In(lstProductionAreaId), isActive: true },
    });
    if (lstProductionAreaId.length !== lstProArea.length)
      throw new BusinessException('Not found production area');
    const mapProductionArea = new Map(lstProArea.map((pro) => [pro.id, pro]));

    // Validation và Tạo dict general data
    let lstKpiGroupId = [];
    for (let group of data.kpiSetGroups) {
      lstKpiGroupId.push(group.kpiGroupId);
    }
    lstKpiGroupId = [...new Set(lstKpiGroupId)];
    const lstKpiGroup = await this.generalDataDetailRepo.find({
      where: { id: In(lstKpiGroupId), isActive: true },
    });
    if (lstKpiGroupId.length !== lstKpiGroup.length)
      throw new BusinessException('Not found Kpi Group');
    const mapKpiGroup = new Map(lstKpiGroup.map((group) => [group.id, group]));

    foundKpiSet.code = data.code;
    foundKpiSet.shortName = data.shortName;
    foundKpiSet.longName = data.longName;
    foundKpiSet.budget = data.budget;
    foundKpiSet.status = data.status ? 1 : 0;
    // foundKpiSet.updatedBy = 'admin';
    foundKpiSet.updatedDate = new Date();
    await this.repo.update(foundKpiSet.id, foundKpiSet);

    const lstProAreaInsert = [];
    for (let group of data.factoryProcessAreas) {
      const newProArea = new KpiSetProductAreaEntity();
      newProArea.kpiSetHeaderId = foundKpiSet.id;
      newProArea.productionAreaId = group.productionAreaId;
      newProArea.status = group.status ? 1 : 0;
      newProArea.factoryId = mapProductionArea.get(group.productionAreaId)?.organizationUnitId;
      newProArea.siteId = group.siteId;
      // newProArea.createdBy = 'admin';
      lstProAreaInsert.push(newProArea);
    }
    await this.kpiSetProductAreaRepo.insert(lstProAreaInsert);

    // const checkLstKpiGroup = await this.kpiSetGroupRepo.find({
    //   where: { kpiSetHeaderId: foundKpiSet.id },
    // });
    // await this.kpiSetDetailRepo.delete({ kpiSetGroupId: In(checkLstKpiGroup.map((gr) => gr.id)) });
    // await this.kpiSetGroupRepo.delete({ kpiSetHeaderId: foundKpiSet.id });

    // const lstKpiGroupInsert = [];
    // const lstKpiDetailInsert = [];
    // let totalPercent = 0;
    // for (let group of data.kpiSetGroups) {
    //   const curKpiGroup = mapKpiGroup.get(group.kpiGroupId);
    //   const newKpiGroup = new KpiSetGroupEntity();
    //   newKpiGroup.kpiSetHeaderId = foundKpiSet.id;
    //   newKpiGroup.kpiGroupId = group.kpiGroupId;
    //   newKpiGroup.kpiGroupCode = curKpiGroup?.code;
    //   newKpiGroup.kpiGroupName = curKpiGroup?.name;
    //   newKpiGroup.budget = group.budget;
    //   newKpiGroup.weighTarget = +group.weighTarget || 0;
    //   newKpiGroup.budget = +group.budget || 0;
    //   newKpiGroup.id = uuidv4();
    //   newKpiGroup.createdBy = 'admin';
    //   totalPercent += newKpiGroup.weighTarget;
    //   lstKpiGroupInsert.push(newKpiGroup);

    //   let totalPercentDetail = 0;
    //   for (let detail of group.details) {
    //     const newDetail = new KpiSetDetailEntity();
    //     newDetail.kpiSetGroupId = newKpiGroup.id;
    //     newDetail.weighTarget = +detail.weighTarget || 0;
    //     newDetail.budget = +detail.budget || 0;
    //     newDetail.kpiId = detail.kpiId;
    //     newDetail.createdBy = 'admin';
    //     totalPercentDetail += newDetail.weighTarget;
    //     lstKpiDetailInsert.push(newDetail);
    //   }

    //   if (totalPercentDetail !== newKpiGroup.weighTarget && group.details?.length) {
    //     throw new BusinessException(
    //       'Tỷ trọng chia thưởng và số tiền chia thưởng cho KPI chưa đúng',
    //     );
    //   }
    // }

    // if (totalPercent !== 100 && data.kpiSetGroups?.length) {
    //   throw new BusinessException(
    //     'Tỷ trọng chia thưởng và số tiền chia thưởng cho nhóm KPI chưa đúng',
    //   );
    // }

    // await this.kpiSetGroupRepo.insert(lstKpiGroupInsert);
    // await this.kpiSetDetailRepo.insert(lstKpiDetailInsert);

    return { message: `Update KPI Set ${data.code} successfully` };
  }

  @DefTransaction()
  async update(data: UpdateKpiSetDto) {
    const header = await this.repo.findOne({ where: { id: data.id } });
    if (!header) throw new BusinessException('Not found Kpi set');

    const checkCode = await this.repo.findOne({
      where: { code: data.code, id: Not(header.id) },
    });
    if (checkCode) throw new BusinessException('KPI Set Code đã tồn tại');

    header.code = data.code;
    header.shortName = data.shortName;
    header.longName = data.longName;
    header.budget = data.budget;
    header.status = data.status ? 1 : 0;
    // header.updatedBy = 'admin';
    await this.repo.save(header);

    // --- Xử lý Production Area ---
    const productionAreaIdDtos = [
      ...new Set(data.factoryProcessAreas.map((a) => a.productionAreaId)),
    ];
    const lstProArea = await this.productionAreaRepo.find({
      where: { id: In(productionAreaIdDtos), isActive: true },
    });

    if (productionAreaIdDtos.length !== lstProArea.length)
      throw new BusinessException('Not found production area');
    const productionAreaMap = new Map(lstProArea.map((pro) => [pro.id, pro]));

    const oldAreas = await this.kpiSetProductAreaRepo.find({
      where: { kpiSetHeaderId: header.id },
    });
    const oldAreaMap = new Map(oldAreas.map((a) => [`${a.productionAreaId}`, a]));

    // await this.kpiSetProductAreaRepo.delete({ kpiSetHeaderId: header.id });
    const toUpdate: KpiSetProductAreaEntity[] = [];
    const toInsert: KpiSetProductAreaEntity[] = [];
    for (let area of data.factoryProcessAreas) {
      const key = `${area.productionAreaId}`;
      if (oldAreaMap.has(key)) {
        // Cập nhật record cũ
        const exist = oldAreaMap.get(key)!;
        exist.status = area.status ? 1 : 0;
        exist.productionAreaId = area.productionAreaId;
        exist.siteId = area.siteId;
        // exist.updatedBy = 'admin';
        toUpdate.push(exist);
        oldAreaMap.delete(key); // Đánh dấu đã xử lý
      } else {
        const newProArea = new KpiSetProductAreaEntity();
        newProArea.kpiSetHeaderId = header.id;
        newProArea.productionAreaId = area.productionAreaId;
        newProArea.status = area.status ? 1 : 0;
        newProArea.factoryId = productionAreaMap.get(area.productionAreaId)?.organizationUnitId;
        newProArea.siteId = area.siteId;
        // newProArea.createdBy = 'admin';
        toInsert.push(newProArea);
      }
    }
    // await this.kpiSetProductAreaRepo.insert(toInsert);
    if (toUpdate.length) await this.kpiSetProductAreaRepo.save(toUpdate);
    if (toInsert.length) await this.kpiSetProductAreaRepo.insert(toInsert);

    // --- Phần xử lý kpiSetGroup và kpiSetDetail ---

    // Lấy danh sách nhóm cũ theo header
    const oldGroups = await this.kpiSetGroupRepo.find({
      where: { kpiSetHeaderId: header.id },
    });

    // Map nhóm cũ theo id để lookup
    const oldGroupMap = new Map(oldGroups.map((g) => [g.id, g]));

    // Lấy chi tiết theo các nhóm cũ
    const oldGroupIds = oldGroups.map((g) => g.id);
    const oldDetails = await this.kpiSetDetailRepo.find({
      where: { kpiSetGroupId: In(oldGroupIds) },
    });
    // Map chi tiết theo id
    const oldDetailMap = new Map(oldDetails.map((d) => [d.id, d]));

    // Lấy danh sách KPI Score liên quan để kiểm tra đã có điểm hay chưa
    // Giả sử có bảng kpiScoreRepo với field kpiSetGroupId và kpiSetDetailId
    const kpiScores = await this.kpiScoreRepo.find({
      where: [
        { kpiSetHeaderId: header.id },
        // Có thể filter thêm theo nhóm, chi tiết nếu cần
      ],
    });

    // Tạo set id nhóm và id chi tiết đã có điểm
    const groupIdsWithScore = new Set(kpiScores.map((s) => s.kpiSetGroupId).filter(Boolean));
    const detailIdsWithScore = new Set(kpiScores.map((s) => s.kpiSetDetailId).filter(Boolean));

    // Tạo map general data cho kpiGroupId validation
    const groupIdDtos = [...new Set(data.kpiSetGroups.map((g) => g.kpiGroupId))];
    const genGroupLst = await this.generalDataDetailRepo.find({
      where: { id: In(groupIdDtos), isActive: true },
    });
    if (groupIdDtos.length !== genGroupLst.length)
      throw new BusinessException('Not found Kpi Group');
    const genGroupMap = new Map(genGroupLst.map((g) => [g.id, g]));

    // Chuẩn bị danh sách insert/update
    const groupsToInsert: KpiSetGroupEntity[] = [];
    const groupsToUpdate: KpiSetGroupEntity[] = [];

    const detailsToInsert: KpiSetDetailEntity[] = [];
    const detailsToUpdate: KpiSetDetailEntity[] = [];

    let totalPercent = 0;

    for (const groupInput of data.kpiSetGroups) {
      const genGroup = genGroupMap.get(groupInput.kpiGroupId);
      if (!genGroup) throw new BusinessException('Invalid KPI Group');

      // Tìm nhóm cũ tương ứng (nếu có)
      const oldGroup = oldGroups.find((g) => g.kpiGroupId === groupInput.kpiGroupId);

      if (oldGroup) {
        // Nếu nhóm có điểm rồi, chỉ update thông tin đơn giản (không xóa, không giảm % dưới 0)
        if (groupIdsWithScore.has(oldGroup.id)) {
          // Có thể hạn chế cập nhật các trường quan trọng nếu cần
          oldGroup.budget = +groupInput.budget || 0;
          oldGroup.weighTarget = +groupInput.weighTarget || 0;
          // oldGroup.updatedBy = 'admin';

          // Cập nhật chi tiết theo nhóm này cũng phải bảo vệ chi tiết có điểm

          groupsToUpdate.push(oldGroup);
        } else {
          // Nhóm chưa có điểm, cập nhật thoải mái
          oldGroup.budget = +groupInput.budget || 0;
          oldGroup.weighTarget = +groupInput.weighTarget || 0;
          // oldGroup.updatedBy = 'admin';
          oldGroup.updatedDate = new Date();

          groupsToUpdate.push(oldGroup);
        }
      } else {
        // Nhóm mới, tạo mới
        const newGroup = new KpiSetGroupEntity();
        newGroup.kpiSetHeaderId = header.id;
        newGroup.kpiGroupId = groupInput.kpiGroupId;
        newGroup.kpiGroupCode = genGroup.code;
        newGroup.kpiGroupName = genGroup.name;
        newGroup.budget = +groupInput.budget || 0;
        newGroup.weighTarget = +groupInput.weighTarget || 0;
        // newGroup.createdBy = 'admin';

        groupsToInsert.push(newGroup);
      }

      totalPercent += +groupInput.weighTarget || 0;

      // Xử lý chi tiết nhóm này
      const targetGroupId = oldGroup?.id || groupsToInsert[groupsToInsert.length - 1]?.id;
      if (!targetGroupId) throw new BusinessException('Cannot find target group id');

      // Lấy chi tiết cũ của nhóm này
      const oldDetailsInGroup = oldDetails.filter((d) => d.kpiSetGroupId === targetGroupId);

      // Map chi tiết theo kpiId (hoặc id nếu có)
      const oldDetailMapByKpiId = new Map(oldDetailsInGroup.map((d) => [d.kpiId, d]));

      let totalPercentDetail = 0;

      for (const detailInput of groupInput.details || []) {
        const oldDetail = oldDetailMapByKpiId.get(detailInput.kpiId);

        if (oldDetail) {
          // Nếu chi tiết có điểm thì chỉ được update đơn giản
          if (detailIdsWithScore.has(oldDetail.id)) {
            oldDetail.budget = +detailInput.budget || 0;
            oldDetail.weighTarget = +detailInput.weighTarget || 0;
            // oldDetail.updatedBy = 'admin';
            detailsToUpdate.push(oldDetail);
          } else {
            // Chi tiết chưa có điểm, update thoải mái
            oldDetail.budget = +detailInput.budget || 0;
            oldDetail.weighTarget = +detailInput.weighTarget || 0;
            // oldDetail.updatedBy = 'admin';
            detailsToUpdate.push(oldDetail);
          }
        } else {
          // Chi tiết mới, tạo mới
          const newDetail = new KpiSetDetailEntity();
          newDetail.kpiSetGroupId = targetGroupId;
          newDetail.kpiId = detailInput.kpiId;
          newDetail.budget = +detailInput.budget || 0;
          newDetail.weighTarget = +detailInput.weighTarget || 0;
          // newDetail.createdBy = 'admin';

          detailsToInsert.push(newDetail);
        }
        totalPercentDetail += +detailInput.weighTarget || 0;
      }

      if (groupInput.details?.length && totalPercentDetail !== (+groupInput.weighTarget || 0)) {
        throw new BusinessException(
          'Tỷ trọng chia thưởng và số tiền chia thưởng cho KPI chưa đúng',
        );
      }
    }

    if (data.kpiSetGroups?.length && totalPercent !== 100) {
      throw new BusinessException(
        'Tỷ trọng chia thưởng và số tiền chia thưởng cho nhóm KPI chưa đúng',
      );
    }

    // Lưu các nhóm và chi tiết cập nhật, thêm mới
    if (groupsToUpdate.length) await this.kpiSetGroupRepo.save(groupsToUpdate);
    if (groupsToInsert.length) await this.kpiSetGroupRepo.insert(groupsToInsert);
    if (detailsToUpdate.length) await this.kpiSetDetailRepo.save(detailsToUpdate);
    if (detailsToInsert.length) await this.kpiSetDetailRepo.insert(detailsToInsert);

    return { message: `Update KPI Set ${data.code} successfully` };
  }

  @DefTransaction()
  async delete(id: string) {
    const checkKpi = await this.kpiStandardRepo.findOne({
      where: { kpiSetId: id },
      select: ['kpiStandardId'],
    });
    if (checkKpi) throw new BusinessException('KPI đã phát sinh dữ liệu liên quan, không được xóa');
    const checkKpiGroup = await this.kpiSetGroupRepo.findOne({ where: { kpiSetHeaderId: id } });
    if (checkKpiGroup)
      return { isWarning: true, message: 'KPI Set đã khai trọng số, bạn có xóa không ?' };

    await this.delete(id);
    return { message: 'Delete success' };
  }

  @DefTransaction()
  async deleteNotConfirm(id: string) {
    const checkKpi = await this.kpiStandardRepo.findOne({
      where: { kpiSetId: id },
      select: ['kpiStandardId'],
    });
    if (checkKpi) throw new BusinessException('KPI đã phát sinh dữ liệu liên quan, không được xóa');
    await this.kpiSetProductAreaRepo.delete({ kpiSetHeaderId: id });
    const checkLstKpiGroup = await this.kpiSetGroupRepo.find({
      where: { kpiSetHeaderId: id },
    });
    await this.kpiSetDetailRepo.delete({ kpiSetGroupId: In(checkLstKpiGroup.map((gr) => gr.id)) });
    await this.kpiSetGroupRepo.delete({ kpiSetHeaderId: id });
    await this.repo.delete({ id });
    return { message: 'Delete success' };
  }

  async pagination(params: KpiSetPageReq) {
    let whereCon: any = {};
    // if (params.longName) whereCon.longName = Like(`%${params.longName}%`);
    if (params.factoryId || params.productionAreaId || params.siteId) {
      const whereConProduct: any = {};
      if (params.factoryId) whereConProduct.factoryId = params.factoryId;
      if (params.productionAreaId) whereConProduct.productionAreaId = params.productionAreaId;
      if (params.siteId && !params.factoryId) {
        const lstFactoryInSite = await this.organizationUnitRepo.find({
          where: { parentId: params.siteId, isActive: true },
          select: ['id'],
        });
        whereConProduct.factoryId = In(lstFactoryInSite.map((f) => f.id));
      }
      const lstProFactory = await this.kpiSetProductAreaRepo.find({
        where: whereConProduct,
        select: ['kpiSetHeaderId'],
      });
      whereCon.id = In(lstProFactory.map((pro) => pro.kpiSetHeaderId));
    }
    if (params.code)
      whereCon.code = Raw((alias) => `LOWER(${alias}) LIKE LOWER(:code)`, {
        code: `%${params.code}%`,
      });
    if ([0, 1, true, false].includes(params.status)) whereCon.status = params.status ? 1 : 0;

    if (params.shortName) {
      const whereCon2: any = { ...whereCon };
      whereCon.shortName = Raw((alias) => `LOWER(${alias}) LIKE LOWER(:shortName)`, {
        shortName: `%${params.shortName}%`,
      });
      whereCon2.longName = Raw((alias) => `LOWER(${alias}) LIKE LOWER(:longName)`, {
        longName: `%${params.shortName}%`,
      });
      whereCon = [whereCon, whereCon2];
    }
    const res: any = await this.repo.findPagination(
      {
        where: whereCon,
        order: { createdDate: 'DESC' },
        relations: ['productionAreas'],
      },
      params,
    );

    const setProductionAreaId = new Set();
    const setFactoryId = new Set();
    for (let item of res.data) {
      item.lstProductionAreaId = [];
      item.lstFactoryId = [];
      for (let proArea of item.productionAreas) {
        setProductionAreaId.add(proArea.productionAreaId);
        setFactoryId.add(proArea.factoryId);
        item.lstProductionAreaId.push(proArea.productionAreaId);
        item.lstFactoryId.push(proArea.factoryId);
      }
    }
    const [lstProductionArea, lstFactory] = await Promise.all([
      this.productionAreaRepo.find({ where: { id: In([...setProductionAreaId]) } }),
      this.organizationUnitRepo.find({ where: { id: In([...setFactoryId]) } }),
    ]);

    const mapProductionArea = new Map(lstProductionArea.map((pro) => [pro.id, pro]));
    const mapOriginUnit = new Map(lstFactory.map((fac) => [fac.id, fac]));

    let no = (params.pageIndex - 1) * params.pageSize + 1;
    for (let item of res.data) {
      item.no = no++;
      item.productionAreaName = item.lstProductionAreaId
        .map((id) => mapProductionArea.get(id)?.name)
        .join(', ');
      item.productionAreaCode = item.lstProductionAreaId
        .map((id) => mapProductionArea.get(id)?.code)
        .join(', ');
      item.factoryName = [
        ...new Set(item.lstFactoryId.map((id) => mapOriginUnit.get(id)?.name)),
      ].join(', ');
      item.factoryCode = [
        ...new Set(item.lstFactoryId.map((id) => mapOriginUnit.get(id)?.code)),
      ].join(', ');
    }
    return res;
  }

  async loadDataSelectForCreate(userId?: string) {
    const lstGeneralData = await this.generalDataRepo.find({
      where: {
        code: In([
          NSGeneralData.EGeneralDataCode.ORG_LEVEL,
          NSGeneralData.EGeneralDataCode.KPI_GROUP,
        ]),
      },
    });

    const orgLevel = lstGeneralData.find(
      (gen) => gen.code === NSGeneralData.EGeneralDataCode.ORG_LEVEL,
    );
    const levelFactory = await this.generalDataDetailRepo.findOne({
      where: { code: 'FACTORY', generalId: orgLevel?.id, isActive: true },
    });
    const levelSite = await this.generalDataDetailRepo.findOne({
      where: { code: 'SITE', generalId: orgLevel?.id, isActive: true },
    });
    const listAccess = await this.accessRepo.find({
      where: { user: { id: userId }, status: true },
      relations: ['user', 'organizationUnit'],
    });
    // Lấy thông tin master data Factory
    const factories = await this.organizationUnitRepo.find({
      where: {
        levelId: levelFactory?.id,
        isActive: true,
        id: In(listAccess.map((i) => i.organizationUnit.id)),
      },
      select: ['id', 'code', 'name', 'parentId'],
    });

    // Lấy thông tin master data Site
    const sites = await this.organizationUnitRepo.find({
      where: { levelId: levelSite?.id, isActive: true, id: In(factories.map((i) => i.parentId)) },
      select: ['id', 'code', 'name', 'parentId'],
    });

    const kpiGroup = lstGeneralData.find(
      (gen) => gen.code === NSGeneralData.EGeneralDataCode.KPI_GROUP,
    );
    const kpiGroups = await this.generalDataDetailRepo.find({
      where: { generalId: kpiGroup?.id, isActive: true },
      select: ['id', 'code', 'name'],
    });

    const productionAreas: any[] = await this.productionAreaRepo.find({
      where: { isActive: true },
      relations: ['organization'],
      order: { name: 'ASC' },
    });

    const mapSite = new Map(sites.map((site) => [site.id, site]));
    for (let item of productionAreas) {
      item.factoryCode = item.organization?.code;
      item.factoryName = item.organization?.name;

      const curSite = mapSite.get(item.organization?.parentId);
      item.siteId = curSite?.id;
      item.siteCode = curSite?.code;
      item.siteName = curSite?.name;
      delete item.organization;
    }

    return { productionAreas, factories, kpiGroups, sites };
  }

  async findDetail(id: string) {
    const kpiSet: any = await this.repo.findOne({ where: { id } });
    if (!kpiSet) throw new BusinessException('Not found kpi set');
    const [lstKpiProductionArea, lstGroup] = await Promise.all([
      this.kpiSetProductAreaRepo.find({
        where: { kpiSetHeaderId: kpiSet.id },
        relations: ['factory', 'productionArea', 'site'],
      }),
      await this.kpiSetGroupRepo.find({
        where: { kpiSetHeaderId: kpiSet.id },
        relations: ['details', 'details.kpi'],
        order: { kpiGroupName: 'ASC' },
      }),
    ]);
    kpiSet.lstProductionAreaId = [];
    kpiSet.lstDetailProductionArea = [];
    kpiSet.lstKpiGroup = [];
    let idx = 1;
    for (let pa of lstKpiProductionArea) {
      kpiSet.lstProductionAreaId.push(pa.productionAreaId);
      kpiSet.lstDetailProductionArea.push({
        no: idx++,
        factoryName: pa.factory?.code + ' - ' + pa.factory?.name,
        name: pa.productionArea?.code + ' - ' + pa.productionArea?.name,
        siteId: pa.siteId,
        siteCode: pa.site?.code,
        productionAreaId: pa.productionAreaId,
        kpiSetHeaderCode: kpiSet.code,
        status: pa.status,
      });
    }

    const lstNewGroup = [];
    for (let gr of lstGroup) {
      const newDetails: any[] = gr.details.map((detail) => ({
        id: detail.kpiId,
        kpiId: detail.kpiId,
        weighTarget: detail.weighTarget,
        budget: detail.budget,
        name: detail.kpi.longName,
        kpiSetDetailId: detail.id,
      }));
      newDetails.sort((a, b) => a.name.localeCompare(b.name));
      lstNewGroup.push({
        kpiGroupId: gr.kpiGroupId,
        id: gr.kpiGroupId,
        code: gr.kpiGroupCode,
        name: gr.kpiGroupName,
        details: newDetails,
        weighTarget: gr.weighTarget,
        budget: gr.budget,
        kpiSetGroupId: gr.id,
      });
    }
    kpiSet.lstKpiGroup = lstNewGroup;
    return kpiSet;
  }
}
