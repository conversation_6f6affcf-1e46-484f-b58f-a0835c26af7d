import { forwardRef, MiddlewareConsumer, NestModule, RequestMethod } from '@nestjs/common';
import { ChildModule } from '~/@core/decorator';
import { RefixModule } from '../config-module';
import * as allController from './controllers';
import * as allService from './services';
import { EventHubService } from '~/x-modules/@global/services/event-hub.service';
import { AdminModule } from '~/x-modules/admin';
import { PublicMiddleware } from '~/x-modules/publics/public.middleware';
@ChildModule({
  prefix: RefixModule.publics,
  providers: [...Object.values(allService), EventHubService],
  controllers: [...Object.values(allController)],
  imports: [forwardRef(() => AdminModule)],
})
export class PublicModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(PublicMiddleware)
      .exclude()
      .forRoutes({ path: `${RefixModule.publics}*`, method: RequestMethod.ALL });
  }
}
