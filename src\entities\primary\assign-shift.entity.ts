import { Column, Entity, Join<PERSON>olumn, ManyToOne } from 'typeorm';
import { PrimaryBaseEntity } from '~/entities/primary-base.entity';
import { OrganizationUnitEntity } from './organization-unit.entity';
import { ShiftEntity } from './shift.entity';

/** <PERSON><PERSON> báo ở mức organization đối với ca */
@Entity('assign_shift')
export class AssignShiftEntity extends PrimaryBaseEntity {
  /** Ngày bắt đầu có hiệu lực */
  @Column({ type: 'timestamptz' })
  validFrom?: Date;

  /** Ng<PERSON>y kết thúc hiệu lực */
  @Column({ type: 'timestamptz' })
  validTo?: Date;

  /** Trạng thái hoạt động organization đối với ca*/
  @Column({ default: true })
  status: boolean;

  /** Id ca */
  @Column({ type: 'text' })
  shiftId: string;
  /** Relation với bảng Shift */
  @ManyToOne(() => ShiftEntity, shift => shift.assignShifts, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'shiftId', referencedColumnName: 'id' })
  shift?: ShiftEntity;

  /** Id tổ chức */
  @Column({ type: 'text' })
  organizationId: string;
  // /** Relation với bảng OrganizationUnit */
  @ManyToOne(() => OrganizationUnitEntity, org => org.assignShifts, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'organizationId', referencedColumnName: 'id' })
  organization?: OrganizationUnitEntity;
}
