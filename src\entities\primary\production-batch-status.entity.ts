import { Column, Entity, Unique, PrimaryGeneratedColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { IsUUID } from 'class-validator';
import { PrimaryBaseEntity } from '../primary-base.entity';

@Entity('production_batch_status')
@Unique(['machineId', 'batchId'])
export class MesDxProdBatchStatusEntity extends PrimaryBaseEntity {
  @ApiProperty()
  @PrimaryGeneratedColumn('uuid')
  @IsUUID(4)
  productionBatchStatusId?: string;

  @ApiProperty({ description: 'Batch id của mẻ sản xuất', example: '1234' })
  @Column()
  machineId: string;

  @ApiProperty({ description: '<PERSON><PERSON><PERSON> mà mẻ sản xuất chạy qua', example: '6789' })
  @Column()
  batchId: string;

  @ApiProperty({ description: 'Thời gian kết thúc', example: '2024-03-06T14:30:00Z' })
  @Column({ type: 'timestamptz' })
  startTime: Date | null;

  @ApiProperty({ description: 'Thời gian kết thúc', example: '2024-03-06T14:30:00Z' })
  @Column({ type: 'timestamptz', nullable: true })
  endTime: Date | null;

  @ApiProperty({
    description: 'Thời gian cập nhật cuối của dòng dữ liệu',
    example: '2024-03-06T14:30:00Z',
  })
  @ApiProperty({ description: '', example: '-1' })
  @Column({ nullable: true })
  lastUpdateBy: number;
  @ApiProperty({ description: 'Trạng thái', example: 'GOOD' })
  @Column()
  status: string;
}
