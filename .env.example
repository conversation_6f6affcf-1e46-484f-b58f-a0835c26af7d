  NODE_ENV='development'                  # Môi trường chạy ứng dụng (development, production, v.v.)
  PORT='3001'                             # Cổng mà server sẽ lắng nghe
  # TZ='UTC'                              # Múi giờ hệ thống (bỏ comment nếu dùng UTC)
  TZ='Asia/Ho_Chi_Minh'                   # Mú<PERSON> giờ hệ thống (Asia/Ho_Chi_Minh)
  REQUEST_TIMEOUT='180000'                # Thời gian timeout cho request (ms)

  # Swagger Config
  SWAGGER_TITLE='MES API'                 # Tiêu đề tài liệu Swagger
  SWAGGER_DESCRIPTION='THE MES API'       # Mô tả tài liệu Swagger
  SWAGGER_VERSION='1.0'                   # Phiên bản API

  # MS SSO
  MS_CLIENT_ID=                           # Client ID cho Microsoft SSO
  MS_CLIENT_SECRET=                       # Client Secret cho Microsoft SSO
  MS_TENANT_ID=                           # Tenant ID cho Microsoft SSO

  # Primary Database
  DB_PRIMARY_HOST=                        # Địa chỉ host database chính
  DB_PRIMARY_PORT=                        # Cổng database chính
  DB_PRIMARY_USERNAME=                    # Username database chính
  DB_PRIMARY_PASSWORD=                    # Password database chính
  DB_PRIMARY_DATABASE=                    # Tên database chính
  DB_PRIMARY_SYNCHRONIZE=                 # Có tự động đồng bộ schema không (true/false)
  DB_PRIMARY_SSL=                         # Kết nối SSL (true/false)
  DB_PRIMARY_SSL_REJECT_UNAUTHORIZED=     # Từ chối SSL không xác thực (true/false)

  # Scada Database
  DB_SCADA_HOST=                          # Địa chỉ host database SCADA
  DB_SCADA_PORT=                          # Cổng database SCADA
  DB_SCADA_USERNAME=                      # Username database SCADA
  DB_SCADA_PASSWORD=                      # Password database SCADA
  DB_SCADA_DATABASE=                      # Tên database SCADA
  DB_SCADA_SYNCHRONIZE=                   # Có tự động đồng bộ schema không (true/false)
  DB_SCADA_SSL=                           # Kết nối SSL (true/false)
  DB_SCADA_SSL_REJECT_UNAUTHORIZED=       # Từ chối SSL không xác thực (true/false)

  JWT_SECRET=                             # Secret dùng để ký JWT
  JWT_EXPIRY=                             # Thời gian hết hạn của JWT

  API_TOKEN_CACHE_INTERVAL=               # Thời gian cache token API (ms)
  API_TOKEN_SECRET=                       # Secret cho API token
  API_SCHEDULE_SECRET=                    # Secret cho API schedule

  TOKEN_ERP_URL=                          # URL lấy token ERP
  ERP_INVENTORY=                          # Đường dẫn API kiểm kê ERP
  EBS_MES_USERNAME=                       # Username kết nối EBS MES
  EBS_MES_PASSWORD=                       # Password kết nối EBS MES
  INVENTORY_ERP_URL=                      # URL kiểm kê ERP

  LINK_UPLOAD_S3=                         # Link upload file lên S3
  AWS_S3_BUCKET_NAME=                     # Tên bucket S3
  AWS_S3_ACCESS_KEY_ID=                   # Access key S3
  AWS_S3_SECRET_ACCESS_KEY=               # Secret key S3
  AWS_S3_REGION=                          # Khu vực S3
  AWS_S3_UPLOAD_FOLDER=                   # Thư mục upload trên S3

  QLONE_URL=
  # URL Call QLONE Sample
  QLONE_SAMPLE_URL=  # URL Lấy data sample từ QLONE cho tính năng quality notification
  QLONE_CREATE_SAMPLE_URL=  # URL tạo data sample bên QLONE cho tính năng tạo quality notification và gọi API của hệ thống QLONE để tạo sample và lưu sample number vào notification.
  QLONE_VISUAL_INSPECTION_URL= # URL tạo data Inspection bên QLONE cho tính năng tạo quality notification và gọi API của hệ thống QLONE để tạo Visual Inspection và lưu Visual Inspection vào notification.

  #AZURE_EVENT_HUB
  AZURE_EVENT_HUB_CONNECTION_STRING=      # Chuỗi kết nối Azure Event Hub
  AZURE_EVENT_HUB_NAME=                   # Tên Event Hub

  ACCEPT_PUBLIC_IP=                  # IP công cộng được chấp nhận (ip1_ip2) -> ::1_127.0.0.1