import { Body, Query, UseGuards } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { DefController, DefGet, DefPost } from '~/@core/decorator';
import {
  RequestGetManualGPCInventory,
  RequestListFactoryInventory,
  RequestListFiltersInventory,
  RequestListSiteInventory,
  RequestPaginationInventory,
} from '~/dto/inventory.dto';
import { InventoryService } from '../../services';
import { Roles } from '~/@core/decorator/roles.decorator';
import { RoleGuard } from '~/@systems/guard/role.guard';

@ApiTags('inventory')
@DefController('inventory')
export class InventoryController {
  constructor(private readonly inventoryService: InventoryService) {}

  @DefGet('/pagination')
  @Roles('/production-execution/inventory', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Pagination' })
  async pagination(@Query() queryParams: RequestPaginationInventory) {
    const result = await this.inventoryService.pagination(queryParams);

    return {
      status: true,
      message: 'Success',
      data: result,
    };
  }

  @DefPost('/get-manual-gpc')
  @Roles('/production-execution/inventory', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'get manual gpc' })
  async getManualERP(@Body() params: RequestGetManualGPCInventory) {
    const result = await this.inventoryService.getManualERP(params);

    return {
      status: true,
      message: 'The GCP data synchronization process is complete.',
      data: result,
    };
  }

  @DefPost('/list-site')
  @Roles('/production-execution/inventory', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'list site' })
  async listSite(@Body() params: RequestListSiteInventory) {
    const result = await this.inventoryService.listSite(params);

    return {
      status: true,
      data: result,
    };
  }

  @DefPost('/list-factory')
  @Roles('/production-execution/inventory', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'list factory' })
  async listFactory(@Body() params: RequestListFactoryInventory) {
    const result = await this.inventoryService.listFactory(params);

    return {
      status: true,
      data: result,
    };
  }

  @DefPost('/list-filters')
  @Roles('/production-execution/inventory', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'list filters' })
  async listFilters(@Body() params: RequestListFiltersInventory) {
    const result = await this.inventoryService.listFilters();

    return {
      status: true,
      data: result,
    };
  }
}
