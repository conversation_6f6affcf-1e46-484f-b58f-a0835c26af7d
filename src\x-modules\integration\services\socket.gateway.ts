import { forwardRef, Inject } from '@nestjs/common';
import {
  WebSocketGateway,
  WebSocketServer,
  SubscribeMessage,
  OnGatewayConnection,
  OnGatewayDisconnect,
} from '@nestjs/websockets';
import dayjs from 'dayjs';
import { Server, Socket } from 'socket.io';
import { NSSocket } from '~/common/enums';
import { SitewiseLibService } from '~/x-modules/admin/services';
// const sitewiseService = new SitewiseLibService();
@WebSocketGateway({ cors: true }) // enable cors
export class SocketGateway implements OnGatewayConnection, OnGatewayDisconnect {
  constructor(
    @Inject(forwardRef(() => SitewiseLibService))
    private readonly sitewiseService: SitewiseLibService,
  ) {}

  @WebSocketServer()
  server: Server;

  handleConnection(client: Socket) {
    console.log(`Client connected: ${client.id}`);
  }

  handleDisconnect(client: Socket) {
    console.log(`Client disconnected: ${client.id}`);
  }

  @SubscribeMessage('message')
  handleMessage(client: Socket, payload: string): void {
    this.server.emit('message', payload); // Broadcast message to all clients
  }

  /** Hàm test */
  emitDataChange(data: any): void {
    this.server.emit('dataChange', data);
  }

  /** Hàm Cập nhật data Monitor khi Property nhận Noti */
  async emitMonitorLineDashboard(
    assetId: string,
    propertyId: string,
    type: NSSocket.EType = NSSocket.EType.MEASUREMENT,
    value?: string,
  ) {
    if (type === NSSocket.EType.MEASUREMENT) {
      let itemValue = await this.sitewiseService.getAssetPropertyValue(assetId, propertyId);
      itemValue = this.sitewiseService.extractParamValue(itemValue?.propertyValue)?.value;
      this.server.emit(NSSocket.EEmitType.MonitorLineDashboard, {
        assetId,
        propertyId,
        value: itemValue,
      });
    } else if (type === NSSocket.EType.METRIC) {
      let itemValue = await this.sitewiseService.getAssetPropertyValue(assetId, propertyId);
      itemValue = this.sitewiseService.extractParamValue(itemValue?.propertyValue)?.value;
      this.server.emit(NSSocket.EEmitType.MonitorLineDashboard, {
        assetId,
        propertyId,
        value: value,
      });
    }
  }

  /** Hàm Cập nhật data (ca làm so với đầu ca) khi Property nhận Noti */
  async emitRejectionMonitoring(assetId: string, propertyId: string) {
    let shiftValue = await this.sitewiseService.calculateByShiftStartValueCurrent(
      assetId,
      propertyId,
    );
    if (shiftValue !== undefined && shiftValue !== null) {
      this.server.emit(NSSocket.EEmitType.RejectionMonitoring, {
        assetId,
        propertyId,
        shiftValue: shiftValue,
      });
    } else {
      this.server.emit(NSSocket.EEmitType.RejectionMonitoring, {
        assetId,
        propertyId,
        shiftValue: 0,
      });
    }
  }

  async emitUtilityMonitoring(assetId: string, metricId: string) {
    let shiftValue = await this.sitewiseService.calculateSumValueMetricByShift(assetId, metricId);
    if (shiftValue !== undefined && shiftValue !== null) {
      this.server.emit(NSSocket.EEmitType.UtilityMonitoring, {
        assetId,
        metricId,
        shiftValue: shiftValue,
      });
    } else {
      this.server.emit(NSSocket.EEmitType.UtilityMonitoring, {
        assetId,
        metricId,
        shiftValue: 0,
      });
    }
  }

  async emitAuthZktFace(data) {
    this.server.emit(NSSocket.EEmitType.AuthZKTFace, {
      data,
    });
  }

  async emitDataScale(data) {
    this.server.emit(NSSocket.EEmitType.ScaleData, {
      data,
    });
  }
}
