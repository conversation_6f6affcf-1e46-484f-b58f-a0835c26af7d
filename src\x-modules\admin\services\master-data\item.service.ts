import { Injectable } from '@nestjs/common';
import dayjs from 'dayjs';
import { Between, Like } from 'typeorm';
import { BindRepo } from '~/@core/decorator';
import { ListItemReq } from '~/dto/item.dto';
import { ItemRepo } from '~/repositories/primary';

@Injectable()
export class ItemService {
  constructor() {}
  @BindRepo(ItemRepo)
  private readonly itemRepo: ItemRepo;

  async create() {
    return 'Create item';
  }

  async find(params: ListItemReq) {
    let whereCon: any = {};

    if (params?.code) {
      whereCon.code = Like(`%${params.code.trim()}%`);
    }

    if (params?.name) {
      whereCon.name = Like(`%${params.name.trim()}%`);
    }

    if (params?.type) {
      whereCon.type = Like(`%${params.type.trim()}%`);
    }

    if (params?.status) {
      whereCon.status = params.status;
    }

    if (params?.dateFrom || params?.dateTo) {
      const dateFrom = params.dateFrom
        ? dayjs(params.dateFrom).startOf('day').toDate()
        : dayjs('1900-01-01').toDate();

      const dateTo = params.dateTo
        ? dayjs(params.dateTo).endOf('day').toDate()
        : dayjs().endOf('day').toDate();

      whereCon.updatedDate = Between(dateFrom, dateTo);
    }

    if (params?.isSyncScada) {
      whereCon.isSyncScada = params.isSyncScada;
    }

    return await this.itemRepo.findPagination(
      { where: whereCon, order: { createdDate: 'DESC' } },
      params,
    );
  }

  async findById(id: string) {
    return this.itemRepo.findOne(id, { relations: ['uomConversions'] });
  }

  async updateTolerance(id: string, weighingTolerance: number) {
    return await this.itemRepo.update(
      { id: id },
      { weighingTolerance: weighingTolerance, updatedDate: dayjs() },
    );
  }
}
