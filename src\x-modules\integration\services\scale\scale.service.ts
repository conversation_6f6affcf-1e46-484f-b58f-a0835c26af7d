import { Injectable } from '@nestjs/common';
import { BindRepo } from '~/@core/decorator';
import { ScaleDataReq } from '~/dto/scale.dto';
import { DeviceRepo } from '~/repositories/primary';
import { SocketGateway } from '~/x-modules/integration/services/socket.gateway';

@Injectable()
export class ScaleService {
  constructor(private socketGateway: SocketGateway) {}
  @BindRepo(DeviceRepo)
  private readonly deviceRepo: DeviceRepo;
  async getWeight(body: ScaleDataReq) {
    try {
      console.log('body', body);
      this.socketGateway.emitDataScale(body);
    } catch (error) {
      console.log(error, 'error');
    }
  }

  async getListDevice() {
    try {
      const res = await this.deviceRepo.find({
        where: {
          deviceTypeDetailCode: 1,
          isActive: true,
        },
      });
      return res.map((item) => ({ ipAddress: item.ipAddress, devicePort: item.devicePort }));
    } catch (error) {
      console.log('Error to get list device', error);
      return [];
    }
  }
}
