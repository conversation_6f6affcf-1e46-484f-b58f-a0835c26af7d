import { Injectable, NotFoundException } from '@nestjs/common';
import { ShiftEntity, SitewiseAssetEntity, SitewisePropertiesEntity } from '~/entities/primary';
import { SitewiseAssetRepo, SitewisePropertiesRepo } from '~/repositories/primary/sitewise.repo';
import { BindRepo } from '~/@core/decorator';
import { NSSitewise } from '~/common/enums/NSSitewise';
import { getOeeChartReq, getOeeDetailsReq, MonitorLineDashboardByIdReq } from '~/dto/sitewise.dto';
import { MesDxMeasurementRepo } from '~/repositories/scada';
import { Between, EntityManager, In, LessThanOrEqual, MoreThan, MoreThanOrEqual } from 'typeorm';
import {
  AccessRepo,
  GeneralDataDetailRepo,
  GeneralDataRepo,
  MachineParameterRepo,
  MaterialConsumptionScadaRepo,
  MaterialGroupDetailRepo,
  MaterialGroupHeaderRepo,
  OrganizationUnitRepo,
  ProcessMachineRepo,
  ProcessRepo,
  ProductionAreaRepo,
  ProductionBatchRepo,
  ProductionDowntimeRepo,
  ProductionOeeRepo,
  ProductionOrderMaterialRepo,
  ProductionOrderRepo,
  ProductionRejectionRepo,
  ReasonMasterRepo,
  UomConventionRepo,
  UtilityMetersRepo,
} from '~/repositories/primary';
import { ShiftRepo } from '~/repositories/primary/shift.repo';
import { dateHelper } from '~/common/helpers/date.helper';
import { NSReason } from '~/common/enums/NSReason';
const AWS = require('aws-sdk');
import { ItemRepo } from '~/repositories/primary/item.repo';
import { numberHelper } from '~/common/helpers/number.helper';
import dayjs from 'dayjs';
import { ProcessMachineEntity } from '~/entities/primary/process-machine.entity';
import { AssignShiftRepository } from '~/repositories/primary/assign-shift.repo';
import { EOrganizationLevel } from '~/common/enums/organization-level.enum';
interface Result {
  value: any;
  dataType: string;
}
@Injectable()
export class SitewiseLibService {
  //#region  repositories
  @BindRepo(SitewiseAssetRepo)
  private readonly assetRepo: SitewiseAssetRepo;

  @BindRepo(SitewisePropertiesRepo)
  private readonly propertiesRepo: SitewisePropertiesRepo;

  @BindRepo(MesDxMeasurementRepo)
  private readonly mesDxMeasurementRepo: MesDxMeasurementRepo;

  @BindRepo(OrganizationUnitRepo)
  private readonly organizationUnitRepo: OrganizationUnitRepo;

  @BindRepo(ShiftRepo)
  private readonly shiftRepo: ShiftRepo;

  @BindRepo(MaterialConsumptionScadaRepo)
  private readonly materialConsumptionScadaRepo: MaterialConsumptionScadaRepo;

  @BindRepo(ProductionOrderRepo)
  private readonly productionOrderRepo: ProductionOrderRepo;

  @BindRepo(ProductionOrderMaterialRepo)
  private readonly productionOrderMaterialRepo: ProductionOrderMaterialRepo;

  @BindRepo(ProductionDowntimeRepo)
  private readonly productionDowntimeRepo: ProductionDowntimeRepo;

  @BindRepo(MachineParameterRepo)
  private readonly machineParameterRepo: MachineParameterRepo;

  @BindRepo(ItemRepo)
  private readonly itemRepo: ItemRepo;

  @BindRepo(ProcessRepo)
  private readonly processRepo: ProcessRepo;

  @BindRepo(ProductionRejectionRepo)
  private readonly productionRejectionRepo: ProductionRejectionRepo;

  @BindRepo(ProcessMachineRepo)
  private readonly processMachineRepo: ProcessMachineRepo;

  @BindRepo(ProductionOeeRepo)
  private readonly productionOeeRepo: ProductionOeeRepo;

  @BindRepo(AccessRepo)
  private readonly accessRepo: AccessRepo;

  @BindRepo(GeneralDataDetailRepo)
  private readonly generalDataDetailRepo: GeneralDataDetailRepo;

  @BindRepo(AssignShiftRepository)
  private readonly assignShiftRepo: AssignShiftRepository;

  @BindRepo(MaterialGroupHeaderRepo)
  private readonly materialGroupHeaderRepo: MaterialGroupHeaderRepo;

  @BindRepo(MaterialGroupDetailRepo)
  private readonly materialGroupDetailRepo: MaterialGroupDetailRepo;

  @BindRepo(GeneralDataRepo)
  private readonly generalDataRepo: GeneralDataRepo;

  constructor(private readonly entityManager: EntityManager) {}

  //#endregion

  //#region  AWS IoT SiteWise

  // Cấu hình AWS IoT SiteWise
  sitewise = new AWS.IoTSiteWise({
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    region: process.env.AWS_REGION,
  });

  /** Hàm lấy danh sách asset dựa vào modelId */
  async listAssets(assetModelId: string) {
    try {
      const params = {
        assetModelId: assetModelId,
        // maxResults: 2, // Số lượng kết quả tối đa bạn muốn nhận
      };
      const data = await this.sitewise.listAssets(params).promise();
      return data?.assetSummaries;
    } catch (err) {
      console.error('Error listing assets:', err);
    }
  }

  /** Hàm mô tả Asset và danh sách property dựa vào assetId */
  async describeAsset(assetId) {
    try {
      const params = {
        assetId: assetId,
      };
      const data = await this.sitewise.describeAsset(params).promise();
      return data?.assetProperties;
    } catch (err) {
      console.error('Error describing asset:', err);
    }
  }

  /** Hàm lấy danh sách property của model */
  async listAssetModelProperties(assetModelId: string): Promise<any[]> {
    let nextToken: string | undefined;
    let properties: any[] = [];

    do {
      const params = {
        assetModelId: assetModelId,
        nextToken: nextToken,
        maxResults: 50,
      };

      const response = await this.sitewise.listAssetModelProperties(params).promise();

      properties = properties.concat(response.assetModelPropertySummaries || []);
      nextToken = response.nextToken;
    } while (nextToken);

    return properties;
  }

  async getAssetPropertyValue(assetId: string, propertyId: string) {
    try {
      const params = {
        assetId: assetId,
        propertyId: propertyId,
      };
      const data = await this.sitewise.getAssetPropertyValue(params).promise();

      return data;
    } catch (err) {
      // console.log('err', err);
    }
  }

  /** Hàm lấy dữ liệu từ SiteWise và tính toán theo các trường hợp được chỉ định */
  async getAssetPropertyValueHistory(assetId, propertyId, startTime, endTime, maxResults = 10) {
    const params = {
      assetId: assetId,
      propertyId: propertyId,
      startDate: startTime,
      endDate: endTime,
      maxResults: maxResults, // Số lượng kết quả tối đa
    };

    try {
      const response = await this.sitewise.getAssetPropertyValueHistory(params).promise();
      return response.assetPropertyValueHistory;
    } catch (error) {
      // console.error('Lỗi lấy dữ liệu Measurements:', error);
      return null;
    }
  }

  async getAssetPropertyValueHistoryAll(assetId, propertyId, startTime, endTime, maxResults = 10) {
    let nextToken: string | undefined;
    let properties: any[] = [];
    const params = {
      assetId: assetId,
      propertyId: propertyId,
      startDate: startTime,
      endDate: endTime,
      nextToken: nextToken,
      maxResults: 10,
    };
    do {
      const response = await this.sitewise.getAssetPropertyValueHistory(params).promise();
      properties = properties.concat(response.assetPropertyValueHistory || []);
      nextToken = response.nextToken;
    } while (nextToken);
    return properties;
  }

  /** Hàm lấy danh sách Asseets Property đồng bộ vào DB*/
  async syncDataToDb(modelId: string) {
    try {
      const listAssets = await this.listAssets(modelId);
      const listMProperty = await this.listAssetModelProperties(modelId);

      for (let item of listAssets) {
        let asset = new SitewiseAssetEntity();
        asset.name = item.name;
        asset.code = item.arn;
        asset.awsId = item.id;
        asset.sitewiseModelId = modelId;
        asset.description = item.description || '';
        asset.createdDate = item.creationDate;
        asset.updatedDate = item.lastUpdateDate;
        asset.status = item.status?.state;

        await this.assetRepo.save(asset);
      }
      const listSaveP = [];

      // const listProperty = await this.describeAsset(item.id);
      for (let itemP of listMProperty) {
        let property = new SitewisePropertiesEntity();
        property.name = itemP.name;
        property.code = itemP.name;
        // property.description = itemP.description;
        property.awsId = itemP.id;
        property.dataType = itemP.dataType;
        property.dataTypeSpec = itemP.dataTypeSpec;
        property.unit = itemP.unit;
        // property.notificationState = itemP.notification?.state;
        // property.notificationTopic = itemP.notification?.topic;
        property.externalId = itemP.externalId;
        // property.sitewiseAssetId = item.id;
        property.sitewiseModelId = modelId;

        // const itemP = listMProperty.find(p => p.id === itemP.id);
        if (itemP) {
          if (itemP.type.attribute) {
            property.type = NSSitewise.ESitewisePropertyType.ATTRIBUTE;
            property.typeValue = itemP.type.attribute;
          }
          if (itemP.type.measurement) {
            property.type = NSSitewise.ESitewisePropertyType.MEASUREMENT;
            property.typeValue = itemP.type.measurement;
          }
          if (itemP.type.metric) {
            property.type = NSSitewise.ESitewisePropertyType.METRIC;
            property.typeValue = itemP.type.metric;
          }
          if (itemP.type.transform) {
            property.type = NSSitewise.ESitewisePropertyType.TRANSFORM;
            property.typeValue = itemP.type.transform;
          }
        }

        // const assetId = await this.findAssetByProperty(itemP.id);
        // property.sitewiseAssetId = await this.findAssetByProperty(itemP.id);
        listSaveP.push(property);
      }
      await this.propertiesRepo.save(listSaveP);
    } catch (error) {
      console.log(error);
    }
  }

  /** Hàm lấy dữ liệu từ SiteWise và tính toán theo các trường hợp được chỉ định */
  async monitorLineDashboard(data: MonitorLineDashboardByIdReq) {
    const result = [];
    try {
      const shift = await this.shiftRepo.findOne({
        where: {
          id: data.shiftId,
        },
      });
      const resultDateTime = dateHelper.getTodayWithCustomTimePair(
        shift?.startTime, // ví dụ '22:00'
        shift?.endTime, // ví dụ '06:00'
        'Asia/Ho_Chi_Minh',
        'Asia/Ho_Chi_Minh', // Hoặc 'UTC' tùy hệ thống
      );

      const startTime = resultDateTime?.startTime;
      const endTime = resultDateTime?.endTime;
      for (let item of data.lineItem) {
        let resultValue = null;
        if (item.case)
          switch (item.case) {
            case NSSitewise.ESitewiseDashboartLabelType.MIXING_TIME: {
              let itemValueStart = await this.getAssetPropertyValue(
                item.assetId,
                item.propertyIdStart,
              );
              let valueStart = this.extractParamValue(itemValueStart?.propertyValue);

              let itemValueEnd = await this.getAssetPropertyValue(item.assetId, item.propertyIdEnd);
              let valueEnd = this.extractParamValue(itemValueEnd?.propertyValue);

              const today = new Date();

              // Nếu dữ liệu valueStart và valueEnd không đúng (1 trong 2 giá trị là null hoặc một trong 2 có giá trị cũ hơn hiện tại 24h) thì để trống
              if (valueStart?.value === null || valueEnd?.value === null) resultValue = '';
              else if (
                today > dateHelper.add24Hours(new Date(valueStart?.value)) ||
                today > dateHelper.add24Hours(new Date(valueEnd?.value))
              )
                resultValue = '';
              else {
                resultValue = dateHelper.subtractDateObjects(
                  new Date(valueEnd?.value),
                  new Date(valueStart?.value),
                );
              }
              break;
            }
            case NSSitewise.ESitewiseDashboartLabelType.ELECTRICITY_SHIFT: {
              let sumValue = 0;
              for (const propertyId of item.propertyIds) {
                const value = await this.calculateByShiftStartValueByMetric(
                  item.assetId,
                  propertyId,
                  shift,
                );
                if (value) {
                  sumValue += Number(value || 0);
                }
              }
              resultValue = sumValue?.toLocaleString();
              break;
            }
            case NSSitewise.ESitewiseDashboartLabelType.TOTAL_COUNT: {
              const result = await this.calculateByShiftStartValueByMetric(
                item.assetId,
                item.propertyId,
                shift,
              );
              resultValue = numberHelper.formatNumber(result);
              break;
            }
            case NSSitewise.ESitewiseDashboartLabelType.REJECTION_COUNT: {
              const result = await this.calculateByShiftStartValueByMetric(
                item.assetId,
                item.propertyId,
                shift,
              );
              resultValue = numberHelper.formatNumber(result);
              break;
            }
            case NSSitewise.ESitewiseDashboartLabelType.GOOD_COUNT: {
              const result = await this.calculateGoodCount(
                item.assetId,
                item.propertyIdStart,
                item.propertyIdEnd,
                shift,
              );
              resultValue = numberHelper.formatNumber(result);
              break;
            }
            case NSSitewise.ESitewiseDashboartLabelType.SUM_TOTAL_COUNT: {
              let itemValueG1 = await this.calculateByShiftStartValueByMetric(
                item.assetId,
                item.propertyIdG1,
                shift,
              );

              let itemValueG2 = await this.calculateByShiftStartValueByMetric(
                item.assetId1,
                item.propertyIdG2,
                shift,
              );

              let itemValueG3 = await this.calculateByShiftStartValueByMetric(
                item.assetId2,
                item.propertyIdG3,
                shift,
              );

              const total = itemValueG1 + itemValueG2 + itemValueG3;
              resultValue = numberHelper.formatNumber(total);
              break;
            }
            case NSSitewise.ESitewiseDashboartLabelType.SUM_TOTAL_REJECTION: {
              let itemValueR1 = await this.calculateByShiftStartValueByMetric(
                item.assetId,
                item.propertyIdR1,
                shift,
              );

              let itemValueR2 = await this.calculateByShiftStartValueByMetric(
                item.assetId1,
                item.propertyIdR2,
                shift,
              );

              let itemValueR3 = await this.calculateByShiftStartValueByMetric(
                item.assetId2,
                item.propertyIdR3,
                shift,
              );

              const total = itemValueR1 + itemValueR2 + itemValueR3;
              resultValue = numberHelper.formatNumber(total);
              break;
            }
            case NSSitewise.ESitewiseDashboartLabelType.SUM_TOTAL_GOOD: {
              let itemValueG1 = await this.calculateByShiftStartValueByMetric(
                item.assetId,
                item.propertyIdG1,
                shift,
              );

              let itemValueG2 = await this.calculateByShiftStartValueByMetric(
                item.assetId1,
                item.propertyIdG2,
                shift,
              );

              let itemValueG3 = await this.calculateByShiftStartValueByMetric(
                item.assetId2,
                item.propertyIdG3,
                shift,
              );
              let itemValueR1 = await this.calculateByShiftStartValueByMetric(
                item.assetId,
                item.propertyIdR1,
                shift,
              );

              let itemValueR2 = await this.calculateByShiftStartValueByMetric(
                item.assetId1,
                item.propertyIdR2,
                shift,
              );

              let itemValueR3 = await this.calculateByShiftStartValueByMetric(
                item.assetId2,
                item.propertyIdR3,
                shift,
              );

              const total =
                itemValueG1 + itemValueG2 + itemValueG3 - itemValueR1 - itemValueR2 - itemValueR3;
              resultValue = numberHelper.formatNumber(total);
              break;
            }
            case NSSitewise.ESitewiseDashboartLabelType.MATERIAL_CONSUMPTION: {
              // Từ propertyBatchId: Vd: 16652893-c543-49fb-8d01-4e3538c5eaf8
              let batchNumberAndPO = await this.getAssetPropertyValue(
                item.assetId,
                item.propertyProductId,
              );
              batchNumberAndPO = this.extractParamValue(batchNumberAndPO?.propertyValue)?.value;

              const batchNumber = batchNumberAndPO?.split('|')[0];
              const orderNo = batchNumberAndPO?.split('|')[1];
              resultValue = [];
              if (orderNo && batchNumber) {
                const material_consumption = await this.materialConsumptionScadaRepo.find({
                  where: {
                    orderNumber: orderNo,
                    batchCode: batchNumber,
                  },
                });

                for (const item of material_consumption) {
                  const material = await this.itemRepo.findOne({
                    where: {
                      code: item?.ingredientCode,
                    },
                  });

                  resultValue.push({
                    materialCode: item?.ingredientCode,
                    materialName: material?.name,
                    lot: item?.lotNumber,
                    qty: item?.qty,
                    uom: item?.uom,
                  });
                }
              }
              break;
            }
            case NSSitewise.ESitewiseDashboartLabelType.PRODUCT_CODE_PRODUCT_NAME: {
              // Từ giá trị của measurement production order link với bảng production_order_material theo trường order_no và ràng line_type bằng 1.
              // Lấy 1 dòng để tìm ra material_code, material_name

              let itemValue = await this.getAssetPropertyValue(item.assetId, item.propertyId);
              itemValue = this.extractParamValue(itemValue?.propertyValue)?.value;
              const production_order_material = await this.productionOrderMaterialRepo.findOne({
                where: {
                  orderNo: itemValue,
                  lineType: 1,
                },
              });
              const materialCode = production_order_material?.materialCode ?? '';

              resultValue = `${materialCode}|${itemValue}`;
              break;
            }
            case NSSitewise.ESitewiseDashboartLabelType.FLOUR_LOSS_SHIFT: {
              // Sum dữ liệu quantity của production rejection có process_area_id bằng X,Y trong ca hiện tại.
              // X là line 3, Y là line 2
              const productionRejection = await this.productionRejectionRepo.find({
                where: {
                  processAreaId: item.processAreaId,
                  productionLineId: data.orgUnitLineId,
                  transactionDate: Between(
                    dateHelper.toStartOfDay(startTime),
                    dateHelper.toStartOfDay(endTime),
                  ),
                  shiftId: shift.id,
                },
              });
              resultValue = productionRejection?.reduce(
                (acc, curr) => acc + Number(curr.quantity),
                0,
              );
              break;
            }
            case NSSitewise.ESitewiseDashboartLabelType.STEAM_SHIFT: {
              //"Tính toán lượng steam tiêu thụ từ đầu ca theo dũ liệu trong bảng mes_dx_measures.
              // Cách tính theo từng khoảng thời gian của giá trị thay đổi theo công thức: lấy giá trị của mes_dx_metrics * khoảng thời gian /60.

              // Tôi có 1 tín hiệu "metricId" = A từ máy gửi lên liên tục tín hiệu steam lưu vào bảng mes_dx_metrics gồm các cột thông tin thời gian, giá trị là steam.
              // Các tín hiệu nãy sẽ gửi tuần tự cách nhau 1 phút.
              // Hãy viết giúp tôi một câu query trả về cho tôi biết danh sách giá trị steam và khoảng thời gian thay đổi của các giá trị steam đó bằng cách trừ thời gian của giá trị hiện tại với giá trị trước đó.
              if (shift) {
                let sumValue = 0;
                const value = await this.calculateByShiftStartValueByMetric(
                  item.assetId,
                  item.propertyId,
                  shift,
                );
                if (value) {
                  sumValue += Number(value || 0);
                }
                resultValue = sumValue?.toLocaleString();
              } else resultValue = 0;

              break;
            }
            case NSSitewise.ESitewiseDashboartLabelType.SHORT_SHIFT: {
              if (shift) {
                const calculateShortValue = await this.calculateShortValue(
                  startTime,
                  endTime,
                  shift.id,
                  data.orgUnitLineId,
                );
                resultValue = numberHelper.formatNumber(calculateShortValue);
              }
              break;
            }
            case NSSitewise.ESitewiseDashboartLabelType.SHORT_HOUR: {
              // Tính toán lượng short tiêu thụ từ đầu ca tới thời điểm của dữ liệu gần nhất và chia ra số giờ dựa theo dữ liệu trong bảng materialConsumptionScada.

              if (shift) {
                const now = dateHelper.newDate('Asia/Ho_Chi_Minh');
                const hours = dateHelper.subtractDatesAndGetHours(startTime, now);
                const calculateShortValue = await this.calculateShortValue(
                  startTime,
                  now,
                  shift.id,
                  data.orgUnitLineId,
                );
                resultValue = (calculateShortValue / hours).toFixed(2);
              }
              break;
            }
            case NSSitewise.ESitewiseDashboartLabelType.BROKEN_NOODLES_SHIFT: {
              // Lấy từ dữ liệu production rejection
              // Sum dữ liệu production rejection có process_area_id bằng Z,T trong ca hiện tại
              const productionRejection = await this.productionRejectionRepo.find({
                where: {
                  processAreaId: item.processAreaId,
                  productionLineId: data.orgUnitLineId,
                  transactionDate: Between(
                    dateHelper.toStartOfDay(startTime),
                    dateHelper.toStartOfDay(endTime),
                  ),
                  shiftId: shift.id,
                },
              });
              const result = productionRejection?.reduce((acc, curr) => acc + curr.quantity, 0);
              resultValue = numberHelper.formatNumber(result);
              break;
            }
            case NSSitewise.ESitewiseDashboartLabelType.AVERAGE_10_BOX: {
              // Lấy trung bình 10 tín hiệu gần nhất có giá trị > 0
              resultValue = await this.calculateAverage10Box(item.assetId, item.propertyId, shift);
              break;
            }
            default: {
              let itemValue = await this.getAssetPropertyValue(item.assetId, item.propertyId);
              resultValue = this.extractParamValue(itemValue?.propertyValue)?.value;
              break;
            }
          }
        else {
          let itemValue = await this.getAssetPropertyValue(item.assetId, item.propertyId);
          resultValue = this.extractParamValue(itemValue?.propertyValue)?.value;
        }
        result.push({
          case: item?.case,
          assetId: item.assetId,
          assetId1: item.assetId1,
          assetId2: item.assetId2,
          propertyId: item.propertyId,
          propertyIdStart: item.propertyIdStart,
          propertyIdEnd: item.propertyIdEnd,
          propertyIdG1: item.propertyIdG1,
          propertyIdG2: item.propertyIdG2,
          propertyIdG3: item.propertyIdG3,
          propertyIdR1: item.propertyIdR1,
          propertyIdR2: item.propertyIdR2,
          propertyIdR3: item.propertyIdR3,
          propertyIds: item.propertyIds,
          processAreaId: item.processAreaId,
          propertyBatchId: item?.propertyBatchId,
          propertyProductId: item?.propertyProductId,
          value: resultValue !== null ? resultValue : '',
        });
      }
      return result;
    } catch (error) {
      console.log(error);
      return result;
    }
  }

  extractParamValue(jsonData: any): Result | null {
    if (jsonData && jsonData.value) {
      const value = jsonData.value;
      if (value.stringValue !== null) {
        // try {
        //   const parsedString = JSON.parse(value.stringValue);
        //   if (parsedString.value && parsedString.dataType) {
        //     return { value: parsedString.value, dataType: parsedString.dataType };
        //   } else {
        //     return { value: value.stringValue, dataType: NSSitewise.ESitewiseDataType.STRING };
        //   }
        // } catch (error) {
        //   return { value: value.stringValue, dataType: NSSitewise.ESitewiseDataType.STRING };
        // }
        return {
          value: value.stringValue,
          dataType: NSSitewise.ESitewiseDataType.STRING,
        };
      }

      if (value.integerValue !== null) {
        return {
          value: numberHelper.formatNumber(value.integerValue),
          dataType: NSSitewise.ESitewiseDataType.INTEGER,
        };
      }

      if (value.doubleValue !== null) {
        return {
          value: numberHelper.formatNumber(value.doubleValue),
          dataType: NSSitewise.ESitewiseDataType.DOUBLE,
        };
      }

      if (value.booleanValue !== null) {
        return { value: value.booleanValue, dataType: NSSitewise.ESitewiseDataType.BOOLEAN };
      }

      return null; // Return null if all values are null.
    } else return null;
  }

  async calculateGoodCount(
    assetId: string,
    propertyIdStart: string,
    propertyIdEnd: string,
    shift: ShiftEntity,
  ) {
    let itemValueStart = await this.calculateByShiftStartValueByMetric(
      assetId,
      propertyIdStart,
      shift,
    );

    let itemValueEnd = await this.calculateByShiftStartValueByMetric(assetId, propertyIdEnd, shift);

    return itemValueEnd - itemValueStart;
  }

  /** Hàm tính toán theo giá trị bắt đầu của theo ca theo measurement */
  async calculateByShiftStartValue(assetId: string, propertyId: string, shift: ShiftEntity) {
    // Lấy giá trị của measurement này trừ giá trị của thời điểm bắt đầu của ca hiện tại
    let resultValue;
    if (shift) {
      // Lấy giá trị của measurement này trừ giá trị của thời điểm hiện tại (lấy trong từ Sitewise)
      let itemValue = await this.getAssetPropertyValue(assetId, propertyId);
      itemValue = this.extractParamValue(itemValue?.propertyValue)?.value;
      // Lấy giá trị của measurement này trừ giá trị của thời điểm bắt đầu của ca hiện tại (lấy trong từ Sitewise)
      const result = dateHelper.getTodayWithCustomTimePair(
        shift?.startTime, // ví dụ '22:00'
        shift?.endTime, // ví dụ '06:00'
        'Asia/Ho_Chi_Minh',
        'Asia/Ho_Chi_Minh', // Hoặc 'UTC' tùy hệ thống
      );

      const startTime = result?.startTime;
      const endTime = result?.endTime;

      const query = `SELECT 
        MAX(CAST("value" AS NUMERIC)) - MIN(CAST("value" AS NUMERIC)) AS value_difference
      FROM mes_dx_measurements
      WHERE 
        "datetime" BETWEEN '${dateHelper.formatDateForQueryWithTimeZoneMoment(startTime, 'Asia/Ho_Chi_Minh')}' AND '${dateHelper.formatDateForQueryWithTimeZoneMoment(endTime, 'Asia/Ho_Chi_Minh')}'
        AND "measurementId" = '${propertyId}'
        AND "value" ~ '^[0-9]+(\.[0-9]+)?$'
        AND CAST("value" AS NUMERIC) != 0;`;
      console.log(query, 'query');
      const resultQuery = await this.mesDxMeasurementRepo.query(query);
      resultValue = Number(resultQuery[0]?.value_difference);
    }

    return resultValue;
  }

  /** Hàm tính toán theo giá trị bắt đầu của theo ca theo metric.
   * Dùng để tính TOTAL_COUNT, GOOD_COUNT, REJECTION_COUNT, SUM_TOTAL_COUNT, SUM_GOOD_COUNT, SUM_REJECTION_COUNT, ELECTRICITY_SHIFT, STEAM_SHIFT
   * */
  async calculateByShiftStartValueByMetric(
    assetId: string,
    propertyId: string,
    shift: ShiftEntity,
  ) {
    // Lấy giá trị của measurement này trừ giá trị của thời điểm bắt đầu của ca hiện tại
    let resultValue;
    if (shift) {
      // Lấy giá trị của measurement này trừ giá trị của thời điểm hiện tại (lấy trong từ Sitewise)
      let itemValue = await this.getAssetPropertyValue(assetId, propertyId);
      itemValue = this.extractParamValue(itemValue?.propertyValue)?.value;
      // Lấy giá trị của measurement này trừ giá trị của thời điểm bắt đầu của ca hiện tại (lấy trong từ Sitewise)
      const result = dateHelper.getTodayWithCustomTimePair(
        shift?.startTime, // ví dụ '22:00'
        shift?.endTime, // ví dụ '06:00'
        'Asia/Ho_Chi_Minh',
        'Asia/Ho_Chi_Minh', // Hoặc 'UTC' tùy hệ thống
      );

      const startTime = result?.startTime;

      const query = `
            with metrics as (
            select	
              cast(value as numeric) as value,
              smd.datetime,
              lag(cast(value as numeric)) over (partition by smd."metricId" 
            order by
              datetime asc) as prev_value
            from
              scada_mes_dx_metrics smd
            where
              smd.datetime >= '${dateHelper.formatDateForQueryWithTimeZoneMoment(startTime, 'Asia/Ho_Chi_Minh')}'
              -- Thời điểm bắt đầu
              and smd.datetime <= CURRENT_TIMESTAMP
              -- Thời điểm hiện tại
              and value not like '[object Object]'
              and value is not null
              and smd."metricId" ='${propertyId}'
            ),
            differences as (
              select	
                datetime,
                case
                  when value >= prev_value then value - prev_value
                  else 0
                end as raw_difference
              from metrics
            ),
            filtered_differences as (
              select
                datetime,
                case 
                  when raw_difference > 2000 then 0
                  else raw_difference
                end as difference
              from differences
            )
            select	
              SUM(difference) as total_difference
            from
              filtered_differences
            where
              difference <> 0;`;
      const resultQuery = await this.machineParameterRepo.query(query);
      resultValue = Number(resultQuery[0]?.total_difference);
    }

    return resultValue;
  }

  async calculateByShiftStartValueCurrent(assetId: string, propertyId: string, shiftId?: string) {
    //"Electricity/shift cần lấy theo ca:
    // Lấy giá trị của measurement này trừ giá trị của thời điểm bắt đầu của ca hiện tại
    // Cách lấy ca hiện tại: từ thông tin ca trên line dashboard tìm trong bảng Shift để lấy startime.
    let resultValue;
    const whereCon: any = {};

    if (!shiftId) {
      const now = dayjs().tz('Asia/Ho_Chi_Minh').format('HH:mm:ss');
      whereCon.startTime = LessThanOrEqual(now);
      whereCon.endTime = MoreThanOrEqual(now);
    } else {
      whereCon.id = shiftId;
    }
    const shift = await this.shiftRepo.findOne({
      where: whereCon,
      order: { startTime: 'DESC' },
    });
    if (shift) {
      // Lấy giá trị của measurement này trừ giá trị của thời điểm hiện tại (lấy trong từ Sitewise)
      let itemValue = await this.getAssetPropertyValue(assetId, propertyId);
      itemValue =
        itemValue?.propertyValue?.value.integerValue || itemValue?.propertyValue?.value.doubleValue;
      // startDate.setHours(shift.startTime.split(':')[0]);
      const startTime = dayjs()
        .tz('Asia/Ho_Chi_Minh')
        .hour(Number(shift?.startTime?.split(':')[0]))
        .minute(Number(shift?.startTime?.split(':')[1]))
        .second(Number(shift?.startTime?.split(':')[2]));
      const endTime = dayjs()
        .tz('Asia/Ho_Chi_Minh')
        .hour(Number(shift?.endTime?.split(':')[0]))
        .minute(Number(shift?.endTime?.split(':')[1]))
        .second(Number(shift?.endTime?.split(':')[2]));
      // Trừ 7 giờ
      const start = startTime.utc().toDate();
      const end = endTime.utc().toDate();
      // Lấy giá trị của measurement này trừ giá trị của thời điểm bắt đầu của ca hiện tại (lấy trong từ Sitewise)
      const startValue = await this.getAssetPropertyValueHistory(
        assetId,
        propertyId,
        start,
        end,
        2,
      );
      if (startValue && startValue[0]) {
        const subValue = startValue[0].value.integerValue || startValue[0].value.doubleValue;
        // Trừ giá trị của measurement này trừ giá trị của thời điểm hiện tại và thời điểm bắt đầu ca

        const itemNum = Number.parseFloat(itemValue);
        const subNum = Number.parseFloat(subValue);
        if (!isNaN(itemNum) && !isNaN(subNum)) {
          resultValue = (itemNum - subNum).toString();
        }
      } else resultValue = itemValue;
    }
    return resultValue;
  }

  async calculateSumValueMetricByShift(assetId: string, metricId: string) {
    const whereCon: any = {};
    const now = dayjs().tz('Asia/Ho_Chi_Minh').format('HH:mm:ss');
    whereCon.startTime = LessThanOrEqual(now);
    whereCon.endTime = MoreThanOrEqual(now);
    const shift = await this.shiftRepo.findOne({
      where: whereCon,
      order: { startTime: 'DESC' },
    });
    if (!shift) return 0;
    const startTime = dayjs()
      .tz('Asia/Ho_Chi_Minh')
      .hour(Number(shift?.startTime?.split(':')[0]))
      .minute(Number(shift?.startTime?.split(':')[1]))
      .second(Number(shift?.startTime?.split(':')[2]))
      .format('YYYY-MM-DD HH:mm:ss');
    const endTime = dayjs()
      .tz('Asia/Ho_Chi_Minh')
      .hour(Number(shift?.endTime?.split(':')[0]))
      .minute(Number(shift?.endTime?.split(':')[1]))
      .second(Number(shift?.endTime?.split(':')[2]))
      .format('YYYY-MM-DD HH:mm:ss');
    const queryParam: any[] = [assetId, metricId, startTime, endTime];
    const baseSql = `
    WITH df_utility AS (
      SELECT
        ou_site.code "site",
        um.* 
      FROM
        utility_meter um
        INNER JOIN organization_units ou_factory ON um.factory_id = ou_factory.
        ID INNER JOIN organization_units ou_site ON ou_factory."parentId" = ou_site.ID 
      WHERE
        um.asset_id = $1
        AND um.metric_id = $2
        AND um.is_active = TRUE 
    ),
    df_metric_utility AS (
      SELECT
        smdm.*,
        du.ID AS utility_id,
        du.site,
        du.cal_method_code,
        du.code,
        du."name",
        du.tag_name,
        du.utility_meter_type_detail_code
      FROM
        scada_mes_dx_metrics smdm
        INNER JOIN ( SELECT DISTINCT site, ID, asset_id, metric_id, cal_method_code, code, "name", tag_name, utility_meter_type_detail_code FROM df_utility ) AS du ON smdm."assetId" = du.asset_id 
        AND smdm."metricId" = du.metric_id 
      WHERE
        smdm.datetime BETWEEN $3
        AND $4
    ),
    marked AS (
      SELECT
        *,
      CASE
          
          WHEN 
          VALUE
            :: NUMERIC != 0 
            AND (
              LAG( VALUE :: NUMERIC, 1 ) OVER ( PARTITION BY site, "assetId", "metricId", utility_id, tag_name ORDER BY datetime ) = 0 
              OR LAG( VALUE :: NUMERIC, 1 ) OVER ( PARTITION BY site, "assetId", "metricId", utility_id, tag_name ORDER BY datetime ) IS NULL 
              OR ABS(
                LAG( VALUE :: NUMERIC, 1 ) OVER ( PARTITION BY site, "assetId", "metricId", utility_id, tag_name ORDER BY datetime ) - 
                VALUE
                  :: NUMERIC 
              ) > 1000 
              ) THEN
              1 ELSE 0 
            END AS new_group 
          FROM
            df_metric_utility 
          WHERE
            cal_method_code = 'DIFFERENCE_BASED' 
        ),
        numbered AS (
          SELECT
            *,
            SUM( new_group ) OVER ( PARTITION BY site, "assetId", "metricId", utility_id, tag_name ORDER BY datetime ROWS UNBOUNDED PRECEDING ) AS segment_id 
          FROM
            marked 
        ),
        filtered AS ( SELECT * FROM numbered WHERE VALUE :: NUMERIC != 0 ),
        range_values AS (
          SELECT DISTINCT
            site,
            "assetId",
            "metricId",
            utility_id,
            code,
            "name",
            tag_name,
            segment_id,
            utility_meter_type_detail_code,
    --  MIN(value::numeric) AS min_val, MAX(value::numeric) AS max_val
            FIRST_VALUE( VALUE :: NUMERIC ) OVER w AS min_val,
            LAST_VALUE( VALUE :: NUMERIC ) OVER w2 AS max_val 
          FROM
            filtered --  GROUP BY site,"assetId","metricId",utility_id,code,"name",tag_name, segment_id
            WINDOW w AS (
              PARTITION BY site,
              "assetId",
              "metricId",
              utility_id,
              code,
              "name",
              tag_name,
              segment_id 
              ORDER BY
                datetime ROWS BETWEEN UNBOUNDED PRECEDING 
              AND UNBOUNDED FOLLOWING 
            ),
            w2 AS (
              PARTITION BY site,
              "assetId",
              "metricId",
              utility_id,
              code,
              "name",
              tag_name,
              segment_id 
              ORDER BY
                datetime ROWS BETWEEN UNBOUNDED PRECEDING 
              AND UNBOUNDED FOLLOWING 
            ) 
        ),
        final_diff AS (
          SELECT
            site,
            "assetId",
            "metricId",
            utility_id,
            code,
            "name",
            tag_name,
            SUM( max_val - min_val ) AS total_diff,
            utility_meter_type_detail_code
          FROM
            range_values 
          GROUP BY
            site,
            "assetId",
            "metricId",
            utility_id,
            code,
            "name",
            tag_name,
            utility_meter_type_detail_code 
        ),
        df_utility_flow AS (
          SELECT
            dmu.site,
            dmu.utility_id,
            b.code,
            b.NAME,
            b.tag_name,
            b.utility_meter_type_detail_code,
            dmu."assetId",
            dmu."metricId",
            dmu.cal_method_code,
            SUM( val_per_min ) AS utility_val 
          FROM
            ( SELECT dmus.*, dmus.VALUE :: NUMERIC / 60 AS val_per_min FROM df_metric_utility dmus WHERE dmus.cal_method_code = 'FLOWRATE-BASED' ) AS dmu
            INNER JOIN df_utility b ON dmu.utility_id = b.ID 
          GROUP BY
            dmu.site,
            dmu.utility_id,
            b.code,
            b.NAME,
            b.tag_name,
            b.utility_meter_type_detail_code,
            dmu."assetId",
            dmu."metricId",
            dmu.cal_method_code
        ) SELECT
        site "Site",
        code "Machine Code",
        "name" "Machine Name",
        tag_name "Tag",
        'DIFFERENCE_BASED' "Method",
        total_diff "value",
        utility_meter_type_detail_code "Type"
      FROM
        final_diff UNION ALL
      SELECT
        b.site "Site",
        b.code "Machine Code",
        b.NAME "Machine Name",
        b.tag_name "Tag",
        b.cal_method_code "Method",
        b.utility_val "value" ,
      b.utility_meter_type_detail_code "Type"
      FROM
        df_utility_flow b 
      ORDER BY
        "Site",
    "Machine Code",
    "Method"
    `;
    const result = await this.entityManager.query(baseSql, queryParam);
    if (!result || !result[0] || !result[0].value) return 0;
    return result[0].value;
  }

  async calculateAverage10Box(assetId: string, propertyId: string, shift: ShiftEntity) {
    const params = {
      assetId: assetId,
      propertyId: propertyId,
      startDate: dateHelper.getTodayWithCustomTime(
        shift?.startTime,
        'Asia/Ho_Chi_Minh',
        'Asia/Ho_Chi_Minh',
      ),
      endDate: dateHelper.getTodayWithCustomTime(
        shift?.endTime,
        'Asia/Ho_Chi_Minh',
        'Asia/Ho_Chi_Minh',
      ),
      maxResults: 200,
    };
    let sumValue = 0;
    let count = 0;
    let nextToken: string | undefined;
    let loop = 0;
    do {
      loop++;
      const response = await this.sitewise
        .getAssetPropertyValueHistory({ ...params, nextToken })
        .promise();
      for (const item of response?.assetPropertyValueHistory) {
        let itemValue = this.extractParamValue(item)?.value;
        if (typeof itemValue === 'string') {
          itemValue = numberHelper.stringToNumber(itemValue);
        } else {
          itemValue = Number(itemValue);
        }
        if (itemValue > 0 && count < 10) {
          sumValue += itemValue;
          count++;
        }
      }
      nextToken = response.nextToken;
    } while (count < 10 && loop < 100);

    return (sumValue / 10).toFixed(2);
  }

  async calculateShortValue(
    startTime: Date,
    endTime: Date,
    shiftId: string,
    orgUnitLineId: string,
  ) {
    const materialGroupHeader = await this.materialGroupHeaderRepo.findOne({
      where: {
        materialGroupCode: 'F_KPI_MATERIAL_SHORT',
      },
    });
    const materialGroupDetail = await this.materialGroupDetailRepo.find({
      where: {
        headerId: materialGroupHeader?.id,
      },
    });
    const listIngredientId = materialGroupDetail?.map((p) => p.itemId);
    // TODO: convêr planStartDate về 00:00:00
    const listProductionOrder = await this.productionOrderRepo.find({
      where: {
        planStartDate: dateHelper.toStartOfDay(startTime),
        shiftId: shiftId,
        lineId: orgUnitLineId,
      },
    });
    const materialConsumptionScada = await this.materialConsumptionScadaRepo.find({
      where: {
        ingredientId: In(listIngredientId),
        orderId: In(listProductionOrder?.map((p) => p.id)),
      },
    });

    const sumValue = materialConsumptionScada?.reduce((acc, curr) => acc + Number(curr.qty), 0);
    const resultValue = sumValue;

    return resultValue;
  }

  //#endregion

  //#region OEE
  async calculateOverallOEE(shiftId: string, orgUnitLineId: string) {
    const shift = await this.shiftRepo.findOne({
      where: {
        id: shiftId,
      },
    });
    if (!shift) {
      return {
        oee: 0,
        availability: 0,
        performance: 0,
        quality: 0,
        expectedProductQty: 0,
        actualProductQty: 0,
        defectiveProducts: 0,
        operationTime: 0,
        shutdownLossTime: 0,
        loadingTime: 0,
        downtime: 0,
        utilisationTime: 0,
      };
    }
    const result = dateHelper.getTodayWithCustomTimePair(
      shift?.startTime, // ví dụ '22:00'
      shift?.endTime, // ví dụ '06:00'
      'Asia/Ho_Chi_Minh',
      'Asia/Ho_Chi_Minh', // Hoặc 'UTC' tùy hệ thống
    );

    if (!result) {
      return {
        oee: 0,
        availability: 0,
        performance: 0,
        quality: 0,
        expectedProductQty: 0,
        actualProductQty: 0,
        defectiveProducts: 0,
        operationTime: 0,
        shutdownLossTime: 0,
        loadingTime: 0,
        downtime: 0,
        utilisationTime: 0,
      };
    }
    // if (result) {
    const startTime = result?.startTime;
    const endTime = result?.endTime;

    const processArea = await this.organizationUnitRepo.find({
      where: {
        parentId: orgUnitLineId,
      },
    });
    // Tìm bảng process từ organization_unit_id của process area đang cần tính OEE.
    const process = await this.processRepo.find({
      where: {
        organizationUnitId: In(processArea?.map((p) => p.id)),
      },
    });

    const processMachine = await this.processMachineRepo.find({
      where: {
        processId: In(process?.map((p) => p.id)),
      },
    });

    // 1. Tính Operation Time
    const operationTime = dateHelper.convertHHMMSSToMM(
      dateHelper.subtractTimeStrings(shift?.startTime, shift?.endTime),
    );

    // 2. Tính Shutdown Loss Time
    const shutdownLossTime = await this.calculateShutdownLossTime(shift, startTime, endTime);

    // 3. Tính Loading Time
    const loadingTime = numberHelper.checkNaN(operationTime - shutdownLossTime);

    // 4. Tính Downtime
    const downtime = await this.calculateDowntime(shift, startTime, endTime);

    // 5. Tính Utilisation Time
    const utilisationTime = numberHelper.checkNaN(loadingTime - downtime);

    // 6. Tính Availability
    const availability = numberHelper.checkNaN(((loadingTime - downtime) / loadingTime) * 100);

    // 7. Tính Expected Product Quantity
    const expectedProductQty = await this.calculateExpectedProductQty(
      startTime,
      endTime,
      processMachine,
      processArea?.map((p) => p.id),
    );

    const { result4, result9 } = await this.calculateActualAndDefectiveProducts(
      startTime,
      orgUnitLineId,
    );

    const actualProductQty = numberHelper.checkNaN(Number(result4)); //+ numberHelper.checkNaN(Number(result9));

    // 9. Tính Performance
    const performance = numberHelper.checkNaN(
      (actualProductQty / (expectedProductQty === 0 ? 1 : expectedProductQty)) * 100,
    );

    // 10. Tính Defective Products
    const defectiveProducts = result9;

    // 11. Tính Quality
    const quality = numberHelper.checkNaN(
      ((actualProductQty - defectiveProducts) / (actualProductQty === 0 ? 1 : actualProductQty)) *
        100,
    );

    // 12. Tính OEE cuối cùng
    const oee = (availability * performance * quality) / 10000; // Chia cho 10000 vì mỗi metric là phần trăm

    return {
      oee,
      availability,
      performance,
      quality,
      expectedProductQty,
      actualProductQty,
      defectiveProducts,
      operationTime,
      shutdownLossTime,
      loadingTime,
      downtime,
      utilisationTime,
    };
  }

  async calculateOverallOEEAll() {
    const listLineAndShift = await this.getAllLineAndShift();
    let oee = 0;
    let availability = 0;
    let performance = 0;
    let quality = 0;
    let expectedProductQty = 0;
    let actualProductQty = 0;
    let defectiveProducts = 0;
    let operationTime = 0;
    let shutdownLossTime = 0;
    let loadingTime = 0;
    let downtime = 0;
    let utilisationTime = 0;
    let result4All = 0;
    let result9All = 0;
    for (const item of listLineAndShift) {
      const shift = await this.shiftRepo.findOne({
        where: {
          id: item.shiftId,
        },
      });
      if (!shift) {
        continue;
      }

      const result = dateHelper.getTodayWithCustomTimePair(
        shift?.startTime, // ví dụ '22:00'
        shift?.endTime, // ví dụ '06:00'
        'Asia/Ho_Chi_Minh',
        'Asia/Ho_Chi_Minh', // Hoặc 'UTC' tùy hệ thống
      );

      if (!result) {
        continue;
      }

      const startTime = result?.startTime;
      const endTime = result?.endTime;

      const processArea = await this.organizationUnitRepo.find({
        where: {
          parentId: item.orgUnitLineId,
        },
      });
      // Tìm bảng process từ organization_unit_id của process area đang cần tính OEE.
      const process = await this.processRepo.find({
        where: {
          organizationUnitId: In(processArea?.map((p) => p.id)),
        },
      });

      const processMachine = await this.processMachineRepo.find({
        where: {
          processId: In(process?.map((p) => p.id)),
        },
      });

      // 1. Tính Operation Time
      operationTime += dateHelper.convertHHMMSSToMM(
        dateHelper.subtractTimeStrings(shift?.startTime, shift?.endTime),
      );

      // 2. Tính Shutdown Loss Time
      shutdownLossTime += await this.calculateShutdownLossTime(shift, startTime, endTime);

      // 3. Tính Loading Time
      loadingTime += numberHelper.checkNaN(operationTime - shutdownLossTime);

      // 4. Tính Downtime
      downtime += await this.calculateDowntime(shift, startTime, endTime);

      // 7. Tính Expected Product Quantity
      expectedProductQty += await this.calculateExpectedProductQty(
        startTime,
        endTime,
        processMachine,
        processArea?.map((p) => p.id),
      );

      const { result4, result9 } = await this.calculateActualAndDefectiveProducts(
        startTime,
        item.orgUnitLineId,
      );

      result4All += Number(result4);
      result9All += Number(result9);
    }

    // // 5. Tính Utilisation Time
    utilisationTime = numberHelper.checkNaN(loadingTime - downtime);

    // // 6. Tính Availability
    availability = numberHelper.checkNaN(((loadingTime - downtime) / loadingTime) * 100);

    actualProductQty = numberHelper.checkNaN(Number(result4All)); //+ numberHelper.checkNaN(Number(result9All));

    // // 9. Tính Performance
    performance = numberHelper.checkNaN(
      (actualProductQty / (expectedProductQty === 0 ? 1 : expectedProductQty)) * 100,
    );

    // 10. Tính Defective Products
    defectiveProducts = result9All;

    // 11. Tính Quality
    quality = numberHelper.checkNaN(
      ((actualProductQty - defectiveProducts) / (actualProductQty === 0 ? 1 : actualProductQty)) *
        100,
    );
    // 12. Tính OEE cuối cùng
    oee = (availability * performance * quality) / 10000; // Chia cho 10000 vì mỗi metric là phần trăm

    return {
      oee,
      availability,
      performance,
      quality,
      expectedProductQty,
      actualProductQty,
      defectiveProducts,
      operationTime,
      shutdownLossTime,
      loadingTime,
      downtime,
      utilisationTime,
    };
  }

  // Tính Shutdown Loss Time
  async calculateShutdownLossTime(
    shift: ShiftEntity,
    shiftStartDate: Date,
    shiftEndDate: Date,
  ): Promise<number> {
    let totalDowntime = 0;
    const currentTime = dateHelper.newDate();
    // Lấy downtime có reason code là SHUTDOWN và thời gian bắt đầu downtime nằm trong khoảng từ 00:00:00 đến 23:59:59 của ngày hiện tại
    const productionDowntimes = await this.productionDowntimeRepo.find({
      where: {
        startTime: Between(
          dateHelper.getTodayWithCustomTime('00:00:00', 'Asia/Ho_Chi_Minh', 'Asia/Ho_Chi_Minh'),
          dateHelper.getTodayWithCustomTime('23:59:59', 'Asia/Ho_Chi_Minh', 'Asia/Ho_Chi_Minh'),
        ),
        reasonCode: NSReason.EReasonGroup.SHUTDOWN,
      },
    });

    for (const downtime of productionDowntimes) {
      // TH1: Downtime xảy ra trong ca và đã kết thúc với thời gian kết thúc <= thời gian hiện tại: tính toàn bộ thời gian downtime.
      if (
        downtime?.startTime &&
        downtime?.startTime?.getTime() >= shiftStartDate.getTime() &&
        downtime?.endTime &&
        downtime?.endTime?.getTime() <= shiftEndDate.getTime() &&
        downtime?.endTime?.getTime() <= currentTime.getTime()
      ) {
        totalDowntime = dateHelper.convertHHMMSSToMM(
          dateHelper.subtractDatesAndGetHHmmss(downtime?.endTime, downtime?.startTime),
        );
      }

      // TH2: Nếu downtime xảy ra trước ca và đã kết thúc với thời gian kết thúc <= thời gian hiện tại: tính thời gian downtime từ đầu ca đến thời điểm kết thúc downtime
      else if (
        downtime?.startTime &&
        downtime?.startTime?.getTime() < shiftStartDate.getTime() &&
        downtime?.endTime &&
        downtime?.endTime?.getTime() <= currentTime.getTime()
      ) {
        totalDowntime = dateHelper.convertHHMMSSToMM(
          dateHelper.subtractDatesAndGetHHmmss(downtime?.endTime, shiftStartDate),
        );
      }
      // TH3: nếu downtime xảy ra trong ca và chưa kết thúc: tính thời gian downtime từ thời gian bắt đầu downtime đến thời điểm hiện tại.
      else if (
        downtime?.startTime &&
        downtime?.startTime?.getTime() >= shiftStartDate.getTime() &&
        downtime?.endTime === null
      ) {
        totalDowntime = dateHelper.convertHHMMSSToMM(
          dateHelper.subtractDatesAndGetHHmmss(currentTime, downtime?.startTime),
        );
      }
      // TH4: nếu downtime xảy ra trước ca và chưa kết thúc: tính thời gian downtime từ đầu ca đến thời điểm hiện tại.
      else if (
        downtime?.startTime &&
        downtime?.startTime?.getTime() < shiftStartDate.getTime() &&
        downtime?.endTime === null
      ) {
        totalDowntime = dateHelper.convertHHMMSSToMM(
          dateHelper.subtractDatesAndGetHHmmss(currentTime, shiftStartDate),
        );
      }
    }

    return totalDowntime;
  }

  // Tính Downtime
  async calculateDowntime(
    shift: ShiftEntity,
    shiftStartDate: Date,
    shiftEndDate: Date,
  ): Promise<number> {
    let totalDowntime = 0;
    const currentTime = dateHelper.newDate();
    const productionDowntimes = await this.productionDowntimeRepo.find({
      where: {
        startTime: Between(shiftStartDate, shiftEndDate),
        reasonCode: NSReason.EReasonGroup.DOWNTIME,
      },
    });
    for (const downtime of productionDowntimes) {
      // TH1: Nếu downtime xảy ra trong ca và đã kết thúc với thời gian kết thúc <= thời gian hiện tại: tính toàn bộ thời gian downtime.
      if (
        downtime?.startTime &&
        downtime?.startTime?.getTime() >= shiftStartDate.getTime() &&
        downtime?.endTime &&
        downtime?.endTime?.getTime() <= shiftEndDate.getTime() &&
        downtime?.endTime?.getTime() <= currentTime.getTime()
      ) {
        totalDowntime = dateHelper.convertHHMMSSToMM(
          dateHelper.subtractDatesAndGetHHmmss(downtime?.endTime, downtime?.startTime),
        );
      }
      // TH2: Nếu downtime xảy ra trước ca và đã kết thúc với thời gian kết thúc <= thời gian hiện tại: tính thời gian downtime từ đầu ca đến thời điểm kết thúc downtime
      else if (
        downtime?.startTime &&
        downtime?.startTime?.getTime() < shiftStartDate.getTime() &&
        downtime?.endTime &&
        downtime?.endTime?.getTime() <= currentTime.getTime()
      ) {
        totalDowntime = dateHelper.convertHHMMSSToMM(
          dateHelper.subtractDatesAndGetHHmmss(downtime?.endTime, shiftStartDate),
        );
      }
      // TH3: nếu downtime xảy ra trong ca và chưa kết thúc: tính thời gian downtime từ thời gian bắt đầu downtime đến thời điểm hiện tại.
      else if (
        downtime?.startTime &&
        downtime?.startTime?.getTime() >= shiftStartDate.getTime() &&
        downtime?.endTime === null
      ) {
        totalDowntime = dateHelper.convertHHMMSSToMM(
          dateHelper.subtractDatesAndGetHHmmss(currentTime, downtime?.startTime),
        );
      }
      // TH4: nếu downtime xảy ra trước ca và chưa kết thúc: tính thời gian downtime từ đầu ca đến thời điểm hiện tại.
      else if (
        downtime?.startTime &&
        downtime?.startTime?.getTime() < shiftStartDate.getTime() &&
        downtime?.endTime === null
      ) {
        totalDowntime = dateHelper.convertHHMMSSToMM(
          dateHelper.subtractDatesAndGetHHmmss(currentTime, shiftStartDate),
        );
      }
    }

    return totalDowntime;
  }

  // Tính Expected Product Quantity
  async calculateExpectedProductQty(
    startTime: Date,
    endTime: Date,
    processMachine: ProcessMachineEntity[],
    processAreaId: Array<string>,
  ): Promise<number> {
    let result = 0;
    // --B1. Lấy tín hiệu standard speed, mỗi line chỉ có 1 tín hiệu standard speed
    // -- Ra các tín hiệu tương ứng với từng line
    // --and mp."machineId" in (...) để lấy tín hiệu tương ứng với line cần tính OEE
    const machineParameterCount = await this.machineParameterRepo.findOne({
      where: {
        machineId: In(processMachine.map((pm) => pm.machineId)),
        oeeCal: 1, // OEE_Cal
        typeCode: 8,
        // iotsitewisePropertyTypeCode: 2,
      },
    });
    if (machineParameterCount) {
      const processAreaIdForQuery = processAreaId.map((id) => `'${id}'`).join(',');
      const propertyId = machineParameterCount?.iotsitewisePropertyId;
      // --B2: Tìm các khoảng thay đổi giá trị của tín hiệu standard từ đầu ca đến hiện tại
      // --B3. Dựa vào dữ liệu downtime để tìm ra những khoảng thời gian của B2 mà không downtime
      // --B4. Lấy giá trị của tín hiệu nhân với số phút của từng khoảng thời gian ở B3, sau đó cộng lại sẽ ra expected qty
      // Tôi có 1 tín hiệu metricId='A' từ máy Seasoning gửi lên liên tục tín hiệu standard speed lưu vào bảng scada_mes_dx_metrics gồm các cột thông tin thời gian, giá trị là standard speed. Lưu mỗi phút 1 lần.
      // Tôi có bảng production_downtime gồm các cột thông tin thời gian bắt đầu: startTime và thời gian kết thúc downtime: endTime.
      // Tôi có bảng reason_master với cột groupCode. Bảng này sẽ link với production_downtime bằng cột reasonCode. Chỉ lấy các downtime có groupCode = SHUTDOWN
      // Hãy viết giúp tôi một câu query: Tìm các khoảng thay đổi giá trị của tín hiệu standard từ đầu ca đến hiện tại, Dựa vào dữ liệu downtime để tìm ra những khoảng thời gian mà không downtime.
      // Lấy giá trị của tín hiệu nhân với số phút của từng khoảng thời gian mà không downtime
      const query = `
      SELECT SUM
        ( CAST ( M.VALUE AS NUMERIC ) ) AS 
      VALUE
        
      FROM
        scada_mes_dx_metrics
        M LEFT JOIN production_downtime d ON M."datetime" >= d."startTime" 
        AND M."datetime" <= COALESCE ( d."endTime", '9999-12-31 23:59:59' ) 
        AND d."cancelledFlag" != 1
        AND d."processAreaId" IN (${processAreaIdForQuery})  -- điều kiện thêm
        LEFT JOIN reason_master rm ON d."reasonCode" = rm.code 
      WHERE
        M."datetime" BETWEEN '${dateHelper.formatDateForQueryWithTimeZoneMoment(startTime, 'Asia/Ho_Chi_Minh')}' 
        AND '${dateHelper.formatDateForQueryWithTimeZoneMoment(endTime, 'Asia/Ho_Chi_Minh')}' --truyen thoi gian bat dau - ket thuc ca
        -- AND m."value" ~ '^[-+]?[0-9]+(\\.[0-9]+)?$'
        AND (
          d.ID IS NULL 
          OR (
            rm."groupCode" = 'DOWNTIME' 
            AND rm.ID IS NULL 
          ) 
        )
        AND M."metricId" = '${propertyId}'
      `;
      const excuteQuery = await this.machineParameterRepo.query(query);
      if (excuteQuery && excuteQuery.length > 0) {
        result = Number(excuteQuery[0].value);
      }
    }
    return result;
  }

  async calculateActualAndDefectiveProducts(startTime: Date, orgUnitLineId: string) {
    const now = dateHelper.newDate('Asia/Ho_Chi_Minh');
    const query = `WITH metrics AS (
                    SELECT 
                    ou."parentId" AS line_id,
                    smd."metricId" AS metricId,
                    mp."desc" as metric_desc,
                    mp."typeCode" as typeCode,
                    CAST(value AS NUMERIC) AS value,
                    smd.datetime,
                    LAG(CAST(value AS NUMERIC)) OVER (PARTITION BY smd."metricId" ORDER BY datetime ASC) AS prev_value
                    FROM 
                    scada_mes_dx_metrics smd
                    JOIN machine_parameter mp ON mp."iotsitewisePropertyId" = smd."metricId"
                    JOIN process_machine pm ON pm."machineId" = mp."machineId" AND pm.assign = TRUE
                    JOIN process p ON p.id = pm."processId"
                    JOIN organization_units ou ON ou.id = p."organizationUnitId"
                    WHERE 
                    smd.datetime >= '${dateHelper.formatDateForQueryWithTimeZoneMoment(startTime, 'Asia/Ho_Chi_Minh')}' -- Thời điểm bắt đầu
                    AND smd.datetime <= '${dateHelper.formatDateForQueryWithTimeZoneMoment(now, 'Asia/Ho_Chi_Minh')}' -- Thời điểm hiện tại
                    AND value NOT LIKE '[object Object]'
                    AND value IS NOT NULL
                    AND mp."typeCode" IN ('4', '9')
                    AND mp."oeeCal" = 1
                    AND mp."iotsitewisePropertyTypeCode" = '2'
                    AND mp."isActive" = TRUE
                    AND ou."parentId" = '${orgUnitLineId}'
                    -- AND smd."metricId" = '65006049-2455-4ff8-9349-a81aeb6a1a2b'
                    ),
                    differences AS (
                      SELECT 
                        line_id,
                        metricId,
                        metric_desc,
                        typeCode,
                        datetime,
                        CASE 
                          WHEN value >= prev_value THEN value - prev_value
                          ELSE 0
                        END AS raw_difference
                      FROM metrics
                    ),
                    filtered_differences AS (
                        SELECT
                          line_id,
                          metricId,
                          metric_desc,
                          typeCode,
                          datetime,
                          CASE 
                            WHEN raw_difference > 2000 THEN 0
                            ELSE raw_difference
                          END AS difference
                        FROM differences
                    )
                    select
                      line_id,
                      metricId,
                      metric_desc,
                      typeCode,
                      SUM(difference) AS total_difference
                    FROM 
                        filtered_differences
                    WHERE
                      difference <> 0
                    GROUP BY 
                      line_id,
                      metricId,
                      metric_desc,
                      typeCode
                    ORDER by
                      line_id,
                      metric_desc;`;
    let queryResult = await this.machineParameterRepo.query(query);
    let result4 = 0;
    let result9 = 0;
    for (const itemQ of queryResult) {
      if (itemQ.typecode === '4') {
        result4 += Number(itemQ.total_difference);
      } else if (itemQ.typecode === '9') {
        result9 += Number(itemQ.total_difference);
      }
    }
    return {
      result4,
      result9,
    };
  }

  async getOeeForChart(params: getOeeChartReq) {
    const where: any = {};

    if (params.productionLineId) {
      const line = await this.organizationUnitRepo.findOne({
        where: {
          id: params.productionLineId,
          levelGeneralDataDetailCode: 'LINE',
        },
      });

      if (!line) return [];

      const isValidCategory = !params.categoryId || line.categoryId === params.categoryId;

      const factory = await this.organizationUnitRepo.findOne({
        where: {
          id: line.parentId,
          levelGeneralDataDetailCode: 'FACTORY',
        },
      });

      const isValidFactory = !params.factoryId || factory?.id === params.factoryId;

      const site = await this.organizationUnitRepo.findOne({
        where: {
          id: factory?.parentId,
          levelGeneralDataDetailCode: 'SITE',
        },
      });

      const isValidSite = !params.siteId || site?.id === params.siteId;

      if (isValidCategory && isValidFactory && isValidSite) {
        where.productionLineId = params.productionLineId;
      } else {
        return [];
      }
    } else if (params.categoryId || params.factoryId || params.siteId) {
      let lines: any[] = [];

      let categoryLines: any[] = [];
      if (params.categoryId) {
        categoryLines = await this.organizationUnitRepo.find({
          where: {
            categoryId: params.categoryId,
            levelGeneralDataDetailCode: 'LINE',
          },
        });
      }

      let validFactoryIds: string[] = [];
      if (params.siteId) {
        const factories = await this.organizationUnitRepo.find({
          where: {
            parentId: params.siteId,
            levelGeneralDataDetailCode: 'FACTORY',
          },
        });
        validFactoryIds = factories.map((f) => f.id);
      }

      if (params.factoryId) {
        validFactoryIds.push(params.factoryId);
      }

      if (validFactoryIds.length > 0) {
        const linesInFactory = await this.organizationUnitRepo.find({
          where: {
            parentId: In(validFactoryIds),
            levelGeneralDataDetailCode: 'LINE',
          },
        });

        if (params.categoryId) {
          const categoryLineIds = new Set(categoryLines.map((l) => l.id));
          lines = linesInFactory.filter((line) => categoryLineIds.has(line.id));
        } else {
          lines = linesInFactory;
        }
      } else {
        lines = categoryLines;
      }

      if (lines.length === 0) return [];
      where.productionLineId = In(lines.map((l) => l.id));
    }

    if (params.startDate && params.endDate)
      where.productionDate = Between(new Date(params.startDate), new Date(params.endDate));

    const data = await this.productionOeeRepo.find({
      where,
      order: {
        productionDate: 'ASC',
      },
    });
    const productionLineIds = [...new Set(data.map((oee) => oee.productionLineId))];

    const productionLines = await this.organizationUnitRepo.find({
      where: { id: In(productionLineIds) },
    });
    const productionLineMap = new Map(productionLines.map((line) => [line.id, line]));

    const factoryIds = [...new Set(productionLines.map((line) => line.parentId))];
    const factories = await this.organizationUnitRepo.find({ where: { id: In(factoryIds) } });
    const factoryMap = new Map(factories.map((f) => [f.id, f]));

    const siteIds = [...new Set(factories.map((f) => f.parentId))];
    const sites = await this.organizationUnitRepo.find({ where: { id: In(siteIds) } });
    const siteMap = new Map(sites.map((s) => [s.id, s]));

    const shiftIds = [...new Set(data.map((x) => x.shiftId))];

    const shifts = await this.shiftRepo.findByIds(shiftIds);

    const shiftMap = shifts.reduce((acc, cur) => {
      acc[cur.id] = cur.code;
      return acc;
    }, {});

    const enrichedData = data.map((item) => {
      const productionLine = productionLineMap.get(item.productionLineId);
      const factory = factoryMap.get(productionLine?.parentId);
      const site = siteMap.get(factory?.parentId);
      return {
        name: `${dateHelper.formatDateForOeeChart(item.productionDate)}-${shiftMap[item.shiftId] || ''}`,
        oee: Number(item.oee).toFixed(2),
        productionDate: item.productionDate,
        lineName: `${site?.code || ''}${factory?.code || ''}${productionLine?.code || ''}`,
      };
    });
    const sortedEnrichedData = enrichedData.sort((a, b) => {
      const dateCompare =
        new Date(a.productionDate).getTime() - new Date(b.productionDate).getTime();
      if (dateCompare !== 0) return dateCompare;
      const shiftA = a.name.split('-')[1];
      const shiftB = b.name.split('-')[1];
      return shiftA.localeCompare(shiftB);
    });

    return sortedEnrichedData;
  }

  async getOeeForDetails(params: getOeeDetailsReq): Promise<any[]> {
    const where: any = {};
    if (params.productionLineId) {
      const line = await this.organizationUnitRepo.findOne({
        where: {
          id: params.productionLineId,
          levelGeneralDataDetailCode: 'LINE',
        },
      });

      if (!line) return [];

      const isValidCategory = !params.categoryId || line.categoryId === params.categoryId;

      const factory = await this.organizationUnitRepo.findOne({
        where: {
          id: line.parentId,
          levelGeneralDataDetailCode: 'FACTORY',
        },
      });

      const isValidFactory = !params.factoryId || factory?.id === params.factoryId;

      const site = await this.organizationUnitRepo.findOne({
        where: {
          id: factory?.parentId,
          levelGeneralDataDetailCode: 'SITE',
        },
      });

      const isValidSite = !params.siteId || site?.id === params.siteId;

      if (isValidCategory && isValidFactory && isValidSite) {
        where.productionLineId = params.productionLineId;
      } else {
        return [];
      }
    } else if (params.categoryId || params.factoryId || params.siteId) {
      let lines: any[] = [];

      let categoryLines: any[] = [];
      if (params.categoryId) {
        categoryLines = await this.organizationUnitRepo.find({
          where: {
            categoryId: params.categoryId,
            levelGeneralDataDetailCode: 'LINE',
          },
        });
      }

      let validFactoryIds: string[] = [];
      if (params.siteId) {
        const factories = await this.organizationUnitRepo.find({
          where: {
            parentId: params.siteId,
            levelGeneralDataDetailCode: 'FACTORY',
          },
        });
        validFactoryIds = factories.map((f) => f.id);
      }

      if (params.factoryId) {
        validFactoryIds.push(params.factoryId);
      }

      if (validFactoryIds.length > 0) {
        const linesInFactory = await this.organizationUnitRepo.find({
          where: {
            parentId: In(validFactoryIds),
            levelGeneralDataDetailCode: 'LINE',
          },
        });

        if (params.categoryId) {
          const categoryLineIds = new Set(categoryLines.map((l) => l.id));
          lines = linesInFactory.filter((line) => categoryLineIds.has(line.id));
        } else {
          lines = linesInFactory;
        }
      } else {
        lines = categoryLines;
      }

      if (lines.length === 0) return [];
      where.productionLineId = In(lines.map((l) => l.id));
    }
    if (params.startDate && params.endDate) {
      where.productionDate = Between(new Date(params.startDate), new Date(params.endDate));
    }

    const oeeData = await this.productionOeeRepo.find({ where });
    if (oeeData.length === 0) return [];

    // danh sách shiftId và productionLineId
    const shiftIds = [...new Set(oeeData.map((oee) => oee.shiftId))];
    const productionLineIds = [...new Set(oeeData.map((oee) => oee.productionLineId))];

    const shifts = await this.shiftRepo.find({ where: { id: In(shiftIds) } });
    const shiftMap = new Map(shifts.map((s) => [s.id, s.code]));

    const productionLines = await this.organizationUnitRepo.find({
      where: { id: In(productionLineIds) },
    });
    const productionLineMap = new Map(productionLines.map((line) => [line.id, line]));

    const factoryIds = [...new Set(productionLines.map((line) => line.parentId))];
    const factories = await this.organizationUnitRepo.find({ where: { id: In(factoryIds) } });
    const factoryMap = new Map(factories.map((f) => [f.id, f]));

    const siteIds = [...new Set(factories.map((f) => f.parentId))];
    const sites = await this.organizationUnitRepo.find({ where: { id: In(siteIds) } });
    const siteMap = new Map(sites.map((s) => [s.id, s]));

    const categoryIds = [
      ...new Set(productionLines.map((line) => line.categoryId).filter(Boolean)),
    ];
    const categories = await this.generalDataDetailRepo.find({ where: { id: In(categoryIds) } });
    const categoryMap = new Map(categories.map((c) => [c.id, c.name]));

    const enrichedData = oeeData.map((oee) => {
      const productionLine = productionLineMap.get(oee.productionLineId);
      const factory = factoryMap.get(productionLine?.parentId);
      const site = siteMap.get(factory?.parentId);
      const categoryName = categoryMap.get(productionLine?.categoryId);

      return {
        ...oee,
        productionDate: oee.productionDate ? dayjs(oee.productionDate).format('DD-MM-YYYY') : null,
        shiftName: shiftMap.get(oee.shiftId) || null,
        categoryName: categoryName || null,
        productionLineCode: productionLine?.code || null,
        factoryCode: factory?.code || null,
        siteCode: site?.code || null,
      };
    });
    return enrichedData;
  }

  async getOptions(levelCode?: string, userId?: string): Promise<any[]> {
    let lstFactoryId = [];
    if (userId) {
      const lstAccess = await this.accessRepo.find({
        where: { user: { id: userId }, status: true },
        relations: ['organizationUnit', 'user'],
      });
      if (lstAccess.length === 0) return [];

      lstFactoryId = lstAccess.map((i) => i.organizationUnit.id);
    }
    levelCode = levelCode?.replace(/\?$/, '');
    const whereCon: any = { levelGeneralDataDetailCode: levelCode, isActive: true };

    if (lstFactoryId.length > 0 && levelCode === EOrganizationLevel.Factory) {
      whereCon.id = In(lstFactoryId);
    }
    if (lstFactoryId.length > 0 && levelCode === EOrganizationLevel.Site) {
      const lstFactory = await this.organizationUnitRepo.find({
        where: { id: In(lstFactoryId), isActive: true },
        select: ['parentId'],
      });
      const lstSiteId = lstFactory.map((i) => i.parentId);
      whereCon.id = In(lstSiteId);
    }
    try {
      const data = await this.organizationUnitRepo.find({
        where: whereCon,
      });
      return data.map((i) => ({
        label: i.code + ' - ' + i.name,
        value: i.id,
        parentId: i.parentId,
        code: i.code,
        name: i.name,
        categoryId: i.categoryId,
      }));
    } catch (error) {
      console.error('Error in getOptions:', error);
      return [];
    }
  }

  async loadShift() {
    return await this.shiftRepo.find();
  }
  async findByCode(code: string) {
    const generalData = await this.generalDataRepo.findOne({ where: { code } });
    if (!generalData) {
      throw new NotFoundException(`General data with code ${code} not found`);
    }

    // Lấy chi tiết của general data
    const details = await this.generalDataDetailRepo.find({
      where: { generalId: generalData.id },
      order: { createdDate: 'DESC' },
    });

    return {
      message: 'Success',
      data: {
        ...generalData,
        details,
      },
    };
  }

  // Hàm lấy shift hiện tại của orgUnit Line
  async getShiftFromOrgUnitLine(orgUnitId: string) {
    // Line
    const orgUnitLine = await this.organizationUnitRepo.findOne({
      where: {
        id: orgUnitId,
      },
    });

    const orgUnitFactory = await this.organizationUnitRepo.findOne({
      where: {
        id: orgUnitLine?.parentId,
      },
    });

    // Lấy assignShift của orgUnit có validFrom và validTo hiện tại
    // assignShift sẽ được gắn vs orgUnit type là Factory

    const assignShifts = await this.assignShiftRepo.find({
      where: {
        organizationId: orgUnitFactory.id,
        validFrom: LessThanOrEqual(new Date()),
        validTo: MoreThanOrEqual(new Date()),
      },
    });
    const listShiftId = assignShifts?.map((p) => p.shiftId);
    // Giờ hiện tại

    // Lấy shift có startTime <= currentTime < endTime
    const currentTime = dayjs().format('HH:mm:ss');

    const currentShift = await this.shiftRepo.findOne({
      where: [
        // Case 1: Ca ngày
        {
          id: In(listShiftId),
          nightShift: false,
          startTime: LessThanOrEqual(currentTime),
          endTime: MoreThan(currentTime),
        },
        // Case 2: Ca đêm
        {
          id: In(listShiftId),
          nightShift: true,
          startTime: LessThanOrEqual(currentTime),
        },
        {
          id: In(listShiftId),
          nightShift: true,
          endTime: MoreThan(currentTime),
        },
      ],
      order: {
        startTime: 'DESC',
      },
    });
    return currentShift;
  }

  async getAllLineAndShift() {
    // Lấy tất cả các line và shift của nó
    const lines = await this.organizationUnitRepo.find({
      where: {
        levelGeneralDataDetailCode: 'LINE',
      },
    });
    const listLineAndShift = [];
    for (const lineId of lines) {
      const shift = await this.getShiftFromOrgUnitLine(lineId.id);
      listLineAndShift.push({
        orgUnitLineId: lineId.id,
        shiftId: shift?.id,
      });
    }
    return listLineAndShift;
  }

  async getDefaultAccessByUser(userId: string) {
    const defaultAccess = await this.accessRepo.findOne({
      where: { user: { id: userId }, status: true, default: true },
      relations: ['organizationUnit'],
    });
    if (!defaultAccess) return null;
    const factory = await this.organizationUnitRepo.findOne({
      where: { id: defaultAccess.organizationUnit.id },
    });
    const site = await this.organizationUnitRepo.findOne({ where: { id: factory.parentId } });
    return {
      factoryName: factory.name,
      factoryCode: factory.code,
      factoryId: factory.id,
      siteId: site.id,
      siteCode: site.code,
      siteName: site.name,
    };
  }

  async test(id: string) {
    const result = await this.getShiftFromOrgUnitLine(id);
    return result;
  }
  //#endregion
}
