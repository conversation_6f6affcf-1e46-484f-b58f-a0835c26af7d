import { Body, forwardRef, Inject, Req } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { Request } from 'express';
import { DefController, DefPost } from '~/@core/decorator';
import requestHelper from '~/common/helpers/request.helper';
import {
  RequestCheckInDTO,
  RequestCheckOutDTO,
  RequestGetListStation,
  RequestProductionAreaSelectionTimeSheetDTO,
  RequestShiftSelectionSheetTimeDTO,
} from '~/dto';
import { TimeSheetRecordService } from '~/x-modules/admin/services';

@ApiTags('Timekeeping')
@DefController('timekeeping')
export class TimeKeepingController {
  constructor(
    @Inject(forwardRef(() => TimeSheetRecordService))
    private readonly timeSheetService: TimeSheetRecordService,
  ) {}

  @DefPost('/list-station')
  @ApiOperation({ summary: 'list-station' })
  async getListStation(@Body() params: RequestGetListStation, @Req() req: Request) {
    const request = requestHelper(req);

    const listStation = await this.timeSheetService.getListStation(params, request.clientIP);

    return {
      status: true,
      data: listStation,
    };
  }

  @DefPost('/list-production-area')
  @ApiOperation({ summary: 'List production area' })
  async listProductionArea(@Body() params: RequestProductionAreaSelectionTimeSheetDTO) {
    const result = await this.timeSheetService.listProductionArea(params);
    return {
      status: true,
      data: result,
    };
  }

  @DefPost('/list-shift')
  @ApiOperation({ summary: 'List shift' })
  async listShift(@Body() params: RequestShiftSelectionSheetTimeDTO) {
    const result = await this.timeSheetService.listShift(params);
    return {
      status: true,
      data: result,
    };
  }

  @DefPost('/get-user-timesheet')
  @ApiOperation({ summary: 'Lấy thông tin timesheet của user' })
  async getUserTimeSheet(@Body() params: { userNo: number }) {
    const result = await this.timeSheetService.getUserTimeSheet(params.userNo);
    return {
      status: true,
      data: result,
    };
  }

  @DefPost('/checkin')
  @ApiOperation({ summary: 'Checkin' })
  async checkIn(@Body() params: RequestCheckInDTO) {
    const result = await this.timeSheetService.checkIn(params);

    return {
      status: true,
      message: 'Check-in successfully',
      data: result,
    };
  }

  @DefPost('/checkout')
  @ApiOperation({ summary: 'Checkout' })
  async checkOut(@Body() params: RequestCheckOutDTO) {
    const result = await this.timeSheetService.checkOut(params);
    return {
      status: true,
      message: 'Check-out successfullgy',
      data: result,
    };
  }

  @DefPost('/check-public-ip')
  @ApiOperation({ summary: 'Check public ip' })
  async checkStationPublicIP(@Req() req: Request) {
    const request = requestHelper(req);

    return {
      status: true,
      data: {
        isAccepted: true,
        request,
      },
    };
  }
}
