import { ConsoleLogger, Injectable } from '@nestjs/common';
import { BindRepo } from '~/@core/decorator';
import { MesDxTransformRepo } from '~/repositories/scada';
import { Between, LessThan, MoreThan, In, Not, LessThanOrEqual, MoreThanOrEqual, Connection } from 'typeorm';
import { JwtService } from '@nestjs/jwt';
import moment from 'moment';
import { MesDxMetricsRepo } from '~/repositories/scada';
import {
  MachineParameterRepo,
  MachineRepo,
  ReasonMasterRepo,
  ProductionOrderRepo,
  OrganizationUnitRepo,
  ProductionDowntimeRepo,
  ProductionOrderMaterialTransactionRepo,
  RecipeProcessRepo,
  ProductionBatchRepo,
  ProductionOeeRepo,
  ProductionRejectionRepo,
  UomConventionRepo,
  ProcessMachineRepo,
  ItemRepo,
} from '~/repositories/primary';
import { ShiftRepo } from '~/repositories/primary/shift.repo';
import { dateHelper } from '~/common/helpers/date.helper';
@Injectable()
export class ProductionOeeService {
  constructor(
    private jwtService: JwtService,
    private readonly connection: Connection,
  ) { }

  @BindRepo(ItemRepo)
  private itemRepo: ItemRepo;
  @BindRepo(ShiftRepo)
  private readonly shiftRepo: ShiftRepo;
  @BindRepo(MesDxMetricsRepo)
  private readonly mesDxMetricsRepo: MesDxMetricsRepo;
  @BindRepo(MachineParameterRepo)
  private readonly machineParameterRepo: MachineParameterRepo;
  @BindRepo(ReasonMasterRepo)
  private readonly reasonMasterRepo: ReasonMasterRepo;
  @BindRepo(ProductionOrderRepo)
  private readonly productionOrderRepo: ProductionOrderRepo;
  @BindRepo(OrganizationUnitRepo)
  private readonly organizationUnitRepo: OrganizationUnitRepo;
  @BindRepo(ProductionDowntimeRepo)
  private readonly productionDowntimeRepo: ProductionDowntimeRepo;
  @BindRepo(ProductionOrderMaterialTransactionRepo)
  private readonly productionOrderMaterialTransactionRepo: ProductionOrderMaterialTransactionRepo;
  @BindRepo(RecipeProcessRepo)
  private readonly recipeProcessRepo: RecipeProcessRepo;
  @BindRepo(ProcessMachineRepo)
  private readonly processMachineRepo: ProcessMachineRepo;
  @BindRepo(ProductionRejectionRepo)
  private readonly productionRejectionRepo: ProductionRejectionRepo;
  @BindRepo(ProductionOeeRepo)
  private readonly productionOeeRepo: ProductionOeeRepo;
  // Quy đồi UOM
  @BindRepo(UomConventionRepo)
  private readonly uomConventionRepo: UomConventionRepo;

 async getMetricValueExcludeDowntime(
    metricIds: any,
    startTime: any,
    endTime: any,
    productionOrderLineId: string,
  ): Promise<number> {
    if (!metricIds || metricIds.length === 0) return 0;

    const handleStartTime = moment(startTime, "YYYY-MM-DD HH:mm:ss.SSS Z")
      .utcOffset(7 * 60)
      .format("YYYY-MM-DD HH:mm:ss.SSS Z");
    const handleEndTime = moment(endTime, "YYYY-MM-DD HH:mm:ss.SSS Z")
      .utcOffset(7 * 60)
      .format("YYYY-MM-DD HH:mm:ss.SSS Z");

    const metricPlaceholders = metricIds.map((_, index) => `$${3 + index}`).join(", ");
    const paramsArray = [handleStartTime, handleEndTime, ...metricIds, productionOrderLineId];

    try {
      const query = `
        SELECT SUM(CAST(m.value AS NUMERIC)) AS value
        FROM scada_mes_dx_metrics m
        LEFT JOIN production_downtime d 
          ON m."datetime" >= d."startTime"
        AND m."datetime" <= COALESCE(d."endTime", '9999-12-31 23:59:59')
        AND d."cancelledFlag" != 1
        AND d."processAreaId" IN (
            SELECT ou.id
            FROM organization_units ou
            WHERE ou."parentId" = $4
        )
        LEFT JOIN reason_master rm 
          ON d."reasonCode" = rm.code
        WHERE m."datetime" BETWEEN $1 AND $2
          AND (
            d.id IS NULL 
            OR (rm."groupCode" != 'DOWNTIME' AND rm.id IS not NULL)
          )
          AND m."metricId" IN (${metricPlaceholders})
      `;

      const result = await this.connection.query(query, paramsArray);
      return Number(result[0]?.value) || 0;
    } catch (error) {
      console.error('Error executing raw query:', error);
      throw error;
    }
  }

  private async isProductionOeeExists(shiftIdInfo: string, productionLineIdInfo: string, productionDate: any) {
    const productionOeeExistsData = await this.productionOeeRepo.findOne(
      { shiftId: shiftIdInfo, productionLineId: productionLineIdInfo, productionDate: productionDate }
    );
    if(!productionOeeExistsData) return '';

    return productionOeeExistsData?.id
  }

  findSingleIntermediate(fromUom: any, toUom: any, lstUomConverse: any) {
    const unitMap = {};
    const conversionMap = {};
  
    // Tạo map các đơn vị kết nối lẫn nhau
    lstUomConverse.forEach(({ fromUnit, toUnit, conversion }) => {
      if (!unitMap[fromUnit]) unitMap[fromUnit] = new Set();
      if (!unitMap[toUnit]) unitMap[toUnit] = new Set();
  
      unitMap[fromUnit].add(toUnit);
      unitMap[toUnit].add(fromUnit); // 2 chiều
      // Lưu tỉ lệ quy đổi theo cả 2 chiều
      conversionMap[`${fromUnit}->${toUnit}`] = conversion;
      conversionMap[`${toUnit}->${fromUnit}`] = 1 / conversion;
    });
  
    const visited = new Set();
    const queue = [{ unit: fromUom, rate: 1 }];
  
    while (queue.length > 0) {
      const { unit, rate } = queue.shift();
      if (unit === toUom) {
        return rate;
      }
  
      visited.add(unit);
  
      const neighbors = unitMap[unit] || new Set();
      for (const neighbor of neighbors) {
        if (!visited.has(neighbor)) {
          const conversionRate = conversionMap[`${unit}->${neighbor}`];
          queue.push({ unit: neighbor, rate: rate * conversionRate });
        }
      }
    }
  
    return null; // Không tìm thấy chuyển đổi
  }

  private async onFindRejectionByDefectiveProduct(productionOrderInfo: any, shiftInfo:any, transactionTypeCodeInfo: number, productionDateInfo: any) {
    const productionRejectionData = await this.productionRejectionRepo.find({
      where: { 
        productionLineId: productionOrderInfo?.lineId,
        shiftId: shiftInfo?.id,
        transactionTypeDetailCode: transactionTypeCodeInfo,
        transactionDate : productionDateInfo,
        oeeCal: '1'
      }
    })
    return productionRejectionData
  }

  private async onFindMachineParameter(){
    const productionLineData = await this.machineParameterRepo
      .createQueryBuilder('mp')
      .select(['ou.parentId AS production_line_id', 'mp'])
      .innerJoin('machine', 'm', 'mp.machineId = m.id')
      .innerJoin('process_machine', 'pm', 'pm.machineId = m.id AND pm.assign = true')
      .innerJoin('process', 'p', 'p.id = pm.processId')
      .innerJoin('organization_units', 'ou', 'ou.id = p.organizationUnitId')
      .where('mp.isActive = :isActive', { isActive: true })
      .andWhere('mp.oeeCal = :oeeCal', { oeeCal: 1 })
      .andWhere('mp.typeCode = :typeCode', { typeCode: '8' })
      .andWhere('mp.iotsitewisePropertyTypeCode = :iotTypeCode', { iotTypeCode: '2' })
      .getRawMany();
    return productionLineData
  }

  private async machineParameterByOrder(productionInfo: any, machineParameterInfo: any) {
    const result = machineParameterInfo
      .filter((item) => item.production_line_id === productionInfo?.lineId)
      .map((item) => item.mp_iotsitewisePropertyId);
    return result
  }


  private onHandleTime(
    timeDate: any,
    indexDate: number,
    getDateTo: any
  ) {
      const [hours, minutes, seconds] = timeDate.split(":").map(Number);
      let getTime = moment().utcOffset(7 * 60).subtract(indexDate, 'days');
      if(getDateTo) getTime = moment(getDateTo, "YYYY-MM-DD HH:mm:ss.SSS Z").utcOffset(7 * 60).subtract(indexDate, 'days');

      getTime.set({
        hour: hours,
        minute: minutes,
        second: seconds,
        millisecond: 0
      });
      return getTime.format('YYYY-MM-DD HH:mm:ss.SSS Z')
  }

  private async onDataExistsByReasonCode(datas: any, reasonCodekey: string) {
    if(!datas || datas?.length === 0) return [];

    let dataNew = []
    await Promise.all(
      datas.map(async (data: { reasonCode: string }) => {
        const existsByReasonCodeData = await this.reasonMasterRepo.findOne({
          where: { code: data?.reasonCode, groupCode: reasonCodekey }
        })
        if(existsByReasonCodeData){ dataNew.push(data) }
      })
    )
    return dataNew
  }

  private async onDataExistsByProductionLineId(datas: any, productionLineId: string) {
    if(!datas || datas?.length === 0) return datas;

    let dataNew = []
    await Promise.all(
      datas.map(async (data: { reasonCode: string, processAreaId: string }) => {
        const organizationUnitData = await this.organizationUnitRepo.findOne({ 
          where: { id: data?.processAreaId, parentId: productionLineId }
        })
        if(organizationUnitData){ dataNew.push(data) }
      })
    )

    return dataNew;
  }

  private async onHandleShutdownLossTime(startTime: any, endTime: any, reasonCodekey: string, productionLineId: string) {
    let minTime = moment(startTime, "YYYY-MM-DD HH:mm:ss.SSS Z")
    let maxTime = moment(endTime, "YYYY-MM-DD HH:mm:ss.SSS Z")
    const maxDiffInMinutes = maxTime.diff(minTime, 'minutes');
    // TH4: nếu downtime xảy ra trước ca và đã kết thúc sau ca hoặc chưa kết thúc
    let downtimeOutShiftData = await this.productionDowntimeRepo.find({ 
      where: { 
        startTime: LessThan(startTime), 
        endTime: MoreThan(endTime) 
      } 
    });
    downtimeOutShiftData = await this.onDataExistsByReasonCode(downtimeOutShiftData, reasonCodekey)
    downtimeOutShiftData = await this.onDataExistsByProductionLineId(downtimeOutShiftData, productionLineId)

    if(downtimeOutShiftData && downtimeOutShiftData?.length > 0){
      // Nếu TH4 thì return kết quả vì giá trị max
      return maxDiffInMinutes
    }else{
      let downtimeData = []
      // TH1: downtime xảy ra trong ca và đã kết thúc trong ca
      let downtimeIntoShiftData = await this.productionDowntimeRepo.find({ 
        where: {
          startTime: Between(startTime, endTime), 
          endTime: Between(startTime, endTime)
        }
      })
      downtimeIntoShiftData = await this.onDataExistsByReasonCode(downtimeIntoShiftData, reasonCodekey)
      if(downtimeIntoShiftData && downtimeIntoShiftData?.length > 0){ 
        downtimeData = [...downtimeData, ... downtimeIntoShiftData]
      };

      // TH2: downtime xảy ra trong ca và đã kết thúc sau ca hoặc chưa kết thúc
      let downtimeEndOutShiftData = await this.productionDowntimeRepo.find({ 
        where: {
          startTime: Between(startTime, endTime),
          endTime: MoreThan(endTime)
        }
      })
      downtimeEndOutShiftData = await this.onDataExistsByReasonCode(downtimeEndOutShiftData, reasonCodekey)
      if(downtimeEndOutShiftData && downtimeEndOutShiftData?.length > 0){ 
        downtimeData = [...downtimeData, ...downtimeEndOutShiftData] 
      };

      // TH3: Nếu downtime xảy ra trước ca và đã kết thúc trong ca
      let downtimeStartOutShiftData = await this.productionDowntimeRepo.find({ 
        where: {
          startTime: LessThan(startTime), 
          endTime: Between(startTime, endTime)
        } 
      })
      downtimeStartOutShiftData = await this.onDataExistsByReasonCode(downtimeStartOutShiftData, reasonCodekey)
      if(downtimeStartOutShiftData && downtimeStartOutShiftData?.length > 0){ 
        downtimeData = [...downtimeData, ...downtimeStartOutShiftData] 
      };
      // Handle diff downtime
      if(downtimeData?.length <= 0) return 0;

      // Handle Shutdown data
      let downtimeDataResult = await this.onDataExistsByReasonCode(downtimeData, reasonCodekey)
      downtimeDataResult = await this.onDataExistsByProductionLineId(downtimeDataResult, productionLineId)
      minTime = moment.min(downtimeDataResult.map(d => moment(d.startTime)))
      maxTime = moment.max(downtimeDataResult.map(d => moment(d.endTime)))
      if(downtimeStartOutShiftData?.length > 0){ minTime = moment(startTime, "YYYY-MM-DD HH:mm:ss.SSS Z")};
      if(downtimeEndOutShiftData?.length > 0){ maxTime = moment(endTime, "YYYY-MM-DD HH:mm:ss.SSS Z") };
      const diffInMinutes = maxTime.diff(minTime, 'minutes');

      if(diffInMinutes >= maxDiffInMinutes && downtimeDataResult?.length === 0) return 0;

      if(diffInMinutes >= maxDiffInMinutes) return maxDiffInMinutes;

      if(diffInMinutes < 0 && downtimeDataResult?.length === 0) return 0;

      return diffInMinutes;
    }
  }
  // private async onUpdatePrevStatus(getPrevStatus: any, endTime: any) { }

  private async onStoredByProductionOrder(shiftInfo: any, productionOrderInfo: any, startTime: any, endTime: any, uomConventionInfo: any, productionDateInfo: any, machineParameterInfo: any) {
    // Tìm base unit theo itemId
    const itemData = await this.itemRepo.findOne({ where: { id: productionOrderInfo?.itemId } })

    const uomConventionByItem = uomConventionInfo.filter(
      (item) => item.itemId === productionOrderInfo?.itemId,
    ); 
    
    // Tìm kiếm Production_line
    const organizationUnitData = await this.organizationUnitRepo.findOne({ 
      where: { id: productionOrderInfo?.processAreaId }
    })
    if(!organizationUnitData) return;

    // Tính operationTime = endTime - startTime (Đơn vị phút-Min)
    const operationTimeValue = Math.ceil((moment(endTime, "YYYY-MM-DD HH:mm:ss.SSS Z").toDate().getTime() - moment(startTime, "YYYY-MM-DD HH:mm:ss.SSS Z").toDate().getTime()) / 60000);

    // Tính ShutdownLossTime 
    const shutdownLossTimeValue = await this.onHandleShutdownLossTime(startTime, endTime, "SHUTDOWN", productionOrderInfo?.lineId)

    // Tính Loading time
    const loadingTimeValue = operationTimeValue - shutdownLossTimeValue

    // Tính Downtime
    const downtimeValue = await this.onHandleShutdownLossTime(startTime, endTime, "DOWNTIME", productionOrderInfo?.lineId)

    // Tính utilisationTime
    const utilisationTimeValue = loadingTimeValue - downtimeValue

    // Tính availability = ((Loading time - downtime)/loading time)*100
    let availabilityValue = ((loadingTimeValue - downtimeValue)/loadingTimeValue)*100
    // Làm tròn đến 2 chữ số thập phân
    availabilityValue = Math.round(availabilityValue * 100) / 100;

    // Tính Expected product qty
    const iotsitewisePropertyIds = await this.machineParameterByOrder(productionOrderInfo, machineParameterInfo)
    const expectedProductQtyValue = await this.getMetricValueExcludeDowntime(iotsitewisePropertyIds, startTime, endTime, productionOrderInfo?.lineId)

    // Quét tất cả giao dịch trong bảng production_order_material_transaction
    // Tính Actual product qty: Tổng transaction_qty của các dòng dữ liệu của B2 quy về base uom của material_id
    const materialTransactionData = await this.productionOrderMaterialTransactionRepo.find({
      where : { oeeCal : 1, orderId: productionOrderInfo?.id}
    })
    let actualProductQtyValue = 0
    if(materialTransactionData || materialTransactionData?.length > 0){
      await Promise.all(
        materialTransactionData.map(async (materialTransaction) => {
          let transactionQtyValue = isNaN(Number(materialTransaction?.transactionQty)) ? 0 : Number(materialTransaction?.transactionQty) || 0
          const getTransactionType = materialTransaction?.transactionType;
          if(materialTransaction?.transactionUom !== itemData?.baseUnit){
            const UOMConversionData = this.findSingleIntermediate(materialTransaction?.transactionUom, itemData?.baseUnit, uomConventionByItem) || 1  
            transactionQtyValue = transactionQtyValue * UOMConversionData
          }
          if(getTransactionType === 'WIP_COMPLETION_RETURN'){
            actualProductQtyValue -= transactionQtyValue;
          }else if(getTransactionType === 'WIP_COMPLETION'){
            actualProductQtyValue += transactionQtyValue;
          }
        })
      )
    };

    // Tính Defective products
    let defectiveProductsValue = 0
    const rejectionDataByRejactionData = await this.onFindRejectionByDefectiveProduct(productionOrderInfo, shiftInfo, 1, productionDateInfo)
    const rejectionDataByReworkData = await this.onFindRejectionByDefectiveProduct(productionOrderInfo, shiftInfo, 2, productionDateInfo)
    await Promise.all(
      rejectionDataByRejactionData.map(async (rejectionDataByRejaction) => {
        // Tìm Base uom cho rejectionDataByRejaction
        const itemByRejaction = await this.itemRepo.findOne({ where: { id: rejectionDataByRejaction?.itemId } })
        const uomConventionByRejaction = uomConventionInfo.filter(
          (item) => item.itemId === rejectionDataByRejaction?.itemId,
        ); 
        let rejectionDataByRejactionQuantity = isNaN(Number(rejectionDataByRejaction?.quantity)) ? 0 : Number(rejectionDataByRejaction?.quantity);
        if(rejectionDataByRejaction?.uomCode !== itemByRejaction?.baseUnit){
          const UOMConversionData = this.findSingleIntermediate(rejectionDataByRejaction?.uomCode, itemByRejaction?.baseUnit, uomConventionByRejaction) || 1
          rejectionDataByRejactionQuantity = rejectionDataByRejactionQuantity * UOMConversionData
        }
        defectiveProductsValue += rejectionDataByRejactionQuantity;
      })
    )
    await Promise.all(
      rejectionDataByReworkData.map(async (rejectionDataByRework) => {
        // Tìm Base uom cho rejectionDataByRework
        const itemByRework = await this.itemRepo.findOne({ where: { id: rejectionDataByRework?.itemId } })
        const uomConventionByRework = uomConventionInfo.filter(
          (item) => item.itemId === rejectionDataByRework?.itemId,
        ); 
        let rejectionDataByReworkQuantity = isNaN(Number(rejectionDataByRework?.quantity)) ? 0 : Number(rejectionDataByRework?.quantity);
        if(rejectionDataByRework?.uomCode !== itemByRework?.baseUnit){
          const UOMConversionData = this.findSingleIntermediate(rejectionDataByRework?.uomCode, itemByRework?.baseUnit, uomConventionByRework) || 1
          // const UOMConversionData = getUOMConversion ? getUOMConversion : 1
          rejectionDataByReworkQuantity = rejectionDataByReworkQuantity * UOMConversionData
        }
        defectiveProductsValue -= rejectionDataByReworkQuantity;
      })
    )

    // Create OBJ
    const productionOEEObj = {
      loadingTime: Math.ceil(loadingTimeValue),
      operationTime: Math.ceil(operationTimeValue),
      shutdownLossTime: Math.ceil(shutdownLossTimeValue),
      downtime: Math.ceil(downtimeValue),
      utilisationTime: Math.ceil(utilisationTimeValue),
      availability: availabilityValue,
      expectedProductQty: expectedProductQtyValue,
      actualProductQty: actualProductQtyValue,
      defectiveProducts: defectiveProductsValue,
      shiftId: productionOrderInfo?.shiftId,
      productionDate: productionDateInfo,
      productionLineId: productionOrderInfo?.lineId || '',
      posted: 0,
      createdByUser: '-1',
      lastUpdate: new Date(),
      lastUpdateBy: '-1',
    }

    return productionOEEObj;
  }

  private async onHandleByProductionOrder( uomConventionInfo: any, machineParameterInfo: any, inclusiveDays: any, getDateTo: any ) {
    const limitLopp = inclusiveDays ? inclusiveDays : 6 

    // Xử lý tìm dữ liệu từ ngày hiện tại và 1 ngày trước.
    for (let i = 0; i < limitLopp; i++) {
      const startTimeByOrder = this.onHandleTime('00:00:00', i, getDateTo)
      const endTimeByOrder = this.onHandleTime('23:59:59', i, getDateTo)

      // Tìm các record nằm trong khoản thời gian theo ca và ngày từ productionOrder table
      const productionOrderData = await this.productionOrderRepo.find({
        where: {
          planStartDate: Between(startTimeByOrder, endTimeByOrder),
          // shiftId: shiftInfo?.id
        },
      });
      let productionOeeData = [];
      if(productionOrderData?.length > 0){
        await Promise.all(
          productionOrderData.map(async (productionOrder) => {
            const getShiftData = await this.shiftRepo.findOne({ where: {id: productionOrder?.shiftId} })
            const startTime = this.onHandleTime(getShiftData?.startTime, i, getDateTo)
            const endTime = this.onHandleTime(getShiftData?.endTime, getShiftData?.nightShift ? i-1 : i, getDateTo)
            const handleProductionOee =  await this.onStoredByProductionOrder(getShiftData, productionOrder, startTime, endTime, uomConventionInfo, startTimeByOrder, machineParameterInfo)
            productionOeeData.push(handleProductionOee)
          })
        )
      }

      // Nhóm các đối tượng cùng OEE
      const handledProductionOee = productionOeeData.reduce((acc, item) => {
        const key = `${item.shiftId}-${item.productionDate}-${item.productionLineId}`;
        if (!acc[key]) {
          acc[key] = { ...item };
        } else {
          acc[key].actualProductQty += item.actualProductQty;
          // acc[key].expectedProductQty += item.expectedProductQty;
        }
      
        return acc;
      }, {} as Record<string, typeof productionOeeData[0]>);
      
      // Nếu muốn kết quả là mảng:
      productionOeeData = Object.values(handledProductionOee);

      // Lưu dữ liệu đã lọc theo đối tượng OEE 
      await Promise.all(
        productionOeeData.map(async (productionOee) => {
          // Tính Performance: Actual product qty/Expected product qty*100
          const performanceValue =  productionOee?.expectedProductQty !== 0 ? (productionOee?.actualProductQty/productionOee?.expectedProductQty) * 100 : 0

          // Tính Quality: (Actual product qty - Defective products)/Actual product qty*100
          const qualityValue =  productionOee?.actualProductQty !== 0 ? ((productionOee?.actualProductQty - productionOee?.defectiveProducts)/productionOee?.actualProductQty)*100 : 0

          // Tính OEE: Availability*Performance*Quality
          const availabilityPercent = (productionOee?.availability ?? 0) / 100;
          const performancePercent = (performanceValue ?? 0) / 100;
          const qualityPercent = (qualityValue ?? 0) / 100;
          const OEEValue = availabilityPercent * performancePercent * qualityPercent * 100

          const prodOEEObj = {
            ...productionOee,
            performance: performanceValue,
            quality: qualityValue,
            oee: OEEValue,
          }
          const isProductionOeeExistsCheck = await this.isProductionOeeExists(prodOEEObj?.shiftId, prodOEEObj?.productionLineId, prodOEEObj?.productionDate)
          if(!isProductionOeeExistsCheck || isProductionOeeExistsCheck === ''){ 
            await this.productionOeeRepo.save({...prodOEEObj}) 
          } else {
            await this.productionOeeRepo.update({ id: isProductionOeeExistsCheck }, prodOEEObj );
          }
        })
      ) 
    }
  }

  async createProductionOeeService(data: { fromDate?: string; toDate?: string }) {
    try {
      // set-up data
      let getDateFrom = null
      let getDateTo = null
      if(data.fromDate && data.toDate){
        getDateFrom = dateHelper.formatDateByJobManual(data.fromDate)
        getDateTo = dateHelper.formatDateByJobManual(data.toDate)
      }

      // Tính số ngày chênh lệch
      const getDateFromTamp = moment(getDateFrom, "YYYY-MM-DD HH:mm:ss.SSS Z");
      const getDateToTamp = moment(getDateTo, "YYYY-MM-DD HH:mm:ss.SSS Z");

      const diffDays = getDateToTamp.diff(getDateFromTamp, 'days');
      const inclusiveDays = getDateToTamp.startOf('day').diff(getDateFromTamp.startOf('day'), 'days') + 1;

      const uomConventionData = await this.uomConventionRepo.find()
      const machineParameterData = await this.onFindMachineParameter()
      // Tìm dữ liệu từng ca trong shift
      const shiftData = await this.shiftRepo.find()
      if(!shiftData) return;

      await this.onHandleByProductionOrder( uomConventionData, machineParameterData, inclusiveDays, getDateTo)
    } catch (error) {
      console.log(error, 'error');
    }
  }

}
