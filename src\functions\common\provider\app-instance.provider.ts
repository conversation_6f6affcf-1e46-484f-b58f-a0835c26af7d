import { INestApplicationContext } from '@nestjs/common';
import { NestFactory } from '@nestjs/core';
import { AppModule } from '~/app.module';
import { setupTransactionContext } from '~/@core/decorator';

class AppInstanceProvider {
  private static instance: INestApplicationContext | null = null;
  private initPromise: Promise<INestApplicationContext> | null = null;

  /**
   * Get the shared application instance
   * @returns Promise<INestApplicationContext>
   */
  async getAppInstance(): Promise<INestApplicationContext> {
    // If we already have an instance, return it
    if (AppInstanceProvider.instance) {
      return AppInstanceProvider.instance;
    }

    // If initialization is in progress, return the existing promise
    if (this.initPromise) {
      return this.initPromise;
    }

    // Start initialization and store the promise
    this.initPromise = this.initializeApp();
    return this.initPromise;
  }

  /**
   * Initialize the NestJS application
   * @private
   */
  private async initializeApp(): Promise<INestApplicationContext> {
    console.log('Initializing NestJS application...');
    try {
      const app = await NestFactory.createApplicationContext(AppModule);
      setupTransactionContext();
      await app.init();
      console.log('NestJS application initialized successfully');
      AppInstanceProvider.instance = app;
      return app;
    } catch (error) {
      console.error('Failed to initialize NestJS application:', error);
      this.initPromise = null; // Reset promise to allow retry
      throw error;
    }
  }

  /**
   * For testing purposes - clear the instance
   */
  clearInstance(): void {
    AppInstanceProvider.instance = null;
    this.initPromise = null;
  }
}

// Export a singleton instance
export const appInstanceProvider = new AppInstanceProvider();
