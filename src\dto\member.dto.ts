import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsArray, IsNotEmpty, IsOptional, IsString, IsUrl, IsUUID } from 'class-validator';
import { PageRequest } from '~/@systems/utils';
import { NSMember } from '~/common/enums';

export class ListMemberReq extends PageRequest {
  @ApiPropertyOptional()
  username?: string;

  @ApiPropertyOptional({
    enum: NSMember.EMemberType,
    enumName: 'EMemberType',
  })
  memberType?: NSMember.EMemberType;

  @ApiPropertyOptional()
  fullName?: string;

  @ApiPropertyOptional()
  email?: string;

  @ApiPropertyOptional({ enum: NSMember.EStatus })
  status?: NSMember.EStatus;
}

export class UpdateMemberProfileReq {
  @ApiPropertyOptional({ description: 'Bộ phận nếu khách hàng là doanh nghiệp' })
  dep?: string;

  /** Email */
  @ApiPropertyOptional()
  email?: string;

  /** Họ và tên */
  @ApiPropertyOptional()
  fullName?: string;
}

export class ChangePasswordReq {
  @ApiPropertyOptional({ description: 'Mật khẩu cũ' })
  oldPassword?: string;

  @ApiProperty({ description: 'Mật khấu mới' })
  @IsNotEmpty()
  newPassword: string;

  @ApiProperty({ description: 'Xác nhận mật khẩu mới' })
  @IsNotEmpty()
  newPasswordConfirm: string;
}

export class UpdatePassword {
  @ApiProperty({ description: 'ID của CTV/Khách hàng' })
  @IsUUID()
  memberId: string;

  @ApiProperty({ description: 'Mật khẩu mới' })
  @IsNotEmpty()
  @IsString()
  newPassword: string;

  @ApiProperty({ description: 'Xác nhận mật khẩu mới' })
  @IsNotEmpty()
  @IsString()
  confirmPassword: string;
}
