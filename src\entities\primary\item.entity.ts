import { ApiProperty } from '@nestjs/swagger';
import { Column, Entity, Index, OneToMany } from 'typeorm';
import { NSItem } from '~/common/enums/NSItem';
import { PrimaryBaseEntity } from '../primary-base.entity';
import { RecipeProcessItemEntity } from './recipe-process-item.entity';
import { RecipeEntity } from './recipe.entity';
import { SetPointEntity } from './set-point.entity';
import { UomConversionEntity } from './uom-conversion.entity';
import { MaterialGroupDetailEntity } from '~/entities/primary/material-group-detail.entity';
import { WeighingTareDetailEntity } from '~/entities/primary/weighing-tare-detail.entity';

/** Bảng thành phần (vật liệu, sản phẩm, phế phẩm) */
@Entity('item')
export class ItemEntity extends PrimaryBaseEntity {
  @OneToMany(() => RecipeProcessItemEntity, (rpi) => rpi.item)
  recipeProcessItems: Array<RecipeProcessItemEntity>;
  @OneToMany(() => RecipeEntity, (r) => r.product)
  recipes: Array<RecipeEntity>;
  @OneToMany(() => UomConversionEntity, (uom) => uom.item, { cascade: true })
  uomConversions: Array<UomConversionEntity>;
  @OneToMany(() => MaterialGroupDetailEntity, (e) => e.itemId)
  materialGroupDetails: Promise<MaterialGroupDetailEntity[]>;
  @OneToMany(() => WeighingTareDetailEntity, (e) => e.itemId)
  weighingTareDetails: Promise<WeighingTareDetailEntity[]>;
  /** Tên */
  @Column({ nullable: true })
  @ApiProperty({ description: 'Tên', example: 'Mì gói' })
  name: string;

  /** Đơn vị tính */
  @ApiProperty({ description: 'Đơn vị tính', example: 'Kgs' })
  @Column({ nullable: true })
  baseUnit: string;

  /** Mã thành phần */
  @ApiProperty({ description: 'Mã thành phần', example: 'ITEM001' })
  @Column({ nullable: false })
  @Index({ unique: true })
  code: string;

  /** Loại */
  @ApiProperty({ description: 'Loại', example: 'FG' })
  @Column({ nullable: true })
  @Index({ unique: false })
  type: string;

  /** Nhóm */
  @ApiProperty({ description: 'Nhóm', example: 'Noodle' })
  @Column({ nullable: true })
  group: string;

  /** Ngành hàng */
  @ApiProperty({ description: 'Ngành hàng', example: 'Noodle' })
  @Column({ nullable: true })
  category: string;

  /** Nhãn hiệu */
  @ApiProperty({ description: 'Nhãn hiệu', example: 'Nhãn hiệu' })
  @Column({ nullable: true })
  brand: string;

  /** Đơn vị tính của kho */
  @ApiProperty({ description: 'Đơn vị tính của kho', example: 'KILOGRAM' })
  @Column({ nullable: true })
  inventoryUnit: string;

  /** Trạng thái */
  @ApiProperty({ description: 'Trạng thái', example: 'ACTIVE' })
  @Column({ nullable: true, type: 'varchar', length: 10 })
  status: NSItem.Status;

  @OneToMany(() => SetPointEntity, (setPoint) => setPoint.item)
  setPoints: SetPointEntity[];

  @ApiProperty({ description: 'Đã đồng bộ sang SCADA', example: false })
  @Column({ nullable: true, default: false })
  isSyncScada: boolean;

  @ApiProperty({ description: 'Thời gian đồng bộ sang SCADA', example: new Date() })
  @Column({ nullable: true })
  dateSyncScada: Date;

  @ApiProperty({ description: 'Lỗi khi đồng bộ Scada', example: 'Can not find' })
  @Column({ type: 'text', nullable: true })
  errorSyncScada: string;

  @ApiProperty({ description: 'Weighing Tolerance', example: 0 })
  @Column({ type: 'numeric', default: 0 })
  weighingTolerance: number;

  @ApiProperty({ description: 'Shelf Life Day' })
  @Column({ type: 'int', nullable: true })
  shelfLifeDay?: number;
}
