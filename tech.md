**I<PERSON> <PERSON><PERSON><PERSON> công nghệ được sử dụng:**

<PERSON><PERSON> thống được chia thành 4 phần chính: INTERGRATION, MES, SCADA và một số dịch vụ chung.

**1. INTERGRATION (Tích hợp):**

- **Hrm (Human Resources Management):** <PERSON>ệ thống quản lý nhân sự.
- **QLONE:** <PERSON>ệ thống kiểm tra chất lượng.
- **K8s (Kubernetes):** Nền tảng điều phối container (container orchestration).
- **Oracle:** ERP Oracle.
- **GCP Data Lake:** Google Cloud Platform, dùng để lưu trữ lượng lớn dữ liệu thô.
- **EventHub:** Dịch vụ phân luồng dữ liệu (data streaming) của Azure.

**2. MES (Manufacturing Execution System - Hệ thống điều hành sản xuất):**

- **VPC (Virtual Private Cloud):** <PERSON>ạng ảo riêng biệt trong AWS.
- **EKS (Elastic Kubernetes Service):** Dịch vụ Kubernetes được quản lý bởi AWS.
- **EC2 Auto Scaling:** Tự động điều chỉnh số lượng máy chủ EC2 dựa trên tải.
- **Nginx:** Web server và reverse proxy.
- **Scale docker server:** Máy chủ chạy các ứng dụng cân, faceId.
- **ElasticCache:** Dịch vụ bộ nhớ đệm phân tán của AWS (Valkey).
- **PostgreSQL:** Hệ quản trị cơ sở dữ liệu.
- **Loadbalancer:** Bộ cân bằng tải để phân phối lưu lượng truy cập.
- **SNS (Simple Notification Service):** Dịch vụ thông báo đẩy của AWS.
- **ECR (Elastic Container Registry):** Kho lưu trữ hình ảnh Docker được quản lý bởi AWS.
- **SQS (Simple Queue Service):** Dịch vụ hàng đợi tin nhắn của AWS.
- **Certificate:** Chứng chỉ bảo mật (ví dụ: SSL/TLS).
- **FaceID:** Hệ thống nhận diện khuôn mặt.
- **Scale:** Hệ thống cân điện tử.

**3. SCADA (Supervisory Control and Data Acquisition - Hệ thống điều khiển giám sát và thu thập dữ liệu):**

- **Greengrass:** Dịch vụ biên (edge service) của AWS cho phép chạy các ứng dụng đám mây cục bộ trên các thiết bị.
- **SQS (Simple Queue Service):** Dịch vụ hàng đợi tin nhắn của AWS.
- **IOTSiteWise:** Dịch vụ của AWS để thu thập, tổ chức và phân tích dữ liệu công nghiệp.
- **MES docker server ...:** Máy chủ Docker của MES.
- \*\*OPC UA (Open Platform Communications Unified Architecture):
  Tiêu chuẩn truyền thông công nghiệp.
- **SCADA server:** Máy chủ SCADA.
- **PLC (Programmable Logic Controller):** Bộ điều khiển logic khả trình, thiết bị điều khiển công nghiệp.
- **Sensor 1, Sensor 2, Sensor n:** Các thiết bị cảm biến thu thập dữ liệu từ môi trường vật lý.

**II. Luồng vận hành chính:**

Dựa trên sơ đồ, luồng vận hành có thể được mô tả như sau:

1.  **Thu thập dữ liệu từ tầng SCADA:**

    - Các **Sensor** (cảm biến) thu thập dữ liệu vật lý.
    - Dữ liệu từ cảm biến được đưa về các thiết bị **PLC** và sau đó truyền đến **SCADA server** thông qua chuẩn **OPC UA**.
    - **Greengrass** tại biên có thể xử lý và lọc dữ liệu từ PLC/SCADA server trước khi gửi lên đám mây.
    - **IOTSiteWise** tiếp nhận dữ liệu công nghiệp từ các thiết bị và tổ chức chúng.
    - Dữ liệu có thể được đưa vào **SQS** để xử lý bất đồng bộ hoặc gửi trực tiếp đến các thành phần khác.

2.  **Xử lý dữ liệu và vận hành tại MES:**

    - Dữ liệu từ SCADA (thông qua IOTSiteWise hoặc SQS) được gửi đến các **MES docker server**.
    - **EKS** và **EC2 Auto Scaling** đảm bảo các ứng dụng MES được triển khai, vận hành và tự động mở rộng theo nhu cầu.
    - **Nginx** hoạt động như một reverse proxy cho các ứng dụng MES.
    - Các ứng dụng MES tương tác với cơ sở dữ liệu **PostgreSQL** (có thể thông qua **RDS**) và sử dụng **ElasticCache** để tăng tốc độ truy vấn.
    - **Loadbalancer** phân phối lưu lượng truy cập giữa các MES docker server.
    - Dữ liệu từ các thiết bị như **FaceID** và **Scale** cũng được đưa vào hệ thống MES để xử lý, có thể liên quan đến quy trình sản xuất hoặc kiểm soát chất lượng.
    - **SNS** và **SQS** được sử dụng cho các thông báo và giao tiếp bất đồng bộ trong nội bộ MES.

3.  **Tích hợp dữ liệu (INTERGRATION):**

    - Hệ thống **Hrm** có thể cung cấp dữ liệu nhân sự cho các quy trình sản xuất.
    - **QLONE** và **K8s** là các thành phần trong lớp tích hợp, có thể dùng để đồng bộ dữ liệu hoặc cung cấp API cho các hệ thống khác.
    - Dữ liệu từ **Oracle** (hệ thống cơ sở dữ liệu hiện có) được tích hợp vào luồng.
    - **GCP Data Lake** và **EventHub** được sử dụng để thu thập và lưu trữ dữ liệu từ nhiều nguồn, có thể là dữ liệu MES, SCADA hoặc các hệ thống khác để phân tích lớn hoặc báo cáo.

4.  **Luồng dữ liệu tổng quát:**
    - Dữ liệu thu thập từ các cảm biến và thiết bị công nghiệp (PLC, SCADA) được đưa vào tầng SCADA, sau đó chuyển lên MES.
    - Hệ thống MES xử lý dữ liệu sản xuất, quản lý quy trình, và lưu trữ vào các cơ sở dữ liệu (PostgreSQL, ElasticCache).
    - Dữ liệu từ MES và các hệ thống khác (Hrm, Oracle) được tích hợp vào lớp INTERGRATION.
    - **GCP Data Lake** có thể là điểm cuối cho việc lưu trữ dữ liệu lớn từ tất cả các nguồn để phục vụ phân tích nghiệp vụ hoặc AI/ML sau này.
    - Các dịch vụ như **SQS, SNS, ECR, Certificate** là các dịch vụ hỗ trợ cho việc triển khai, vận hành, bảo mật và giao tiếp giữa các thành phần.
