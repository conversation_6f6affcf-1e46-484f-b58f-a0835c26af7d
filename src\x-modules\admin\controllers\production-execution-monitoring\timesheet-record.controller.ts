import { Body, Param, Query, Req, UseGuards } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { Request } from 'express';
import { DefController, DefGet, DefPost } from '~/@core/decorator';
import { Roles } from '~/@core/decorator/roles.decorator';
import { RoleGuard } from '~/@systems/guard/role.guard';
import requestHelper from '~/common/helpers/request.helper';
import {
  ListFactoryTimeSheetDTO,
  ListSiteTimeSheetDTO,
  RequestCheckInDTO,
  RequestCheckOutDTO,
  RequestCreateTimeSheetDTO,
  RequestDeleteTimeSheetDTO,
  RequestDetailTimeSheetDTO,
  RequestGetListStation,
  RequestListDevice,
  RequestListHoliadyDTO,
  RequestListUserTimeSheetDTO,
  RequestPaginationTimeSheetDTO,
  RequestProductionAreaSelectionTimeSheetDTO,
  RequestShiftSelectionSheetTimeDTO,
  RequestUpdateTimeSheetDTO,
  RequestUserSelectionTimeSheetDTO,
} from '~/dto/timesheet-record.dto';
import { TimeSheetRecordService } from '../../services';

@ApiTags('timesheet-records')
@DefController('timesheet-records')
export class TimeSheetRecordController {
  constructor(private readonly timeSheetService: TimeSheetRecordService) {}

  @DefGet('/pagination')
  @Roles('/production-execution/timesheet-records', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Lấy tất cả bản ghi chấm công' })
  async pagination(@Query() queryParams: RequestPaginationTimeSheetDTO) {
    const result = await this.timeSheetService.pagination(queryParams);
    return {
      status: true,
      message: 'Success',
      data: result,
    };
  }

  @DefGet('/detail/:id')
  @Roles('/production-execution/timesheet-records', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Lấy chi tiết bản ghi chấm công theo ID' })
  async detailTimeSheetRecord(@Param() params: RequestDetailTimeSheetDTO) {
    const result = await this.timeSheetService.detailTimeSheetRecord(params);
    return {
      status: true,
      message: 'Success',
      data: result,
    };
  }

  @DefPost('/create')
  @Roles('/production-execution/timesheet-records', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Tạo mới một bản ghi chấm công' })
  async createTimeSheetRecord(@Body() params: RequestCreateTimeSheetDTO) {
    const result = await this.timeSheetService.createTimeSheetRecord(params);
    return {
      status: true,
      message: 'Timesheet record created successfully.',
      data: result,
    };
  }

  @DefPost('/update')
  @Roles('/production-execution/timesheet-records', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Cập nhật bản ghi chấm công' })
  async updateTimeSheetRecord(@Body() params: RequestUpdateTimeSheetDTO) {
    const result = await this.timeSheetService.updateTimeSheetRecord(params);
    return {
      status: true,
      message: 'Timesheet record updated successfully.',
      data: result,
    };
  }

  @DefPost('/delete')
  @Roles('/production-execution/timesheet-records', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Xóa bản ghi chấm công' })
  async deleteTimeSheetRecord(@Body() params: RequestDeleteTimeSheetDTO) {
    const result = await this.timeSheetService.deleteTimeSheetRecord(params);
    return {
      status: true,
      message: 'Timesheet record deleted successfully.',
      data: result,
    };
  }

  @DefPost('/user-selection')
  @Roles('/production-execution/timesheet-records', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Lấy danh sách chấm công dựa trên người dùng' })
  async userSelection(@Body() params: RequestUserSelectionTimeSheetDTO) {
    const result = await this.timeSheetService.userSelection(params);
    return {
      status: true,
      message: 'Thành công',
      data: result,
    };
  }

  @DefPost('/production-area-selection')
  @Roles('/production-execution/timesheet-records', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Lấy danh sách chấm công theo khu vực sản xuất' })
  async productionAreaSelection(@Body() params: RequestProductionAreaSelectionTimeSheetDTO) {
    const result = await this.timeSheetService.productionAreaSelection(params);
    return {
      status: true,
      message: 'Thành công',
      data: result,
    };
  }

  @DefPost('/shift-selection')
  @Roles('/production-execution/timesheet-records', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Lấy danh sách chấm công theo ca làm việc' })
  async shiftSelection(@Body() params: RequestShiftSelectionSheetTimeDTO) {
    const result = await this.timeSheetService.shiftSelection(params);
    return {
      status: true,
      message: 'Thành công',
      data: result,
    };
  }

  @DefPost('/list-production-area')
  @Roles('/production-execution/timesheet-records', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'List production area' })
  async listProductionArea(@Body() params: RequestProductionAreaSelectionTimeSheetDTO) {
    const result = await this.timeSheetService.listProductionArea(params);
    return {
      status: true,
      data: result,
    };
  }

  @DefPost('/list-shift')
  @Roles('/production-execution/timesheet-records', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'List shift' })
  async listShift(@Body() params: RequestShiftSelectionSheetTimeDTO) {
    const result = await this.timeSheetService.listShift(params);
    return {
      status: true,
      data: result,
    };
  }

  @DefPost('/checkin')
  @Roles('/production-execution/timesheet-records', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Checkin' })
  async checkIn(@Body() params: RequestCheckInDTO) {
    const result = await this.timeSheetService.checkIn(params);

    return {
      status: true,
      message: 'Check-in successfully',
      data: result,
    };
  }

  @DefPost('/checkout')
  @Roles('/production-execution/timesheet-records', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Checkout' })
  async checkOut(@Body() params: RequestCheckOutDTO) {
    const result = await this.timeSheetService.checkOut(params);
    return {
      status: true,
      message: 'Check-out successfully',
      data: result,
    };
  }

  @DefPost('/list-site')
  @Roles('/production-execution/timesheet-records', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'list site' })
  async listSite(@Body() params: ListSiteTimeSheetDTO) {
    const result = await this.timeSheetService.listSite(params);

    return {
      status: true,
      data: result,
    };
  }

  @DefPost('/list-factory')
  @Roles('/production-execution/timesheet-records', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'list factory' })
  async listFactory(@Body() params: ListFactoryTimeSheetDTO) {
    const result = await this.timeSheetService.listFactory(params);

    return {
      status: true,
      data: result,
    };
  }

  @DefPost('/list-user')
  @Roles('/production-execution/timesheet-records', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'list user' })
  async listUser(@Body() params: RequestListUserTimeSheetDTO) {
    const result = await this.timeSheetService.listUser(params);

    return {
      status: true,
      data: result,
    };
  }

  @DefPost('/list-user-for-device')
  @Roles('/production-execution/timesheet-records', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'list user' })
  async listUserForDevice(@Body() params: RequestListUserTimeSheetDTO) {
    const result = await this.timeSheetService.listUserForDevice(params);

    return {
      status: true,
      data: result,
    };
  }

  @DefPost('/list-holiday')
  @Roles('/production-execution/timesheet-records', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'list holiday' })
  async listHoliday(@Body() params: RequestListHoliadyDTO) {
    const result = await this.timeSheetService.listHoliday(params);

    return {
      status: true,
      data: result,
    };
  }

  @DefPost('/list-device')
  @Roles('/production-execution/timesheet-records', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'list-device' })
  async listDeviceByPlace(@Body() params: RequestListDevice) {
    const result = await this.timeSheetService.listDevice(params);

    return {
      status: true,
      data: result,
    };
  }

  @DefPost('/list-user-by-production-area')
  @Roles('/production-execution/timesheet-records', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'list-user-by-sn' })
  async listUserBy(@Body() params: RequestListDevice) {
    const result = await this.timeSheetService.listDevice(params);

    return {
      status: true,
      data: result,
    };
  }

  @DefPost('/list-station')
  @Roles('/production-execution/timesheet-records', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'list-station' })
  async getListStation(@Body() params: RequestGetListStation, @Req() req: Request) {
    const request = requestHelper(req);
    const listStation = await this.timeSheetService.getListStation(params, request.clientIP);

    return {
      status: true,
      data: listStation,
    };
  }

  @DefPost('/get-user-timesheet')
  @Roles('/production-execution/timesheet-records', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Lấy thông tin timesheet của user' })
  async getUserTimeSheet(@Body() params: { userNo: number }) {
    const result = await this.timeSheetService.getUserTimeSheet(params.userNo);
    return {
      status: true,
      data: result,
    };
  }
}
