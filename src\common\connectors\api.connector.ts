import { configEnv } from '~/@config/env';
import { EMediaType, createHttpClient } from '~/@core/network';

export const optionalApiConnector = createHttpClient({
  baseURL: '',
  timeout: 2 * 60 * 1000,
  beforeRequest: (config) => {
    return config;
  },
  handleError: (err) => {
    return err;
  },
  handleResponse: async (res) => {
    return res.data;
  },
});

export const scadaApiConnector = createHttpClient({
  baseURL: ``,
  timeout: 2 * 60 * 1000,
  beforeRequest: (config) => {
    config.headers['Content-Type'] = EMediaType.APPLICATION_JSON;
    config.headers['Accept'] = EMediaType.APPLICATION_JSON;
    return config;
  },
  handleError: (err) => {
    if (err.response && err.response.data) {
      // console.error('API call failed. Request body:', err.config?.data);
      // console.error('Response error data:', err.response.data);
      throw { error: err.response.data, body: err.config?.data };
    }
    // Nếu không có data lỗi chi tiết, ném lỗi gốc
    throw err;
  },
  handleResponse: async (res) => {
    return res.data;
  },
});
