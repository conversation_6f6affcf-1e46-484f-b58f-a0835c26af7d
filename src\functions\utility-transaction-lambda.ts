import { Handler, Context } from 'aws-lambda';
import {
  ListUtilityTransaction,
  UtilityTransactionReq,
  UtilityMonitoringReq,
} from '~/dto/utility-transaction.dto'; // <PERSON>i<PERSON><PERSON> chỉnh theo đường dẫn DTO của bạn
import { NestFactory } from '@nestjs/core';
import { AppModule } from '~/app.module';
import { UtilityTransactionService } from '~/x-modules/admin/services';

let cachedApp: any = null;

async function bootstrap() {
  if (!cachedApp) {
    const app = await NestFactory.createApplicationContext(AppModule);
    await app.init();
    cachedApp = app;
  }
  return cachedApp;
}

// Lambda handler cho việc phân trang danh sách utility transaction
export const listUtilityTransaction: Handler = async (event: any, context: Context) => {
  const app = await bootstrap(); // Khởi tạo NestJS app
  const service = app.get(UtilityTransactionService);

  const params: ListUtilityTransaction = event.queryStringParameters; // Lấy tham số từ query string

  try {
    const result = await service.pagination(params); // Gọi phương thức phân trang
    return {
      statusCode: 200,
      body: JSON.stringify(result),
      headers: {
        'Content-Type': 'application/json',
      },
    };
  } catch (error) {
    return {
      statusCode: error.status || 500,
      body: JSON.stringify({
        message: error.message || 'Internal server error',
        error: error.response?.error || error.name,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    };
  }
};

// Lambda handler cho việc tạo mới Utility Transaction
export const createUtilityTransaction: Handler = async (event: any, context: Context) => {
  const app = await bootstrap(); // Khởi tạo NestJS app
  const service = app.get(UtilityTransactionService);

  const body: UtilityTransactionReq = JSON.parse(event.body); // Lấy dữ liệu từ body

  try {
    const result = await service.create(body); // Gọi phương thức tạo mới transaction
    return {
      statusCode: 201,
      body: JSON.stringify(result),
      headers: {
        'Content-Type': 'application/json',
      },
    };
  } catch (error) {
    return {
      statusCode: error.status || 500,
      body: JSON.stringify({
        message: error.message || 'Internal server error',
        error: error.response?.error || error.name,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    };
  }
};

// Lambda handler cho việc lấy danh sách utility meter
export const loadUtilityMeter: Handler = async (event: any, context: Context) => {
  const app = await bootstrap(); // Khởi tạo NestJS app
  const service = app.get(UtilityTransactionService);

  const factoryId: string = event.queryStringParameters.factoryId; // Lấy factoryId từ query string

  try {
    const result = await service.loadUtilityMeter(factoryId); // Gọi phương thức load utility meter
    return {
      statusCode: 200,
      body: JSON.stringify(result),
      headers: {
        'Content-Type': 'application/json',
      },
    };
  } catch (error) {
    return {
      statusCode: error.status || 500,
      body: JSON.stringify({
        message: error.message || 'Internal server error',
        error: error.response?.error || error.name,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    };
  }
};

// Lambda handler cho việc lấy danh sách shift
export const loadShift: Handler = async (event: any, context: Context) => {
  const app = await bootstrap(); // Khởi tạo NestJS app
  const service = app.get(UtilityTransactionService);

  const factoryId: string = event.queryStringParameters.factoryId; // Lấy factoryId từ query string

  try {
    const result = await service.loadShift(factoryId); // Gọi phương thức load shift
    return {
      statusCode: 200,
      body: JSON.stringify(result),
      headers: {
        'Content-Type': 'application/json',
      },
    };
  } catch (error) {
    return {
      statusCode: error.status || 500,
      body: JSON.stringify({
        message: error.message || 'Internal server error',
        error: error.response?.error || error.name,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    };
  }
};

// Lambda handler cho việc lấy utility monitoring
export const getUtilityMonitor: Handler = async (event: any, context: Context) => {
  const app = await bootstrap(); // Khởi tạo NestJS app
  const service = app.get(UtilityTransactionService);

  const params: UtilityMonitoringReq = event.queryStringParameters; // Lấy tham số từ query string

  try {
    const result = await service.getUtilityMonitor(params); // Gọi phương thức lấy utility monitoring
    return {
      statusCode: 200,
      body: JSON.stringify(result),
      headers: {
        'Content-Type': 'application/json',
      },
    };
  } catch (error) {
    return {
      statusCode: error.status || 500,
      body: JSON.stringify({
        message: error.message || 'Internal server error',
        error: error.response?.error || error.name,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    };
  }
};
