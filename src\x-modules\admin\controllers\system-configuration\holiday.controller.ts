import {
  BadRequestException,
  Body,
  InternalServerErrorException,
  Param,
  ParseIntPipe,
  UseGuards,
} from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { DefController, DefDelete, DefGet, DefPost, DefPut } from '~/@core/decorator';
import { CreateHolidayDto, UpdateHolidayDto } from '~/dto/holiday.dto';
import { HolidayService } from '../../services/system-configuration/holiday.service';
import { Roles } from '~/@core/decorator/roles.decorator';
import { RoleGuard } from '~/@systems/guard/role.guard';

@ApiTags('Holiday')
@DefController('holidays')
export class HolidayController {
  constructor(private readonly holidaysService: HolidayService) {}

  @DefPost('', {
    summary: 'Create a new holiday', // Mô tả cho Swagger
    bodyType: CreateHolidayDto, // Kiểu dữ liệu body
    responseType: CreateHolidayDto, // Kiểu dữ liệu trả về (có thể thay đổi tùy logic)
    statusCode: 201, // 201 Created
  })
  @Roles('/system-configuration/holidays', 'View')
  @UseGuards(RoleGuard)
  async create(@Body() createHolidayDto: CreateHolidayDto) {
    try {
      return await this.holidaysService.create(createHolidayDto);
    } catch (error) {
      if (error.message) {
        throw new BadRequestException(error.message);
      } else {
        throw new InternalServerErrorException('Something went wrong');
      }
    }
  }

  @DefGet('', {
    summary: 'Get all holidays', // Mô tả cho Swagger
    responseType: [CreateHolidayDto], // Trả về mảng các holiday
  })
  @Roles('/system-configuration/holidays', 'View')
  @UseGuards(RoleGuard)
  async findAll() {
    return this.holidaysService.findAll();
  }

  @DefGet(':id', {
    summary: 'Get a holiday by ID', // Mô tả cho Swagger
    responseType: CreateHolidayDto, // Kiểu dữ liệu trả về
  })
  @Roles('/system-configuration/holidays', 'View')
  @UseGuards(RoleGuard)
  async findOne(@Param('id') id: string) {
    return this.holidaysService.findOne(id);
  }

  @DefPut(':id', {
    summary: 'Update a holiday', // Mô tả cho Swagger
    bodyType: UpdateHolidayDto, // Kiểu dữ liệu body
    responseType: UpdateHolidayDto, // Kiểu dữ liệu trả về (có thể thay đổi tùy logic)
  })
  @Roles('/system-configuration/holidays', 'View')
  @UseGuards(RoleGuard)
  async update(@Param('id') id: string, @Body() updateHolidayDto: UpdateHolidayDto) {
    try {
      await this.holidaysService.update(id, updateHolidayDto);
    } catch (error) {
      throw new BadRequestException(error.message);
    }
  }

  @DefDelete(':id', {
    summary: 'Delete a holiday', // Mô tả cho Swagger
    statusCode: 204, // 204 No Content
  })
  @Roles('/system-configuration/holidays', 'View')
  @UseGuards(RoleGuard)
  async remove(@Param('id') id: string) {
    return this.holidaysService.remove(id);
  }
}
