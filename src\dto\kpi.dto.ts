import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';
import { PageRequest } from '~/@systems/utils';
import { KpiEntity } from '~/entities/primary/kpi.entity';

export class CreateKPIDto {
  @ApiProperty({ description: 'Mã ngành hàng' })
  @IsNotEmpty()
  @IsString()
  kpiCategory: string;

  @ApiProperty({ description: 'Id ngành hàng' })
  @IsNotEmpty()
  @IsString()
  kpiCategoryId: string;

  @ApiProperty({ description: 'Nhóm KPI' })
  @IsNotEmpty()
  @IsString()
  kpiGroup: string;

  @ApiProperty({ description: 'Id Nhóm KPI' })
  @IsNotEmpty()
  @IsString()
  kpiGroupId: string;

  @ApiProperty({ description: 'Mã KPI' })
  @IsNotEmpty()
  @IsString()
  code: string;

  @ApiProperty({ description: 'Tên viết tắt của <PERSON>' })
  @IsNotEmpty()
  @IsString()
  shortName: string;

  @ApiProperty({ description: 'Tên đầy đủ của KPI' })
  @IsNotEmpty()
  @IsString()
  longName: string;

  @ApiProperty({ description: 'Đơn vị đo lường' })
  @IsNotEmpty()
  @IsString()
  unit: string;

  @ApiProperty({ description: 'Id Đơn vị đo lường' })
  @IsNotEmpty()
  @IsString()
  unitId: string;

  @ApiProperty({ description: 'Phương pháp đo KPI' })
  @IsNotEmpty()
  @IsString()
  kpiMethod: string;

  @ApiProperty({ description: 'Id Phương pháp đo KPI' })
  @IsNotEmpty()
  @IsString()
  kpiMethodId: string;

  @ApiProperty({ description: 'Hàm tự động tính toán' })
  @IsOptional()
  @IsString()
  autoFunction?: string;

  @ApiProperty({ description: 'Id Hàm tự động tính toán' })
  @IsOptional()
  @IsString()
  autoFunctionId?: string;

  @ApiProperty({ description: 'Danh sách các tham số (Para1 - Para6)' })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  parameters?: string[];

  @ApiProperty({ description: 'Tham số lấy theo Material Group' })
  @IsOptional()
  @IsString()
  materialGroup?: string;

  @ApiProperty({ description: 'Id Tham số lấy theo Material Group' })
  @IsOptional()
  @IsString()
  materialGroupId?: string;

  @ApiProperty({ description: 'Tham số lấy theo Utility Type' })
  @IsOptional()
  @IsString()
  utilityType?: string;

  @ApiProperty({ description: 'Id Tham số lấy theo Utility Type' })
  @IsOptional()
  @IsString()
  utilityTypeId?: string;

  @ApiProperty({ description: 'Loại tính toán' })
  @IsNotEmpty()
  @IsString()
  calType: string;

  @ApiProperty({ description: 'Id Loại tính toán' })
  @IsNotEmpty()
  @IsString()
  calTypeId: string;

  @ApiProperty({ description: 'Công thức tính' })
  @IsNotEmpty()
  @IsString()
  calMethod: string;

  @ApiProperty({ description: 'Id Công thức tính' })
  @IsNotEmpty()
  @IsString()
  calMethodId: string;

  @ApiProperty({ description: 'Tần suất nhập dữ liệu' })
  @IsNotEmpty()
  @IsString()
  inputFrequency: string;

  @ApiProperty({ description: 'Id Tần suất nhập dữ liệu' })
  @IsNotEmpty()
  @IsString()
  inputFrequencyId: string;

  @ApiProperty({ description: 'Trạng thái KPI (true: hoạt động, false: không hoạt động)' })
  @IsNotEmpty()
  @IsNumber()
  status: number;
}

export class KpiEditDto extends CreateKPIDto {
  @ApiProperty({ description: 'ID kpi' })
  @IsNotEmpty()
  @IsString()
  id: string;
}
export class KpiEditResponseDto extends KpiEntity {
  isDisable: boolean;
}

export class KpiPaginationDto extends PageRequest {
  @ApiProperty({
    type: Object,
  })
  where: any;
}
