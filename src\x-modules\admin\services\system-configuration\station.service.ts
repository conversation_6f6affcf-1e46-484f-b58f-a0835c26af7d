import { Injectable } from '@nestjs/common';
import { <PERSON>ike, In, Not, Raw } from 'typeorm';
import { BindRepo, DefTransaction } from '~/@core/decorator';
import { DataSuccessResponse, PageRequest, PageResponse } from '~/@systems/utils/page.utils';
import { NSGeneralData } from '~/common/enums';
import { StationEntity } from '~/entities/primary';
import {
  AccessRepo,
  DeviceRepo,
  GeneralDataDetailRepo,
  GeneralDataRepo,
  OrganizationUnitRepo,
  ProcessRepo,
  ReportHistoryRepo,
  StationDetailRepo,
  StationRepo,
  UserRepo,
} from '~/repositories/primary';
import { adminSessionContext } from '~/x-modules/admin/admin-session.context';
import { StationReq } from '~/dto/station.dto';

@Injectable()
export class StationService {
  constructor() {}

  @BindRepo(StationRepo)
  private stationRepo: StationRepo;
  @BindRepo(DeviceRepo)
  private deviceRepo: DeviceRepo;
  @BindRepo(StationDetailRepo)
  private stationDetailRepo: StationDetailRepo;
  @BindRepo(OrganizationUnitRepo)
  private organizationUnitRepo: OrganizationUnitRepo;
  @BindRepo(GeneralDataRepo)
  private generalDataRepo: GeneralDataRepo;
  @BindRepo(GeneralDataDetailRepo)
  private generalDataDetailRepo: GeneralDataDetailRepo;

  @BindRepo(AccessRepo)
  private accessRepo: AccessRepo;
  @BindRepo(UserRepo)
  private userRepo: UserRepo;

  async getAll(): Promise<PageResponse> {
    const [data, total] = await this.stationRepo.findAndCount();
    return { data: data, meta: { totalRecords: total } };
  }

  async search(
    req: {
      code?: string;
      description?: string;
      typeCode?: string;
      isActive?: boolean;
    } & PageRequest,
  ): Promise<PageResponse> {
    const { code, description, typeCode, isActive, pageIndex, pageSize } = req;
    const [data, total] = await this.stationRepo.findAndCount({
      where: {
        ...(code && {
          code: Raw((alias) => `CAST(${alias} AS TEXT) LIKE :code`, { code: `%${code}%` }),
        }),

        ...(description && { description: ILike(`%${description}%`) }),
        ...(typeCode && { typeCode: typeCode }),
        ...(isActive !== undefined && { isActive: isActive }),
      },
      order: { code: 'DESC', createdDate: 'DESC' },
      relations: ['site', 'factory', 'line', 'process'],
      skip: pageIndex && pageSize ? (pageIndex - 1) * pageSize : undefined,
      take: pageIndex && pageSize ? pageSize : undefined,
    });

    const result = await Promise.all(
      data.map(async (item) => {
        const site = await item.site;
        const factory = await item.factory;
        const line = await item.line;
        const process = await item.process;
        const user = await this.userRepo.findOne({ id: item.createdBy });
        return {
          ...item,
          siteCode: site?.code || null,
          siteName: site?.name || null,
          factoryCode: factory?.code || null,
          factoryName: factory?.name || null,
          lineCode: line?.code || null,
          lineName: line?.name || null,
          processCode: process?.code || null,
          processName: process?.name || null,
          createdByEmail: user?.email || null,
        };
      }),
    );

    return {
      data: result,
      meta: {
        totalRecords: total,
        request: req,
      },
    };
  }

  @DefTransaction()
  async save(data: StationReq) {
    const { userId } = adminSessionContext;
    const { deviceIds, id, typeCode, ...rest } = data;

    let station: StationEntity;

    if (id) {
      // update
      station = await this.stationRepo.findOne({ where: { id }, relations: ['assigns'] });
      if (!station) throw new Error('Station không tồn tại. Vui lòng kiểm tra lại!');

      this.stationRepo.merge(station, {
        ...rest,
        typeCode: typeCode,
        updatedBy: userId,
      });
    } else {
      // create
      const last = await this.stationRepo.findOne({
        order: { code: 'DESC' },
        select: ['code'],
      });

      const nextCode = (last?.code ?? -1) + 1;

      station = this.stationRepo.create({
        ...rest,
        code: nextCode,
        typeCode,
        typeGenCode: NSGeneralData.EGeneralDataCode.STATION_TYPE,
        createdBy: userId,
      });
    }

    const saved = await this.stationRepo.save(station);

    const assigns = await this.updateDeviceAssigns(saved.id, deviceIds || [], Boolean(id));

    return { data: { ...saved, assigns } };
  }

  private async updateDeviceAssigns(stationId: string, newDeviceIds: string[], isUpdate: boolean) {
    // Lấy danh sách hiện tại nếu là update
    const currentDeviceIds = isUpdate
      ? (await this.stationDetailRepo.find({ where: { stationId: stationId } })).map(
          (d) => d.deviceId,
        )
      : [];

    // Tìm device mới cần thêm (tránh device đã tồn tại)
    const devicesToAdd = newDeviceIds.filter((id) => !currentDeviceIds.includes(id));
    if (!devicesToAdd.length) return;

    // Check device đã gán
    const assignedDevices = await this.stationDetailRepo.find({
      where: { deviceId: In(devicesToAdd), assign: true },
    });

    if (assignedDevices.length) {
      throw new Error('Một hoặc nhiều device đã được gán cho station khác. Vui lòng kiểm tra lại!');
    }

    const devices = await this.deviceRepo.findByIds(devicesToAdd);
    if (devices.length !== devicesToAdd.length) {
      throw new Error('Một hoặc nhiều device không tồn tại!');
    }

    const entities = devices.map((device) =>
      this.stationDetailRepo.create({
        stationId: stationId,
        deviceId: device.id,
        assign: true,
      }),
    );

    const saved = await this.stationDetailRepo.save(entities);
    return saved;
  }

  async getDetail(id: string) {
    const station = await this.stationRepo.findOne({
      where: { id: id },
      relations: ['assigns', 'assigns.device'],
    });

    if (!station) {
      throw new Error('Station không tồn tại. Vui lòng kiểm tra lại ID!');
    }

    return station;
  }

  async getAvailableDevices(dto: any) {
    const { code, description, deviceTypeDetailCode, isActive, stationId } = dto;

    // Lấy danh sách deviceId đã được assign vào bất kỳ station nào
    const assignedDeviceIds = (
      await this.stationDetailRepo.find({
        where: { assign: true },
        select: ['deviceId'],
      })
    ).map((pm) => pm.deviceId);

    let excludedDeviceIds = assignedDeviceIds;

    // Nếu có stationId, loại ra những device đã thuộc stationId đó
    if (stationId) {
      const deviceInStation = (
        await this.stationDetailRepo.find({
          where: { stationId: stationId },
          select: ['deviceId'],
        })
      ).map((pm) => pm.deviceId);

      excludedDeviceIds = [...new Set([...assignedDeviceIds, ...deviceInStation])];
    }

    // Lọc danh sách device chưa được gán vào process hoặc processId được chỉ định
    const [devices, total] = await this.deviceRepo.findAndCount({
      where: [
        {
          id: Not(In(excludedDeviceIds)),
          ...(code && {
            code: Raw((alias) => `CAST(${alias} AS TEXT) LIKE :code`, { code: `%${code}%` }),
          }),
          ...(description && { description: ILike(`%${description}%`) }),
          ...(deviceTypeDetailCode && { deviceTypeDetailCode }),
          ...(isActive !== undefined && { isActive }),
        },
      ],
      order: { isActive: 'DESC', code: 'ASC', createdDate: 'DESC' },
    });

    return {
      data: devices,
      total,
    };
  }

  async changeAssignDevice(stationId: string, deviceId: string, assign: boolean) {
    const assignEntity = await this.stationDetailRepo.findOne({
      where: { stationId, deviceId },
    });

    if (!assignEntity) {
      throw new Error('Device không tồn tại trong Station này.');
    }

    // Nếu đang gán assign = true, mới kiểm tra thiết bị đã được assign ở station khác chưa
    if (!!assign) {
      const checkAssigned = await this.stationDetailRepo.findOne({
        where: { deviceId, assign: true, stationId: Not(stationId) },
      });

      if (checkAssigned) {
        throw new Error('Device này đã được assign vào một Station khác.');
      }
    }

    assignEntity.assign = assign;
    const saved = await this.stationDetailRepo.save(assignEntity);

    return new DataSuccessResponse({ data: saved });
  }

  async getMetaStationTypes(req: PageRequest): Promise<PageResponse<any>> {
    const genData = await this.generalDataRepo.find({
      where: { code: NSGeneralData.EGeneralDataCode.STATION_TYPE },
      select: ['id', 'code'],
    });

    const details = await this.generalDataDetailRepo.find({
      where: { generalId: In(genData.map((item) => item.id)) },
      order: { code: 'ASC' },
      select: ['id', 'code', 'name'],
    });

    return { data: details, meta: { totalRecords: details.length, request: req } };
  }

  async getMetaSites(req: PageRequest): Promise<PageResponse<any>> {
    const { userId } = adminSessionContext;
    if (!userId) return null;

    const lstAccess = await this.accessRepo.find({
      where: { status: true, user: { id: userId } },
      relations: ['user', 'organizationUnit'],
    });
    if (lstAccess.length === 0) return { data: [], meta: { totalRecords: 0, request: req } };
    const lstFactoryId = lstAccess.map((item) => item.organizationUnit.id);
    const lstFactory = await this.organizationUnitRepo.find({
      where: { isActive: true, id: In(lstFactoryId) },
    });
    const sites = await this.organizationUnitRepo.find({
      where: {
        isActive: true,
        id: In(lstFactory.map((i) => i.parentId)),
        levelGeneralDataDetailCode: NSGeneralData.EOrgLevel.SITE,
      },
      order: { code: 'ASC', createdDate: 'DESC' },
    });
    return { data: sites, meta: { totalRecords: sites.length, request: req } };
  }

  async getMetaFactories(
    req: { userId?: string; siteId?: string } & PageRequest,
  ): Promise<PageResponse<any>> {
    if (!req?.userId) return { data: [], meta: { totalRecords: 0, request: req } };
    const lstAccess = await this.accessRepo.find({
      where: {
        status: true,
        user: { id: req.userId },
      },
      relations: ['user', 'organizationUnit'],
    });
    if (lstAccess.length === 0) return { data: [], meta: { totalRecords: 0, request: req } };
    const lstFactory = lstAccess.map((item) => item.organizationUnit.id);
    const factories = await this.organizationUnitRepo.find({
      where: {
        isActive: true,
        id: In(lstFactory),
        levelGeneralDataDetailCode: NSGeneralData.EOrgLevel.FACTORY,
        ...(req?.siteId && { parentId: req.siteId }),
      },
      order: { code: 'ASC', createdDate: 'DESC' },
    });
    return { data: factories, meta: { totalRecords: factories.length, request: req } };
  }

  async getMetaLines(
    req: { userId?: string; factoryId?: string } & PageRequest,
  ): Promise<PageResponse<any>> {
    if (!req?.userId) return { data: [], meta: { totalRecords: 0, request: req } };
    const lstAccess = await this.accessRepo.find({
      where: { status: true, user: { id: req.userId } },
      relations: ['user', 'organizationUnit'],
    });
    if (lstAccess.length === 0) return { data: [], meta: { totalRecords: 0, request: req } };
    const lstFactory = lstAccess.map((item) => item.organizationUnit.id);
    const lstLine = await this.organizationUnitRepo.find({
      where: { levelGeneralDataDetailCode: NSGeneralData.EOrgLevel.LINE, parentId: In(lstFactory) },
    });
    const lines = await this.organizationUnitRepo.find({
      where: {
        isActive: true,
        id: In(lstLine.map((item) => item.id)),
        levelGeneralDataDetailCode: NSGeneralData.EOrgLevel.LINE,
        ...(req?.factoryId && { parentId: req.factoryId }),
      },
      order: { code: 'ASC', createdDate: 'DESC' },
    });
    return { data: lines, meta: { totalRecords: lines.length, request: req } };
  }

  async getMetaProcesses(
    req: { userId?: string; lineId?: string } & PageRequest,
  ): Promise<PageResponse<any>> {
    if (!req?.userId) return { data: [], meta: { totalRecords: 0, request: req } };
    const lstAccess = await this.accessRepo.find({
      where: { status: true, user: { id: req.userId } },
      relations: ['user', 'organizationUnit'],
    });
    if (lstAccess.length === 0) return { data: [], meta: { totalRecords: 0, request: req } };
    const lstFactory = lstAccess.map((item) => item.organizationUnit.id);
    const lstLine = await this.organizationUnitRepo.find({
      where: {
        levelGeneralDataDetailCode: NSGeneralData.EOrgLevel.PROCESS,
        parentId: In(lstFactory),
      },
    });
    const lines = await this.organizationUnitRepo.find({
      where: {
        isActive: true,
        levelGeneralDataDetailCode: NSGeneralData.EOrgLevel.PROCESS,
        ...(req?.lineId ? { parentId: req.lineId } : { parentId: In(lstLine.map((i) => i.id)) }),
      },
      order: { code: 'ASC', createdDate: 'DESC' },
    });
    return { data: lines, meta: { totalRecords: lines.length, request: req } };
  }

  async getMetaDeviceTypes(req: PageRequest): Promise<PageResponse<any>> {
    const genData = await this.generalDataRepo.find({
      where: { code: NSGeneralData.EGeneralDataCode.DEVICE_TYPE },
      select: ['id', 'code'],
    });

    const details = await this.generalDataDetailRepo.find({
      where: { generalId: In(genData.map((item) => item.id)) },
      order: { code: 'ASC' },
      select: ['id', 'code', 'name'],
    });

    return { data: details, meta: { totalRecords: details.length, request: req } };
  }

  async getMetaAccessList() {
    const { userId } = adminSessionContext;
    if (!userId) return null;
    const lstAccess = await this.accessRepo.find({
      where: { status: true, user: { id: userId } },
      relations: ['user', 'organizationUnit'],
    });
  }

  async getMetaDefaultAccess() {
    const { userId } = adminSessionContext;
    if (!userId) return null;

    const defaultAccess = await this.accessRepo.findOne({
      where: { status: true, default: true, user: { id: userId } },
      relations: ['user', 'organizationUnit'],
    });

    if (!defaultAccess?.organizationUnit) return null;

    const [factory, site] = await Promise.all([
      this.organizationUnitRepo.findOne({
        where: { id: defaultAccess.organizationUnit?.id },
      }),
      this.organizationUnitRepo.findOne({
        where: { id: defaultAccess.organizationUnit?.parentId },
      }),
    ]);

    return {
      factoryId: factory?.id,
      factoryCode: factory?.code,
      factoryName: factory?.name,
      siteId: site?.id,
      siteCode: site?.code,
      siteName: site?.name,
    };
  }
}

class POMaterialReportOutput {
  site: string;
  factory: string;
  line: string;
  processArea: string;
  productionOrderNo: string;
  recipeCode: string;
  recipeVer: number;
  orderQty: number;
  orderUom: string;
  productionDate: Date;
  shift: string;
  orderStatus: string;
  createdBy: string;
  creationDate: Date;
  operator: string;
  processCode: string;
  processName: string;
  materialCode: string;
  materialName: string;
  type: string; // e.g., ING, PKG, etc.
  uom: string;
  planQty: number;
  actualQty: number;
  sumTransactionQty: number;
  transactionId: string;
  batchNumber: string;
  transactionType: string;
  transactionUom: string;
  transactionQty: number;
  transactionDate: Date;
  lot: string;
  oeeCalculation: string; // OEE tổng hợp, nếu null thì dùng undefined hoặc nullable
}

class ProductionOrderResourceReportOutput {
  site: string; // code - name
  factory: string; // code - name
  line: string; // code - name
  processArea: string; // code - name
  // production_order
  productionOrderNo: string;
  recipeCode: string;
  recipeVer: number;
  orderQty: number;
  orderUom: string;
  productionDate: Date; // YYYY-MM-DD
  // shift
  shift: string; // code - description
  // status
  orderStatus: string; // name
  createdBy: string;
  creationDate: Date;
  // operator
  operator: string; // employeeCode - employeeName
  // production_order_resource
  processCode: string;
  processName: string;
  resourceCode: string;
  resourceName: string;
  uom: string;
  planProdQty: number;
  actualProdQty: number;
  planQty: number;
  actualQty: number;
}

class ProductionBatchAndProcessParameterReportOutput {
  site: string;
  factory: string;
  line: string;
  processArea: string;
  // Production Order Details
  productionOrderNo: string;
  recipeCode: string;
  recipeVer: number;
  orderQty: number;
  orderUom: string;
  productionDate: Date;
  shift: string;
  orderStatus: string;
  // Created By Information
  createdBy: string;
  creationDate: Date;
  // Operator Information
  operator: string;
  // Batch Information
  batchNumber: number | string;
  batchStatus: string;
  startTime: Date;
  endTime: Date;
  // Process Information
  processCode: number;
  processName: string;
  // Machine Information
  machineCode: string;
  machineName: string;
  // Process Parameter
  processParameterCode: string;
  processParameterName: string;
  value: string;
  datetime: Date;
}
