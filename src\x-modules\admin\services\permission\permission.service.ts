import { ConflictException, Injectable, NotFoundException } from '@nestjs/common';
import { In, Like } from 'typeorm';
import { BindRepo } from '~/@core/decorator';
import {
  PermissionCreateDto,
  PermissionPageReq,
  PermissionUpdateDto,
  PermissionUpdateRoleDto,
  PermissionUpdateUserDto,
} from '~/dto/permission.dto';
import { PermissionEntity, PermissionUserEntity } from '~/entities/primary';
import { PermissionRepo, PermissionUserRepo } from '~/repositories/primary';
import { UserRepo } from '~/repositories/primary/user.repo';
import { v4 as uuidv4 } from 'uuid';
import { RolePermission } from '~/common/enums/enumRole.enum';
import { adminSessionContext } from '~/x-modules/admin/admin-session.context';
import dayjs from 'dayjs';
import { Buffer } from 'buffer';

@Injectable()
export class PermissionService {
  constructor() {}

  @BindRepo(PermissionRepo)
  private readonly repo: PermissionRepo;

  @BindRepo(PermissionUserRepo)
  private readonly permissionUserRepo: PermissionUserRepo;

  @BindRepo(UserRepo)
  private userRepo: UserRepo;

  /* Hàm find ra danh sách nhóm quyền danh cho selection box */
  public async find() {
    const res: any[] = await this.repo.find({
      where: { isActive: true },
      select: ['id', 'name'],
    });
    return res;
  }

  /* Hàm phân trang cho nhóm quyền */
  async pagination(params: PermissionPageReq) {
    const whereCon: any = {};
    const where = params.where || {};
    if (where.code) whereCon.code = Like(`%${where.code}%`);
    if (where.name) whereCon.name = Like(`%${where.name}%`);
    if ([true, false, 0, 1].includes(where.isActive)) whereCon.isActive = where.isActive;

    const res: any = await this.repo.findPagination(
      {
        where: whereCon,
        order: { createdDate: 'DESC' },
        relations: ['permissionUsers'],
      },
      params,
    );

    for (let item of res.data) {
      item.numberUser = item.__permissionUsers__.length;
    }
    return res;
  }
  /* Hàm tạo mới quyền duyệt
  Nếu truyền vào list userId thì sẽ tạo quyền duyệt cho các user đó */
  async createData(data: PermissionCreateDto) {
    const existed = await this.repo.findOne({ where: { code: data.code } });
    if (existed) {
      throw new NotFoundException(`Permission with code "${data.code}" already exists`);
    }
    /* nếu như có danh sách nhân viên thì check kiểm tra id truyền vào có tồn tại hay không */
    if (data.listUserId && data.listUserId.length > 0) {
      const lstUser = await this.userRepo.find({
        where: { id: In(data.listUserId), isActive: true },
        select: ['id'],
      });
      if (lstUser.length !== data.listUserId.length) {
        const lstId = lstUser.map((u) => u.id);
        const lstNotExisted = data.listUserId.filter((id) => !lstId.includes(id));
        throw new Error(`User with id ${lstNotExisted.join(', ')} not found`);
      }
    }
    const permission = new PermissionEntity();
    permission.id = uuidv4();
    permission.name = data.name;
    permission.code = data.code;
    permission.description = data.description;
    // permission.createdBy = 'admin';
    await this.repo.insert(permission);
    /* sau khi luư xong nếu có nhập sẵn nhân viên thì thêm nhân viên vào quyền bằng cách insert vào bảng permissionUser */
    if (data.listUserId && data.listUserId.length > 0) {
      const lstPermissionUser = data.listUserId.map((userId) => {
        const permissionUser = new PermissionUserEntity();
        permissionUser.id = uuidv4();
        permissionUser.userId = userId;
        permissionUser.permissionId = permission.id;
        return permissionUser;
      });
      await this.permissionUserRepo.insert(lstPermissionUser);
    }
    return { message: 'success' };
  }
  /* Hàm chỉnh sửa quyền duyệt */
  async updateData(data: PermissionUpdateDto) {
    if (data.listUserId && data.listUserId.length > 0) {
      const lstUser = await this.userRepo.find({
        where: { id: In(data.listUserId), isActive: true },
        select: ['id'],
      });
      if (lstUser.length !== data.listUserId.length) {
        const lstId = lstUser.map((u) => u.id);
        const lstNotExisted = data.listUserId.filter((id) => !lstId.includes(id));
        throw new Error(`User with id ${lstNotExisted.join(', ')} not found`);
      }
    }
    const permission = await this.repo.findOne({ where: { id: data.id } });
    if (!permission) throw new Error(`Permission with id ${data.id} not found`);
    permission.name = data.name;
    permission.code = data.code;
    permission.description = data.description;
    // permission.updatedBy = 'admin';
    await this.repo.update(permission.id, permission);
    return { message: 'success' };
  }

  async loadDataPermissionFirst(permissionId: string) {
    const data = await this.repo.findOne({
      where: { id: permissionId },
    });

    if (data) {
      (data as any).permissionName = `${data.code} - ${data.name}` || '';
    }

    return {
      message: 'Success',
      data,
    };
  }

  /* hàm lấy ra chi tiết của quyền thêm */
  async findById(id: string) {
    const permission: any = await this.repo.findOne({
      where: { id: id },
      relations: ['permissionUsers'],
    });
    if (!permission) throw new Error(`Permission with id ${id} not found`);
    /* tìm ra số lượng nhân viên trong quyền này bằng cách lấy userId của permission.permissionUsers */
    permission.numberUser = permission.__permissionUsers__.length;
    const listUserId = permission.__permissionUsers__.map((pu) => pu.userId);
    permission.lstUser = await this.userRepo.find({
      select: ['fullName', 'id', 'employeeCode', 'phone', 'email'],
      where: { id: In(listUserId), isActive: true },
    });
    return { data: permission };
  }

  /* hàm load ra danh sách quyền của */
  public async loadPermissionGroup(id: string) {
    const foundPermissionGroup = await this.repo.findOne({
      where: { id: id },
    });

    if (!foundPermissionGroup) throw new Error('Permission not found');

    let treeNodes = this.convertToTreeNodes(RolePermission);
    let checkedKeysMap = {};
    let lstEnumFinalRole = [];

    if (foundPermissionGroup.roleStringify) {
      const parsedData = JSON.parse(foundPermissionGroup.roleStringify);
      const savedTreeNodes = parsedData.treeNodes || [];
      checkedKeysMap = parsedData.checkedKeysMap || {};

      treeNodes = this.mergeTreeNodes(treeNodes, savedTreeNodes);
    }
    lstEnumFinalRole = this.getListRole(treeNodes, []);

    return { treeNodes, checkedKeysMap, lstEnumFinalRole };
  }

  private mergeTreeNodes(defaultNodes: any[], savedNodes: any[]): any[] {
    const merge = (defaultNode, savedNode) => {
      const mergedNode = { ...defaultNode };
      if (savedNode) {
        mergedNode.expanded = savedNode.expanded ?? defaultNode.expanded;
      }

      if (defaultNode.children && defaultNode.children.length > 0) {
        mergedNode.children = defaultNode.children.map((childDefault) => {
          const matchedSaved = savedNode?.children?.find(
            (childSaved) => childSaved.key === childDefault.key,
          );
          return merge(childDefault, matchedSaved);
        });
      }

      return mergedNode;
    };

    return defaultNodes.map((defaultNode) => {
      const savedNode = savedNodes?.find((s) => s[0]?.key === defaultNode[0]?.key);
      return [merge(defaultNode[0], savedNode?.[0])];
    });
  }
  private getListRole(lstRole, lstEnumRole) {
    for (let role of lstRole) {
      const dataRole = role?.[0] || role;
      if (dataRole?.children?.length) {
        this.getListRole(dataRole.children, lstEnumRole);
      } else {
        lstEnumRole.push({
          ...dataRole,
        });
      }
    }
    return lstEnumRole;
  }

  /* hàm set phân quyền cho permission*/
  async updatePermissionGroupRole(data: PermissionUpdateRoleDto) {
    const foundPermissionGroup = await this.repo.findOne({
      where: { id: data.id },
    });

    if (!foundPermissionGroup) throw new Error('Permission not found');

    const decodedJson = Buffer.from(data.listPermission, 'base64').toString('utf-8');

    await this.repo.update(data.id, {
      roleStringify: decodedJson,
    });

    return { message: 'success' };
  }
  //#region "Private"

  convertToTreeNodes(roleGroups: { [key: string]: any }) {
    const result: any[] = [];

    // lặp qua các menu cấp 1
    Object.keys(roleGroups).forEach((groupKey) => {
      const group = roleGroups[groupKey];
      const groupNode: any = {
        title: group.name,
        code: group.code, // Add code for the parent root node!
        key: `${group.id}`,
        children: [],
        path: group.path,
        expanded: true, // Assuming you want the group nodes to be initially expanded
      };

      if (group.children?.length) {
        // lặp qua các menu cấp 2
        group.children.forEach((child1, index1) => {
          const childNode1: any = {
            title: child1.name,
            key: `${group.id}-${index1}`,
            children: [],
            path: child1.path,
            expanded: false,
          };

          if (child1.children?.length) {
            // lặp qua các menu cấp cấp 3
            child1.children.forEach((child2, index2) => {
              const childNode2: any = {
                title: child2.name,
                key: `${group.id}-${index1}-${index2}`,
                children: [],
                path: child2.path,
                expanded: false,
              };
              if (child2.children?.length) {
                child2.children.forEach((child3, index3) => {
                  const childNode3: any = {
                    title: child3.name,
                    key: `${group.id}-${index1}-${index2}-${index3}`,
                    children: [],
                    path: group.path,
                    expanded: false,
                  };
                  Object.keys(child3).forEach((actionKey, idx) => {
                    if (typeof child3[actionKey] === 'object') {
                      const action = child3[actionKey] as any;
                      childNode3.children?.push(
                        this.getValueAction({ ...action, key: `${childNode3.key}-${idx}` }),
                      );
                    }
                  });
                  childNode2.children?.push(childNode3);
                });
              } else {
                Object.keys(child2).forEach((actionKey, idx) => {
                  if (typeof child2[actionKey] === 'object') {
                    const action = child2[actionKey] as any;
                    childNode2.children?.push(
                      this.getValueAction({ ...action, key: `${childNode2.key}-${idx}` }),
                    );
                  }
                });
              }

              childNode1.children?.push(childNode2);
            });
          } else {
            Object.keys(child1).forEach((actionKey, idx) => {
              if (typeof child1[actionKey] === 'object') {
                const action = child1[actionKey] as any;
                childNode1.children?.push(
                  this.getValueAction({ ...action, key: `${childNode1.key}-${idx}` }),
                );
              }
            });
          }
          groupNode.children?.push(childNode1);
        });
      }
      result.push([groupNode]);
    });

    return result;
  }

  getValueAction(action: any) {
    return {
      title: action.name,
      code: action.code,
      key: `${action.key}`,
      isLeaf: true,
      checked: action.value,
      selected: action.value, // Assuming the 'selected' state is determined by 'value'
      path: action.path,
    };
  }

  async loadDataSelect() {
    return await this.repo.find({
      select: ['id', 'code', 'name'],
    });
  }

  getRolePermissions() {
    return RolePermission;
  }

  async updateActivePermission(id: string) {
    const entity = await this.repo.findOne({ where: { id: id } });
    if (!entity) throw new Error('Not Found Material Group Header');
    const { userId: userId } = adminSessionContext;
    const today = new Date();
    return await this.repo.update(
      { id: id },
      { isActive: !entity.isActive, updatedBy: userId, updatedDate: today },
    );
  }

  async delete(id: string): Promise<void> {
    const user = await this.permissionUserRepo.findOne(id);
    if (!user) {
      throw new NotFoundException(`User Not Found!`);
    }
    await this.permissionUserRepo.remove(user);
  }

  async getAllPermissions() {
    const permissions = await this.repo.find();

    return {
      items: permissions,
      total: permissions.length,
    };
  }

  async getUserRoles(userId: string) {
    const userPermission = await this.userRepo.findOne({
      where: { id: userId },
      relations: ['permission'],
    });

    if (!userPermission) {
      return [];
    }

    let lstEnumRole = [];

    try {
      const permission = await userPermission.permission;

      // Parse chỉ phần `treeNodes`
      const { treeNodes } = JSON.parse(permission.roleStringify) || {};

      if (!treeNodes) {
        return [];
      }

      lstEnumRole = this.getLstRole(treeNodes, lstEnumRole);
      lstEnumRole = lstEnumRole.filter((item) => item.checked);

      return lstEnumRole;
    } catch (error) {
      console.error('Error parsing treeNodes:', error);
      return [];
    }
  }
  private getLstRole(lstRole, lstEnumRole) {
    for (let role of lstRole) {
      const dataRole = role?.[0] || role;
      if (dataRole?.children?.length) {
        this.getListRole(dataRole.children, lstEnumRole);
      } else if (dataRole.isLeaf) {
        lstEnumRole.push({
          ...dataRole,
        });
      }
    }
    return lstEnumRole;
  }

  //#endregion "Private"
}
