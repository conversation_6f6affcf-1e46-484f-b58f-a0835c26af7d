import { Column, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>umn, ManyToOne } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { PrimaryBaseEntity } from '~/entities/primary-base.entity';
import { MaterialGroupHeaderEntity } from '~/entities/primary/material-group-header.entity';
import { ItemEntity } from '~/entities/primary/item.entity';

/** Chi tiết nhóm nguyên vật liệu*/
@Entity('material_group_detail')
export class MaterialGroupDetailEntity extends PrimaryBaseEntity {
  /** Mã nhóm nguyên vật liệu header */
  @ApiProperty()
  @Column({ nullable: true })
  headerId: string;

  /** Mã sản phẩm */
  @ApiProperty()
  @Column({ nullable: true })
  itemId: string;

  /** Trạng thái chi tiết nhóm nguyên vật liệu */
  @Column({ default: true })
  active?: boolean;

  /** Nhóm nguyên vật liệu header */
  @ManyToOne(() => MaterialGroupHeaderEntity, (p) => p.details)
  @JoinColumn({ name: 'headerId', referencedColumnName: 'id' })
  header: Promise<MaterialGroupHeaderEntity>;

  /** Sản phẩm */
  @ManyToOne(() => ItemEntity, (p) => p.materialGroupDetails)
  @JoinColumn({ name: 'itemId', referencedColumnName: 'id' })
  item: Promise<ItemEntity>;
}
