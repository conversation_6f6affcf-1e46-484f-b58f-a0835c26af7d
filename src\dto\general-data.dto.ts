import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Transform } from 'class-transformer';
import { IsBoolean, IsNotEmpty, IsOptional, IsString } from 'class-validator';
import { PageRequest } from '~/@systems/utils';

export class ListGeneralReq extends PageRequest {
  @ApiPropertyOptional({ description: 'Mã của General Master Data' })
  @IsOptional()
  @IsString()
  code?: string;

  @ApiPropertyOptional({ description: 'Tên General Master Data' })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiPropertyOptional({ description: 'Mô tả General Master Data' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiPropertyOptional({ description: 'Ghi chú' })
  @IsOptional()
  @IsString()
  note?: string;

  @ApiPropertyOptional({ description: 'Trạng thái hoạt động' })
  @IsOptional()
  @IsBoolean()
  @Transform(({ value }) => {
    if (value === 'true') return true;
    if (value === 'false') return false;
    return value;
  })
  isActive?: boolean;

  @ApiPropertyOptional({
    description: 'Ngày tạo từ (ISO format)',
    example: '2023-01-01T00:00:00.000Z',
  })
  @IsOptional()
  @IsString()
  createdDateFrom?: string;

  @ApiPropertyOptional({
    description: 'Ngày tạo đến (ISO format)',
    example: '2023-12-31T23:59:59.999Z',
  })
  @IsOptional()
  @IsString()
  createdDateTo?: string;

  @ApiPropertyOptional({
    description: 'Ngày cập nhật từ (ISO format)',
    example: '2023-01-01T00:00:00.000Z',
  })
  @IsOptional()
  @IsString()
  updatedDateFrom?: string;

  @ApiPropertyOptional({
    description: 'Ngày cập nhật đến (ISO format)',
    example: '2023-12-31T23:59:59.999Z',
  })
  @IsOptional()
  @IsString()
  updatedDateTo?: string;
}

export class GeneralMasterDataCreateDto {
  @ApiProperty({ description: 'Mã của General Master Data' })
  @IsNotEmpty()
  @IsString()
  code: string;

  @ApiProperty({ description: 'Tên General Master Data' })
  @IsNotEmpty()
  @IsString()
  name: string;

  @ApiProperty({ description: 'Mô tả General Master Data' })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: 'Ghi chú ' })
  @IsOptional()
  @IsString()
  note?: string;

  @ApiProperty({
    description: 'Trạng thái hoạt động',
    default: true,
  })
  @IsOptional()
  @IsBoolean()
  isActive: boolean = true;
}

export class GeneralMasterDataUpdateDto extends GeneralMasterDataCreateDto {
  @ApiProperty({ description: 'ID General Master Data' })
  @IsNotEmpty()
  @IsString()
  id: string;
}
