import { <PERSON>, <PERSON>, Body, Headers, Res, HttpStatus, UseGuards } from '@nestjs/common';
import { Response } from 'express';
import { ScheduleAuthGuard } from '~/@systems/guard/schedule-auth.guard';
import { SitewiseAuthGuard } from '~/@systems/guard/sitewise-auth.guard';
import { SitewiseInService, SitewiseService } from '~/x-modules/integration/services/sitewise';

@Controller('mecdx-controller')
export class SitewiseController {
  constructor(
    private readonly sitewiseService: SitewiseService,
    private readonly sitewiseInService: SitewiseInService,
  ) {}

  @UseGuards(SitewiseAuthGuard)
  @Post('listen-iotsitewise')
  async receiveIotsitewiseData(@Body() data: any, @Headers() headers: any, @Res() res: Response) {
    // console.log(data, 'data');
    if (data.messageType === 'DestinationConfirmation') {
      // console.log(data, 'data');
      return res.status(HttpStatus.OK).send('OK');
    } else {
      await this.sitewiseService.createMesDXSitewise(data);
      return res.status(HttpStatus.OK).json({ message: 'Data received successfully' });
    }
  }

  @UseGuards(ScheduleAuthGuard)
  @Post('sync-recipe-to-scada')
  async syncRecipeToScada(@Res() res: Response) {
    const recipe = await this.sitewiseInService.syncRecipeToScadaFalse();
    return res.status(HttpStatus.OK).json({ message: 'Data received successfully' });
  }

  @UseGuards(ScheduleAuthGuard)
  @Post('sync-production-order-to-scada')
  async syncProductionOrderToScada(@Res() res: Response) {
    const productionOrder = await this.sitewiseInService.syncProductionOrderToScadaFalse();
    return res.status(HttpStatus.OK).json({ message: 'Data received successfully' });
  }

  @UseGuards(ScheduleAuthGuard)
  @Post('sync-inventory-to-scada')
  async syncInventoryToScada(@Res() res: Response) {
    const inventory = await this.sitewiseInService.syncInventoryToScadaFalse();
    return res.status(HttpStatus.OK).json({ message: 'Data received successfully' });
  }

  // @Post('test')
  // async test() {
  //   const result = await this.sitewiseInService.syncRecipeToScada(
  //     '90496bdb-5ed9-4b12-b3b9-1a85ad50fdc6',
  //   );
  //   console.log(result, 'result');
  //   return { message: 'Data received successfully' };
  // }
}
