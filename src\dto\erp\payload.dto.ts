import { ApiProperty } from '@nestjs/swagger';

/**
 * DTO chính cho Message Payload - Response.
 */
export class ERPMessagePayloadRes<DataPayload> {
  @ApiProperty({
    description: 'Mã code để xác định trạng thái trả về ERP',
    example: 200,
  })
  code: number;

  @ApiProperty({
    description: '<PERSON><PERSON><PERSON> giải cho nhóm mã code 2xx',
    example: 'success',
  })
  status?: string;

  @ApiProperty({
    description: 'Diễn giải chi tiết cho thông tin trả về từ ERP',
    example: 'successfully',
  })
  message: string;

  @ApiProperty({
    type: Object,
    description: 'Dữ liệu trả về',
  })
  data: DataPayload;
}

/**
 * DTO  ở cấp "data" trong payload.
 */
export class ERPDataPayloadRes<Ref> {
  @ApiProperty({
    description: 'Thời gian tạo thông tin',
    example: '2024-11-08T05:05:21.953Z',
  })
  created_at: Date | string;

  @ApiProperty({
    description: 'Thời gian cập nhật thông tin',
    example: '2024-11-08T05:05:21.953Z',
  })
  updated_at: Date | string;

  @ApiProperty({
    type: [Object],
    description: 'Danh sách các bản ghi chi tiết giao dịch resource',
  })
  refs: Ref[];
}
