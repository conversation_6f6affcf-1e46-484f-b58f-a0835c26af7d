import { Controller, Get, Post, Body, Param, Delete, Put } from '@nestjs/common';
import { RejectionReasonService } from '../../services/master-data/rejection-reason.service';
import { RejectionReasonEntity } from '~/entities/primary';
import { DefController, DefGet, DefPost } from '~/@core/decorator';
import { ApiTags } from '@nestjs/swagger';
import { RejectionReasonPageReq } from '~/dto/rejection-reason.dto';

@ApiTags('RejectionReason')
@DefController('rejection-reason')
export class RejectionReasonController {
  constructor(private readonly service: RejectionReasonService) {}

  @DefPost('refresh')
  create() {
    return this.service.refresh();
  }

  @DefPost('/pagination')
  pagination(@Body() body: RejectionReasonPageReq) {
    return this.service.pagination(body);
  }

  @DefGet('/all')
  findAll() {
    return this.service.findAll();
  }

  @DefGet('detail/:id')
  findOne(@Param('id') id: string) {
    return this.service.findOne(id);
  }
}
