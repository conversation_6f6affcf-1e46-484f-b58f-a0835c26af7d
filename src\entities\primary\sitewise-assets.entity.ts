import { Column, CreateDateColumn, Entity, Index, PrimaryColumn, UpdateDateColumn } from 'typeorm';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsUUID } from 'class-validator';
import { PrimaryBaseEntity } from '~/entities/primary-base.entity';

@Entity('sitewise_asset')
export class SitewiseAssetEntity extends PrimaryBaseEntity {
  /** Tên sitewise asset */
  @Column({ nullable: true })
  name: string;

  /** code sitewise asset */
  @Column({ nullable: true })
  code: string;

  /** Mô tả sitewise asset */
  @Column({ name: 'description', nullable: true })
  description: string;

  /** Trạng thái hoạt động sitewise asset */
  @ApiProperty({ description: 'Trạng thái hoạt động' })
  @Column({ nullable: true })
  status: string;

  /** Mã assetId */
  @PrimaryColumn()
  @IsUUID(4)
  awsId: string;

  /** Mã externalId */
  @Column({ name: 'externalId', nullable: true })
  externalId: string;

  @Column({ name: 'sitewiseModelId', nullable: true })
  sitewiseModelId: string;
}
