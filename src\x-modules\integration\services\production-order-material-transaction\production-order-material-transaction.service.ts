import { Injectable } from '@nestjs/common';
import { BindRepo } from '~/@core/decorator';
import { MesDxTransformRepo } from '~/repositories/scada';
import { JwtService } from '@nestjs/jwt';
import moment from 'moment';
import {
  ProductionBatchRepo,
  MesDxProdBatchStatusRepo,
  ProductionOrderMaterialRepo,
  MaterialConsumptionScadaRepo,
  ProductionOrderMaterialTransactionRepo,
  OrganizationUnitRepo,
  ItemRepo,
} from '~/repositories/primary';
import { In, Not } from 'typeorm';
import { GetProductionOrderMaterialTransactionsRequest } from '~/dto/erp/production-order-material.dto';
import dayjs from 'dayjs';
import utc from 'dayjs/plugin/utc';
import timezone from 'dayjs/plugin/timezone';

dayjs.extend(utc);
dayjs.extend(timezone);

@Injectable()
export class ProdOrderMaterialTransactionService {
  constructor(private jwtService: JwtService) {}

  //   // Admin Database
  @BindRepo(MesDxProdBatchStatusRepo)
  private mesDxProdBatchStatusRepo: MesDxProdBatchStatusRepo;
  @BindRepo(ProductionBatchRepo)
  private productionBatchRepo: ProductionBatchRepo;

  @BindRepo(ProductionOrderMaterialTransactionRepo)
  private productionOrderMaterialTransactionRepo: ProductionOrderMaterialTransactionRepo;

  @BindRepo(ProductionOrderMaterialRepo)
  private productionOrderMaterialRepo: ProductionOrderMaterialRepo;

  @BindRepo(MaterialConsumptionScadaRepo)
  private readonly materialConsumptionScadaRepo: MaterialConsumptionScadaRepo;

  @BindRepo(OrganizationUnitRepo)
  private readonly organizationUnitRepo: OrganizationUnitRepo;

  @BindRepo(ItemRepo)
  private readonly itemRepo: ItemRepo;

  onHandleTransactionDate(date: any) {
    let getTime = moment().utcOffset(7 * 60);
    if (date) getTime = moment(date, 'YYYY-MM-DD HH:mm:ss.SSS Z').utcOffset(7 * 60);
    getTime.startOf('day');
    return getTime.format('YYYY-MM-DD HH:mm:ss.SSS Z');
  }

  private async onTransactionSecondStep(prodOrderMaterial: any, materialConsumptionScadas: any) {
    const objTransaction = {
      orderId: materialConsumptionScadas?.orderId,
      batchId: materialConsumptionScadas?.batchId,
      productionOrderMaterialId: prodOrderMaterial?.id,
      materialId: prodOrderMaterial?.materialId,
      transactionDate: materialConsumptionScadas?.datetime
        ? this.onHandleTransactionDate(materialConsumptionScadas?.datetime)
        : materialConsumptionScadas?.datetime,
      transactionType: 'WIP_ISSUE',
      lotNumber: materialConsumptionScadas?.lotNumber,
      transactionQty: !isNaN(Number(materialConsumptionScadas?.qty))
        ? Number(materialConsumptionScadas?.qty)
        : 0,
      transactionUom: materialConsumptionScadas?.uom,
      createdByUser: '-1',
      lastUpdate: new Date(),
      lastUpdateBy: -1,
      scadaTransactionId: materialConsumptionScadas?.id,
    };
    const transactionQtyNumber = !isNaN(Number(objTransaction.transactionQty))
      ? Number(objTransaction.transactionQty)
      : 0;
    prodOrderMaterial.actualTrxQty += objTransaction.transactionQty || 0;
    await this.productionOrderMaterialTransactionRepo.save({ ...(objTransaction as any) });
    await this.materialConsumptionScadaRepo.update(
      { id: materialConsumptionScadas?.id },
      { isProcessed: true },
    );
    await this.productionOrderMaterialRepo.increment(
      { id: prodOrderMaterial?.id },
      'actualTrxQty',
      transactionQtyNumber,
    );
  }

  private async onTransactionFirstStep(materialConsumptionScadas: any) {
    // Tìm record production_batch tương ứng status
    const productionBatchData = await this.productionBatchRepo.findOne({
      where: { id: materialConsumptionScadas?.batchId },
    });
    if (!productionBatchData) return;

    // Tìm tất cả record ở production_order_material table có line_type=-1
    const prodOrderMaterialData = await this.productionOrderMaterialRepo.findOne({
      where: {
        orderId: materialConsumptionScadas?.orderId,
        lineType: -1,
        materialId: materialConsumptionScadas?.ingredientId,
      },
    });
    if (!prodOrderMaterialData) return;

    await this.onTransactionSecondStep(prodOrderMaterialData, materialConsumptionScadas);
  }
  async createProdOrderMaterialTransaction() {
    try {
      const materialConsumptionScadaData = await this.materialConsumptionScadaRepo.find({
        where: { isProcessed: false },
      });
      if (materialConsumptionScadaData?.length > 0) {
        await Promise.all(
          materialConsumptionScadaData.map(async (materialConsumptionScada) => {
            await this.onTransactionFirstStep(materialConsumptionScada);
          }),
        );
      }
    } catch (error) {
      console.log(error, 'error');
    }
  }

  async getProductionOrderMaterialTransactions(
    params: GetProductionOrderMaterialTransactionsRequest,
  ) {
    try {
      /*

        1. Để load api-key: truy cập validate-token lấy idToken 
        2. /api/admin/api-token header authorization = idToken => x-header
        3. /api/integration/* add vào x-header = x-header 

      */

      let {
        orderNo,
        site: siteCode,
        factory: factoryCode,
        fromtransactionDate,
        totransactionDate,
        fromUpdatedDate,
        toUpdatedDate,
      } = params;

      function convertToUTC(dateStr: string): string {
        const normalized = dateStr.replace(/\s(\d{2})$/, '+$1:00');
        return dayjs(normalized).utc().format('YYYY-MM-DD HH:mm:ss.SSSSSS+00');
      }

      if (fromtransactionDate) {
        fromtransactionDate = convertToUTC(fromtransactionDate);
      }

      if (totransactionDate) {
        totransactionDate = convertToUTC(totransactionDate);
      }

      if (fromUpdatedDate) {
        fromUpdatedDate = convertToUTC(fromUpdatedDate);
      }

      if (toUpdatedDate) {
        toUpdatedDate = convertToUTC(toUpdatedDate);
      }

      const query = this.productionOrderMaterialTransactionRepo
        .createQueryBuilder('pomt')
        .leftJoin('production_order_material', 'pom', 'pom.id = pomt.productionOrderMaterialId')
        .leftJoin('production_order', 'po', 'po.id = pom.orderId')
        .leftJoin('organization_units', 'site', 'site.id = po.siteId AND site.isActive = true')
        .leftJoin('process', 'p', 'p.id = pom.processId')
        .leftJoin('organization_units', 'ou1', 'ou1.id = po.factoryId AND ou1.isActive = true')
        .leftJoin(
          'organization_units',
          'ou2',
          'ou2.id = p.organizationUnitId AND ou2.isActive = true',
        )
        .leftJoin('item', 'item', 'item.id = pomt.materialId')
        .where('po.orderStatusCode != :status', { status: -1 });

      if (fromtransactionDate && totransactionDate) {
        query.andWhere('pomt.transactionDate BETWEEN :fromtransactionDate AND :totransactionDate', {
          fromtransactionDate,
          totransactionDate,
        });
      } else if (fromtransactionDate) {
        query.andWhere('pomt.transactionDate >= :fromtransactionDate', { fromtransactionDate });
      } else if (totransactionDate) {
        query.andWhere('pomt.transactionDate <= :totransactionDate', { totransactionDate });
      }

      if (fromUpdatedDate && toUpdatedDate) {
        query.andWhere('pomt.updatedDate BETWEEN :fromUpdatedDate AND :toUpdatedDate', {
          fromUpdatedDate,
          toUpdatedDate,
        });
      } else if (fromUpdatedDate) {
        query.andWhere('pomt.updatedDate >= :fromUpdatedDate', { fromUpdatedDate });
      } else if (toUpdatedDate) {
        query.andWhere('pomt.updatedDate <= :toUpdatedDate', { toUpdatedDate });
      }

      if (orderNo) {
        query.andWhere('po.orderNo = :orderNo', { orderNo });
      }

      if (siteCode) {
        query.andWhere('site.code = :siteCode', { siteCode });
      }

      if (factoryCode) {
        query.andWhere(
          `(CASE 
        WHEN pom.lineType != -1 THEN ou1.code
        ELSE ou2.storageLocation
      END) = :factoryCode`,
          { factoryCode },
        );
      }

      // SELECT: show locator only, no filter
      query.select([
        'pomt.materialTransactionId AS "materialTransactionId"',
        'pomt.orderId AS "orderId"',
        'po.orderNo AS "orderNo"',
        'pomt.transactionType AS "transactionType"',
        'po.trxUom AS "uom"',
        'pomt.transactionQty AS "transactionQty"',
        'pomt.lotNumber AS "lotNumber"',
        'pomt.transactionDate AS "transactionDate"',
        'site.code AS "siteCode"',
        `CASE 
          WHEN pom.lineType != -1 THEN ou1.code
          ELSE ou2.storageLocation
        END AS "factoryCode"`,
        `CASE 
          WHEN pom.lineType != -1 THEN ou2."ingredientLocator"
          ELSE ou2."productsLocator"
        END AS "locator"`,
        'item.code AS "itemCode"',
        'pom.lineType',
      ]);

      // const query = this.productionOrderMaterialTransactionRepo
      //   .createQueryBuilder('trans')
      //   .leftJoin('production_order', 'order', 'order.id = trans.orderId')
      //   .leftJoin('organization_units', 'site', 'site.id = order.siteId AND site.isActive = true')
      //   .leftJoin(
      //     'organization_units',
      //     'factory',
      //     'factory.id = order.factoryId AND factory.isActive = true',
      //   )
      //   .leftJoin(
      //     'organization_units',
      //     'process',
      //     'process.id = order.processAreaId AND process.isActive = true',
      //   )
      //   .leftJoin('item', 'item', 'item.id = trans.materialId')
      //   .where('order.orderStatusCode != :status', { status: -1 });

      // if (fromtransactionDate && totransactionDate) {
      //   query.andWhere(
      //     'trans.transactionDate BETWEEN :fromtransactionDate AND :totransactionDate',
      //     {
      //       fromtransactionDate,
      //       totransactionDate,
      //     },
      //   );
      // } else if (fromtransactionDate) {
      //   query.andWhere('trans.transactionDate >= :fromtransactionDate', { fromtransactionDate });
      // } else if (totransactionDate) {
      //   query.andWhere('trans.transactionDate <= :totransactionDate', { totransactionDate });
      // }

      // if (fromUpdatedDate && toUpdatedDate) {
      //   query.andWhere('trans.updatedDate BETWEEN :fromUpdatedDate AND :toUpdatedDate', {
      //     fromUpdatedDate,
      //     toUpdatedDate,
      //   });
      // } else if (fromUpdatedDate) {
      //   query.andWhere('trans.updatedDate >= :fromUpdatedDate', { fromUpdatedDate });
      // } else if (toUpdatedDate) {
      //   query.andWhere('trans.updatedDate <= :toUpdatedDate', { toUpdatedDate });
      // }

      // if (orderNo) {
      //   query.andWhere('order.orderNo = :orderNo', { orderNo });
      // }

      // if (siteCode) {
      //   query.andWhere('site.code = :siteCode', { siteCode });
      // }

      // if (factoryCode) {
      //   query.andWhere('factory.code = :factoryCode', { factoryCode });
      // }

      // query.select([
      //   'trans.materialTransactionId AS "materialTransactionId"',
      //   'trans.orderId AS "orderId"',
      //   'order.orderNo AS "orderNo"',
      //   'trans.transactionType AS "transactionType"',
      //   'order.trxUom AS "uom"',
      //   'trans.transactionQty AS "transactionQty"',
      //   'trans.lotNumber AS "lotNumber"',
      //   'trans.transactionDate AS "transactionDate"',
      //   'site.code AS "siteCode"',
      //   'process.storageLocation AS "storageLocation"',
      //   'factory.code AS "factoryCode"',
      //   'item.code AS "itemCode"',
      // ]);

      const records = await query.getRawMany();

      const nowUTC = new Date().toISOString();

      return {
        code: 200,
        status: 'success',
        message: 'successfully',
        data: {
          created_at: nowUTC,
          updated_at: nowUTC,
          refs: records,
        },
      };
    } catch (error) {
      const statusCode = error.statusCode || 500;
      const message = error.message || 'An unexpected error occurred';

      throw Error(
        JSON.stringify({
          code: statusCode,
          status: 'error',
          message: message,
        }),
      );
    }
  }
}
