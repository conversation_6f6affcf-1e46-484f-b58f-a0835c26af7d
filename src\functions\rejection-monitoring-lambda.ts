import { Handler, Context } from 'aws-lambda';
import { NestFactory } from '@nestjs/core';
import { AppModule } from '~/app.module';
import {
  ProductionRejectionReq,
  ListProductionRejection,
  RejectionMonitoringReq,
} from '~/dto/rejection-monitoring.dto';
import { RejectionMonitoringService } from '~/x-modules/admin/services';

let cachedApp: any = null;

async function bootstrap() {
  if (!cachedApp) {
    const app = await NestFactory.createApplicationContext(AppModule);
    await app.init();
    cachedApp = app;
  }
  return cachedApp;
}

/**
 * Lambda Handler to create a production rejection transaction
 */
export const createRejectionTransaction: Handler = async (event: any, context: Context) => {
  const app = await bootstrap();
  const service = app.get(RejectionMonitoringService);
  const body: ProductionRejectionReq = JSON.parse(event.body);

  try {
    const result = await service.create(body);
    return {
      statusCode: 201,
      body: JSON.stringify(result),
      headers: {
        'Content-Type': 'application/json',
      },
      message: result.message,
    };
  } catch (error) {
    return {
      statusCode: error.status || 500,
      body: JSON.stringify({
        message: error.message || 'Internal server error',
        error: error.response?.error || error.name,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    };
  }
};

/**
 * Lambda Handler to get list of production rejection transactions
 */
export const listRejectionTransaction: Handler = async (event: any, context: Context) => {
  const app = await bootstrap();
  const service = app.get(RejectionMonitoringService);
  const params: ListProductionRejection = event.queryStringParameters;

  try {
    const result = await service.pagination(params);
    return {
      statusCode: 200,
      body: JSON.stringify(result),
      headers: {
        'Content-Type': 'application/json',
      },
      message: result.message,
    };
  } catch (error) {
    return {
      statusCode: error.status || 500,
      body: JSON.stringify({
        message: error.message || 'Internal server error',
        error: error.response?.error || error.name,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    };
  }
};

export const listRejectionMonitoring: Handler = async (event: any, context: Context) => {
  const app = await bootstrap(); // Khởi tạo NestJS app
  const service = app.get(RejectionMonitoringService);

  // Lấy tham số query từ event (có thể có params như siteId, factoryId, ... )
  const params: RejectionMonitoringReq = event.queryStringParameters;

  try {
    // Gọi service.listRejectionMonitoring và truyền tham số
    const result = await service.listRejectionMonitoring(params);

    // Trả về kết quả dưới dạng JSON
    return {
      statusCode: 200,
      body: JSON.stringify(result),
      headers: {
        'Content-Type': 'application/json',
      },
    };
  } catch (error) {
    // Xử lý lỗi nếu có
    return {
      statusCode: error.status || 500,
      body: JSON.stringify({
        message: error.message || 'Internal server error',
        error: error.response?.error || error.name,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    };
  }
};

/**
 * Lambda Handler to get rejection monitoring details
 */
export const getRejectionMonitoringDetails: Handler = async (event: any, context: Context) => {
  const app = await bootstrap();
  const service = app.get(RejectionMonitoringService);
  const id: string = event.pathParameters?.id;

  try {
    const result = await service.detail(id);
    return {
      statusCode: 200,
      body: JSON.stringify(result),
      headers: {
        'Content-Type': 'application/json',
      },
      message: result.message,
    };
  } catch (error) {
    return {
      statusCode: error.status || 500,
      body: JSON.stringify({
        message: error.message || 'Internal server error',
        error: error.response?.error || error.name,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    };
  }
};

/**
 * Lambda Handler to update a production rejection transaction
 */
export const updateRejectionTransaction: Handler = async (event: any, context: Context) => {
  const app = await bootstrap();
  const service = app.get(RejectionMonitoringService);
  const id: string = event.pathParameters?.id;
  const body: ProductionRejectionReq = JSON.parse(event.body);

  try {
    const result = await service.update(id, body);
    return {
      statusCode: 200,
      body: JSON.stringify(result),
      headers: {
        'Content-Type': 'application/json',
      },
      message: result.message,
    };
  } catch (error) {
    return {
      statusCode: error.status || 500,
      body: JSON.stringify({
        message: error.message || 'Internal server error',
        error: error.response?.error || error.name,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    };
  }
};

/**
 * Lambda Handler to get options
 */
export const getRejectionOptions: Handler = async (event: any, context: Context) => {
  const app = await bootstrap();
  const service = app.get(RejectionMonitoringService);
  const levelCode: string = event.queryStringParameters?.levelCode;

  try {
    const result = await service.getOptions(levelCode);
    return {
      statusCode: 200,
      body: JSON.stringify(result),
      headers: {
        'Content-Type': 'application/json',
      },
      message: result.message,
    };
  } catch (error) {
    return {
      statusCode: error.status || 500,
      body: JSON.stringify({
        message: error.message || 'Internal server error',
        error: error.response?.error || error.name,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    };
  }
};

/**
 * Lambda Handler to load process area
 */
export const loadProcessArea: Handler = async (event: any, context: Context) => {
  const app = await bootstrap();
  const service = app.get(RejectionMonitoringService);
  const lineId: string = event.queryStringParameters?.lineId;

  try {
    const result = await service.loadProcessArea(lineId);
    return {
      statusCode: 200,
      body: JSON.stringify(result),
      headers: {
        'Content-Type': 'application/json',
      },
      message: result.message,
    };
  } catch (error) {
    return {
      statusCode: error.status || 500,
      body: JSON.stringify({
        message: error.message || 'Internal server error',
        error: error.response?.error || error.name,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    };
  }
};

export const loadProductionOrder: Handler = async (event: any, context: Context) => {
  const app = await bootstrap();
  const service = app.get(RejectionMonitoringService);

  try {
    const result = await service.loadProductionOrder(); // Gọi service.loadProductionOrder để lấy danh sách production orders
    return {
      statusCode: 200,
      body: JSON.stringify(result),
      headers: { 'Content-Type': 'application/json' },
    };
  } catch (error) {
    return {
      statusCode: error.status || 500,
      body: JSON.stringify({ message: error.message || 'Internal server error' }),
      headers: { 'Content-Type': 'application/json' },
    };
  }
};

export const loadShift: Handler = async (event: any, context: Context) => {
  const app = await bootstrap();
  const service = app.get(RejectionMonitoringService);

  try {
    const result = await service.loadShift(); // Gọi service.loadShift để lấy danh sách shifts
    return {
      statusCode: 200,
      body: JSON.stringify(result),
      headers: { 'Content-Type': 'application/json' },
    };
  } catch (error) {
    return {
      statusCode: error.status || 500,
      body: JSON.stringify({ message: error.message || 'Internal server error' }),
      headers: { 'Content-Type': 'application/json' },
    };
  }
};

export const loadTransactionType: Handler = async (event: any, context: Context) => {
  const app = await bootstrap();
  const service = app.get(RejectionMonitoringService);

  try {
    const result = await service.loadTransactionType(); // Gọi service.loadTransactionType để lấy danh sách transaction types
    return {
      statusCode: 200,
      body: JSON.stringify(result),
      headers: { 'Content-Type': 'application/json' },
    };
  } catch (error) {
    return {
      statusCode: error.status || 500,
      body: JSON.stringify({ message: error.message || 'Internal server error' }),
      headers: { 'Content-Type': 'application/json' },
    };
  }
};

export const loadItem: Handler = async (event: any, context: Context) => {
  const app = await bootstrap();
  const service = app.get(RejectionMonitoringService);

  try {
    const result = await service.loadItem(); // Gọi service.loadItem để lấy danh sách items
    return {
      statusCode: 200,
      body: JSON.stringify(result),
      headers: { 'Content-Type': 'application/json' },
    };
  } catch (error) {
    return {
      statusCode: error.status || 500,
      body: JSON.stringify({ message: error.message || 'Internal server error' }),
      headers: { 'Content-Type': 'application/json' },
    };
  }
};

export const loadUom: Handler = async (event: any, context: Context) => {
  const app = await bootstrap();
  const service = app.get(RejectionMonitoringService);

  try {
    const result = await service.loadUom(); // Gọi service.loadUom để lấy danh sách UOM
    return {
      statusCode: 200,
      body: JSON.stringify(result),
      headers: { 'Content-Type': 'application/json' },
    };
  } catch (error) {
    return {
      statusCode: error.status || 500,
      body: JSON.stringify({ message: error.message || 'Internal server error' }),
      headers: { 'Content-Type': 'application/json' },
    };
  }
};

export const loadRejectionReason: Handler = async (event: any, context: Context) => {
  const app = await bootstrap();
  const service = app.get(RejectionMonitoringService);

  try {
    const result = await service.loadRejectionReason(); // Gọi service.loadRejectionReason để lấy danh sách rejection reasons
    return {
      statusCode: 200,
      body: JSON.stringify(result),
      headers: { 'Content-Type': 'application/json' },
    };
  } catch (error) {
    return {
      statusCode: error.status || 500,
      body: JSON.stringify({ message: error.message || 'Internal server error' }),
      headers: { 'Content-Type': 'application/json' },
    };
  }
};

// Add more handlers as needed, similar to the above structure.
