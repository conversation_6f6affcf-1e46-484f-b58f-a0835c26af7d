import { ApiProperty } from '@nestjs/swagger';
import { Column, Entity, Index, ManyToOne } from 'typeorm';
import { NSRecipe } from '~/common/enums';
import { PrimaryBaseEntity } from '../primary-base.entity';
import { ItemEntity } from './item.entity';
import { RecipeProcessEntity } from './recipe-process.entity';

/** Bảng liện kết giữa công thức công đoạn và thành phần */
@Entity('recipe_process_item')
@Index(['recipeProcessId', 'itemId', 'typeCode'], { unique: true })
export class RecipeProcessItemEntity extends PrimaryBaseEntity {
  /** ID thành phần */
  @ApiProperty({ description: 'ID thành phần' })
  @Column({ nullable: false, type: 'uuid' })
  itemId: string;

  /** ID công đoạn */
  @ApiProperty({ description: 'ID công đoạn' })
  @Column({ nullable: false, type: 'uuid' })
  recipeProcessId: string;

  /** Liên kết công thức công đoạn */
  @ManyToOne(() => RecipeProcessEntity, recipeProcess => recipeProcess.recipeProcessItems, {
    nullable: false,
  })
  recipeProcess: RecipeProcessEntity;

  /** Liên kết thành phần */
  @ApiProperty({ description: 'ID thành phần' })
  @ManyToOne(() => ItemEntity, item => item.recipeProcessItems, { nullable: false })
  item: ItemEntity;

  /** Số thứ tự */
  @ApiProperty({ description: 'Số thứ tự' })
  @Column({ nullable: false })
  lineId: number;

  /** Mã loại */
  @ApiProperty({ description: 'Mã loại' })
  @Column({ length: 50, nullable: false })
  @Index({ unique: false })
  typeCode: NSRecipe.RecipeProcessItemTypeCode;

  /** Số lượng */
  @ApiProperty({ description: 'Số lượng' })
  @Column({ nullable: true, type: 'numeric' })
  quantity: number;

  /** Đơn vị tính */
  @ApiProperty({ description: 'Đơn vị tính' })
  @Column({ length: 50, nullable: false })
  uom: string;
}
