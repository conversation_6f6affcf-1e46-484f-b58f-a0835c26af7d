import { Body, HttpException, HttpStatus, Post, UseGuards } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { DefController, DefGet, DefPost } from '~/@core/decorator';
import { ValidateTokenReq, ValidateTokenRes } from '~/dto/auth.dto';
import { UserService } from '../services';
import { AuthService } from '../services/auth.service';
import { Roles } from '~/@core/decorator/roles.decorator';
import { RoleGuard } from '~/@systems/guard/role.guard';

@ApiTags('Auth')
@DefController('auth')
export class AuthController {
  constructor(
    private readonly authService: AuthService,
    private readonly userService: UserService,
  ) {}

  @Post('validate-token')
  @ApiOperation({ summary: 'Xác thực token từ Microsoft SSO' })
  async validateToken(@Body() body: ValidateTokenReq): Promise<ValidateTokenRes> {
    const result = await this.authService.validateMsToken(body.idToken);
    if (!result.isValid) {
      return {
        isValid: false,
        error: result.error,
      };
    }

    const isUserExist = await this.userService.isUserExist(result.payload.preferred_username);
    if (!isUserExist) {
      return {
        isValid: false,
        error: 'Account not found or inactive',
      };
    }
    // Lấy danh sách quyền của user
    const roles = await this.authService.getUserRoles(isUserExist.id);

    return {
      isValid: result.isValid,
      roles,
      isUserExist,
    };
  }
}
