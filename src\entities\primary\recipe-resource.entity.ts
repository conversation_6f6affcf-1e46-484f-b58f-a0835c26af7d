import { ApiProperty } from '@nestjs/swagger';
import { Column, Entity, ManyToOne } from 'typeorm';
import { PrimaryBaseEntity } from '../primary-base.entity';
import { RecipeProcessEntity } from './recipe-process.entity';

/** Bảng nguồn lực sản xuất */
@Entity('recipe_resource')
export class RecipeResourceEntity extends PrimaryBaseEntity {
  /** ID công thức - công đoạn */
  @ApiProperty({ description: 'ID công thức' })
  @Column({ type: 'uuid' })
  recipeProcessId: string;

  /** Nguồn lực sản xuất */
  @ApiProperty({ description: 'Nguồn lực sản xuất' })
  @Column({ length: 50, nullable: false })
  resource: string;

  /** Mô tả nguồn lực sản xuất */
  @ApiProperty({ description: '<PERSON><PERSON> tả nguồn lực sản xuất' })
  @Column({ type: 'text', nullable: false })
  resourceDesc: string;

  /** Số lượng nguồn lực sản xuất */
  @ApiProperty({ description: 'Số lượng nguồn lực sản xuất' })
  @Column({ nullable: true, type: 'numeric' })
  resourceUsage: number;

  /** Đơn vị đo lượng nguồn lực sản xuất */
  @ApiProperty({ description: 'Đơn vị đo lượng nguồn lực sản xuất' })
  @Column({ length: 10, nullable: false })
  resourceUsageUom: string;

  /** Công thức và công đoạn */
  @ManyToOne(() => RecipeProcessEntity, rp => rp.recipeResources, { nullable: false })
  recipeProcess: RecipeProcessEntity;
}
