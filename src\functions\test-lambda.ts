import { Context, Handler } from 'aws-lambda';
import { NestFactory } from '@nestjs/core';
import { AppModule } from '../app.module';
import { CardService } from '~/x-modules/admin/services';
let cachedApp: any = null;

async function bootstrap() {
  if (!cachedApp) {
    const app = await NestFactory.createApplicationContext(AppModule);
    // const app = await NestFactory.create(AppModule);
    await app.init();
    cachedApp = app;
  }
  return cachedApp;
}

export const test: Handler = async (event: any, context: Context) => {
  const app = await bootstrap();
  return {
    statusCode: 200,
    body: JSON.stringify('Test gateway success'),
  };
};

export const createCard: Handler = async (event: any, context: Context) => {
  const app = await bootstrap();
  const cardService = app.get(CardService);
  const { name, code } = JSON.parse(event.body);
  const result = await cardService.createCard(name, code);
  return {
    statusCode: 200,
    body: JSON.stringify('Test createCard success'),
  };
};

export const updateCard: Handler = async (event: any, context: Context) => {
  const app = await bootstrap();
  const cardService = app.get(CardService);
  const { id, name, code } = JSON.parse(event.body);
  const result = await cardService.updateCard(id, name, code);
  return {
    statusCode: 200,
    body: JSON.stringify(result),
  };
};

export const findCardById: Handler = async (event: any, context: Context) => {
  const app = await bootstrap();
  const cardService = app.get(CardService);
  const { id } = event.pathParameters;
  const result = await cardService.findCardById(parseInt(id));
  return {
    statusCode: 200,
    body: JSON.stringify(result),
  };
};
