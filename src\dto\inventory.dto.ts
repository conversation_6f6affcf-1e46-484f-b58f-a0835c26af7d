import { ApiProperty } from '@nestjs/swagger';
import { IsOptional } from 'class-validator';
import { PageRequest } from '~/@systems/utils';

export class RequestPaginationInventory extends PageRequest {
  @ApiProperty()
  @IsOptional()
  siteId?: string;

  @ApiProperty()
  @IsOptional()
  siteCode?: string;

  @ApiProperty()
  @IsOptional()
  factoryId?: string;

  @ApiProperty()
  @IsOptional()
  factoryCode?: string;

  @ApiProperty()
  @IsOptional()
  locatorCode?: string;

  @ApiProperty()
  @IsOptional()
  materialLotNumber?: string;

  @ApiProperty()
  @IsOptional()
  materialCode?: string;
}

export class ResponsePaginationInventory {}

export class RequestListSiteInventory {}

export class ResponseListSiteInventory {}

export class RequestListFactoryInventory {
  @ApiProperty()
  siteId: string;
}

export class ResponseListFactoryInventory {}

export class RequestListFiltersInventory {}

export class ResponseListFiltersInventory {}

export class RequestGetManualGPCInventory {
  @ApiProperty()
  siteId: string;

  @ApiProperty()
  factoryId?: string;
}

export class ResponseGetManualGPCInventory {}
