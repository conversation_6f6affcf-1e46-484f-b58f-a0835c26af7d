import { Column, CreateDateColumn, Entity, Index, PrimaryGeneratedColumn } from 'typeorm';
import { PrimaryBaseEntity } from '../primary-base.entity';
import { ApiProperty } from '@nestjs/swagger';
import { IsUUID } from 'class-validator';

@Entity('mes_dx_transforms')
export class MesDxTransformEntity {
  @ApiProperty()
  @PrimaryGeneratedColumn('uuid')
  @IsUUID(4)
  id?: string;

  @Index()
  @ApiProperty()
  @CreateDateColumn({ type: 'timestamptz' })
  createdDate?: Date;

  @ApiProperty({ description: 'Tên của gateway liên quan đến phép biến đổi', example: 'Gateway A' })
  @Column()
  gatewayName: string;

  @ApiProperty({ description: 'ID duy nhất của gateway', example: 'GATEWAY_12345' })
  @Column()
  gatewayId: string;

  @ApiProperty({ description: 'Tên của nguồn dữ liệu', example: 'Datasource 1' })
  @Column()
  datasourceName: string;

  @ApiProperty({ description: 'ID của nguồn dữ liệu', example: 'DS_001' })
  @Column()
  datasourceId: string;

  @ApiProperty({ description: 'Tên của mô hình', example: 'Model X' })
  @Column()
  modelName: string;

  @ApiProperty({ description: 'ID của mô hình', example: 'MODEL_9876' })
  @Column()
  modelId: string;

  @ApiProperty({ description: 'Tên của tài sản', example: 'Turbine 1' })
  @Column()
  assetName: string;

  @ApiProperty({ description: 'ID của tài sản', example: 'ASSET_5678' })
  @Column()
  assetId: string;

  @ApiProperty({ description: 'Tên của phép biến đổi', example: 'Processed Efficiency' })
  @Column()
  transformName: string;

  @Index()
  @ApiProperty({ description: 'ID của phép biến đổi', example: 'TRANSFORM_1234' })
  @Column()
  transformId: string;

  @ApiProperty({ description: 'Giá trị sau khi biến đổi', example: 90.2 })
  @Column({
    type: 'varchar',
    nullable: true,
  })
  value: string;

  @Index()
  @ApiProperty({ description: 'Thời gian ghi nhận phép biến đổi', example: '2024-03-06T14:30:00Z' })
  @Column({ type: 'timestamptz', nullable: true })
  datetime?: Date;
}
