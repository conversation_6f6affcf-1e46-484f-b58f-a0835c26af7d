import { Enti<PERSON>, Column, ManyToOne, Join<PERSON>olumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { ProductionOrderEntity } from './production-order.entity'; // Import ProductionOrder entity
import { PrimaryBaseEntity } from '../primary-base.entity';

@Entity('url')
export class UrlEntity extends PrimaryBaseEntity {
  /** Mỗi Production Order được tạo, thông tin detail của Production Order được lưu vào bảng Material. Order id link từ bảng Production Order. */
  @ApiProperty({ description: 'ID lệnh sản xuất (liên kết đến ProductionOrder)' })
  @Column({ type: 'uuid', nullable: true })
  orderId: string;

  @ApiProperty({ description: 'Id bảng url' })
  @Column()
  url: string;

  @ApiProperty({ description: 'File name được upload' })
  @Column({ nullable: true })
  fileName: string;

  // /** Relation với bảng GeneralDataDetail */
  @ManyToOne(() => ProductionOrderEntity)
  @JoinColumn({ name: 'orderId', referencedColumnName: 'id' })
  productionOrder: ProductionOrderEntity;

  @ApiProperty({ description: 'File name gốc' })
  @Column({ nullable: true })
  originalFileName: string;
}
