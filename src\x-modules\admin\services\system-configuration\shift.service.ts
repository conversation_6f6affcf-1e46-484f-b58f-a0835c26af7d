import { Injectable } from '@nestjs/common';
import dayjs from 'dayjs';
import { BindRepo } from '~/@core/decorator';
import { BusinessException } from '~/@systems/exceptions';
import { CreateShiftDto, UpdateShiftDto } from '~/dto/shift.dto';
import { ShiftEntity } from '~/entities/primary/shift.entity';
import { OrganizationUnitRepo } from '~/repositories/primary';
import { AssignShiftRepository } from '~/repositories/primary/assign-shift.repo';
import { ShiftRepo } from '~/repositories/primary/shift.repo';

@Injectable()
export class ShiftService {
  @BindRepo(ShiftRepo)
  private ShiftRepo: ShiftRepo;

  @BindRepo(AssignShiftRepository)
  private assignShiftRepo: AssignShiftRepository;

  @BindRepo(OrganizationUnitRepo)
  private organizationUnitRepo: OrganizationUnitRepo;

  async create(createShiftDto: CreateShiftDto): Promise<ShiftEntity> {
    // Convert code to uppercase before any further processing
    createShiftDto.code = createShiftDto.code.toUpperCase();

    // Kiểm tra startTime phải nhỏ hơn endTime
    // if (new Date(createShiftDto.startTime) >= new Date(createShiftDto.endTime)) {
    //   throw new Error('Start time must be earlier than end time');
    // }

    // Kiểm tra xem code đã tồn tại chưa
    const existingShift = await this.ShiftRepo.findOne({ where: { code: createShiftDto.code } });

    if (existingShift) {
      throw new Error('Shift with this code already exists');
    }

    const currentDateTime = dayjs().format('YYYY-MM-DD HH:mm:ss');

    // Nếu chưa tồn tại, tiếp tục tạo mới
    const shift = this.ShiftRepo.create({
      ...createShiftDto,
      // createdBy: null, // Cần cập nhật bằng userId nếu có user authentication
      createdDate: currentDateTime,
      updatedBy: null,
      updatedDate: currentDateTime,
    });
    return this.ShiftRepo.save(shift);
  }

  async findAll(): Promise<ShiftEntity[]> {
    return this.ShiftRepo.find({
      order: {
        updatedDate: 'DESC', // Sắp xếp giảm dần theo lastModifiedDate
      },
    });
  }

  async findOne(id: string): Promise<ShiftEntity> {
    return this.ShiftRepo.findOneOrFail({ where: { id } });
  }

  async update(id: string, updateShiftDto: UpdateShiftDto): Promise<ShiftEntity> {
    const shift = await this.ShiftRepo.findOneOrFail({ where: { id } });

    // Không cho phép update code
    // if (updateShiftDto.code && updateShiftDto.code !== shift.code) {
    //   throw new Error('Updating code is not allowed');
    // }

    // Convert code to uppercase before any further processing
    updateShiftDto.code = updateShiftDto.code.toUpperCase();

    const existingShift = await this.ShiftRepo.findOne({ where: { code: updateShiftDto.code } });

    if (existingShift && existingShift.id != shift.id) {
      throw new Error('Shift with this code already exists');
    }

    // Nếu có startTime hoặc endTime, kiểm tra điều kiện
    // if (updateShiftDto.startTime || updateShiftDto.endTime) {
    //   const newStartTime = updateShiftDto.startTime ? new Date(updateShiftDto.startTime) : shift.startTime;
    //   const newEndTime = updateShiftDto.endTime ? new Date(updateShiftDto.endTime) : shift.endTime;

    //   if (newStartTime >= newEndTime) {
    //     throw new Error('Start time must be earlier than end time');
    //   }
    // }

    const currentDateTime = dayjs().format('YYYY-MM-DD HH:mm:ss');

    await this.ShiftRepo.update(id, {
      ...updateShiftDto,
      updatedBy: null,
      updatedDate: currentDateTime,
    });
    return this.findOne(id);
  }

  async remove(id: string): Promise<void> {
    await this.ShiftRepo.delete(id);
  }

  /** Tìm ca theo line hoặc factory */
  async findShiftByLineOrFactory(data: { lineId: string }) {
    const checkOrgUnit = await this.organizationUnitRepo.findOne({
      where: { id: data.lineId, isActive: true },
    });
    if (!checkOrgUnit) throw new BusinessException('not found org');

    let checkAssignShift = await this.assignShiftRepo.find({
      where: { organizationId: data.lineId, status: 1 },
      relations: ['shift'],
      order: { createdDate: 'DESC' },
    });
    if (checkAssignShift?.length) return checkAssignShift.map((ass) => ass.shift);

    checkAssignShift = await this.assignShiftRepo.find({
      where: { organizationId: checkOrgUnit.parentId, status: 1 },
      relations: ['shift'],
      order: { createdDate: 'DESC' },
    });
    return checkAssignShift.map((ass) => ass.shift);
  }
}
