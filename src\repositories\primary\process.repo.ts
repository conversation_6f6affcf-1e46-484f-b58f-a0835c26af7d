import { EntityRepository } from 'typeorm';
import { PrimaryRepo } from '~/repositories/primary.repo';
import { ProcessEntity } from '~/entities/primary';
import { ProcessMachineEntity } from '~/entities/primary/process-machine.entity';

@EntityRepository(ProcessEntity)
export class ProcessRepo extends PrimaryRepo<ProcessEntity> {}

@EntityRepository(ProcessMachineEntity)
export class ProcessMachineRepo extends PrimaryRepo<ProcessMachineEntity> {}
