import { Body, UseGuards } from '@nestjs/common';

import { ApiOperation } from '@nestjs/swagger';
import { DefController, DefPost } from '~/@core/decorator';
import {
  AssignShiftDto,
  AssignShiftEditDto,
  AssignShiftPaginationDto,
} from '~/dto/assign-shift.dto';
import { AssignShiftEntity } from '~/entities/primary/assign-shift.entity';
import { ShiftEntity } from '~/entities/primary/shift.entity';
import { AssignShiftService } from '../../services';
import { Roles } from '~/@core/decorator/roles.decorator';
import { RoleGuard } from '~/@systems/guard/role.guard';

@DefController('assign-shifts')
export class AssignShiftController {
  constructor(private readonly assignShiftService: AssignShiftService) {}

  @DefPost('create')
  @Roles('/system-configuration/assign-shift', 'View')
  @UseGuards(RoleGuard)
  async create(@Body() body: AssignShiftDto): Promise<AssignShiftEntity> {
    return await this.assignShiftService.create(body);
  }

  @DefPost('find-all')
  @Roles('/system-configuration/assign-shift', 'View')
  @UseGuards(RoleGuard)
  async findAll(): Promise<AssignShiftEntity[]> {
    return await this.assignShiftService.findAll();
  }

  @DefPost('update')
  @Roles('/system-configuration/assign-shift', 'View')
  @UseGuards(RoleGuard)
  async update(@Body() updateData: AssignShiftEditDto): Promise<AssignShiftEntity> {
    return await this.assignShiftService.update(updateData);
  }

  @DefPost('delete')
  @Roles('/system-configuration/assign-shift', 'View')
  @UseGuards(RoleGuard)
  async delete(@Body('id') id: number): Promise<void> {
    return await this.assignShiftService.delete(id);
  }

  @DefPost('get-select-box-organization-unit')
  @Roles('/system-configuration/assign-shift', 'View')
  @UseGuards(RoleGuard)
  async getSelectBoxOrganizationUnit(): Promise<any> {
    return await this.assignShiftService.getSelectBoxOrganizationUnit();
  }
  @DefPost('get-select-box-shift')
  @Roles('/system-configuration/assign-shift', 'View')
  @UseGuards(RoleGuard)
  async getSelectBoxShift(): Promise<ShiftEntity[]> {
    return await this.assignShiftService.getSelectBoxShift();
  }

  @ApiOperation({ summary: 'Phân trang danh sách nhóm ca' })
  @DefPost('pagination')
  @Roles('/report/oee-dashboard', 'View')
  @Roles('/system-configuration/assign-shift', 'View')
  @UseGuards(RoleGuard)
  async pagination(@Body() params: AssignShiftPaginationDto) {
    return await this.assignShiftService.pagination(params);
  }
}
