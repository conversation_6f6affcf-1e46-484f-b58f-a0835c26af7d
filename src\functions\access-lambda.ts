import { NestFactory } from '@nestjs/core';
import { APIGatewayProxyEvent, APIGatewayProxyResult, Context, Handler } from 'aws-lambda';
import { setupTransactionContext } from '~/@core/decorator';
import { AppModule } from '~/app.module';
import { CustomError, ICustomError, LambdaError } from '~/common/constants/Error.constant';
import { AccessService, UserService } from '~/x-modules/admin/services';

let cachedApp: any = null;

async function bootstrap() {
  if (!cachedApp) {
    const app = await NestFactory.createApplicationContext(AppModule);
    setupTransactionContext();
    await app.init();
    cachedApp = app;
  }
  return cachedApp;
}

const isUserExist = async (employeeID: string): Promise<boolean> => {
  const app = await bootstrap();
  const userService = app.get(UserService) as UserService;
  const result = await userService.getUser(employeeID);
  return !!result;
};

export const getOrganizationList: Handler = async event => {
  const app = await bootstrap();
  try {
    const accessService = app.get(AccessService);
    const {
      group,
      site,
      factory,
      line,
      process,
      page = '1',
      pageSize = '10',
    } = event.queryStringParameters || {};

    const result = await accessService.getOrganizationList({
      group,
      site,
      factory,
      line,
      process,
      page: parseInt(page),
      pageSize: parseInt(pageSize),
    });

    return {
      statusCode: 200,
      body: JSON.stringify(result),
    };
  } catch (error) {
    return {
      statusCode: 500,
      body: JSON.stringify({ message: error.message }),
    };
  }
};

export const updateAccess: Handler = async event => {
  const app = await bootstrap();
  try {
    const accessService = app.get(AccessService);
    const userId = event.pathParameters?.userId;
    const body = JSON.parse(event.body || '{}');
    const { addedIds = [], removedIds = [] } = body;

    const result = await accessService.updateAccess(userId!, addedIds, removedIds);

    return {
      statusCode: 200,
      body: JSON.stringify(result),
    };
  } catch (error) {
    return {
      statusCode: 500,
      body: JSON.stringify({ message: error.message }),
    };
  }
};

export const getUserAccess: Handler = async event => {
  const app = await bootstrap();
  try {
    const accessService = app.get(AccessService);
    const userId = event.pathParameters?.userId;

    const result = await accessService.getUserAccess(userId!);

    return {
      statusCode: 200,
      body: JSON.stringify(result),
    };
  } catch (error) {
    return {
      statusCode: 500,
      body: JSON.stringify({ message: error.message }),
    };
  }
};

export const getUserAccessList: Handler = async (
  event: APIGatewayProxyEvent,
  context: Context,
): Promise<APIGatewayProxyResult> => {
  return handler(async () => {
    const app = await bootstrap();
    const accessService = app.get(AccessService) as AccessService;
    const { employeeID } = event.pathParameters;

    const isEmployeeExist = await isUserExist(employeeID);
    if (!isEmployeeExist) {
      throw LambdaError.NOT_FOUND();
    }

    const userAccessList = await accessService.getUserAccessList(employeeID);

    return {
      data: userAccessList,
      total: userAccessList.length,
    };
  });
};

type UpdateUserAccessListRequestBody = {
  organizationUnitIDs: string[];
};
export const updateUserAccessList: Handler = async (
  event: APIGatewayProxyEvent,
  context: Context,
): Promise<APIGatewayProxyResult> => {
  return handler(async () => {
    const app = await bootstrap();
    const userService = app.get(UserService) as UserService;
    const accessService = app.get(AccessService) as AccessService;
    const { employeeID } = event.pathParameters;
    const { organizationUnitIDs } = JSON.parse(event.body) as UpdateUserAccessListRequestBody;

    // Kiểm tra nhân viên có tồn tại hay không
    const user = await userService.getUser(employeeID);
    if (!user) {
      throw LambdaError.NOT_FOUND();
    }

    // Kiểm tra các organization unit có tồn tại không
    const organizationUnitsInDB =
      await accessService.getOrganizationUnitsByIDs(organizationUnitIDs);
    if (organizationUnitsInDB.length !== organizationUnitIDs.length) {
      throw LambdaError.NOT_FOUND();
    }

    // Lưu access
    await accessService.updateOrganizationAccess(user, organizationUnitsInDB);

    return {
      message: 'success',
    };
  });
};

const handler = async <T>(func: () => Promise<T>): Promise<APIGatewayProxyResult> => {
  try {
    const result = await func();

    return {
      statusCode: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Credentials': true,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(result),
    };
  } catch (error) {
    if (error instanceof CustomError) {
      return handleError(error);
    } else {
      return handleError(LambdaError.INTERNAL_ERROR());
    }
  }
};

const handleError = (error: ICustomError): APIGatewayProxyResult => {
  return {
    statusCode: 400,
    body: JSON.stringify(error),
  };
};
