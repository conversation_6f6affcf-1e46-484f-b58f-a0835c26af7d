// export const USD_DECIMALS = 30;
const formatAmountToken = ({
  stringNumber,
  tokenDecimals,
}: {
  stringNumber: string;
  tokenDecimals: number;
}) => {
  const numberValue = BigInt(stringNumber);
  const divisor = BigInt('1'.padEnd(tokenDecimals + 1, '0'));
  const scaledNumber = Number(numberValue / divisor);
  return scaledNumber;
  // const formattedNumber = scaledNumber.toLocaleString('en-US', { minimumFractionDigits: 2, maximumFractionDigits: 2 });
};

const checkNaN = (value: number) => {
  if (isNaN(value)) {
    return 0;
  } else {
    return value; // Hoặc giá trị khác tùy vào yêu cầu
  }
};

// format number string to number
function parseFormattedNumber(input: string | number): number {
  if (typeof input === 'string') {
    input = input.replace(/,/g, '');
  }
  const num = Number(input);
  return isNaN(num) ? 0 : num;
}

function formatNumber(input: string | number | null | undefined, decimal?: number): string {
  if (input === null || input === undefined || input === '') {
    return '0';
  }

  const num = Number(input);
  if (!Number.isFinite(num)) {
    return '0';
  }

  // Tách phần nguyên và phần thập phân dựa trên số tuyệt đối
  const [integerPart, decimalPart] = Math.abs(num).toString().split('.');

  // Format phần nguyên
  const formattedInteger = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, ',');

  // Thêm dấu âm nếu cần
  const signPrefix = num < 0 ? '-' : '';

  // Format phần thập phân nếu có
  return decimalPart
    ? `${signPrefix}${formattedInteger}.${decimalPart.slice(0, decimal ?? 2)}`
    : `${signPrefix}${formattedInteger}`;
}

function stringToNumber(str: string): number | null {
  // Loại bỏ dấu phẩy và chuyển đổi sang số
  const cleanedStr = str.replace(/,/g, '');

  // Kiểm tra xem chuỗi đã làm sạch có phải là một số hợp lệ hay không
  if (!isNaN(Number(cleanedStr))) {
    return Number(cleanedStr);
  }

  return null; // Trả về null nếu không phải là một số hợp lệ
}

export const numberHelper = {
  formatAmountToken,
  checkNaN,
  formatNumber,
  stringToNumber,
  parseFormattedNumber,
};

// {
//     "assetId": "4a2e68a4-d5fa-46d0-88f8-674ddb553973",
//     "value": 1164580.13457889014567890257
// },
// {
//     "assetId": "4a2e68a4-d5fa-46d0-88f8-674ddb553973",
//     "propertyId": "010e2d43-33db-4bc0-8a4b-e13c9bacedd3",
//     "value": 1111220
// },
// {
//     "assetId": "4a2e68a4-d5fa-46d0-88f8-674ddb553973",
//     "propertyId": "eca41ec0-2f86-4e3d-bc69-5f009eb9d580",
//     "value": 53360
// },
// {
//     "assetId": "4a2e68a4-d5fa-46d0-88f8-674ddb553973",
//     "propertyId": "c4865aea-1227-45b7-a625-93be9bee1433",
//     "value": 235694.40625
// },
