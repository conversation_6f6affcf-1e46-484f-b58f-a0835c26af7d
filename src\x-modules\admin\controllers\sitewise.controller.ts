import { Body, Param, Query, UseGuards } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { DefController, DefDelete, DefGet, DefPatch, DefPost, DefPut } from '~/@core/decorator';
import { UUIDReq } from '~/dto/common.dto';
import { SitewiseLibService } from '../services';
import {
  AssetPropertyValueReq,
  MonitorLineDashboardByIdReq,
  CalculateOEEReq,
  getOeeChartReq,
  getOeeDetailsReq,
} from '~/dto/sitewise.dto';
import { Roles } from '~/@core/decorator/roles.decorator';
import { RoleGuard } from '~/@systems/guard/role.guard';
import { AssignShiftPaginationDto } from '~/dto';

@ApiTags('sitewise')
@DefController('sitewise')
export class SitewiseController {
  constructor(private readonly sitewiseLibService: SitewiseLibService) {}

  @DefPost('sync-data')
  async syncDataToDb(@Body() data: UUIDReq) {
    return await this.sitewiseLibService.syncDataToDb(data.id);
  }

  @DefPost('get-asset-property-value')
  async getAssetPropertyValue(@Body() data: AssetPropertyValueReq) {
    return await this.sitewiseLibService.getAssetPropertyValue(data.assetId, data.propertyId);
  }

  @DefPost('monitor-line')
  @Roles('/report/line-dashboard', 'View')
  @UseGuards(RoleGuard)
  async monitorLineDashboard(@Body() data: MonitorLineDashboardByIdReq) {
    return await this.sitewiseLibService.monitorLineDashboard(data);
  }

  @DefPost('calculate-oee')
  @Roles('/report/line-dashboard', 'View')
  @UseGuards(RoleGuard)
  async calculateOEE(@Body() data: CalculateOEEReq) {
    return await this.sitewiseLibService.calculateOverallOEE(data.shiftId, data.orgUnitLineId);
  }

  @DefGet('get-oee-chart')
  @Roles('/report/oee-dashboard', 'View')
  @UseGuards(RoleGuard)
  async getOeeForChart(@Query() params: getOeeChartReq) {
    return this.sitewiseLibService.getOeeForChart(params);
  }
  @DefGet('get-oee-details')
  @Roles('/report/oee-dashboard', 'View')
  @UseGuards(RoleGuard)
  async getOeeForDetails(@Query() params: getOeeDetailsReq) {
    return this.sitewiseLibService.getOeeForDetails(params);
  }

  @DefGet('get-options/:userId')
  @Roles('/report/oee-dashboard', 'View')
  @UseGuards(RoleGuard)
  async getOptions(@Param('userId') id: string, @Query('levelCode') levelCode: string) {
    return this.sitewiseLibService.getOptions(levelCode, id);
  }

  @DefGet('load-shift')
  @Roles('/report/oee-dashboard', 'View')
  @UseGuards(RoleGuard)
  async loadShift() {
    return await this.sitewiseLibService.loadShift();
  }

  @DefGet('by-code/:code')
  @Roles('/report/oee-dashboard', 'View')
  @UseGuards(RoleGuard)
  async findByCode(@Param('code') code: string) {
    return this.sitewiseLibService.findByCode(code);
  }

  @DefPost('calculate-oee-all')
  @Roles('/report/oee-dashboard', 'View')
  @UseGuards(RoleGuard)
  async calculateOEEAll() {
    return await this.sitewiseLibService.calculateOverallOEEAll();
  }

  @DefPost('test')
  async test(@Body() data: CalculateOEEReq) {
    return await this.sitewiseLibService.test(data.orgUnitLineId);
  }
}
