export const RolePermission = {
  SystemConfiguration: {
    id: 1,
    name: 'System Configuration',
    code: 'SystemConfiguration',
    children: [
      {
        code: 'GeneralData',
        name: 'General Data',
        path: '/system-configuration/general-data',
        View: {
          name: 'Xem',
          code: 'View',
          value: false,
          path: '/system-configuration/general-data',
        },
        Edit: {
          name: 'Chỉnh sửa',
          code: 'Edit',
          value: false,
          path: '/system-configuration/general-data',
        },
        Create: {
          name: 'Thêm mới',
          code: 'Create',
          value: false,
          path: '/system-configuration/general-data',
        },
      },
      {
        code: 'Shift',
        name: 'Shift',
        path: '/system-configuration/shift',
        View: { name: 'Xem', code: 'View', value: false, path: '/system-configuration/shift' },
        Edit: {
          name: 'Chỉnh sửa',
          code: 'Edit',
          value: false,
          path: '/system-configuration/shift',
        },
        Create: {
          name: 'Thêm mới',
          code: 'Create',
          value: false,
          path: '/system-configuration/shift',
        },
      },
      {
        code: 'AssignShift',
        name: 'Assign Shift',
        path: '/system-configuration/assign-shift',
        View: {
          name: 'Xem',
          code: 'View',
          value: false,
          path: '/system-configuration/assign-shift',
        },
        Edit: {
          name: 'Chỉnh sửa',
          code: 'Edit',
          value: false,
          path: '/system-configuration/assign-shift',
        },
        Create: {
          name: 'Thêm mới',
          code: 'Create',
          value: false,
          path: '/system-configuration/assign-shift',
        },
      },
      {
        code: 'Holidays',
        path: '/system-configuration/holidays',
        name: 'Holidays',
        View: {
          name: 'Xem',
          code: 'View',
          value: false,
          path: '/system-configuration/holidays',
        },
        Edit: {
          name: 'Chỉnh sửa',
          code: 'Edit',
          value: false,
          path: '/system-configuration/holidays',
        },
        Create: {
          name: 'Thêm mới',
          code: 'Create',
          value: false,
          path: '/system-configuration/holidays',
        },
      },
      {
        code: 'OrganizationUnit',
        path: '/system-configuration/organization-unit',
        name: 'Organization Unit',
        View: {
          name: 'Xem',
          code: 'View',
          value: false,
          path: '/system-configuration/organization-unit',
        },
        Edit: {
          name: 'Chỉnh sửa',
          code: 'Edit',
          value: false,
          path: '/system-configuration/organization-unit',
        },
        Create: {
          name: 'Thêm mới',
          code: 'Create',
          value: false,
          path: '/system-configuration/organization-unit',
        },
      },
      {
        code: 'ProductionArena',
        name: 'Production Arena',
        path: '/system-configuration/production-area',
        View: {
          name: 'Xem',
          code: 'View',
          value: false,
          path: '/system-configuration/production-area',
        },
        Edit: {
          name: 'Chỉnh sửa',
          code: 'Edit',
          value: false,
          path: '/system-configuration/production-area',
        },
        Create: {
          name: 'Thêm mới',
          code: 'Create',
          value: false,
          path: '/system-configuration/production-area',
        },
      },
      {
        code: 'User',
        name: 'User',
        path: '/system-configuration/user',
        View: { name: 'Xem', code: 'View', value: false, path: '/system-configuration/user' },
        Edit: {
          name: 'Chỉnh sửa',
          code: 'Edit',
          value: false,
          path: '/system-configuration/user',
        },
        Create: {
          name: 'Thêm mới',
          code: 'Create',
          value: false,
          path: '/system-configuration/user',
        },
      },
      {
        code: 'Access',
        name: 'Access',
        path: '/system-configuration/access',
        View: {
          name: 'Xem',
          code: 'View',
          value: false,
          path: '/system-configuration/access',
        },
        Edit: {
          name: 'Chỉnh sửa',
          code: 'Edit',
          value: false,
          path: '/system-configuration/access',
        },
        Create: {
          name: 'Thêm mới',
          code: 'Create',
          value: false,
          path: '/system-configuration/access',
        },
      },
      {
        code: 'UserGroup',
        name: 'User Group',
        path: '/system-configuration/user-group',
        View: {
          name: 'Xem',
          code: 'View',
          value: false,
          path: '/system-configuration/user-group',
        },
        Edit: {
          name: 'Chỉnh sửa',
          code: 'Edit',
          value: false,
          path: '/system-configuration/user-group',
        },
        Create: {
          name: 'Thêm mới',
          code: 'Create',
          value: false,
          path: '/system-configuration/user-group',
        },
      },
      {
        code: 'UtilityMeters',
        name: 'Utility Meters',
        path: '/system-configuration/utility-meters',
        View: {
          name: 'Xem',
          code: 'View',
          value: false,
          path: '/system-configuration/utility-meters',
        },
        Edit: {
          name: 'Chỉnh sửa',
          code: 'Edit',
          value: false,
          path: '/system-configuration/utility-meters',
        },
        Create: {
          name: 'Thêm mới',
          code: 'Create',
          value: false,
          path: '/system-configuration/utility-meters',
        },
      },
      {
        code: 'Process',
        name: 'Process',
        path: '/system-configuration/process',
        View: {
          name: 'Xem',
          code: 'View',
          value: false,
          path: '/system-configuration/process',
        },
        Edit: {
          name: 'Chỉnh sửa',
          code: 'Edit',
          value: false,
          path: '/system-configuration/process',
        },
        Create: {
          name: 'Thêm mới',
          code: 'Create',
          value: false,
          path: '/system-configuration/process',
        },
      },
      {
        code: 'Machines',
        name: 'Machines',
        path: '/system-configuration/machines',
        View: {
          name: 'Xem',
          code: 'View',
          value: false,
          path: '/system-configuration/machines',
        },
        Edit: {
          name: 'Chỉnh sửa',
          code: 'Edit',
          value: false,
          path: '/system-configuration/machines',
        },
        Create: {
          name: 'Thêm mới',
          code: 'Create',
          value: false,
          path: '/system-configuration/machines',
        },
      },
      {
        code: 'KPIPeriod',
        name: 'KPI Period',
        path: '/system-configuration/kpi-period',
        View: {
          name: 'Xem',
          code: 'View',
          value: false,
          path: '/system-configuration/kpi-period',
        },
      },
      {
        code: 'LockKPIPeriod',
        name: 'Lock KPI Period',
        path: '/system-configuration/lock-kpi-period',
        View: {
          name: 'Xem',
          code: 'View',
          value: false,
          path: '/system-configuration/lock-kpi-period',
        },
        Edit: {
          name: 'Chỉnh sửa',
          code: 'Edit',
          value: false,
          path: '/system-configuration/lock-kpi-period',
        },
        Create: {
          name: 'Thêm mới',
          code: 'Create',
          value: false,
          path: '/system-configuration/lock-kpi-period',
        },
      },
      {
        code: 'KPIPeriodRule',
        name: 'KPI Period Rule',
        path: '/system-configuration/kpi-period-rule',
        View: {
          name: 'Xem',
          code: 'View',
          value: false,
          path: '/system-configuration/kpi-period-rule',
        },
        Edit: {
          name: 'Chỉnh sửa',
          code: 'Edit',
          value: false,
          path: '/system-configuration/kpi-period-rule',
        },
        Create: {
          name: 'Thêm mới',
          code: 'Create',
          value: false,
          path: '/system-configuration/kpi-period-rule',
        },
      },
      {
        code: 'Permission',
        name: 'Permission',
        path: '/system-configuration/permission',
        View: {
          name: 'Xem',
          code: 'View',
          value: false,
          path: '/system-configuration/permission',
        },
        Edit: {
          name: 'Chỉnh sửa',
          code: 'Edit',
          value: false,
          path: '/system-configuration/permission',
        },
        Create: {
          name: 'Thêm mới',
          code: 'Create',
          value: false,
          path: '/system-configuration/permission',
        },
      },
      {
        code: 'LogScheduling',
        name: 'Log Scheduling',
        path: '/system-configuration/log-scheduling',
        View: {
          name: 'Xem',
          code: 'View',
          value: false,
          path: '/system-configuration/log-scheduling',
        },
        Edit: {
          name: 'Chỉnh sửa',
          code: 'Edit',
          value: false,
          path: '/system-configuration/log-scheduling',
        },
      },
      {
        code: 'ApiToken',
        name: 'ApiToken',
        path: '/system-configuration/api-token',
        View: {
          name: 'Xem',
          code: 'View',
          value: false,
          path: '/system-configuration/api-token',
        },
        Edit: {
          name: 'Chỉnh sửa',
          code: 'Edit',
          value: false,
          path: '/system-configuration/api-token',
        },
        Create: {
          name: 'Thêm mới',
          code: 'Create',
          value: false,
          path: '/system-configuration/api-token',
        },
      },
      {
        code: 'Station',
        name: 'Station',
        path: '/system-configuration/station',
        View: {
          name: 'Xem',
          code: 'View',
          value: false,
          path: '/system-configuration/station',
        },
        Edit: {
          name: 'Chỉnh sửa',
          code: 'Edit',
          value: false,
          path: '/system-configuration/station',
        },
        Create: {
          name: 'Thêm mới',
          code: 'Create',
          value: false,
          path: '/system-configuration/station',
        },
      },
      {
        code: 'Device',
        name: 'Device',
        path: '/system-configuration/device',
        View: {
          name: 'Xem',
          code: 'View',
          value: false,
          path: '/system-configuration/device',
        },
        Edit: {
          name: 'Chỉnh sửa',
          code: 'Edit',
          value: false,
          path: '/system-configuration/device',
        },
        Create: {
          name: 'Thêm mới',
          code: 'Create',
          value: false,
          path: '/system-configuration/device',
        },
      },
    ],
  },
  MasterData: {
    id: 2,
    name: 'Master Data',
    code: 'MasterData',
    children: [
      {
        code: 'ReasonsMaster',
        name: 'Reasons Master',
        path: '/master-data/reasons-master',
        View: {
          name: 'Xem',
          code: 'View',
          value: false,
          path: '/master-data/reasons-master',
        },
        Edit: {
          name: 'Chỉnh sửa',
          code: 'Edit',
          value: false,
          path: '/master-data/reasons-master',
        },
        Create: {
          name: 'Thêm mới',
          code: 'Create',
          value: false,
          path: '/master-data/reasons-master',
        },
      },
      {
        code: 'InspectionPlan',
        name: 'Inspection Plan',
        path: '/master-data/inspection-plan',
        View: {
          name: 'Xem',
          code: 'View',
          value: false,
          path: '/master-data/inspection-plan',
        },
        Edit: {
          name: 'Chỉnh sửa',
          code: 'Edit',
          value: false,
          path: '/master-data/inspection-plan',
        },
        Create: {
          name: 'Thêm mới',
          code: 'Create',
          value: false,
          path: '/master-data/inspection-plan',
        },
      },
      {
        code: 'StaffPerformanceKPI',
        name: 'Staff Performance KPI',
        path: '/master-data/kpi-list',
        View: {
          name: 'Xem',
          code: 'View',
          value: false,
          path: '/master-data/kpi-list',
        },
        Edit: {
          name: 'Chỉnh sửa',
          code: 'Edit',
          value: false,
          path: '/master-data/kpi-list',
        },
        Create: {
          name: 'Thêm mới',
          code: 'Create',
          value: false,
          path: '/master-data/kpi-list',
        },
      },
      {
        code: 'KPISet',
        name: 'KPI Set',
        path: '/master-data/kpi-set',
        View: { name: 'Xem', code: 'View', value: false, path: '/master-data/kpi-set' },
        Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/master-data/kpi-set' },
        Create: {
          name: 'Thêm mới',
          code: 'Create',
          value: false,
          path: '/master-data/kpi-set',
        },
      },
      {
        code: 'KPITargets',
        name: 'KPI Targets',
        path: '/master-data/kpi-standard',
        View: {
          name: 'Xem',
          code: 'View',
          value: false,
          path: '/master-data/kpi-standard',
        },
        Edit: {
          name: 'Chỉnh sửa',
          code: 'Edit',
          value: false,
          path: '/master-data/kpi-standard',
        },
        Create: {
          name: 'Thêm mới',
          code: 'Create',
          value: false,
          path: '/master-data/kpi-standard',
        },
      },
      {
        code: 'MasterItem',
        name: 'Master Item',
        path: '/master-data/items',
        View: { name: 'Xem', code: 'View', value: false, path: '/master-data/items' },
        Edit: {
          name: 'Chỉnh sửa',
          code: 'Edit',
          value: false,
          path: '/master-data/items',
        },
        Create: {
          name: 'Thêm mới',
          code: 'Create',
          value: false,
          path: '/master-data/items',
        },
      },
      {
        code: 'MaterialGroup',
        name: 'Material Group',
        path: '/master-data/material-group',
        View: { name: 'Xem', code: 'View', value: false, path: '/master-data/material-group' },
        Edit: {
          name: 'Chỉnh sửa',
          code: 'Edit',
          value: false,
          path: '/master-data/material-group',
        },
        Create: {
          name: 'Thêm mới',
          code: 'Create',
          value: false,
          path: '/master-data/material-group',
        },
      },
      {
        code: 'WeighingTare',
        name: 'Weighing Tare',
        path: '/master-data/weighing-tare',
        View: { name: 'Xem', code: 'View', value: false, path: '/master-data/weighing-tare' },
        Edit: {
          name: 'Chỉnh sửa',
          code: 'Edit',
          value: false,
          path: '/master-data/weighing-tare',
        },
        Create: {
          name: 'Thêm mới',
          code: 'Create',
          value: false,
          path: '/master-data/weighing-tare',
        },
      },
    ],
  },
  Recipe: {
    id: 3,
    name: 'Recipe',
    code: 'Recipe',
    children: [
      {
        code: 'Recipe',
        name: 'Recipe',
        path: '/recipe/recipes',
        View: { name: 'Xem', code: 'View', value: false, path: '/recipe/recipes' },
      },
      {
        code: 'SetPoint',
        name: 'Set Point',
        path: '/recipe/set-point',
        View: { name: 'Xem', code: 'View', value: false, path: '/recipe/set-point' },
        Edit: { name: 'Chỉnh sửa', code: 'Edit', value: false, path: '/recipe/set-point' },
        Create: {
          name: 'Thêm mới',
          code: 'Create',
          value: false,
          path: '/recipe/set-point',
        },
      },
    ],
  },
  ProductionOrder: {
    id: 4,
    name: 'Production Order',
    code: 'ProductionOrder',
    children: [
      {
        code: 'ProductionOrder',
        name: 'Production Order',
        path: '/production-order',
        View: { name: 'Xem', code: 'View', value: false, path: '/production-order' },
        Edit: {
          name: 'Chỉnh sửa',
          code: 'Edit',
          value: false,
          path: '/production-order',
        },
        Create: {
          name: 'Thêm mới',
          code: 'Create',
          value: false,
          path: '/production-order',
        },
      },
    ],
  },
  ProductionExecution: {
    id: 5,
    name: 'Production Execution',
    code: 'ProductionExecution',
    children: [
      {
        code: 'DashBoard',
        name: 'DashBoard',
        path: '/production-execution/dashboard',
        View: {
          name: 'Xem',
          code: 'View',
          value: false,
          path: '/production-execution/dashboard',
        },
      },

      {
        code: 'KPIEvaluation',
        name: 'KPI Evaluation',
        path: '/production-execution/kpi-score',
        View: {
          name: 'Xem',
          code: 'View',
          value: false,
          path: '/production-execution/kpi-score',
        },
        Edit: {
          name: 'Chỉnh sửa',
          code: 'Edit',
          value: false,
          path: '/production-execution/kpi-score',
        },
        Create: {
          name: 'Thêm mới',
          code: 'Create',
          value: false,
          path: '/production-execution/kpi-score',
        },
      },
      {
        code: 'TimeSheet',
        name: 'Time Sheet',
        path: '/production-execution/timesheet-records',
        View: {
          name: 'Xem',
          code: 'View',
          value: false,
          path: '/production-execution/timesheet-records',
        },
        Edit: {
          name: 'Chỉnh sửa',
          code: 'Edit',
          value: false,
          path: '/production-execution/timesheet-records',
        },
        Create: {
          name: 'Thêm mới',
          code: 'Create',
          value: false,
          path: '/production-execution/timesheet-records',
        },
      },
      {
        code: 'RejectionMontitoring',
        name: 'Rejection Montitoring',
        path: '/production-execution/rejection-monitoring',
        View: {
          name: 'Xem',
          code: 'View',
          value: false,
          path: '/production-execution/rejection-monitoring',
        },
        Edit: {
          name: 'Chỉnh sửa',
          code: 'Edit',
          value: false,
          path: '/production-execution/rejection-monitoring',
        },
        Create: {
          name: 'Thêm mới',
          code: 'Create',
          value: false,
          path: '/production-execution/rejection-monitoring',
        },
      },
      {
        code: 'UtilityMontitoring',
        name: 'Utility Montitoring',
        path: '/production-execution/utility-transaction',
        View: {
          name: 'Xem',
          code: 'View',
          value: false,
          path: '/production-execution/utility-transaction',
        },
        Create: {
          name: 'Thêm mới',
          code: 'Create',
          value: false,
          path: '/production-execution/utility-transaction',
        },
      },
      {
        code: 'Inventory',
        name: 'Inventory',
        path: '/production-execution/inventory',
        View: {
          name: 'Xem',
          code: 'View',
          value: false,
          path: '/production-execution/inventory',
        },
      },
      {
        code: 'ManhourMontitoring',
        name: 'Manhour Montitoring',
        path: '/production-execution/monitor-manhour',
        View: {
          name: 'Xem',
          code: 'View',
          value: false,
          path: '/production-execution/monitor-manhour',
        },
        Create: {
          name: 'Thêm mới',
          code: 'Create',
          value: false,
          path: '/production-execution/monitor-manhour',
        },
      },
      {
        code: 'DowntimeMontitoring',
        name: 'Downtime Montitoring',
        path: '/production-execution/downtime-monitoring',
        View: {
          name: 'Xem',
          code: 'View',
          value: false,
          path: '/production-execution/downtime-monitoring',
        },
        Edit: {
          name: 'Chỉnh sửa',
          code: 'Edit',
          value: false,
          path: '/production-execution/downtime-monitoring',
        },
        Create: {
          name: 'Thêm mới',
          code: 'Create',
          value: false,
          path: '/production-execution/downtime-monitoring',
        },
      },
      {
        code: 'QualityNotification',
        name: 'Quality Notification',
        path: '/production-execution/quality-notification',
        View: {
          name: 'Xem',
          code: 'View',
          value: false,
          path: '/production-execution/quality-notification',
        },
      },
      {
        code: 'WeighingScreen',
        name: 'Weighing Screen',
        path: '/production-execution/weighing-screen',
        View: {
          name: 'Xem',
          code: 'View',
          value: false,
          path: '/production-execution/weighing-screen',
        },
        Edit: {
          name: 'Chỉnh sửa',
          code: 'Edit',
          value: false,
          path: '/production-execution/weighing-screen',
        },
      },
      {
        code: 'WeighingScan',
        name: 'Weighing Scan',
        path: '/production-execution/weighing-scan',
        View: {
          name: 'Xem',
          code: 'View',
          value: false,
          path: '/production-execution/weighing-scan',
        },
        Edit: {
          name: 'Chỉnh sửa',
          code: 'Edit',
          value: false,
          path: '/production-execution/weighing-scan',
        },
      },
    ],
  },
  Report: {
    id: 6,
    name: 'Report & Dashboard',
    code: 'Report',
    children: [
      {
        code: 'OEEDashboard',
        name: 'OEE Dashboard',
        path: '/report/oee-dashboard',
        View: {
          name: 'Xem',
          code: 'View',
          value: false,
          path: '/report/oee-dashboard',
        },
      },
      {
        code: 'LineDashboard',
        name: 'Line Dashboard',
        path: '/report/line-dashboard',
        View: {
          name: 'Xem',
          code: 'View',
          value: false,
          path: '/report/line-dashboard',
        },
      },
      {
        code: 'ProductionOrderMaterialReport',
        name: 'Production Order Material Report',
        path: '/report/production-order-material-report',
        View: {
          name: 'Xem',
          code: 'View',
          value: false,
          path: '/report/production-order-material-report',
        },
        Edit: {
          name: 'Chỉnh sửa',
          code: 'Edit',
          value: false,
          path: '/report/production-order-material-report',
        },
        Create: {
          name: 'Thêm mới',
          code: 'Create',
          value: false,
          path: '/report/production-order-material-report',
        },
      },
      {
        code: 'ProductionOrderResourceReport',
        name: 'Production Order Resource Report',
        path: '/report/production-order-resource-report',
        View: {
          name: 'Xem',
          code: 'View',
          value: false,
          path: '/report/production-order-resource-report',
        },
        Edit: {
          name: 'Chỉnh sửa',
          code: 'Edit',
          value: false,
          path: '/report/production-order-resource-report',
        },
        Create: {
          name: 'Thêm mới',
          code: 'Create',
          value: false,
          path: '/report/production-order-resource-report',
        },
      },
      {
        code: 'ProductionBatchAndProcessParameterReport',
        name: 'Production Batch And Process Parameter Report',
        path: '/report/production-batch-and-process-parameter-report',
        View: {
          name: 'Xem',
          code: 'View',
          value: false,
          path: '/report/production-batch-and-process-parameter-report',
        },
        Edit: {
          name: 'Chỉnh sửa',
          code: 'Edit',
          value: false,
          path: '/report/production-batch-and-process-parameter-report',
        },
        Create: {
          name: 'Thêm mới',
          code: 'Create',
          value: false,
          path: '/report/production-batch-and-process-parameter-report',
        },
      },
      {
        code: 'PQCDSDashboard',
        name: 'PQCDS Dashboard',
        path: '/report/pqcds-dashboard',
        View: {
          name: 'Xem',
          code: 'View',
          value: false,
          path: '/report/pqcds-dashboard',
        },
      },
      {
        code: 'CZCodeShipmentReport',
        name: 'CZ Code Shipment Report',
        path: '/report/cz-code-shipment-report',
        View: {
          name: 'Xem',
          code: 'View',
          value: false,
          path: '/report/cz-code-shipment-report',
        },
        Create: {
          name: 'Thêm mới',
          code: 'Create',
          value: false,
          path: '/report/cz-code-shipment-report',
        },
      },
    ],
  },

  KpiAward: {
    id: 8,
    name: 'Kpi Award',
    code: 'KpiAward',
    children: [
      {
        code: 'KpiAward',
        name: 'Kpi Award',
        path: '/kpi-award',
        View: { name: 'Xem', code: 'View', value: false, path: '/kpi-award' },
      },
    ],
  },
  ScanCz: {
    id: 9,
    name: 'Scan Cz',
    code: 'ScanCz',
    children: [
      {
        code: 'ScanCheck',
        name: 'Scan Check',
        path: '/scan-cz/scan-check',
        View: { name: 'Xem', code: 'View', value: false, path: '/scan-cz/scan-check' },
        Edit: {
          name: 'Chỉnh sửa',
          code: 'Edit',
          value: false,
          path: '/scan-cz/scan-check',
        },
      },
      {
        code: 'ScanActive',
        name: 'Scan Active',
        path: '/scan-cz/scan-active',
        View: { name: 'Xem', code: 'View', value: false, path: '/scan-cz/scan-active' },
        Edit: {
          name: 'Chỉnh sửa',
          code: 'Edit',
          value: false,
          path: '/scan-cz/scan-active',
        },
      },
      {
        code: 'ScanReject',
        name: 'Scan Reject',
        path: '/scan-cz/scan-reject',
        View: { name: 'Xem', code: 'View', value: false, path: '/scan-cz/scan-reject' },
        Edit: {
          name: 'Chỉnh sửa',
          code: 'Edit',
          value: false,
          path: '/scan-cz/scan-reject',
        },
      },
    ],
  },
};

export enum Role {
  Admin = 'admin',
  ProductionManager = 'production_manager',
  Operator = 'operator',
  ITSupport = 'it_support',
  DataAnalyst = 'data_analyst',
  QualityControl = 'quality_control',
  IntegrationEngineer = 'integration_engineer',
  SecurityOfficer = 'security_officer',
  EndUser = 'end_user',
}
