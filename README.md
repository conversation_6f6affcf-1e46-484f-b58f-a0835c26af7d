# Manufacturing Execution System (MES)

## 📋 Giới thiệu

Hệ thống MES (Manufacturing Execution System) được xây dựng trên nền tảng NestJS, tích hợp với các hệ thống SCADA và có khả năng xử lý sinh trắc học.

## 🏗️ Kiến trúc hệ thống

- **Backend Framework**: NestJS + TypeScript
- **Database**: PostgreSQL với TypeORM
- **Message Queue**: Amazon SQS
- **Caching**: Redis với socket.io adapter
- **Authentication**: JWT
- **Integration**: SCADA systems, Azure Event Hubs

## 📁 Cấu trúc thư mục

### 🗂️ Thư mục gốc

```
├── src/                    # Source code chính của ứng dụng
├── dist/                   # Code đã được build/compile
├── scripts/               # Scripts tiện ích và tự động hóa
├── ZKCropServer/          # Server xử lý ảnh crop (face recognition/fingerprint)
├── .vscode/               # Cấu hình Visual Studio Code
├── .github/               # GitHub Actions và workflows
└── .yarn/                 # Cấu hình Yarn package manager
```

### 🗂️ Thư mục src/ (Source Code chính)

#### **Core System (@-prefixed directories)**

```
├── @core/                 # Các module cốt lõi của hệ thống
│   ├── decorator/         # Custom decorators
│   ├── network/           # Xử lý network và HTTP
│   ├── utils/             # Tiện ích chung
│   ├── helpers/           # Các helper functions
│   └── context/           # Context management
│
├── @systems/              # Các hệ thống con
│   ├── guard/             # Authentication guards
│   ├── exceptions/        # Xử lý exception
│   ├── utils/             # System utilities
│   ├── middlewares/       # HTTP middlewares
│   └── pipe/              # Data transformation pipes
│
└── @config/               # Cấu hình hệ thống
    └── env.ts             # Environment variables
```

#### **Business Logic**

```
└── x-modules/             # Các module nghiệp vụ mở rộng
    ├── integration/       # Tích hợp với hệ thống bên ngoài
    ├── admin/             # Module quản trị
    ├── sqs/               # Amazon SQS message queue
    ├── subscribers/       # Event subscribers
    ├── publics/           # Public APIs
    ├── @schedule/         # Scheduled jobs/cron jobs
    ├── scada/             # SCADA system integration
    └── @global/           # Global modules
```

#### **Data Layer**

```
├── entities/              # Database entities (ORM models)
├── repositories/          # Data access layer (Repository pattern)
└── dto/                   # Data Transfer Objects
```

#### **Shared Components**

```
├── common/                # Shared components, utilities, enums
├── @types/                # TypeScript type definitions
├── functions/             # Utility functions
└── assets/                # Static assets (images, files, etc.)
```

## 🚀 Cài đặt và chạy dự án

### Yêu cầu hệ thống

- Node.js >= 18.x
- Yarn package manager
- PostgreSQL
- Redis

### Cài đặt dependencies

```bash
yarn install
```

### Chạy ở môi trường development

```bash
yarn dev
```

### Build dự án

```bash
yarn build
```

### Chạy tests

```bash
yarn test
```

## 🔧 Scripts tiện ích

- `yarn make-index` - Tạo index files tự động
- `yarn make-index-all` - Tạo tất cả index files

## 🐳 Docker

### Development

```bash
docker-compose up
```

### SCADA Integration

```bash
docker build -f Dockerfile.scada -t mes-scada .
```

## 📊 Tích hợp hệ thống

### SCADA Systems

- Kết nối và đồng bộ dữ liệu với các hệ thống SCADA
- Xử lý real-time data từ thiết bị sản xuất

### Message Queue

- Amazon SQS cho xử lý bất đồng bộ
- Event-driven architecture

## 🔐 Bảo mật

- JWT Authentication
- Role-based access control
- API rate limiting
- Input validation với class-validator
