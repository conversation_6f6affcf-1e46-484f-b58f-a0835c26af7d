import { Column, Entity } from 'typeorm';
import { PrimaryBaseEntity } from '~/entities/primary-base.entity';

@Entity('authorization-face')
export class AuthorizationFaceEntity extends PrimaryBaseEntity {
  @Column({ type: 'varchar' })
  keycode: string;

  @Column({ type: 'varchar' })
  date: string;

  @Column({ type: 'varchar' })
  placeId: string;

  @Column({ type: 'varchar' })
  deviceId: string;

  @Column({ type: 'varchar' })
  placeName: string;

  @Column({ type: 'varchar' })
  deviceName: string;

  @Column({ type: 'varchar' })
  aliasId: string;

  @Column({ type: 'varchar' })
  detectedImageUrl: string;

  @Column({ type: 'varchar', nullable: true })
  personTitle: string;

  @Column({ type: 'double precision', nullable: true })
  temp: number;

  @Column({ type: 'varchar', nullable: true })
  data: string;

  @Column({ type: 'varchar', nullable: true })
  actionType: string;

  @Column({ type: 'varchar', nullable: true })
  personName: string;

  @Column({ type: 'varchar', nullable: true })
  dataType: string;

  @Column({ type: 'varchar', nullable: true })
  personId: string;

  @Column({ type: 'bigint', nullable: true })
  time: number;

  @Column({ type: 'varchar', nullable: true })
  personType: string;

  @Column({ type: 'varchar', nullable: true })
  hash: string;

  @Column({ type: 'varchar', nullable: true })
  mask: string;
}
