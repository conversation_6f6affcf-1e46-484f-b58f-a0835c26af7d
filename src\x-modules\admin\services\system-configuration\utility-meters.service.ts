// src/x-modules/admin/services/system-configuration/utility-meters.service.ts
import { BadRequestException, Injectable } from '@nestjs/common';
import { ILike, In, Like } from 'typeorm';
import { BindRepo, DefTransaction } from '~/@core/decorator';
import { UtilityMetersRepo } from '~/repositories/primary/utility-meters.repo';
import { UtilityMeterDetailRepo } from '~/repositories/primary/utility-meter-detail.repo';
import { UtilityMeterQueryDto, UtilityMeterReq, ProcessAreaReq } from '~/dto/utility-meter.dto';
import { OrganizationUnitRepo } from '~/repositories/primary/organization-unit.repo';
import { GeneralDataDetailRepo } from '~/repositories/primary';
import { ORGANIZATION_LEVEL } from '~/common/constants/organizationLevel.constant';
import { NSGeneralData } from '~/common/enums/NSGeneralData';
import { EntityManager } from 'typeorm';
import { NSUtilityMeter } from '~/common/enums/NSUtilityMeter.enum';

@Injectable()
export class UtilityMetersService {
  @BindRepo(UtilityMetersRepo)
  private readonly utilityMetersRepo: UtilityMetersRepo;

  @BindRepo(UtilityMeterDetailRepo)
  private readonly utilityMeterDetailRepo: UtilityMeterDetailRepo;

  @BindRepo(OrganizationUnitRepo)
  private readonly organizationUnitRepo: OrganizationUnitRepo;

  @BindRepo(GeneralDataDetailRepo)
  private readonly generalDataDetailRepo: GeneralDataDetailRepo;

  constructor(private readonly entityManager: EntityManager) {}

  // Get Factory List
  async getFactoryList() {
    try {
      let factories = [];

      // Cách 1: Lấy factories từ levelId của GeneralDataDetail
      try {
        const factoryLevel = await this.generalDataDetailRepo.findOne({
          where: { code: ORGANIZATION_LEVEL.FACTORY, isActive: true },
        });

        if (factoryLevel) {
          factories = await this.organizationUnitRepo.find({
            where: { levelId: factoryLevel.id, isActive: true },
            relations: ['parent'],
          });

          if (factories.length > 0) {
            return factories;
          }
        }
      } catch (err) {
        // Nếu có lỗi, tiếp tục với cách tiếp theo
      }

      // Cách 2: Lấy factories theo code pattern
      if (factories.length === 0) {
        factories = await this.organizationUnitRepo.find({
          where: {
            isActive: true,
            code: ILike('FACTORY%'),
          },
        });

        if (factories.length > 0) {
          return factories;
        }
      }

      // Cách 3: Lấy tất cả organization units cấp cao nhất (parentId is null)
      if (factories.length === 0) {
        factories = await this.organizationUnitRepo.find({
          where: {
            isActive: true,
            parentId: null,
          },
        });
      }

      return factories;
    } catch (error) {
      return [];
    }
  }

  // Get UOM List
  async getUOMList() {
    // Lấy general data UOM
    const generalData = await this.entityManager.query(
      `SELECT * FROM general_data WHERE code = '${NSGeneralData.EGeneralDataCode.UOM}'`,
    );

    if (!generalData || generalData.length === 0) {
      return [];
    }

    // Lấy general data detail dựa vào generalId
    return this.generalDataDetailRepo.find({
      where: {
        generalId: generalData[0].id,
        isActive: true,
      },
    });
  }

  // Get Utility Meter Type List
  async getUtilityMeterTypeList() {
    // Lấy general data UTILITY_METER_TYPE
    const generalData = await this.entityManager.query(
      `SELECT * FROM general_data WHERE code = '${NSGeneralData.EGeneralDataCode.UTILITY_METER_TYPE}'`,
    );

    if (!generalData || generalData.length === 0) {
      return [];
    }

    // Lấy general data detail dựa vào generalId
    return this.generalDataDetailRepo.find({
      where: {
        generalId: generalData[0].id,
        isActive: true,
      },
    });
  }

  // Get Data Type List
  async getDataTypeList() {
    // Lấy general data DATA_TYPE
    const generalData = await this.entityManager.query(
      `SELECT * FROM general_data WHERE code = '${NSGeneralData.EGeneralDataCode.DATA_TYPE}'`,
    );

    if (!generalData || generalData.length === 0) {
      return [];
    }

    // Lấy general data detail dựa vào generalId
    return this.generalDataDetailRepo.find({
      where: {
        generalId: generalData[0].id,
        isActive: true,
      },
    });
  }

  // Get DateTime Unit List
  async getDateTimeUnitList() {
    // Lấy general data DATETIME_UNIT
    const generalData = await this.entityManager.query(
      `SELECT * FROM general_data WHERE code = '${NSGeneralData.EGeneralDataCode.DATETIME_UNIT}'`,
    );

    if (!generalData || generalData.length === 0) {
      return [];
    }

    // Lấy general data detail dựa vào generalId
    return this.generalDataDetailRepo.find({
      where: {
        generalId: generalData[0].id,
        isActive: true,
      },
    });
  }

  // Get Calculation Method List
  async getCalculationMethodList() {
    // Lấy general data CAL_TYPE
    const generalData = await this.entityManager.query(
      `SELECT * FROM general_data WHERE code = '${NSGeneralData.EGeneralDataCode.CAL_TYPE}'`,
    );

    if (!generalData || generalData.length === 0) {
      return [];
    }

    // Lấy general data detail dựa vào generalId
    return this.generalDataDetailRepo.find({
      where: {
        generalId: generalData[0].id,
        isActive: true,
      },
    });
  }

  // Get UOM Calculation Method List
  async getUOMCalculationMethodList() {
    // Lấy general data UOM
    const generalData = await this.entityManager.query(
      `SELECT * FROM general_data WHERE code = '${NSGeneralData.EGeneralDataCode.UOM}'`,
    );

    if (!generalData || generalData.length === 0) {
      return [];
    }

    // Lấy general data detail dựa vào generalId
    return this.generalDataDetailRepo.find({
      where: {
        generalId: generalData[0].id,
        isActive: true,
      },
    });
  }

  async findAll(query: UtilityMeterQueryDto) {
    const {
      page = 1,
      limit = 10,
      factoryId,
      isActive,
      code,
      utilityMeterTypeDetailCode,
      name,
    } = query;
    const where: any = {};

    if (name) {
      where.name = ILike(`%${name}%`);
    }

    if (factoryId) {
      where.factoryId = factoryId;
    }

    if (isActive) {
      where.isActive = isActive;
    }

    if (code) {
      where.code = ILike(`%${code}%`);
    }

    if (utilityMeterTypeDetailCode) {
      where.utilityMeterTypeDetailCode = utilityMeterTypeDetailCode;
    }

    const [meters, total] = await this.utilityMetersRepo.findAndCount({
      where,
      relations: [
        'factory',
        'uomDetail',
        'utilityMeterType',
        'dataType',
        'datetimeUnit',
        'calMethod',
        'uomCalMethod',
      ],
      skip: (page - 1) * limit,
      take: limit,
    });

    const dictSite: any = {};
    {
      const site = await this.organizationUnitRepo.find({
        where: { id: In(meters.map((m) => m.factory.parentId)) },
      });
      site.forEach((s) => {
        dictSite[s.id] = s.code;
      });
    }

    meters.forEach((meter) => {
      meter.factory.parent = dictSite[meter.factory.parentId] || '';
    });

    return {
      data: meters,
      pagination: {
        total,
        page,
        limit,
        totalPages: Math.ceil(total / limit),
      },
    };
  }

  async findOne(id: string) {
    const meter = await this.utilityMetersRepo.findOne({
      where: { id },
      relations: [
        'factory',
        'uomDetail',
        'utilityMeterType',
        'dataType',
        'datetimeUnit',
        'calMethod',
        'uomCalMethod',
      ],
    });

    if (!meter) {
      throw new BadRequestException('Không tìm thấy đồng hồ tiện ích');
    }
    meter.dataTypeCode = meter.dataType?.code ?? null;
    meter.uomCalMethodCode = meter.uomCalMethod?.code ?? null;
    meter.dataTypeDetailCode = meter.dataType?.code ?? null;
    meter.datetimeUnitCode = meter.datetimeUnit?.code ?? null;
    meter.utilityMeterTypeCode = meter.utilityMeterType?.code ?? null;

    return meter;
  }

  async findOneWithDetails(id: string, page: number = 1, limit: number = 10) {
    try {
      // Lấy thông tin chi tiết của Utility Meter
      const utilityMeter = await this.utilityMetersRepo.findOne({
        where: { id },
        relations: [
          'factory',
          'uomDetail',
          'utilityMeterType',
          'dataType',
          'datetimeUnit',
          'calMethod',
          'uomCalMethod',
        ],
      });

      if (!utilityMeter) {
        throw new Error('Không tìm thấy đồng hồ tiện ích');
      }

      // Tạo query builder cho Process Areas
      const queryBuilder = this.utilityMeterDetailRepo
        .createQueryBuilder('detail')
        .leftJoinAndSelect('detail.utilityMeter', 'utilityMeter')
        .leftJoinAndSelect('detail.organization', 'organization')
        .leftJoinAndSelect('organization.parent', 'line')
        .where('detail.utilityMeterId = :id', { id });

      // Sắp xếp theo tên organization
      queryBuilder.orderBy('organization.name', 'ASC');

      // Tính toán skip và take cho phân trang
      const skip = (page - 1) * limit;

      // Thực hiện đếm tổng số bản ghi
      const total = await queryBuilder.getCount();

      // Thêm phân trang vào query
      queryBuilder.skip(skip).take(limit);

      // Lấy kết quả
      const processAreas = await queryBuilder.getMany();

      // Format kết quả
      const items = processAreas.map((item) => ({
        id: item.id,
        createdDate: item.createdDate,
        updatedDate: item.updatedDate,
        createdBy: item.createdBy,
        updatedBy: item.updatedBy,
        version: item.version,
        isAssigned: item.isAssigned,
        organizationUnitId: item.processAreaId,
        utilityMeterId: item.utilityMeterId,
        utilityMeter: {
          id: utilityMeter.id,
          code: utilityMeter.code,
          name: utilityMeter.name,
          description: utilityMeter.note,
          organizationUnitId: utilityMeter.factoryId,
          isActive: utilityMeter.isActive,
          note: utilityMeter.note,
          uomGeneralCode: utilityMeter.uomCode,
          uomGeneralDetailCode: utilityMeter.uomDetailCode,
          uomGeneralDetailName: utilityMeter.uomDetail?.name,
          tagName: utilityMeter.tagName,
          tagAddress: utilityMeter.tagAddress,
          utilityMeterTypeCode: utilityMeter.utilityMeterTypeCode,
          utilityMeterTypeDetailCode: utilityMeter.utilityMeterTypeDetailCode,
          dataTypeCode: utilityMeter.dataTypeCode,
          dataTypeDetailCode: utilityMeter.dataTypeDetailCode,
          interval: utilityMeter.interval,
          timeValue: utilityMeter.timeValue,
          datetimeUnitCode: utilityMeter.datetimeUnitCode,
          datetimeUnitDetailCode: utilityMeter.datetimeUnitDetailCode,
          nodeId: utilityMeter.nodeId,
          calMethodCode: utilityMeter.calMethodCode,
          uomCalMethodCode: utilityMeter.uomCalMethodCode,
          resourceCode: utilityMeter.resourceCode,
          assetId: utilityMeter.assetId,
          measurementId: utilityMeter.measurementId,
          metricId: utilityMeter.metricId,
        },
        organization: item.organization,
        processAreaId: item.organization?.id || '',
        processAreaCode: item.organization?.code || '',
        processAreaName: item.organization?.name || '',
        line: item.organization?.parent?.code || '',
      }));

      return {
        data: items,
        pagination: {
          total,
          page,
          limit,
          totalPages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      throw new Error(error.message || 'Có lỗi xảy ra khi lấy thông tin chi tiết');
    }
  }

  async getAllData(id: string) {
    try {
      // Lấy thông tin utility meter
      const meter = await this.utilityMetersRepo.findOneWithRelations(id);

      if (!meter) {
        throw new BadRequestException('Không tìm thấy đồng hồ tiện ích');
      }

      // Lấy chi tiết utility meter với process areas
      const detailsQuery = this.utilityMeterDetailRepo
        .createQueryBuilder('detail')
        .leftJoinAndSelect('detail.organization', 'organization')
        .where('detail.utilityMeterId = :id', { id });

      const details = await detailsQuery.getMany();

      // Format lại kết quả trả về để thêm thông tin từ organization
      const formattedDetails = details.map((detail) => ({
        id: detail.id,
        utilityMeterId: detail.utilityMeterId,
        processAreaId: detail.processAreaId,
        isAssigned: detail.isAssigned,
        processAreaCode: detail.organization?.code || '',
        processAreaName: detail.organization?.name || '',
        createdDate: detail.createdDate,
        updatedDate: detail.updatedDate,
      }));

      // Trả về meter và details
      return {
        ...meter,
        details: formattedDetails,
      };
    } catch (error) {
      throw error;
    }
  }

  private async codeDefault() {
    const objData = await this.utilityMetersRepo.findOne({
      order: { code: 'DESC' },
    });
    if (!objData) return NSUtilityMeter.ECodeDefault.CODE_START;
    const number = Number.parseInt(objData.code) + 1;
    return number.toString();
  }

  @DefTransaction()
  async save(body: UtilityMeterReq) {
    const { processAreaDetails, id, ...rest } = body;
    let meter: any = {};

    if (!id) {
      rest.code = await this.codeDefault();
      meter = await this.utilityMetersRepo.save(rest);
    } else {
      meter = await this.utilityMetersRepo.preload({ id, ...rest });
      if (!meter) throw new BadRequestException('Không tìm thấy đồng hồ tiện ích');
      meter = await this.utilityMetersRepo.save(meter);
    }

    // Cập nhật hoặc thêm mới các process areas
    const processAreaDetailList = processAreaDetails?.length
      ? await this.upsertProcessAreas(meter.id, processAreaDetails)
      : [];

    return {
      message: 'success',
      data: {
        ...meter,
        processAreaDetails: processAreaDetailList,
      },
    };
  }

  private async upsertProcessAreas(meterId: string, processAreas: ProcessAreaReq[]) {
    const existingDetails = await this.utilityMeterDetailRepo.find({
      where: { utilityMeterId: meterId },
    });

    const existingMap = new Map(existingDetails.map((p) => [p.processAreaId, p]));
    const processAreaIds = new Set(processAreas.map((p) => p.processAreaId));

    const validProcessAreas = await this.organizationUnitRepo.find({
      where: { id: In([...processAreaIds]), isActive: true },
    });

    const processAreaMap = new Map(validProcessAreas.map((p) => [p.id, p]));

    const toUpdate = [];
    const toInsert = [];
    const changedProcesses = [];
    const incomingIds = new Set();

    for (const item of processAreas) {
      if (!processAreaMap.has(item.processAreaId)) continue;

      const existing = existingMap.get(item.processAreaId);
      incomingIds.add(item.processAreaId);

      if (existing) {
        existing.isAssigned = item.isAssigned;
        toUpdate.push(existing);
        changedProcesses.push({
          processAreaId: existing.processAreaId,
          isAssigned: existing.isAssigned,
          processAreaCode: processAreaMap.get(existing.processAreaId)?.code,
          processAreaName: processAreaMap.get(existing.processAreaId)?.name,
        });
      } else {
        const newProcess = this.utilityMeterDetailRepo.create({
          utilityMeterId: meterId,
          processAreaId: item.processAreaId,
          isAssigned: item.isAssigned,
        });

        toInsert.push(newProcess);
        changedProcesses.push({
          processAreaId: newProcess.processAreaId,
          isAssigned: newProcess.isAssigned,
          processAreaCode: processAreaMap.get(newProcess.processAreaId)?.code,
          processAreaName: processAreaMap.get(newProcess.processAreaId)?.name,
        });
      }
    }

    const toDelete = existingDetails.filter((p) => !incomingIds.has(p.processAreaId));
    const deletedProcessIds = toDelete.map((p) => p.processAreaId);

    await Promise.allSettled([
      toUpdate.length && this.utilityMeterDetailRepo.save(toUpdate),
      toInsert.length && this.utilityMeterDetailRepo.save(toInsert),
      toDelete.length && this.utilityMeterDetailRepo.remove(toDelete),
    ]);

    return {
      ...changedProcesses,
    };
  }

  async getLinesByFactoryId(factoryId: string) {
    try {
      // Lấy level ID của Line từ GeneralDataDetail
      const lineLevel = await this.generalDataDetailRepo.findOne({
        where: { code: ORGANIZATION_LEVEL.LINE, isActive: true },
      });

      if (!lineLevel) {
        return [];
      }

      // Lấy tất cả Line thuộc Factory
      const lines = await this.organizationUnitRepo.find({
        where: {
          levelId: lineLevel.id,
          isActive: true,
        },
        relations: ['parent', 'parent.parent', 'parent.parent.parent'],
        order: {
          name: 'ASC',
        },
      });

      // Lọc các Line theo Factory
      const filteredLines = lines.filter((line) => {
        let current = line;
        let isInFactory = false;

        while (current.parent) {
          if (current.parent.id === factoryId) {
            isInFactory = true;
            break;
          }
          current = current.parent;
        }

        return isInFactory;
      });

      return filteredLines;
    } catch (error) {
      return [];
    }
  }

  async getProcessAreasByFactoryId(
    factoryId: string,
    page: number = 1,
    limit: number = 10,
    processAreaCode?: string,
    processAreaName?: string,
    lineId?: string,
    activeStatus?: string,
  ) {
    try {
      // Lấy level ID của Process Area từ GeneralDataDetail
      const processAreaLevel = await this.generalDataDetailRepo.findOne({
        where: { code: ORGANIZATION_LEVEL.PROCESS, isActive: true },
      });

      if (!processAreaLevel) {
        return {
          items: [],
          total: 0,
          page,
          limit,
          totalPages: 0,
        };
      }

      // Tạo query builder
      const queryBuilder = this.organizationUnitRepo
        .createQueryBuilder('processArea')
        .leftJoinAndSelect('processArea.parent', 'line')
        .leftJoinAndSelect('line.parent', 'factory')
        .where('processArea.levelId = :levelId', { levelId: processAreaLevel.id })
        .andWhere('factory.id = :factoryId', { factoryId });

      // Thêm điều kiện tìm kiếm theo code
      if (processAreaCode) {
        queryBuilder.andWhere('processArea.code ILIKE :code', {
          code: `%${processAreaCode}%`,
        });
      }

      // Thêm điều kiện tìm kiếm theo tên
      if (processAreaName) {
        queryBuilder.andWhere('processArea.name ILIKE :name', {
          name: `%${processAreaName}%`,
        });
      }

      // Thêm điều kiện lọc theo line
      if (lineId) {
        queryBuilder.andWhere('line.id = :lineId', { lineId });
      }

      // Thêm điều kiện lọc theo trạng thái active
      if (activeStatus === 'yes') {
        queryBuilder.andWhere('processArea.isActive = :isActive', { isActive: true });
      } else if (activeStatus === 'no') {
        queryBuilder.andWhere('processArea.isActive = :isActive', { isActive: false });
      }

      // Tính toán skip và take cho phân trang
      const skip = (page - 1) * limit;
      queryBuilder.skip(skip).take(limit);

      // Sắp xếp theo tên
      queryBuilder.orderBy('processArea.name', 'ASC');

      // Lấy kết quả và tổng số bản ghi
      const [items, total] = await queryBuilder.getManyAndCount();

      // Tính tổng số trang
      const totalPages = Math.ceil(total / limit);

      return {
        items,
        total,
        page,
        limit,
        totalPages,
      };
    } catch (error) {
      return {
        items: [],
        total: 0,
        page,
        limit,
        totalPages: 0,
      };
    }
  }

  async getGeneralDataOptions() {
    // Lấy danh sách phương thức tính toán
    const calMethods = await this.getCalculationMethodList();

    // Lấy danh sách đơn vị tính toán
    const uomCalMethods = await this.getUOMCalculationMethodList();

    return {
      calculationMethods: calMethods,
      uomCalculationMethods: uomCalMethods,
    };
  }

  async getProcessAreasDirectly(factoryId: string) {
    try {
      // Lấy tất cả process areas trong cây tổ chức
      const processAreas = await this.organizationUnitRepo
        .createQueryBuilder('org')
        .where('org.levelGeneralDataDetailCode = :level', { level: 'PROCESS' })
        .andWhere(
          'org.parentId IN (SELECT id FROM organization_unit WHERE parentId = :factoryId)',
          { factoryId },
        )
        .andWhere('org.isActive = :isActive', { isActive: true })
        .getMany();

      return processAreas;
    } catch (error) {
      return [];
    }
  }

  async getLinesDirectly(factoryId: string) {
    try {
      // Lấy tất cả lines của factory
      const lines = await this.organizationUnitRepo
        .createQueryBuilder('org')
        .leftJoinAndSelect('org.parent', 'parent')
        .where('org.levelGeneralDataDetailCode = :level', { level: 'LINE' })
        .andWhere('org.parentId = :factoryId', { factoryId })
        .orderBy('org.name', 'ASC')
        .getMany();

      return lines;
    } catch (error) {
      console.error('Error getting lines:', error);
      return [];
    }
  }

  async getLastCode() {
    try {
      // Lấy utility meter có code lớn nhất
      const lastUtilityMeter = await this.utilityMetersRepo
        .createQueryBuilder('um')
        .orderBy('CAST(um.code AS INTEGER)', 'DESC')
        .getOne();

      if (!lastUtilityMeter) {
        // Nếu không có utility meter nào, trả về mã bắt đầu
        return '2000000';
      }

      // Chuyển đổi code thành số và tăng lên 1
      const lastCode = parseInt(lastUtilityMeter.code);
      const nextCode = lastCode + 1;

      return nextCode.toString();
    } catch (error) {
      // Nếu có lỗi, trả về mã bắt đầu
      return '2000000';
    }
  }
}
