import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsBoolean, IsOptional } from 'class-validator';

export class filterReq {
  @ApiPropertyOptional({ description: 'Category code' })
  @IsOptional()
  categoryCode?: string;

  @ApiPropertyOptional({ description: 'Site ID' })
  @IsOptional()
  siteId?: string;

  @ApiPropertyOptional({ description: 'Factory ID' })
  @IsOptional()
  factoryId?: string;
}
