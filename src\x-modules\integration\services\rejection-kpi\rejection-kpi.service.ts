import { Injectable } from '@nestjs/common';
import { BindRepo } from '~/@core/decorator';
import moment from 'moment';
import { JwtService } from '@nestjs/jwt';
import { Connection, FindOperator } from 'typeorm';
import { promises } from 'dns';
import { dateHelper } from '~/common/helpers/date.helper';
import {
  KpiRepo,
  KpiSetDetailRepo,
  KpiSetGroupRepo,
  KpiSetHeaderRepo,
  KpiSetProductAreaRepo,
  KpiScoreRepo,
  MaterialGroupDetailRepo
} from '~/repositories/primary';

interface KpiDataDto {
  kpiGroupId: string;
  kpiGroupCode: string;
  kpiGroupName: string;
  kpiSetHeaderId: string;
  idbanggroup: string;
  kpiId: string;
  kpiSetDetailId: string;
  kpiSetGroupId: string;
  code: string;
  shortName: string;
  utilityType: string;
  factoryId: string;
  siteId: string;
  productionAreaId: string;
  inputFrequency: string;
  unit: string;
}

interface ProdRejectionDto {
  kpiId: any | string | FindOperator<string>;
  kpiSetGroupId: any | string | FindOperator<string>;
  factoryId: string;
  transactionDate: string;
  shiftId: string;
  total_quantity: number;
}

@Injectable()
export class RejectionKpi {
  constructor(
    private jwtService: JwtService,
    private readonly connection: Connection
  ) {}
  @BindRepo(KpiRepo)
  private readonly kpiRepo: KpiRepo;
  @BindRepo(KpiScoreRepo)
  private readonly kpiScoreRepo: KpiScoreRepo;
  @BindRepo(KpiSetHeaderRepo)
  private readonly kpiSetHeaderRepo: KpiSetHeaderRepo;
  @BindRepo(KpiSetGroupRepo)
  private readonly kpiSetGroupRepo: KpiSetGroupRepo;
  @BindRepo(KpiSetDetailRepo)
  private readonly kpiSetDetailRepo: KpiSetDetailRepo;
  @BindRepo(KpiSetProductAreaRepo)
  private readonly kpiSetProductAreaRepo: KpiSetProductAreaRepo;
  @BindRepo(MaterialGroupDetailRepo)
  private readonly materialGroupDetailRepo: MaterialGroupDetailRepo;

  async getDataByKpi() {
    const result = await this.connection.query(`
      SELECT ksh.* ,'||' "||", ksg."kpiGroupId",ksg."kpiGroupCode", ksg."kpiGroupName", ksg."kpiSetHeaderId", ksg.id as idbanggroup,
      '||' "||", ksd."kpiId", ksd."id" AS "kpiSetDetailId", ksd."kpiSetGroupId" ,'||' "||", k.code, k."shortName", k."utilityType" ,'||' "||", 
      kspa."factoryId" , ou.code , kspa."productionAreaId", pa.code , pa."name" , kspa."siteId", k."inputFrequency", k.unit 
      --kp.kpivalue , kp."shiftId" , kp.podate 
      from kpi_set_header ksh 
      join kpi_set_group ksg  on ksh.id = ksg."kpiSetHeaderId"
      join kpi_set_detail ksd on ksd."kpiSetGroupId" = ksg.id 
      join kpi k  on k.id = ksd."kpiId"
      join kpi_set_production_area kspa  on ksh.id = kspa."kpiSetHeaderId"
      join organization_units ou on kspa."factoryId" = ou.id
      join production_area pa on pa.id = kspa."productionAreaId"
      --left join kpivalue as kp on kp.factory_id = kspa."factoryId" and kp."productionAreaId" =kspa."productionAreaId" and kp.utility_meter_type_detail_code =k."utilityType"
      where ksh.status = 1 and k."kpiMethod" = 'A' and k."autoFunction"  = 'F_KPI_REJECTION'  and kspa.status =1 and k.status =1 
    `);
  
    return result;
  }

  async onGetKpiPeriodId (date: string) {
    const getDate = moment(date, "YYYY-MM-DD HH:mm:ss.SSS Z").format('YYYY-MM-DD');
    const KpiPeriodData = await this.connection.query(`
      SELECT kp.*
      FROM kpi_period kp
      join kpi_period_rule kpr on kp."kpiPeriodRuleId" = kpr."id"
      WHERE DATE($1) BETWEEN DATE(kp."startDate") AND DATE(kp."endDate")
      and kpr."cycleCode" = '1'
    `, [getDate]);
    return KpiPeriodData[0] ? KpiPeriodData[0]?.id : null;
  }

  async onProductionRejection (factoryId: string) {
    const prodRejection = await this.connection.query(`
       SELECT 
        "factoryId", 
        "transactionDate", 
        "shiftId", 
        SUM(quantity) AS total_quantity
      FROM 
        production_rejection
      WHERE 
        "factoryId" = $1
        and "transactionTypeDetailCode" = '1'
        and "oeeCal" != 1
      GROUP BY 
        "factoryId", 
        "transactionDate", 
        "shiftId"
    `, [factoryId]);
    return prodRejection;
  }

  async createRejectionKpi(data: { fromDate?: string; toDate?: string }) {
    try {
      const SOURCE_BY_JOB = 'Job47'
      let getDateFrom = null
      let getDateTo = null
      if(data.fromDate && data.toDate){
        getDateFrom = dateHelper.formatDateByJobManual(data.fromDate)
        getDateTo = dateHelper.formatDateByJobManual(data.toDate)
      }
      // Set-up data
      const dataByKpis = await this.getDataByKpi()
      if(!dataByKpis || dataByKpis?.length === 0) return;

      await Promise.all(
        dataByKpis.map(async (dataByKpi: KpiDataDto) => {
          const prodRejectionData = await this.onProductionRejection(dataByKpi?.factoryId)
          if(!prodRejectionData || prodRejectionData?.length === 0) return;

          await Promise.all(
            prodRejectionData.map(async (prodRejection: ProdRejectionDto) => {
              const getKpiPeriodId = await this.onGetKpiPeriodId(prodRejection?.transactionDate) || null;
              if(!getKpiPeriodId) return;

              const kpiScoreObj = {
                kpiSetHeaderId: dataByKpi?.kpiSetHeaderId,
                siteId: dataByKpi?.siteId,
                factoryId: dataByKpi?.factoryId,
                productionAreaId: dataByKpi?.productionAreaId,
                kpiSetGroupId: dataByKpi?.kpiSetGroupId,
                kpiId: dataByKpi?.kpiId,
                shiftId: prodRejection?.shiftId,
                kpiSetDetailId: dataByKpi?.kpiSetDetailId,
                kpiPeriodId: getKpiPeriodId,
                actualScore: prodRejection?.total_quantity,
                scoreDate: prodRejection?.transactionDate,
                source: SOURCE_BY_JOB,
                createdByUser: "-1",
                updatedByUser: '-1'
              }

              const existing = await this.kpiScoreRepo.findOne({
                where: {
                  productionAreaId: dataByKpi?.productionAreaId,
                  kpiSetHeaderId: dataByKpi?.kpiSetHeaderId,
                  shiftId: prodRejection?.shiftId,
                  factoryId: kpiScoreObj?.factoryId,
                  scoreDate: prodRejection?.transactionDate,
                  kpiSetGroupId: prodRejection?.kpiSetGroupId,
                  kpiId: prodRejection?.kpiId,
                  source: SOURCE_BY_JOB,
                },
              });

              if(existing){
                await this.kpiScoreRepo.update({ id: existing?.id },{actualScore: kpiScoreObj.actualScore})
              }else{
                await this.kpiScoreRepo.save({...kpiScoreObj})
              }
            })
          )
        })
      )

    } catch (error) {
    }
  }
}
