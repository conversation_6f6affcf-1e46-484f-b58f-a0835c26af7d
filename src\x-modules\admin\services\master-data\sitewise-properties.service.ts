import { Injectable, ConflictException, NotFoundException } from '@nestjs/common';
import { In, Like, UpdateResult } from 'typeorm';
import { SitewiseAssetRepo, SitewisePropertiesRepo } from '~/repositories/primary/sitewise.repo';
import {
  ListSitewisePropertiesReq,
  SitewisePropertiesImportReq,
  SitewisePropertiesReq,
} from '~/dto/sitewise.dto';
import { adminSessionContext } from '../../admin-session.context';
import { SuccessResponse } from '~/@systems/utils';
import { BindRepo } from '~/@core/decorator';
import { SitewisePropertiesEntity } from '~/entities/primary';

@Injectable()
export class SitewisePropertiesService {
  constructor() {}
  @BindRepo(SitewisePropertiesRepo)
  private readonly repo: SitewisePropertiesRepo;

  @BindRepo(SitewiseAssetRepo)
  private readonly assetRepo: SitewiseAssetRepo;

  async find(data: any) {
    const whereCon: any = {};
    if (data.code) whereCon.code = Like(`%${data.code}%`);
    if (data.type) whereCon.type = data.type;
    if (data.unit) whereCon.unit = data.unit;
    if (data.sitewiseAssetId) whereCon.sitewiseAssetId = data.sitewiseAssetId;
    if (data.lstId?.length > 0) whereCon.id = In(data.lstId);
    if (data.lstCode?.length > 0) whereCon.code = In(data.lstCode);
    return await this.repo.find({ where: whereCon });
  }

  // async findOne(id: string) {
  //   return await this.repo.findOne({ where: { id: id } });
  // }

  // async createData(data: SitewisePropertiesReq): Promise<any> {
  //   const checkCodeExist = await this.repo.findOne({ where: { code: data.code } });
  //   if (checkCodeExist)
  //     throw new ConflictException(`Sitewise Asset with code [${data.code}] already exists`);
  //   const checkSitewiseModel = await this.assetRepo.findOne({
  //     where: { id: data.sitewiseAssetId, isActive: true },
  //   });
  //   if (!checkSitewiseModel)
  //     throw new NotFoundException(`Sitewise asset with ID [${data.sitewiseAssetId}] not found`);
  //   const { userId } = adminSessionContext;

  //   const entity = this.repo.create({ ...data, createdBy: userId });
  //   await this.repo.insert(entity);

  //   return entity;
  // }

  // async updateData(id: string, data: SitewisePropertiesReq) {
  //   const entity = await this.repo.findOne({ where: { id: id } });
  //   if (!entity) throw new NotFoundException(`Sitewise model with ID ${id} not found`);
  //   const { userId } = adminSessionContext;
  //   if (data.code != entity.code) {
  //     const checkCodeExist = await this.repo.findOne({ where: { code: data.code } });
  //     if (checkCodeExist)
  //       throw new ConflictException(`Sitewise Model with code [${data.code}] already exists`);
  //   }
  //   const checkSitewiseModel = await this.assetRepo.findOne({
  //     where: { id: data.sitewiseAssetId, isActive: true },
  //   });
  //   if (!checkSitewiseModel)
  //     throw new NotFoundException(`Sitewise Asset with ID [${data.sitewiseAssetId}] not found`);

  //   entity.code = data.code;
  //   entity.description = data.description;
  //   entity.awsId = data.awsId;
  //   entity.sitewiseAssetId = data.sitewiseAssetId;
  //   entity.updatedBy = data?.updatedBy || userId;
  //   const updatedEntity = await this.repo.update(entity.id, entity);
  //   return { message: UpdateResult, data: updatedEntity };
  // }

  async pagination(body: ListSitewisePropertiesReq) {
    let whereCon: any = {};
    if (body.awsId) whereCon.awsId = Like(`%${body.awsId}%`);
    if (body.sitewiseAssetId) whereCon.sitewiseAssetId = body.sitewiseAssetId;
    if (body.code) whereCon.code = Like(`%${body.code}%`);
    if (body.type) whereCon.type = body.type;
    if (body.unit) whereCon.unit = body.unit;
    if (body.isActive !== undefined) whereCon.isActive = body.isActive;
    let res: any = await this.repo.findPagination({}, body);
    // const dictSitewiseAsset: any = {};
    // {
    //   const listSitewiseAsset = await this.assetRepo.find({
    //     where: { id: In(res.data.map((item: SitewisePropertiesEntity) => item.sitewiseAssetId)) },
    //   });
    //   listSitewiseAsset.forEach(item => (dictSitewiseAsset[item.id] = item.code));
    // }
    // res.data.forEach(item => {
    //   item['sitewiseAssetCode'] = dictSitewiseAsset[item.sitewiseAssetId] || '';
    // });
    return res;
  }

  // async updateIsDelete(id: string) {
  //   const entity = await this.repo.findOne({ where: { id } });
  //   if (!entity) throw new NotFoundException(`Sitewise properties with ID ${id} not found`);

  //   await this.repo.update(entity.id, entity);
  //   return { message: SuccessResponse };
  // }

  // async createDataExcel(data: SitewisePropertiesImportReq[]): Promise<any> {
  //   const existingCodes = new Set(
  //     (await this.repo.find({ where: { isActive: true }, select: ['code'] })).map(c => c.code),
  //   );
  //   const { userId } = adminSessionContext;
  //   const dictSitewiseAsset: any = {};
  //   {
  //     const listSitewiseAsset = await this.assetRepo.find({
  //       where: { code: data.map(item => item.sitewiseAssetCode), isActive: true },
  //       select: ['id', 'code'],
  //     });
  //     listSitewiseAsset.forEach(item => (dictSitewiseAsset[item.code] = item.id));
  //   }
  //   const newEntityList = data
  //     .filter(
  //       (item, index) =>
  //         !existingCodes.has(item.code) &&
  //         data.findIndex(el => el.code === item.code) === index &&
  //         dictSitewiseAsset[item.sitewiseAssetCode],
  //     )
  //     .map(({ sitewiseAssetCode, ...rest }) =>
  //       this.repo.create({
  //         ...rest,
  //         sitewiseAssetId: dictSitewiseAsset[sitewiseAssetCode],
  //         createdBy: userId,
  //       }),
  //     );

  //   await this.repo.insert(newEntityList);
  //   return { message: SuccessResponse };
  // }
}
