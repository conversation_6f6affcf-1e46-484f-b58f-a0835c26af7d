import { ApiProperty } from '@nestjs/swagger';
import { <PERSON>umn, Entity, Join<PERSON><PERSON>umn, ManyToOne, PrimaryGeneratedColumn } from 'typeorm';
import { PrimaryBaseEntity } from '../primary-base.entity';
import { ProductionOrderEntity } from './production-order.entity'; // Import ProductionOrder entity
import { ProcessEntity } from './process.entity';

@Entity('production_order_material')
export class ProductionOrderMaterialEntity extends PrimaryBaseEntity {
  /** Mỗi Production Order được tạo, thông tin detail của Production Order được lưu vào bảng Material. Order id link từ bảng Production Order. */
  @ApiProperty({ description: 'ID lệnh sản xuất (liên kết đến ProductionOrder)' })
  @Column({ nullable: true })
  orderId: string;
  @ManyToOne(() => ProductionOrderEntity, (p) => p.id)
  @JoinColumn({ name: 'orderId', referencedColumnName: 'id' })
  productionOrder: ProductionOrderEntity;

  /** Polulated theo Order id từ bảng Production Order. */
  @ApiProperty({ description: 'Mã lệnh sản xuất (lấy từ ProductionOrder)' })
  @Column({ type: 'varchar', length: 250 })
  orderNo: string;

  /** Polulated theo Order id từ bảng Production Order. */
  @ApiProperty({ description: 'Mã công thức (lấy từ ProductionOrder)' })
  @Column({ type: 'uuid', nullable: true })
  recipeId: string;

  /** Polulated theo Order id từ bảng Production Order. */
  @ApiProperty({ description: 'Phiên bản công thức (lấy từ ProductionOrder)' })
  @Column({ type: 'numeric', nullable: true })
  recipeVersion: number;

  /** Hệ thống tự sinh theo số tự nhiên, để mỗi dòng material là 1 id duy nhất. */
  @ApiProperty({ description: 'ID dòng vật liệu' })
  @PrimaryGeneratedColumn()
  lineId: number;

  /** Process Area Id, thông tin populate từ Recipe */
  @ApiProperty({ description: 'Mã khu vực sản xuất (liên kết đến process)' })
  @Column({ nullable: true })
  processId: string;
  @ManyToOne(() => ProcessEntity, (org) => org.id)
  @JoinColumn({ name: 'processId', referencedColumnName: 'id' })
  process?: ProcessEntity;

  /** Material Id, thông tin populate từ Recipe */
  @ApiProperty({ description: 'Mã vật liệu (liên kết đến Recipe)' })
  @Column({ type: 'uuid', nullable: true })
  materialId: string;

  /** Material Code, thông tin populate từ Recipe */
  @ApiProperty({ description: 'Mã code vật liệu (lấy từ Recipe)' })
  @Column({ type: 'varchar', length: 15, nullable: true })
  materialCode: string;

  /** Material name, thông tin populate từ Material Code, lấy tên từ bảng item. Lấy theo trường type_code trong recipe: -1-Ingredient, 1 Product, 2 - Byproduct. */
  @ApiProperty({ description: 'Tên vật liệu (lấy từ bảng item)' })
  @Column({ type: 'varchar', length: 255 })
  materialName: string;

  /** Lấy theo trường type_code trong recipe: -1-Ingredient, 1 Product, 2 - Byproduct. */
  @ApiProperty({ description: 'Loại dòng vật liệu' })
  @Column({ type: 'numeric', nullable: true })
  lineType: number;

  @ApiProperty({ description: 'Loại dòng vật liệu' })
  @Column({ length: 50, nullable: true })
  lineTypeCode: string;

  /** Id đơn vị tính */
  @ApiProperty({ description: 'Đơn vị tính' })
  @Column({ type: 'varchar', length: 36, nullable: true })
  trxUom: string;

  /** Mã đơn vị tính */
  @ApiProperty({ description: 'Đơn vị tính' })
  @Column({ type: 'varchar', length: 50, nullable: true })
  trxUomCode: string;

  /** Số lượng material dùng để sản xuất theo kế hoạch theo đơn vị tỉnh nhập liệu. (Cô tỉnh toán để ra số lượng theo mô tả quy trình nhập ở bước 5 bên dưới)) */
  @ApiProperty({ description: 'Số lượng vật liệu theo kế hoạch' })
  @Column({ type: 'numeric', nullable: true })
  planTrxQty: number;

  /** Số lượng material dùng để sản xuất theo thực tế theo đơn vị nhập liệu. Hệ thống tự update theo ghi nhận thực tế từ Scada và update vào bảng (Số lượng actual của ingredient, product, by_product) */
  @ApiProperty({ description: 'Số lượng vật liệu thực tế' })
  @Column({ type: 'numeric', nullable: true })
  actualTrxQty: number;

  /** ID của production order cha */
  @ApiProperty({ description: 'PO parent id' })
  @Column({ type: 'uuid', nullable: true })
  poParentId: string;

  /** Mục đích để tính năng cân & scan kiểm soát batch đã cân xong (bằng 1) */
  @ApiProperty({
    description: 'Mục đích để tính năng cân & scan kiểm soát batch đã cân xong (bằng 1)',
  })
  @Column({ type: 'int', nullable: true, default: 0 })
  weighStatus: number;
}
