import { PrimaryBaseEntity } from '~/entities/primary-base.entity';
import { Entity, Column } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';

@Entity('kpi_award')
export class KpiAwardEntity extends PrimaryBaseEntity {
  @ApiProperty({ description: 'Id người dùng' })
  @Column({ type: 'uuid', nullable: true })
  userId: string;

  @ApiProperty({ description: 'Ngày làm việc' })
  @Column({ type: 'timestamptz', nullable: true })
  workDate: Date;

  @ApiProperty({ description: 'Id bộ KPI header' })
  @Column({ type: 'uuid', nullable: true })
  kpiSetHeaderId: string;

  @ApiProperty({ description: 'Id nhóm KPI' })
  @Column({ type: 'uuid', nullable: true })
  kpiSetGroupId: string;

  @ApiProperty({ description: 'Id mã K<PERSON>' })
  @Column({ type: 'uuid', nullable: true })
  kpiCodeId: string;

  @ApiProperty({ description: 'Id nhà máy' })
  @Column({ type: 'uuid', nullable: true })
  factoryId: string;

  @ApiProperty({ description: 'Id khu vực sản xuất' })
  @Column({ type: 'uuid', nullable: true })
  productionAreaId: string;

  @ApiProperty({ description: 'Thưởng thực tế' })
  @Column({ type: 'numeric', nullable: true })
  actualAward: number;

  @ApiProperty({ description: 'Thưởng định mức' })
  @Column({ type: 'numeric', nullable: true })
  standardAward: number;

  @ApiProperty({ description: 'Ngân sách thưởng' })
  @Column({ type: 'numeric', nullable: true })
  budget: number;

  @ApiProperty({ description: 'Tỷ trọng chia thưởng cho KPI' })
  @Column({ type: 'varchar', nullable: true })
  weightageKpi: string;

  @ApiProperty({ description: '% Nhận thưởng theo xếp loại' })
  @Column({ type: 'varchar', nullable: true })
  rewardPercentage: string;

  @ApiProperty({ description: 'Cấp bậc' })
  @Column({ type: 'varchar', nullable: true })
  level: string;
}
