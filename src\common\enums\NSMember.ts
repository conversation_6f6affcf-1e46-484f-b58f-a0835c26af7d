export namespace NSMember {
  export enum EMemberType {
    MEMBER = 'MEMBER',
    COLLABORATOR = 'COLLABORATOR', // Cộng tác viên,
    POST_OFFICE = 'POST_OFFICE', // B<PERSON>u cục
    STORE_TELLER = 'STORE_TELLER', // Giao dịch viên tại cửa hàng
  }

  export enum EStatus {
    INACTIVE = 'INACTIVE',
    ACTIVE = 'ACTIVE',
    WAITING_FOR_VERIFY = 'WAITING_FOR_VERIFY',
    DELETED = 'DELETED',
    WAITING_FOR_APPROVE = 'WAITING_FOR_APPROVE',
  }

  // Kế hoạch kiểm tra theo
  export enum EFrequency {
    PRODUCTION_QUANTITY = 'PRODUCTION_QUANTITY', // Số lượng sản phẩm
    BY_TIME = 'BY_TIME', // thời gian
  }

  // Loại kế hoạch
  export enum EPlanType {
    SAMPLE = 'SAMPLE',
    VISUAL_INSPECTION = 'VISUAL_INSPECTION',
  }

  // Loại <PERSON> sx
  export enum EProductOrderType {
    PRODUCT = 'PRODUCT', // Thành phẩm
    SEMI_PRODUCT = 'SEMI_PRODUCT', // Bán thành phẩm
  }
}
