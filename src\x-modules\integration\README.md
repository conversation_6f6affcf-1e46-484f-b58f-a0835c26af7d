# Integration Module (`integration`)

## Table of Contents

- [Integration Module (`integration`)](#integration-module-integration)
  - [Table of Contents](#table-of-contents)
  - [About ](#about-)
  - [Features ](#features-)
  - [Getting Started ](#getting-started-)
    - [Environment Variables ](#environment-variables-)
  - [Usage ](#usage-)
  - [## Directory Structure ](#-directory-structure-)
  - [Interaction Diagram](#interaction-diagram)

## About <a name="about"></a>

Th<PERSON> mục `integration` chứa các module tích hợp với hệ thống bên ngoài cho MES, như SCADA, IoT, ERP, AWS, và các dịch vụ bên thứ ba khác. <PERSON><PERSON><PERSON> tiêu là đồng bộ dữ liệu, xử lý sự kiện và đảm bảo luồng thông tin giữa MES và các hệ thống ngoại vi diễn ra liền mạch, an toàn.

Ví dụ:

- <PERSON><PERSON> c<PERSON> lệnh sản xuất mới trên MES, module này sẽ gửi thông tin qua API đến hệ thống ERP (SAP).
- <PERSON><PERSON> máy móc gửi tín hiệu cảnh báo lỗi qua IoT, hệ thống SCADA push về MES để tạo notification.

## Features <a name="features"></a>

- Tích hợp dữ liệu real-time từ SCADA và IoT.
- Kết nối với hệ thống ERP (ví dụ: SAP, Oracle).
- Đồng bộ hóa dữ liệu sản xuất, đơn hàng, tài nguyên, cảnh báo chất lượng.
- Hỗ trợ xác thực và bảo mật khi kết nối dịch vụ ngoài.
- Xử lý sự kiện và đồng bộ trạng thái qua message queue (AWS SQS, EventHub...).

## Getting Started <a name="getting_started"></a>

### Environment Variables <a name="environment_variables"></a>

Cấu hình các biến môi trường trong `.env` để kết nối các hệ thống tích hợp (ví dụ: SCADA_URL, ERP_API_KEY, ...).

## Usage <a name="usage"></a>

- Import các service từ module này để sử dụng trong các controller hoặc lambda function.
- Ví dụ:

```typescript
import { QualityNotificationService } from './integration/services/quality-notification/quality-notification.service';
import { POResourceTransactionERPIntegrationController } from './integration/controllers/erp/production-order-resource-transaction/production-order-resource-transaction.controller';
```

- Sử dụng các API endpoint hoặc service để lấy dữ liệu từ hệ thống ngoài, đồng bộ trạng thái, hoặc xử lý sự kiện.

## ## Directory Structure <a name="directory_structure"></a>

```
integration/
├── controllers/                        # Controller cho các API tích hợp
│   ├── index.ts                        # Đăng ký các controller
│   ├── data-calculation-by-job/        # Controller tính toán dữ liệu theo job
│   │   └── data-calculation-by-job.controller.ts
│   ├── erp/                            # Controller tích hợp ERP
│   │   ├── production-order/
│   │   │   └── production-order.controller.ts # API đơn sản xuất
│   │   └── production-order-resource-transaction/
│   │       └── production-order-resource-transaction.controller.ts # API tài nguyên sản xuất
│   ├── scale/                          # Controller tích hợp cân điện tử
│   │   └── scale.controller.ts
│   ├── schedule/                       # Controller lịch sản xuất
│   │   └── schedule.controller.ts
│   ├── sitewise/                       # Controller tích hợp AWS SiteWise
│   │   └── sitewise.controller.ts
│   └── zkt-face-id/                    # Controller tích hợp máy chấm công khuôn mặt
│       └── zkt-face-id.controller.ts
├── services/                           # Business logic tích hợp hệ thống ngoài
│   ├── index.ts                        # Đăng ký các service
│   ├── redis.adapter.ts                # Adapter Redis cho caching/tạm lưu dữ liệu khi tích hợp với hệ thống ngoài, giúp tăng hiệu năng truy xuất và giảm tải cho database
│   ├── socket.gateway.ts               # Gateway cho giao tiếp socket real-time (WebSocket), phục vụ việc push dữ liệu, sự kiện real-time giữa MES và các client/hệ thống ngoài
│   ├── calculate-kansui-and-seasoning/ # Tính toán định lượng Kansui & Seasoning cho sản xuất mì
│   │   └── calculate-kansui-and-seasoning.service.ts # Service xử lý logic tính toán Kansui & Seasoning
│   ├── calculate-utility-usage/        # Tính toán tiêu thụ utility (điện, nước, hơi, ...)
│   │   └── calculate-utility-usage.service.ts # Service xử lý logic tính toán utility
│   ├── call-proceduce/                 # Gọi các stored procedure trong database
│   │   └── call-proceduce.service.ts   # Service thực thi stored procedure
│   ├── manhour-kpi/                    # Tính toán KPI giờ công lao động
│   │   └── manhour-kpi.service.ts      # Service xử lý logic KPI giờ công
│   ├── master-data/                    # Đồng bộ và xử lý master data (danh mục, tham số chuẩn)
│   │   └── master-data.service.ts      # Service xử lý đồng bộ master data
│   ├── prod-batch-paramaters/          # Quản lý tham số batch sản xuất
│   │   └── prod-batch-paramaters.service.ts # Service xử lý tham số batch
│   ├── prod-batch-status/              # Quản lý trạng thái batch sản xuất
│   │   └── prod-batch-status.service.ts # Service xử lý trạng thái batch
│   ├── product-output-by-rejection/    # Thống kê sản lượng loại bỏ/phế phẩm
│   │   └── product-output-by-rejection.service.ts # Service xử lý thống kê loại bỏ
│   ├── production-oee/                 # Tính toán chỉ số OEE (hiệu suất thiết bị tổng thể)
│   │   └── production-oee.service.ts   # Service xử lý logic OEE
│   ├── production-order/               # Xử lý nghiệp vụ lệnh sản xuất
│   │   └── production-order.service.ts # Service xử lý lệnh sản xuất
│   └── production-order-material-transaction/ # Giao dịch nguyên vật liệu cho lệnh sản xuất
│       └── production-order-material-transaction.service.ts # Service xử lý giao dịch NVL
├── middlewares/                        # Middleware xử lý xác thực, logging, kiểm tra dữ liệu tích hợp
│   └── integration.middleware.ts       # Middleware tích hợp
├── index.ts                            # Khởi tạo module integration
├── integration-session.context.ts      # Context lưu session tích hợp
└── README.md                           # Tài liệu hướng dẫn module integration

```

---

## Interaction Diagram

![alt text](../../../docs/src/x-modules/integration/uml/uml1/uml1.png)
![alt text](../../../docs/src/x-modules/integration/uml/uml2/uml2.png)
![alt text](../../../docs/src/x-modules/integration/uml/uml3/uml3.png)
![alt text](../../../docs/src/x-modules/integration/uml/uml4/uml4.png)

```

> Sơ đồ trên mô tả luồng tương tác giữa hệ thống MES, module integration và các hệ thống bên ngoài.

```
