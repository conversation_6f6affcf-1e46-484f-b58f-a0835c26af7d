import { ApiProperty } from '@nestjs/swagger';

export enum ERPRecipeStatus {
  ObsoleteOrArchived = 'Obsolete/Archived',
  ApprovedForGeneralUse = 'Approved for General Use',
}

export class ERPCreateRecipeReq {
  /**
   * Mã công thức
   * @example 99PH00092-MGP
   */
  @ApiProperty({
    description: 'Mã công thức',
    example: '99PH00092-MGP',
  })
  RECIPE_NO: string;

  /**
   * Mô tả công thức
   * @example Gia vị mì Kokomi Tôm (Xuất khẩu) ( Điểu chỉnh công thức, line 3, bột mì xích lô)
   */
  @ApiProperty({
    description: 'Mô tả công thức',
    example: 'Gia vị mì Kokomi Tôm (Xuất khẩu) ( Điểu chỉnh công thức, line 3, bột mì xích lô)',
  })
  RECIPE_DESC: string;

  /**
   * Phiên bản công thức
   * @example 1
   */
  @ApiProperty({
    description: 'Phiên bản công thức',
    example: 1,
  })
  RECIPE_VER: number;

  /**
   * Trạng thái công thức
   * @example Approved for General Use
   */
  @ApiProperty({
    description: 'Trạng thái công thức',
    example: ERPRecipeStatus.ApprovedForGeneralUse,
    enum: ERPRecipeStatus,
  })
  RECIPE_STATUS: ERPRecipeStatus;

  /**
   * Đơn vị
   * @example MGP
   */
  @ApiProperty({
    description: 'Đơn vị',
    example: 'MGP',
  })
  ORGANIZATION: string;

  /**
   * Mã sản phẩm
   * @example 99PH00092
   */
  @ApiProperty({
    description: 'Mã sản phẩm',
    example: '99PH00092',
  })
  PRODUCT: string;

  /**
   * Mã công thức định lượng
   * @example 99PH00092-MGP
   */
  @ApiProperty({
    description: 'Mã công thức định lượng',
    example: '99PH00092-MGP',
  })
  FORMULA_NO: string;

  /**
   * Phiên bản công thức định lượng
   * @example 1
   */
  @ApiProperty({
    description: 'Phiên bản công thức định lượng',
    example: 1,
  })
  FORMULA_VERSION: number;

  /**
   * Mã quy trình sản xuất
   * @example MGPWP3L0202
   */
  @ApiProperty({
    description: 'Mã quy trình sản xuất',
    example: 'MGPWP3L0202',
  })
  ROUTING_NO: string;

  /**
   * Phiên bản quy trình sản xuất
   * @example 1
   */
  @ApiProperty({
    description: 'Phiên bản quy trình sản xuất',
    example: 1,
  })
  ROUTING_VERS: number;

  /**
   * Lượng sản xuất (kg)
   * @example 1272
   */
  @ApiProperty({
    description: 'Lượng sản xuất (kg)',
    example: 1272,
  })
  BATCH_SIZE: number;

  /**
   * Đơn vị lượng sản xuất (kg)
   * @example Kgs
   */
  @ApiProperty({
    description: 'Đơn vị lượng sản xuất (kg)',
    example: 'Kgs',
  })
  BATCH_SIZE_UOM: string;

  /**
   * Quy trình sản xuất
   */
  @ApiProperty({
    description: 'Quy trình sản xuất',
    type: () => [ERPCreateRecipeProcessReq],
  })
  PROCESS: ERPCreateRecipeProcessReq[];
}

/**
 * Quy trình sản xuất
 */
export class ERPCreateRecipeProcessReq {
  /**
   * Mã quy trình sản xuất
   * @example MGPWP3L0202
   */
  @ApiProperty({
    description: 'Mã quy trình sản xuất',
    example: 'MGPWP3L0202',
  })
  PROCESS: string;

  /**
   * Phiên bản quy trình sản xuất
   * @example 1
   */
  @ApiProperty({
    description: 'Phiên bản quy trình sản xuất',
    example: 1,
  })
  PROCESS_FORMULA_VERSION: number;

  /**
   * Thành phần nguyên liệu
   */
  @ApiProperty({
    description: 'Thành phần nguyên liệu',
    type: () => [ERPCreateRecipeIngredientReq],
  })
  Ingredient: ERPCreateRecipeIngredientReq[];

  /**
   * Sản phẩm
   */
  @ApiProperty({
    description: 'Sản phẩm',
    type: () => [ERPCreateRecipeProductReq],
  })
  Product: ERPCreateRecipeProductReq[];

  /**
   * Phụ phẩm
   */
  @ApiProperty({
    description: 'Phụ phẩm',
    type: () => [ERPCreateRecipeByProductReq],
  })
  By_Product: ERPCreateRecipeByProductReq[];

  /**
   * Nguồn lực
   */
  @ApiProperty({
    description: 'Nguồn lực',
    type: () => [ERPCreateRecipeResourceReq],
  })
  Resource: ERPCreateRecipeResourceReq[];
}

/**
 * Thành phần nguyên liệu
 */
export class ERPCreateRecipeIngredientReq {
  /**
   * Số thứ tự thành phần
   * @example 1
   */
  @ApiProperty({
    description: 'Số thứ tự thành phần',
    example: 1,
  })
  ING_LINE_NO: number;

  /**
   * Mã nguyên liệu
   * @example ABNC001
   */
  @ApiProperty({
    description: 'Mã nguyên liệu',
    example: 'ABNC001',
  })
  ING_NO: string;

  /**
   * Mô tả nguyên liệu
   * @example Nước chế biến thực phẩm
   */
  @ApiProperty({
    description: 'Mô tả nguyên liệu',
    example: 'Nước chế biến thực phẩm',
  })
  ING_DESCRIPTION: string;

  /**
   * Lượng nguyên liệu (kg)
   * @example 1200
   */
  @ApiProperty({
    description: 'Lượng nguyên liệu (kg)',
    example: 1200,
  })
  ING_QUANTITY: number;

  /**
   * Đơn vị lượng nguyên liệu (kg)
   * @example Kgs
   */
  @ApiProperty({
    description: 'Đơn vị lượng nguyên liệu (kg)',
    example: 'Kgs',
  })
  ING_UOM_CODE: string;
}

/**
 * Sản phẩm
 */
export class ERPCreateRecipeProductReq {
  /**
   * Số thứ tự sản phẩm
   * @example 1
   */
  @ApiProperty({
    description: 'Số thứ tự sản phẩm',
    example: 1,
  })
  Product_LINE_NO: number;

  /**
   * Mã sản phẩm
   * @example 99PH00092
   */
  @ApiProperty({
    description: 'Mã sản phẩm',
    example: '99PH00092',
  })
  Product_NO: string;

  /**
   * Mô tả sản phẩm
   * @example Gia vị mì Kokomi Tôm (Xuất khẩu)
   */
  @ApiProperty({
    description: 'Mô tả sản phẩm',
    example: 'Gia vị mì Kokomi Tôm (Xuất khẩu)',
  })
  Product_DESCRIPTION: string;

  /**
   * Lượng sản phẩm (kg)
   * @example 1272
   */
  @ApiProperty({
    description: 'Lượng sản phẩm (kg)',
    example: 1272,
  })
  Product_QUANTITY: number;

  /**
   * Đơn vị lượng sản phẩm (kg)
   * @example Kgs
   */
  @ApiProperty({
    description: 'Đơn vị lượng sản phẩm (kg)',
    example: 'Kgs',
  })
  Product_UOM_CODE: string;
}

/**
 * Phụ phẩm
 */
export class ERPCreateRecipeByProductReq {
  /**
   * Số thứ tự phụ phẩm
   * @example 1
   */
  @ApiProperty({
    description: 'Số thứ tự phụ phẩm',
    example: 1,
  })
  By_Product_LINE_NO: number;

  /**
   * Mã phụ phẩm
   * @example 99PH00092
   */
  @ApiProperty({
    description: 'Mã phụ phẩm',
    example: '99PH00092',
  })
  By_Product_NO: string;

  /**
   * Mô tả phụ phẩm
   * @example Gia vị mì Kokomi Tôm (Xuất khẩu)
   */
  @ApiProperty({
    description: 'Mô tả phụ phẩm',
    example: 'Gia vị mì Kokomi Tôm (Xuất khẩu)',
  })
  By_Product_DESCRIPTION: string;

  /**
   * Lượng phụ phẩm (kg)
   * @example 1272
   */
  @ApiProperty({
    description: 'Lượng phụ phẩm (kg)',
    example: 1272,
  })
  By_Product_QUANTITY: number;

  /**
   * Đơn vị lượng phụ phẩm (kg)
   * @example Kgs
   */
  @ApiProperty({
    description: 'Đơn vị lượng phụ phẩm (kg)',
    example: 'Kgs',
  })
  By_Product_UOM_CODE: string;
}

/**
 * Nguồn lực
 */
export class ERPCreateRecipeResourceReq {
  /**
   * Mã nguồn lực
   * @example LB033
   */
  @ApiProperty({
    description: 'Mã nguồn lực',
    example: 'LB033',
  })
  RESOURCES: string;

  /**
   * Mô tả nguồn lực
   * @example Nhân công trực tiếp
   */
  @ApiProperty({
    description: 'Mô tả nguồn lực',
    example: 'Nhân công trực tiếp',
  })
  RESOURCE_DESC: string;

  /**
   * Lượng nguồn lực sử dụng
   * @example 1
   */

  @ApiProperty({
    description: 'Lượng nguồn lực sử dụng',
    example: 1,
  })
  RESOURCE_USAGE: number;

  /**
   * Đơn vị nguồn lực sử dụng
   * @example Kgs
   */
  @ApiProperty({
    description: 'Đơn vị nguồn lực sử dụng',
    example: 'Kgs',
  })
  RESOURCE_USAGE_UOM: string;
}

export class ERPCreateRecipeResponse {
  /**
   * Mã công thức
   * @example 99PH00092-MGP
   */
  @ApiProperty({
    description: 'Mã công thức',
    example: '99PH00092-MGP',
  })
  RECIPE_NO: string;
 /**
  * Phiên bản công thức
  * @example 1
  */
  @ApiProperty({
    description: 'Phiên bản công thức',
    example: 1,
  })
  RECIPE_VER: number;
}