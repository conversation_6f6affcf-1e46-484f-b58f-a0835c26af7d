import { Body, Param, Query, UseGuards } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { response } from 'express';
import { DefController, DefGet, DefPost, DefPut } from '~/@core/decorator';
import { RejectionMonitoringService } from '../../services';
import {
  ListProductionRejection,
  ProductionRejectionReq,
  RejectionMonitoringReq,
} from '~/dto/rejection-monitoring.dto';
import { Roles } from '~/@core/decorator/roles.decorator';
import { RoleGuard } from '~/@systems/guard/role.guard';

@ApiTags('Rejection Monitoring')
@DefController('rejection-monitoring')
export class RejectionMonitoringController {
  constructor(private readonly service: RejectionMonitoringService) {}

  @ApiOperation({ summary: 'Tạo production rejection' })
  @DefGet('')
  @Roles('/production-execution/rejection-monitoring', 'View')
  @UseGuards(RoleGuard)
  async list(@Query() params: ListProductionRejection) {
    return this.service.pagination(params);
  }

  @ApiOperation({ summary: 'Tạo production rejection' })
  @DefPost('')
  @Roles('/production-execution/rejection-monitoring', 'View')
  @UseGuards(RoleGuard)
  async create(@Body() body: ProductionRejectionReq) {
    return this.service.create(body);
  }

  @ApiOperation({ summary: 'Danh sách rejection monitoring' })
  @DefGet('list-rejection-monitoring')
  @Roles('/production-execution/rejection-monitoring', 'View')
  @UseGuards(RoleGuard)
  async listRejectionMonitoring(@Query() params: RejectionMonitoringReq) {
    return this.service.listRejectionMonitoring(params);
  }

  @ApiOperation({ summary: 'Danh sách options' })
  @DefGet('get-options/:userId')
  @Roles('/production-execution/rejection-monitoring', 'View')
  @UseGuards(RoleGuard)
  async getOptions(@Param('userId') id: string, @Query('levelCode') levelCode: string) {
    return this.service.getOptions(levelCode, id);
  }

  @ApiOperation({ summary: 'Cập nhật production rejection' })
  @DefPut(':id')
  @Roles('/production-execution/rejection-monitoring', 'View')
  @UseGuards(RoleGuard)
  async update(@Param('id') id: string, @Body() body: ProductionRejectionReq) {
    return this.service.update(id, body);
  }

  @ApiOperation({ summary: 'Danh sách load-process-area' })
  @DefGet('load-process-area')
  @Roles('/production-execution/rejection-monitoring', 'View')
  @UseGuards(RoleGuard)
  async loadProcessArea(@Query('lineId') lineId: string) {
    return this.service.loadProcessArea(lineId);
  }
  @ApiOperation({ summary: 'Danh sách load-production-order' })
  @DefGet('load-production-order')
  @Roles('/production-execution/rejection-monitoring', 'View')
  @UseGuards(RoleGuard)
  async loadProductionOrder() {
    return this.service.loadProductionOrder();
  }
  @ApiOperation({ summary: 'Danh sách load-shift' })
  @DefGet('load-shift')
  @Roles('/production-execution/rejection-monitoring', 'View')
  @UseGuards(RoleGuard)
  async loadShift() {
    return this.service.loadShift();
  }
  @ApiOperation({ summary: 'Danh sách load-transaction-type' })
  @DefGet('load-transaction-type')
  @Roles('/production-execution/rejection-monitoring', 'View')
  @UseGuards(RoleGuard)
  async loadTransactionType() {
    return this.service.loadTransactionType();
  }
  @ApiOperation({ summary: 'Danh sách load-item' })
  @DefGet('load-item')
  @Roles('/production-execution/rejection-monitoring', 'View')
  @UseGuards(RoleGuard)
  async loadItem() {
    return this.service.loadItem();
  }
  @ApiOperation({ summary: 'Danh sách load-uom' })
  @DefGet('load-uom')
  @Roles('/production-execution/rejection-monitoring', 'View')
  @UseGuards(RoleGuard)
  async loadUom() {
    return this.service.loadUom();
  }
  @ApiOperation({ summary: 'Danh sách load-rejection-reason' })
  @DefGet('load-rejection-reason')
  @Roles('/production-execution/rejection-monitoring', 'View')
  @UseGuards(RoleGuard)
  async loadRejectionReason() {
    return this.service.loadRejectionReason();
  }

  @ApiOperation({ summary: 'Danh sách assign shift' })
  @DefGet('load-assign-shift-by-factoryId')
  @Roles('/production-execution/rejection-monitoring', 'View')
  @UseGuards(RoleGuard)
  async loadAssignShiftByFactoryId(@Query('factoryId') factoryId: string) {
    return this.service.loadAssignShiftByFactoryId(factoryId);
  }

  @ApiOperation({ summary: 'Tạo production rejection' })
  @DefGet(':id')
  @Roles('/production-execution/rejection-monitoring', 'View')
  @UseGuards(RoleGuard)
  async detail(@Param('id') id: string) {
    return this.service.detail(id);
  }
}
