{"name": "vix-trade-point", "version": "0.0.1", "description": "", "author": "", "private": true, "license": "UNLICENSED", "scripts": {"start": "nest start", "dev": "nest start --watch", "debug": "nest start --debug --watch", "build": "nest build", "clean": "<PERSON><PERSON><PERSON> dist", "format": "prettier --write src/", "lint": "eslint \"{src,apps,libs,test}/**/*.ts\" --fix", "test": "jest", "test:watch": "jest --watch", "test:cov": "jest --coverage", "test:debug": "node --inspect-brk -r tsconfig-paths/register -r ts-node/register node_modules/.bin/jest --runInBand", "test:e2e": "jest --config ./test/jest-e2e.json", "make-index": "node ./scripts/index.js", "make-index-enums": "yarn make-index src/common/enums", "make-index-entity-primary": "yarn make-index src/entities/primary", "make-index-repo-primary": "yarn make-index src/repositories/primary", "make-index-all": "yarn make-index-enums & yarn make-index-entity-primary & yarn make-index-repo-primary", "local": "npm run build && sls offline start", "deploy": "npm run build && sls deploy", "deploy-prod": "npm run build && sls deploy --stage prod", "deploy:function": "npm run build && sls deploy function -f"}, "dependencies": {"@aws-sdk/client-sns": "^3.787.0", "@aws-sdk/client-sqs": "^3.787.0", "@azure/event-hubs": "^6.0.0", "@nestjs/cache-manager": "^2.3.0", "@nestjs/common": "^10.0.0", "@nestjs/config": "^3.2.3", "@nestjs/core": "^10.0.0", "@nestjs/jwt": "^10.2.0", "@nestjs/platform-express": "^10.0.0", "@nestjs/platform-socket.io": "^11.0.11", "@nestjs/schedule": "^4.1.1", "@nestjs/swagger": "^7.3.1", "@nestjs/typeorm": "8.0.1", "@nestjs/websockets": "^11.0.11", "@socket.io/redis-adapter": "^8.3.0", "@types/jwk-to-pem": "^2.0.3", "@types/multer": "^1.4.12", "aws-lambda": "^1.0.7", "aws-sdk": "^2.1692.0", "axios": "^1.8.3", "bcrypt": "^5.1.1", "cache-manager": "^4", "class-transformer": "^0.5.1", "class-validator": "^0.14.1", "class-validator-jsonschema": "^5.0.1", "dayjs": "^1.11.13", "exceljs": "^4.4.0", "extensionsjs": "^1.1.7", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "jszip": "^3.10.1", "jwk-to-pem": "^2.0.7", "lodash": "^4.17.21", "moment": "^2.30.1", "moment-timezone": "^0.5.48", "nanoid": "3.3.4", "nestjs-i18n": "^10.5.0", "papaparse": "^5.5.3", "pg": "^8.13.0", "redis": "^4.7.0", "reflect-metadata": "^0.2.0", "rxjs": "^7.8.1", "sqs-consumer": "^11.6.0", "typeorm": "0.2.34", "typeorm-transactional-cls-hooked": "^0.1.21", "xlsx": "^0.18.5"}, "devDependencies": {"@nestjs/cli": "^10.0.0", "@nestjs/schematics": "^10.0.0", "@nestjs/testing": "^10.0.0", "@types/aws-lambda": "^8.10.147", "@types/bcrypt": "^5.0.2", "@types/express": "^4.17.17", "@types/jest": "^29.5.2", "@types/lodash": "^4.17.18", "@types/node": "^20.3.1", "@types/supertest": "^6.0.0", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint": "^8.42.0", "eslint-config-prettier": "^9.0.0", "eslint-plugin-prettier": "^5.0.0", "handlebars": "^4.7.8", "jest": "^29.5.0", "prettier": "^3.0.0", "serverless-jetpack": "^0.11.2", "serverless-offline": "^14.4.0", "serverless-plugin-optimize": "^4.2.1-rc.1", "source-map-support": "^0.5.21", "supertest": "^6.3.3", "ts-jest": "^29.1.0", "ts-loader": "^9.4.3", "ts-node": "^10.9.1", "tsconfig-paths": "^4.2.0", "typescript": "^5.1.3"}, "jest": {"moduleFileExtensions": ["js", "json", "ts"], "rootDir": "src", "testRegex": ".*\\.spec\\.ts$", "transform": {"^.+\\.(t|j)s$": "ts-jest"}, "collectCoverageFrom": ["**/*.(t|j)s"], "coverageDirectory": "../coverage", "testEnvironment": "node"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}