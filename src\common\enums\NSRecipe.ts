export namespace NSRecipe {
  export enum RecipeProcessItemTypeCode {
    Ingredient = 'ING',
    Product = 'PRO',
    ByProduct = 'BY_PRO',
  }

  export enum RecipeStatus {
    ObsoleteOrArchived = 'OBSOLETE_OR_ARCHIVED',
    ApprovedForGeneralUse = 'APPROVED_FOR_GENERAL_USE',
  }

  export enum RecipeRelations {
    Product = 'product',
    OrganizationUnit = 'organizationUnit',
    RecipeProcesses = 'recipeProcesses',
    RecipeProcess = 'recipeProcesses.process',
    RecipeResource = 'recipeProcesses.recipeResources',
    RecipeProcessItem = 'recipeProcesses.recipeProcessItems',
  }
}