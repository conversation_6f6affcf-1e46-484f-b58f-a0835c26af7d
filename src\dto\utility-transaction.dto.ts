import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsBoolean, IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';
import { PageRequest } from '~/@systems/utils';

export class UtilityMonitoringReq extends PageRequest {
  /** Organization Unit level SITE */
  @ApiPropertyOptional({ description: 'Site ID' })
  @IsOptional()
  siteId?: string;

  /** Organization Unit level FACTORY */
  @ApiPropertyOptional({ description: 'Factory ID' })
  @IsOptional()
  factoryId?: string;

  /** Utility meter type */
  @ApiProperty({ description: 'Production Line ID' })
  @IsOptional()
  type?: string;

  /** Utility meter code */
  @ApiProperty({ description: 'Meter Code' })
  @IsOptional()
  meterCode?: Date;

  /** Utility meter name */
  @ApiProperty({ description: 'Meter Name' })
  @IsOptional()
  meterName?: Date;
}

export class UtilityTransactionReq {
  @ApiProperty({ description: '<PERSON><PERSON>y giao dịch' })
  @IsNotEmpty()
  date: Date;

  @ApiProperty({ description: 'Shift ID' })
  @IsNotEmpty()
  shiftId: string;

  @ApiProperty({ description: 'Meter ID' })
  @IsNotEmpty()
  meterId: string;

  @ApiProperty({ description: 'Giá trị' })
  @IsNotEmpty()
  value: number;

  @ApiProperty({ description: 'Posted' })
  @IsOptional()
  posted?: boolean;
}

export class ListUtilityTransaction extends PageRequest {
  @ApiProperty({ description: 'Factory ID' })
  @IsOptional()
  factoryId?: string;

  @ApiProperty({ description: 'Type ID' })
  @IsOptional()
  type?: string;

  @ApiProperty({ description: 'Start Time' })
  @IsOptional()
  startTime?: Date;

  @ApiProperty({ description: 'End Time' })
  @IsOptional()
  endTime?: Date;
}
