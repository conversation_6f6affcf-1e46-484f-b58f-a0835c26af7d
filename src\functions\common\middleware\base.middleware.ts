const withMiddlewares =
  (handler: any, middlewares = []) =>
  (event: any, context: any, callback: (arg0: null, arg1: null) => void) => {
    const chainMiddlewares = (middlewareArray: any[]) => {
      const [firstMiddleware, ...restOfMiddlewares] = middlewareArray;
      if (firstMiddleware) {
        return (e: any, c: any) => {
          try {
            return firstMiddleware(e, c, chainMiddlewares(restOfMiddlewares));
          } catch (error) {
            return Promise.reject(error);
          }
        };
      }

      return handler;
    };

    return chainMiddlewares(middlewares)(event, context)
      .then((result: any) => result)
      .catch((err: any) => err);
  };

export default withMiddlewares;
