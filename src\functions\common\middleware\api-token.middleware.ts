import { APIGatewayProxyEvent, Context } from 'aws-lambda';
import { ApiTokenService } from '~/x-modules/admin/services';
import { appInstanceProvider } from '../provider/app-instance.provider';
import { HttpStatus } from '@nestjs/common';
import { EHttpStatusMessage, HttpMethod } from '~/@core/network';

async function bootstrap() {
  return appInstanceProvider.getAppInstance();
}

export const ApiTokenMiddleware = () => {
  return async (event: APIGatewayProxyEvent, context: Context, next) => {
    // Before Logic
    const token = event.headers['x-header'] || event.headers['X-Header'];
    const app = await bootstrap();
    const apiTokenService = app.get(ApiTokenService);
    console.log('event.path', event.path);
    console.log('event.httpMethod', event.httpMethod);

    let isValidToken = false;
    if (token?.length) {
      isValidToken = await apiTokenService.check(event.path, event.httpMethod as HttpMethod, token);
    }

    if (!token || !isValidToken) {
      return {
        statusCode: HttpStatus.UNAUTHORIZED,
        body: JSON.stringify({
          message: EHttpStatusMessage[HttpStatus.UNAUTHORIZED],
          errors: [
            { property: 'x-header', constraints: { invalid: 'Token is missing or invalid.' } },
          ],
          code: HttpStatus.UNAUTHORIZED,
        }),
      };
    }

    return next(event, context)
      .then((result) => {
        // After Logic
        return result;
      })
      .catch((error) => {
        // Error Handling
        return error;
      });
  };
};
