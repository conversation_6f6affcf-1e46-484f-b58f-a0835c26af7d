import { Injectable } from '@nestjs/common';
import { Between, In, IsNull, Not } from 'typeorm';
import { BindRepo } from '~/@core/decorator';
import { ListProductionDowntime, ProductionDowntimeReq } from '~/dto/production-downtime.dto';
import {
  MachineRepo,
  OrganizationUnitRepo,
  ProcessMachineRepo,
  ProcessRepo,
  ProductionDowntimeRepo,
  ReasonMasterRepo,
} from '~/repositories/primary';
import { adminSessionContext } from '../../admin-session.context';
import dayjs from 'dayjs';
@Injectable()
export class DowntimeMonitoringService {
  constructor() {}

  @BindRepo(ProductionDowntimeRepo)
  private readonly repo: ProductionDowntimeRepo;

  @BindRepo(OrganizationUnitRepo)
  private readonly organizationUnitRepo: OrganizationUnitRepo;

  @BindRepo(MachineRepo)
  private readonly machineRepo: MachineRepo;

  @BindRepo(ProcessMachineRepo)
  private readonly processMachineRepo: ProcessMachineRepo;

  @BindRepo(ProcessRepo)
  private readonly processRepo: ProcessRepo;

  @BindRepo(ReasonMasterRepo)
  private readonly reasonMasterRepo: ReasonMasterRepo;

  async pagination(params: ListProductionDowntime) {
    if (!params.factoryId)
      return {
        data: [],
        total: 0,
      };
    const whereCon: any = {};
    const whereLine: any = {};

    const factory = await this.organizationUnitRepo.findOne({ where: { id: params.factoryId } });
    if (!factory) throw new Error('Not Found Factory');
    whereLine.parentId = factory.id;
    if (params.lineId) whereLine.id = params.lineId;
    const lstLine = await this.organizationUnitRepo.find({ where: whereLine });
    const lstProcessArea = await this.organizationUnitRepo.find({
      where: { parentId: In(lstLine.map((i) => i.id)) },
    });
    whereCon.processAreaId = In(lstProcessArea.map((i) => i.id));
    if (params.start && params.end) {
      const startOfDay = dayjs(params.start).startOf('day').toDate();
      const endOfDay = dayjs(params.end).endOf('day').toDate();

      whereCon.startTime = Between(startOfDay, endOfDay);
    }
    if (params.endTime == 'null') {
      whereCon.endTime = null;
    }
    if (params.cancelledFlag !== undefined) whereCon.cancelledFlag = params.cancelledFlag;
    const result: any = await this.repo.findPagination(
      { where: whereCon, order: { startTime: 'DESC', createdDate: 'DESC' } },
      { pageSize: params.pageSize, pageIndex: params.pageIndex },
    );
    const rs = await this.repo.find({
      where: whereCon,
      order: { startTime: 'DESC', createdDate: 'DESC' },
    });
    let totalDuration = 0;

    rs.forEach((item: any) => {
      const durationStr =
        item.endTime !== null
          ? this.duration(dayjs(item.startTime), dayjs(item.endTime))
          : this.duration(dayjs(item.startTime), dayjs());

      item.duration = durationStr;

      const [hours = 0, minutes = 0] = (durationStr?.split(':') ?? []).map(Number);
      totalDuration += hours * 60 + minutes;
    });

    // Convert back to HH:mm if needed
    const totalHours = Math.floor(totalDuration / 60);
    const totalMinutes = totalDuration % 60;
    const totalDurationStr = `${totalHours.toString().padStart(2, '0')}:${totalMinutes
      .toString()
      .padStart(2, '0')}`;

    const dictMachine: any = {};
    const dictProcess: any = {};
    {
      const lstMachine = await this.machineRepo.find({
        where: { id: In(result.data.map((i) => i.machineId)), isActive: true },
      });
      const lstProcessMachine = await this.processMachineRepo.find({
        where: { machineId: In(lstMachine.map((i) => i.id)) },
      });
      const lstProcess = await this.processRepo.find({
        where: { id: In(lstProcessMachine.map((i) => i.processId)), isActive: true },
      });
      lstMachine.forEach((i) => {
        const process = lstProcess.find(
          (y) => y.id === lstProcessMachine.find((o) => o.machineId === i.id).processId,
        );
        if (process) dictProcess[i.id] = process;
      });
      lstMachine.forEach((i) => (dictMachine[i.id] = i));
    }
    const dictProcessArea: any = {};
    const dictLine: any = {};
    const dictReason: any = {};
    {
      const lstProcessArea = await this.organizationUnitRepo.find({
        where: { id: In(result.data.map((i) => i.processAreaId)), isActive: true },
      });
      lstProcessArea.forEach((i) => (dictProcessArea[i.id] = i.code + ' - ' + i.name));

      const lstLine = await this.organizationUnitRepo.find({
        where: { id: In(lstProcessArea.map((i) => i.parentId)), isActive: true },
      });
      lstProcessArea.forEach(
        (i: any) => (dictLine[i.id] = lstLine.find((y) => y.id === i.parentId)),
      );
      const lstReason = await this.reasonMasterRepo.find({
        where: { code: In(result.data.map((i) => i.reasonCode)) },
      });
      lstReason.forEach((i) => (dictReason[i.code] = i));
    }

    result.data.forEach((item: any) => {
      const machine = dictMachine[item.machineId];
      item.machineName = machine.code + ' - ' + machine.name;
      item.machineLabelName = machine.name;
      item.machineId = machine.id;
      const reason = dictReason[item.reasonCode];
      if (reason) item.reasonName = reason.code + ' - ' + reason.name;
      item.processAreaName = dictProcessArea[item.processAreaId];
      const line = dictLine[item.processAreaId];
      if (line) item.lineName = line.code + ' - ' + line.name;
      item.lineId = line.id;
      const process = dictProcess[item.machineId];
      if (process) {
        item.processName = process.code + ' - ' + process.name;
        item.processId = process.id;
      }
      item.reasonId = dictReason[item.reasonCode]?.id || null;
      if (item.endTime !== null) {
        item.duration = this.duration(dayjs(item.startTime), dayjs(item?.endTime));
      } else {
        item.duration = this.duration(dayjs(item.startTime), dayjs());
      }
    });
    result.totalDuration = totalDurationStr;
    return result;
  }

  duration = (start, end) => {
    if (start && end && end.isSameOrAfter(start)) {
      const diff = end.diff(start, 'minute');
      const hours = String(Math.floor(diff / 60)).padStart(2, '0');
      const minutes = String(diff % 60).padStart(2, '0');
      return `${hours}:${minutes}`;
    } else {
      return;
    }
  };

  async create(body: ProductionDowntimeReq) {
    const { userId: userId } = adminSessionContext;

    const today = new Date();
    const machine = await this.machineRepo.findOne({
      where: { id: body.machineId, isActive: true },
    });
    if (!machine) throw new Error('Not Found Machine');
    const processArea = await this.organizationUnitRepo.findOne({
      where: { id: body.processAreaId, isActive: true },
    });
    if (!processArea) throw new Error('Not Found Process Area');
    let reason;
    if (body.reasonId) {
      reason = await this.reasonMasterRepo.findOne({
        where: { id: body.reasonId, isActive: true },
      });
      if (!reason) throw new Error('Not Found Reason');
    }
    const { reasonId, ...entity } = body;
    const data = await this.repo.save({
      ...entity,
      machineCode: machine.code,
      reasonCode: reason?.code || null,
      reasonDescription: reason?.description || null,
      createdBy: userId,
      createdDate: today,
      updatedBy: userId,
      updatedDate: today,
    });
    return {
      message: 'Successfully',
      data: data,
    };
  }
  async update(id: string, body: ProductionDowntimeReq) {
    const { userId: userId } = adminSessionContext;
    const today = new Date();
    const data = await this.repo.findOne({ where: { id: id } });
    if (!data) throw new Error('Not Found Production Downtime');
    const machine = await this.machineRepo.findOne({
      where: { id: body.machineId, isActive: true },
    });
    if (!machine) throw new Error('Not Found Machine');
    const processArea = await this.organizationUnitRepo.findOne({
      where: { id: body.processAreaId, isActive: true },
    });
    if (!processArea) throw new Error('Not Found Process Area');
    let reason;
    if (body.reasonId) {
      reason = await this.reasonMasterRepo.findOne({
        where: { id: body.reasonId, isActive: true },
      });
      if (!reason) throw new Error('Not Found Reason');
    }
    const { reasonId, ...entity } = body;

    const update = await this.repo.update(
      { id: id },
      {
        ...entity,
        endTime: body.endTime ? body.endTime : null,
        machineCode: machine.code,
        reasonCode: reason?.code || null,
        reasonDescription: reason?.description || null,
        updatedBy: userId,
        updatedDate: today,
      },
    );
    return {
      message: 'Successfully',
      data: update,
    };
  }

  async getProcessArea(factoryId: string) {
    const lstLine = await this.organizationUnitRepo.find({
      where: { parentId: factoryId, levelGeneralDataDetailCode: 'LINE', isActive: true },
    });
    return await this.organizationUnitRepo.find({
      where: { parentId: In(lstLine.map((i) => i.id)), isActive: true },
    });
  }
  async getProcess(factoryId: string) {
    const lstLine = await this.organizationUnitRepo.find({
      where: { parentId: factoryId, isActive: true },
    });
    const lstLineId = lstLine.map((i) => i.id);
    const lstProcessArea = await this.organizationUnitRepo.find({
      where: { parentId: In(lstLineId), isActive: true },
      select: ['id'],
    });
    const lstProcessAreaId = lstProcessArea.map((i) => i.id);
    return await this.processRepo.find({
      where: { organizationUnitId: In(lstProcessAreaId), isActive: true },
    });
  }
  async getMachine(factoryId: string) {
    const lstLine = await this.organizationUnitRepo.find({
      where: { parentId: factoryId, isActive: true },
      select: ['id'],
    });
    const lstLineId = lstLine.map((i) => i.id);
    const lstProcessArea = await this.organizationUnitRepo.find({
      where: { parentId: In(lstLineId), isActive: true },
      select: ['id'],
    });
    const lstProcessAreaId = lstProcessArea.map((i) => i.id);
    const lstProcess = await this.processRepo.find({
      where: { organizationUnitId: In(lstProcessAreaId), isActive: true },
      select: ['id'],
    });
    const lstProcessId = lstProcess.map((i) => i.id);
    const lstProcessMachine = await this.processMachineRepo.find({
      where: { processId: In(lstProcessId) },
      select: ['id', 'processId', 'machineId'],
    });

    const lstProcessMachineId = lstProcessMachine.map((i) => i.machineId);
    const lstMachine: any = await this.machineRepo.find({
      where: { id: In(lstProcessMachineId), isActive: true },
    });
    lstProcessMachine.forEach((i) => {
      lstMachine.find((item: any) => item.id === i.machineId).processId = i.processId || '';
    });
    return lstMachine;
  }

  async getReason() {
    return await this.reasonMasterRepo.find({ where: { isActive: true } });
  }
}
