import { Column, Entity, Index, JoinColumn, ManyToOne, OneToMany } from 'typeorm';
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { PrimaryBaseEntity } from '~/entities/primary-base.entity';
import { OrganizationUnitEntity } from '~/entities/primary';
import { StationDetailEntity } from '~/entities/primary/station-detail.entity';

@Entity('station')
export class StationEntity extends PrimaryBaseEntity {
  /** Mã duy nhất của Station */
  @ApiProperty({ description: 'Mã Station' })
  @Column({ unique: true })
  @Index()
  code: number;

  /** Mô tả Station */
  @ApiPropertyOptional({ description: 'Mô tả Station' })
  @Column({ type: 'text', nullable: true })
  description?: string;

  /** Mã GeneralData của Station Type. */
  @ApiPropertyOptional({ description: 'Mã GeneralData của Station Type' })
  @Column({})
  typeGenCode: string;

  /**  Mã GeneralDataDetail của  StationType. */
  @ApiPropertyOptional({ description: 'Mã GeneralDataDetail của Station Type' })
  @Column({})
  typeCode: string;

  /** ID của Site */
  @ApiPropertyOptional({ description: 'ID của Site' })
  @Column({ nullable: true })
  siteId?: string;
  @ManyToOne(() => OrganizationUnitEntity, (e) => e.id)
  @JoinColumn({ name: 'siteId', referencedColumnName: 'id' })
  site?: Promise<OrganizationUnitEntity>;

  /** ID của Factory */
  @ApiPropertyOptional({ description: 'ID của Factory' })
  @Column({ nullable: true })
  factoryId?: string;
  @ManyToOne(() => OrganizationUnitEntity, (e) => e.id)
  @JoinColumn({ name: 'factoryId', referencedColumnName: 'id' })
  factory?: Promise<OrganizationUnitEntity>;

  /** ID của Line */
  @ApiProperty({ description: 'ID của Line' })
  @Column({ nullable: true })
  lineId?: string;
  @ManyToOne(() => OrganizationUnitEntity, (e) => e.id)
  @JoinColumn({ name: 'lineId', referencedColumnName: 'id' })
  line?: Promise<OrganizationUnitEntity>;

  /** ID của Process */
  @ApiProperty({ description: 'ID của Process' })
  @Column({ nullable: true })
  processId?: string;
  @ManyToOne(() => OrganizationUnitEntity, (e) => e.id)
  @JoinColumn({ name: 'processId', referencedColumnName: 'id' })
  process?: Promise<OrganizationUnitEntity>;

  /** Trạng thái, mặc định true  */
  @ApiProperty({ description: 'Trạng thái', example: true })
  @Column({ default: true })
  isActive: boolean;

  @ApiProperty({ description: 'Ghi chú' })
  @Column({ type: 'text', nullable: true })
  note?: string;

  @OneToMany(() => StationDetailEntity, (e) => e.station)
  assigns?: Promise<StationDetailEntity[]>;
}
