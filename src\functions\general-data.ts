import { NestFactory } from '@nestjs/core';
import { Context, Handler } from 'aws-lambda';
import { AppModule } from '../app.module';
import { GeneralDataService } from '../x-modules/admin/services/system-configuration/general-data.service';
let cachedApp: any = null;

async function bootstrap() {
  if (!cachedApp) {
    const app = await NestFactory.createApplicationContext(AppModule);
    await app.init();
    cachedApp = app;
  }
  return cachedApp;
}

// Helper function to create response
function createResponse(statusCode: number, body: any) {
  return {
    statusCode,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Credentials': true,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(body),
  };
}

// Create general data
export const create: Handler = async (event: any, context: Context) => {
  try {
    console.log('Received create request:', event.body);

    // Parse request body
    const body = JSON.parse(event.body || '{}');
    console.log('Parsed request body:', body);

    // Initialize app and get service
    const app = await bootstrap();
    const generalDataService = app.get(GeneralDataService);
    console.log('Got GeneralDataService');

    // Call service
    const result = await generalDataService.create(body);
    console.log('Service returned result:', result);

    return createResponse(201, result);
  } catch (error) {
    console.error('Error creating general data:', error);
    console.error('Error stack:', error.stack);
    return createResponse(500, { message: error.message || 'Internal server error' });
  }
};

// Get all general data with pagination
export const list: Handler = async (event: any, context: Context) => {
  try {
    console.log('Received list request with query params:', event.queryStringParameters);

    // Parse query parameters
    const queryParams = event.queryStringParameters || {};
    const pageSize = parseInt(queryParams.pageSize) || 10;
    const pageIndex = parseInt(queryParams.pageIndex) || 1;
    const code = queryParams.code;
    const name = queryParams.name;
    const isActive = queryParams.isActive === 'true';
    const description = queryParams.description;
    const note = queryParams.note;

    // Build filter object
    const filter: any = { pageSize, pageIndex };
    if (code) filter.code = code;
    if (name) filter.name = name;
    if (description) filter.description = description;
    if (note) filter.note = note;
    if (queryParams.isActive !== undefined) filter.isActive = isActive;

    const app = await bootstrap();
    const generalDataService = app.get(GeneralDataService);
    console.log('Got GeneralDataService');

    const result = await generalDataService.list(filter);
    console.log('Service returned result:', result);

    return createResponse(200, result);
  } catch (error) {
    console.error('Error listing general data:', error);
    console.error('Error stack:', error.stack);
    return createResponse(500, { message: error.message || 'Internal server error' });
  }
};

// Get all general data without pagination
export const findAll: Handler = async (event: any, context: Context) => {
  try {
    console.log('Received findAll request with query params:', event.queryStringParameters);

    // Parse query parameters for filtering
    const queryParams = event.queryStringParameters || {};
    const code = queryParams.code;
    const name = queryParams.name;
    const isActive = queryParams.isActive === 'true';
    const description = queryParams.description;
    const note = queryParams.note;

    // Build filter object
    const filter: any = {};
    if (code) filter.code = code;
    if (name) filter.name = name;
    if (description) filter.description = description;
    if (note) filter.note = note;
    if (queryParams.isActive !== undefined) filter.isActive = isActive;

    const app = await bootstrap();
    const generalDataService = app.get(GeneralDataService);
    console.log('Got GeneralDataService');

    // We'll need to add a findAll method to the service
    const result = await generalDataService.findAll(filter);
    console.log('Service returned result:', result);

    return createResponse(200, result);
  } catch (error) {
    console.error('Error finding all general data:', error);
    console.error('Error stack:', error.stack);
    return createResponse(500, { message: error.message || 'Internal server error' });
  }
};

// Get general data by ID
export const findOne: Handler = async (event: any, context: Context) => {
  try {
    const id = event.pathParameters?.id;
    console.log('Received findOne request for id:', id);

    const app = await bootstrap();
    const generalDataService = app.get(GeneralDataService);
    console.log('Got GeneralDataService');

    const result = await generalDataService.findOne(id);
    console.log('Service returned result:', result);

    return createResponse(200, result);
  } catch (error) {
    console.error('Error getting general data by id:', error);
    console.error('Error stack:', error.stack);
    return createResponse(500, { message: error.message || 'Internal server error' });
  }
};

// Get general data details
export const listDetails: Handler = async (event: any, context: Context) => {
  try {
    const generalId = event.pathParameters?.id;
    console.log('Received listDetails request for generalId:', generalId);

    // Parse query parameters for filtering
    const queryParams = event.queryStringParameters || {};
    const code = queryParams.code;
    const name = queryParams.name;
    const isActive = queryParams.isActive === 'true';
    const description = queryParams.description;
    const note = queryParams.note;

    // Build filter object
    const filter: any = {};
    if (code) filter.code = code;
    if (name) filter.name = name;
    if (description) filter.description = description;
    if (note) filter.note = note;
    if (queryParams.isActive !== undefined) filter.isActive = isActive;

    const app = await bootstrap();
    const generalDataService = app.get(GeneralDataService);
    console.log('Got GeneralDataService');

    // We'll need to update the listDetails method to accept filters
    const result = await generalDataService.listDetails(generalId, filter);
    console.log('Service returned result:', result);

    return createResponse(200, result);
  } catch (error) {
    console.error('Error getting general data details:', error);
    console.error('Error stack:', error.stack);
    return createResponse(500, { message: error.message || 'Internal server error' });
  }
};

// Update general data
export const update: Handler = async (event: any, context: Context) => {
  try {
    const id = event.pathParameters?.id;
    const body = JSON.parse(event.body || '{}');
    console.log('Received update request for id:', id, 'with body:', body);

    const app = await bootstrap();
    const generalDataService = app.get(GeneralDataService);
    console.log('Got GeneralDataService');

    // We'll need to update the update method to accept id parameter
    const result = await generalDataService.update({ id, ...body });
    console.log('Service returned result:', result);

    return createResponse(200, result);
  } catch (error) {
    console.error('Error updating general data:', error);
    console.error('Error stack:', error.stack);
    return createResponse(500, { message: error.message || 'Internal server error' });
  }
};

// Delete general data
export const remove: Handler = async (event: any, context: Context) => {
  try {
    const id = event.pathParameters?.id;
    console.log('Received remove request for id:', id);

    const app = await bootstrap();
    const generalDataService = app.get(GeneralDataService);
    console.log('Got GeneralDataService');

    // We'll need to add a remove method to the service
    await generalDataService.remove(id);
    console.log('General data removed successfully');

    return createResponse(200, { message: 'General data removed successfully' });
  } catch (error) {
    console.error('Error removing general data:', error);
    console.error('Error stack:', error.stack);
    return createResponse(500, { message: error.message || 'Internal server error' });
  }
};

// Create general data details
export const createDetails: Handler = async (event: any, context: Context) => {
  try {
    const generalId = event.pathParameters?.id;
    const body = JSON.parse(event.body || '{}');
    console.log('Received createDetails request for generalId:', generalId, 'with body:', body);

    const app = await bootstrap();
    const generalDataService = app.get(GeneralDataService);
    console.log('Got GeneralDataService');

    const result = await generalDataService.createDetails(generalId, body);
    console.log('Service returned result:', result);

    return createResponse(201, result);
  } catch (error) {
    console.error('Error creating general data details:', error);
    console.error('Error stack:', error.stack);
    return createResponse(500, { message: error.message || 'Internal server error' });
  }
};

// Get general data detail by ID
export const findDetailById: Handler = async (event: any, context: Context) => {
  try {
    const generalId = event.pathParameters?.generalId;
    const detailId = event.pathParameters?.detailId;
    console.log('Received findDetailById request for generalId:', generalId, 'detailId:', detailId);

    const app = await bootstrap();
    const generalDataService = app.get(GeneralDataService);
    console.log('Got GeneralDataService');

    // We'll need to add a findDetailById method to the service
    const result = await generalDataService.findDetailById(detailId);
    console.log('Service returned result:', result);

    return createResponse(200, result);
  } catch (error) {
    console.error('Error getting general data detail by id:', error);
    console.error('Error stack:', error.stack);
    return createResponse(500, { message: error.message || 'Internal server error' });
  }
};

// Update general data details
export const updateDetails: Handler = async (event: any, context: Context) => {
  try {
    const generalId = event.pathParameters?.generalId;
    const detailId = event.pathParameters?.detailId;
    const body = JSON.parse(event.body || '{}');
    console.log(
      'Received updateDetails request for generalId:',
      generalId,
      'detailId:',
      detailId,
      'with body:',
      body,
    );

    const app = await bootstrap();
    const generalDataService = app.get(GeneralDataService);
    console.log('Got GeneralDataService');

    // We'll need to update the updateDetails method to accept detailId
    const result = await generalDataService.updateDetails(detailId, body);
    console.log('Service returned result:', result);

    return createResponse(200, result);
  } catch (error) {
    console.error('Error updating general data details:', error);
    console.error('Error stack:', error.stack);
    return createResponse(500, { message: error.message || 'Internal server error' });
  }
};

// Delete general data detail
export const removeDetail: Handler = async (event: any, context: Context) => {
  try {
    const generalId = event.pathParameters?.generalId;
    const detailId = event.pathParameters?.detailId;
    console.log('Received removeDetail request for generalId:', generalId, 'detailId:', detailId);

    const app = await bootstrap();
    const generalDataService = app.get(GeneralDataService);
    console.log('Got GeneralDataService');

    // We'll need to add a removeDetail method to the service
    await generalDataService.removeDetail(detailId);
    console.log('General data detail removed successfully');

    return createResponse(200, { message: 'General data detail removed successfully' });
  } catch (error) {
    console.error('Error removing general data detail:', error);
    console.error('Error stack:', error.stack);
    return createResponse(500, { message: error.message || 'Internal server error' });
  }
};

// Handle OPTIONS request for CORS
export const options: Handler = async (event: any, context: Context) => {
  return {
    statusCode: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Headers':
        'Content-Type,X-Amz-Date,Authorization,X-Api-Key,X-Amz-Security-Token,X-Amz-User-Agent',
      'Access-Control-Allow-Methods': 'OPTIONS,POST,GET,PUT,PATCH,DELETE',
      'Access-Control-Allow-Credentials': true,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({}),
  };
};
