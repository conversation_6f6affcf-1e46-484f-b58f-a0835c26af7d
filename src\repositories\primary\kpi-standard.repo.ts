import { EntityRepository } from 'typeorm';
import { PrimaryRepo } from '~/repositories/primary.repo';
import { KpiStandardEntity, KpiStandardScoreEntity } from '~/entities/primary';

@EntityRepository(KpiStandardEntity)
export class KpiStandardRepo extends PrimaryRepo<KpiStandardEntity> {}

@EntityRepository(KpiStandardScoreEntity)
export class KpiStandardScoreRepo extends PrimaryRepo<KpiStandardScoreEntity> {}
