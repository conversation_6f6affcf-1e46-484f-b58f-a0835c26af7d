import { Column, Entity, PrimaryGeneratedColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { IsUUID } from 'class-validator';
import { PrimaryBaseEntity } from '~/entities/primary-base.entity';

@Entity('holiday')
export class HolidayEntity extends PrimaryBaseEntity {
  @ApiProperty({ description: 'Tên ngày lễ', nullable: true })
  @Column({ name: 'description' })
  description: string;

  @ApiProperty({ description: 'Ngày bắt đầu', nullable: true })
  @Column({ type: 'timestamptz', nullable: true })
  startDate?: Date;

  @ApiProperty({ description: 'Ng<PERSON>y kết thúc', nullable: true })
  @Column({ type: 'timestamptz', nullable: true })
  endDate?: Date;

  // Thêm cột dateDetail kiểu date[]
  @ApiProperty({ description: 'Danh sách các ngày chi tiết', type: [Date] })
  @Column({ name: 'dateDetail', type: 'date', array: true, nullable: true })
  dateDetail: Date[];

  @ApiProperty({ description: 'Trạng thái' })
  @Column({ name: 'status', default: false })
  status: boolean;
}
