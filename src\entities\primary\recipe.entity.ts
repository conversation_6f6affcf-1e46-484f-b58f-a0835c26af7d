import { ApiProperty } from '@nestjs/swagger';
import { Column, Entity, Index, JoinColumn, ManyToOne, OneToMany } from 'typeorm';
import { NSRecipe } from '~/common/enums';
import { PrimaryBaseEntity } from '../primary-base.entity';
import { ItemEntity } from './item.entity';
import { OrganizationUnitEntity } from './organization-unit.entity';
import { RecipeProcessEntity } from './recipe-process.entity';
import { SetPointEntity } from './set-point.entity';

@Entity('recipe')
@Index(['organizationCode', 'recipeNo', 'recipeVer'], { unique: false })
export class RecipeEntity extends PrimaryBaseEntity {
  /** ID Khu vực xử lý (đơn vị xử lý) */
  @ApiProperty({ description: 'ID Khu vực xử lý' })
  @Column({ nullable: false, type: 'uuid' })
  processAreaId: string;

  /** <PERSON>ã cơ sở */
  @ApiProperty({ description: 'Mã cơ sở' })
  @Column({ length: 50, nullable: false })
  organizationCode: string;

  @ManyToOne(() => OrganizationUnitEntity, { nullable: false })
  @JoinColumn({ name: 'processAreaId', referencedColumnName: 'id' })
  @ApiProperty({ description: 'Đơn vị' })
  organizationUnit: OrganizationUnitEntity;

  /** Số công thức */
  @ApiProperty({ description: 'Số công thức' })
  @Column({ length: 50, nullable: false })
  recipeNo: string;

  /** Phiên bản công thức */
  @ApiProperty({ description: 'Phiên bản công thức' })
  @Column({ nullable: false, type: 'numeric', precision: 20, scale: 2 })
  recipeVer: number;

  /** Mô tả */
  @ApiProperty({ description: 'Mô tả' })
  @Column({ type: 'text', nullable: false })
  recipeDescription: string;

  /** Trạng thái công thức */
  @ApiProperty({ description: 'Trạng thái công thức' })
  @Column({ length: 50, nullable: false })
  @Index({ unique: false })
  recipeStatus: NSRecipe.RecipeStatus;

  /** Sản phẩm */
  @ApiProperty({ description: 'Sản phẩm' })
  @ManyToOne(() => ItemEntity, { nullable: false })
  product: ItemEntity;

  /** Mã sản phẩm */
  @ApiProperty({ description: 'Mã sản phẩm' })
  @Column({ type: 'uuid', nullable: false })
  productId: string;

  /** Mã công thức sản xuất */
  @ApiProperty({ description: 'Mã công thức sản xuất' })
  @Column({ length: 50, nullable: false })
  formulaNo: string;

  /** Phiên bản công thức sản xuất */
  @ApiProperty({ description: 'Phiên bản công thức sản xuất' })
  @Column({ nullable: false, type: 'numeric', precision: 20, scale: 2 })
  formulaVer: number;

  /** Số luồng */
  @ApiProperty({ description: 'Số luồng' })
  @Column({ length: 50, nullable: false })
  routingNo: string;

  /** Phiên bản luồng */
  @ApiProperty({ description: 'Phiên bản luồng' })
  @Column({ nullable: false, type: 'numeric', precision: 20, scale: 2 })
  routingVer: number;

  /** Kích thước mẻ sản xuất */
  @ApiProperty({ description: 'Kích thước mẻ sản xuất' })
  @Column({ type: 'numeric', precision: 20, scale: 2, nullable: false })
  batchSize: number;

  /** Đơn vị đo kích thước mẻ sản xuất */
  @ApiProperty({ description: 'Đơn vị đo kích thước mẻ sản xuất' })
  @Column({ length: 10, nullable: false })
  batchUom: string;

  /** Công đoạn sản xuất */
  @OneToMany(() => RecipeProcessEntity, (rp) => rp.recipe, { cascade: true })
  recipeProcesses: Array<RecipeProcessEntity>;

  @OneToMany(() => SetPointEntity, (setPoint) => setPoint.recipe)
  setPoints: SetPointEntity[];

  @ApiProperty({ description: 'Trạng thái đồng bộ với SCADA' })
  @Column({ default: false })
  isSyncScada: boolean;

  @ApiProperty({ description: 'Thời gian đồng bộ sang SCADA', example: new Date() })
  @Column({ nullable: true })
  dateSyncScada: Date;

  @ApiProperty({ description: 'Lỗi đồng bộ Scada', example: 'Can not find' })
  @Column({ type: 'text', nullable: true })
  errorSyncScada: string;
}
