import { Column, Entity, Generated, JoinColumn, ManyToOne } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { PrimaryBaseEntity } from '../primary-base.entity';
import {
  KPIPeriodEntity,
  KpiSetDetailEntity,
  KpiSetGroupEntity,
  KpiSetHeaderEntity,
  OrganizationUnitEntity,
  ProductionAreaEntity,
  ShiftEntity,
} from '~/entities/primary';
import { KpiEntity } from '~/entities/primary/kpi.entity';

/**
 * Entity đại diện cho điểm KPI theo ca sản xuất.
 */
@Entity('kpi_score')
export class KpiScoreEntity extends PrimaryBaseEntity {
  /** Mã KPI Score */
  @ApiProperty({ description: 'Mã KPI Score' })
  @Column({ comment: 'Hệ thống tự sinh theo số thứ tự tăng dần, mỗi dòng 1 số duy nhất' })
  @Generated('increment')
  lineId: number;

  /** ID của nhóm bộ KPI (KPI Set Detail) */
  @ApiProperty({ description: 'ID của nhóm bộ KPI (KPI Set Detail)', nullable: true })
  @Column({ nullable: true })
  kpiSetDetailId?: string;
  @ManyToOne(() => KpiSetDetailEntity, (p) => p.id)
  @JoinColumn({ name: 'kpiSetDetailId', referencedColumnName: 'id' })
  kpiSetDetail: Promise<KpiSetDetailEntity>;

  /** ID của KPI */
  @ApiProperty({ description: 'ID của KPI', nullable: true })
  @Column({ nullable: true })
  kpiId?: string;
  @ManyToOne(() => KpiEntity, (p) => p.id)
  @JoinColumn({ name: 'kpiId', referencedColumnName: 'id' })
  kpi: Promise<KpiEntity>;

  /** ID của nhóm bộ KPI (KPI Set Group) */
  @ApiProperty({ description: 'ID của nhóm bộ KPI (KPI Set Group)', nullable: true })
  @Column({ nullable: true })
  kpiSetGroupId?: string;
  @ManyToOne(() => KpiSetGroupEntity, (p) => p.id)
  @JoinColumn({ name: 'kpiSetGroupId', referencedColumnName: 'id' })
  kpiSetGroup: Promise<KpiSetGroupEntity>;

  /** ID của bộ KPI (KPI Set) */
  @ApiProperty({ description: 'ID của bộ KPI (KPI Set)' })
  @Column({ nullable: true })
  kpiSetHeaderId: string;
  @ManyToOne(() => KpiSetHeaderEntity, (p) => p.id)
  @JoinColumn({ name: 'kpiSetHeaderId', referencedColumnName: 'id' })
  kpiSetHeader: Promise<KpiSetHeaderEntity>;

  /** ID của khu vực sản xuất (Production Area) */
  @ApiProperty({ description: 'ID của khu vực sản xuất (Production Area)' })
  @Column({ nullable: true })
  productionAreaId: string;
  @ManyToOne(() => ProductionAreaEntity, (p) => p.id)
  @JoinColumn({ name: 'productionAreaId', referencedColumnName: 'id' })
  productionArea: Promise<ProductionAreaEntity>;

  /** ID của xưởng (Factory) */
  @ApiProperty({ description: 'ID của xưởng (Factory)' })
  @Column({ nullable: true })
  factoryId: string;
  @ManyToOne(() => OrganizationUnitEntity, (p) => p.id)
  @JoinColumn({ name: 'factoryId', referencedColumnName: 'id' })
  factory: Promise<OrganizationUnitEntity>;

  /** ID của nhà máy (Site) */
  @ApiProperty({ description: 'ID của nhà máy (Site)' })
  @Column({ nullable: true })
  siteId: string;
  @ManyToOne(() => OrganizationUnitEntity, (p) => p.id)
  @JoinColumn({ name: 'siteId', referencedColumnName: 'id' })
  site: Promise<OrganizationUnitEntity>;

  /** Mã kỳ KPI */
  @Column({ nullable: true })
  kpiPeriodId: string;
  @ManyToOne(() => KPIPeriodEntity, (p) => p.id)
  @JoinColumn({ name: 'kpiPeriodId', referencedColumnName: 'id' })
  kpiPeriod: Promise<KPIPeriodEntity>;

  /** ID của ca sản xuất (Shift) */
  @ApiProperty({ description: 'ID của ca sản xuất (Shift)' })
  @Column({ nullable: true })
  shiftId: string;
  @ManyToOne(() => ShiftEntity, (p) => p.id)
  @JoinColumn({ name: 'shiftId', referencedColumnName: 'id' })
  shift: Promise<ShiftEntity>;

  /** Ngày chấm điểm KPI */
  @ApiProperty({ description: 'Ngày chấm điểm KPI' })
  @Column({ type: 'timestamptz', nullable: true })
  scoreDate: Date;

  /** Điểm KPI thực tế */
  @ApiProperty({ description: 'Điểm KPI thực tế', default: -1 })
  @Column({ type: 'float', default: -1, nullable: true })
  actualScore?: number;

  /** Giá trị thực tế */
  @ApiProperty({ description: 'Giá trị thực tế' })
  @Column({ type: 'float', nullable: true })
  actualValue: number;

  /** Số lượng thực tế */
  @ApiProperty({ description: 'Số lượng thực tế' })
  @Column({ type: 'float', nullable: true })
  actualQty: number;

  /* Type record được tạo từ job nào */
  @ApiProperty({ description: 'Type record' })
  @Column({ nullable: true })
  source: string;
}
