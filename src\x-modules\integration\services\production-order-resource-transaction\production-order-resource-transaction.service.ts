import { HttpStatus, Injectable, UnprocessableEntityException } from '@nestjs/common';
import { Between, In, LessThanOrEqual, MoreThanOrEqual } from 'typeorm';
import { BindRepo } from '~/@core/decorator';
import { EHttpStatusMessage } from '~/@core/network/EHttpStatusMessage';
import { ErpBusinessException } from '~/@systems/exceptions/dto/erp-exception';
import { PageRequest } from '~/@systems/utils/page.utils';
import { ORGANIZATION_LEVEL } from '~/common/constants';
import { NSGeneralData } from '~/common/enums';
import { ERPDataPayloadRes, ERPMessagePayloadRes } from '~/dto/erp/payload.dto';
import {
  POResourceTransactionRefRes,
  POResourceTransactionReq,
} from '~/dto/erp/po-resource-transaction.dto';
import {
  GeneralDataDetailRepo,
  ItemRepo,
  OrganizationUnitRepo,
  ProcessRepo,
  ProductionOrderRepo,
  ProductionOrderResourceRepo,
  ShiftRepo,
} from '~/repositories/primary';

@Injectable()
export class POResourceTransactionERPIntegrationService {
  constructor() {}

  @BindRepo(ProductionOrderResourceRepo)
  private readonly productionOrderResourceRepo: ProductionOrderResourceRepo;
  @BindRepo(OrganizationUnitRepo)
  private readonly organizationUnitRepo: OrganizationUnitRepo;
  @BindRepo(ItemRepo)
  private readonly itemRepo: ItemRepo;
  @BindRepo(ShiftRepo)
  private readonly shiftRepo: ShiftRepo;
  @BindRepo(ProductionOrderRepo)
  private readonly productionOrderRepo: ProductionOrderRepo;
  @BindRepo(GeneralDataDetailRepo)
  private readonly generalDataDetailRepo: GeneralDataDetailRepo;
  @BindRepo(ProcessRepo)
  private processRepo: ProcessRepo;

  async list(
    data: POResourceTransactionReq,
  ): Promise<ERPMessagePayloadRes<ERPDataPayloadRes<POResourceTransactionRefRes>>> {
    const start = new Date();
    const err = [];

    // 1. Validate đầu vào
    if (!data.fromProductionDate || isNaN(new Date(data.fromProductionDate).getTime())) {
      err.push({ field: 'From Production Date', message: 'Invalid' });
    }
    if (!data.toProductionDate || isNaN(new Date(data.toProductionDate).getTime())) {
      err.push({ field: 'To Production Date', message: 'Invalid' });
    }

    // Validate site
    let siteLst;
    if (data.Site) {
      siteLst = await this.organizationUnitRepo.find({
        where: {
          code: data.Site,
          levelGeneralDataDetailCode: NSGeneralData.EOrgLevel.SITE,
        },
      });
      if (siteLst.length === 0) {
        err.push({ field: 'Site', message: 'Invalid or not found' });
      }
    }

    // Validate factory
    let factoryLst;
    if (data.Factory) {
      factoryLst = await this.organizationUnitRepo.find({
        where: {
          code: data.Factory,
          levelGeneralDataDetailCode: NSGeneralData.EOrgLevel.FACTORY,
        },
      });
      if (factoryLst.length === 0) {
        err.push({ field: 'Factory', message: 'Invalid or not found' });
      }
    }

    // Throw nếu có lỗi validate
    if (err.length > 0) {
      throw new ErpBusinessException(
        EHttpStatusMessage[HttpStatus.UNPROCESSABLE_ENTITY],
        err,
        HttpStatus.UNPROCESSABLE_ENTITY,
      );
    }

    // 2. Truy vấn danh sách PO
    const orders = await this.productionOrderRepo.find({
      where: {
        ...(data.OrderNo && { orderNo: data.OrderNo }),
        ...(siteLst && { siteId: In(siteLst.map((s) => s.id)) }),
        ...(factoryLst && { factoryId: In(factoryLst.map((f) => f.id)) }),
        orderStatusCode: '3',
        planStartDate: Between(data.fromProductionDate, data.toProductionDate),
      },
      relations: ['site', 'factory'],
    });

    if (orders.length === 0) {
      return {
        code: 200,
        status: 'success',
        message: 'No production orders found in given range',
        data: {
          created_at: start.toISOString(),
          updated_at: new Date().toISOString(),
          refs: [],
        },
      };
    }

    // 3. Truy resource theo danh sách đơn hàng
    const orderIds = orders.map((po) => po.id);
    const resources = await this.productionOrderResourceRepo.find({
      where: {
        orderId: In(orderIds),
      },
    });

    // 4. Chuẩn bị dữ liệu mapping bổ sung
    const processIds = Array.from(new Set(resources.map((r) => r.processId)));
    const processes = await this.processRepo.find({ where: { id: In(processIds) } });
    const processMap = new Map(processes.map((p) => [p.id, p]));
    const poMap = new Map(orders.map((po) => [po.id, po]));

    // 5. Mapping kết quả
    const refs: POResourceTransactionRefRes[] = resources.map((resource) => {
      const po = poMap.get(resource.orderId);
      const process = processMap.get(resource.processId);

      return {
        orderID: resource.orderId,
        OrderNo: resource.orderNo,
        Operationcode: process?.code || '',
        resourcecode: resource.resourceCode,
        actualResourceUsage: resource.actualResourceUsage,
        UOM: resource.uom,
        transactionDate: po?.planStartDate,
        createdDate: po?.createdDate,
        updatedDate: po?.updatedDate,
        site: po?.site?.code,
        factory: po?.factory?.code,
        orderStatusCode: po?.orderStatusCode,
        processName: process?.name,
      };
    });

    return {
      code: 200,
      status: 'success',
      message: 'Successfully',
      data: {
        created_at: start.toISOString(),
        updated_at: new Date().toISOString(),
        refs,
      },
    };
  }
}
