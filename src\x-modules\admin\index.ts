import { forwardRef, MiddlewareConsumer, NestModule, RequestMethod } from '@nestjs/common';
import { TypeOrmModule } from '@nestjs/typeorm';
import { ChildModule } from '~/@core/decorator';

import { AssignShiftRepository } from '~/repositories/primary/assign-shift.repo';
import { ShiftRepo } from '~/repositories/primary/shift.repo';
import { OrganizationUnitRepo } from '../../repositories/primary/organization-unit.repo';
import { UtilityMetersRepo } from '../../repositories/primary/utility-meters.repo';
import { UtilityMeterDetailRepo } from '../../repositories/primary/utility-meter-detail.repo';
import { RefixModule } from '../config-module';
import { AdminMiddleware } from './admin.middleware';
import * as allController from './controllers';
import * as allService from './services';
import {
  TimeSheetRecordService,
  KPIPeriodService,
  RejectionReasonService,
  SitewiseLibService,
} from './services';
import { IntegrationModule } from '~/x-modules/integration';
import { SQSModule } from '~/x-modules/sqs';
@ChildModule({
  // providers: [...[JwtService]],
  prefix: 'admin',
  imports: [
    forwardRef(() => IntegrationModule),
    TypeOrmModule.forFeature([
      OrganizationUnitRepo,
      AssignShiftRepository,
      ShiftRepo,
      UtilityMetersRepo,
      UtilityMeterDetailRepo,
    ]),
    forwardRef(() => SQSModule),
  ],
  providers: [...Object.values(allService)],
  controllers: [...Object.values(allController)],
  exports: [TimeSheetRecordService, SitewiseLibService, KPIPeriodService, RejectionReasonService],
})
export class AdminModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(AdminMiddleware)
      .exclude()
      .forRoutes({ path: `${RefixModule.admin}*`, method: RequestMethod.ALL });
  }
}
