import { BadRequestException, Injectable } from '@nestjs/common';
import moment from 'moment';
import { Between, In, Like } from 'typeorm';
import { BindRepo } from '~/@core/decorator';
import { EOrganizationLevel } from '~/common/enums/organization-level.enum';
import {
  ListProductionRejection,
  ProductionRejectionReq,
  RejectionMonitoringReq,
} from '~/dto/rejection-monitoring.dto';
import {
  GeneralDataDetailRepo,
  GeneralDataRepo,
  ItemRepo,
  MachineParameterRepo,
  MachineRepo,
  OrganizationUnitRepo,
  ProcessMachineRepo,
  ProcessRepo,
  ProductionOrderMaterialRepo,
  ProductionOrderRepo,
  ProductionRejectionRepo,
  RejectionReasonRepo,
  UomConventionRepo,
} from '~/repositories/primary';
import { MesDxMeasurementRepo } from '~/repositories/scada';
import { adminSessionContext } from '../../admin-session.context';
import { ShiftRepo } from '~/repositories/primary/shift.repo';
import { UOMRepo } from '~/repositories/primary/uom.repo';
import dayjs from 'dayjs';
import { AccessRepo } from '~/repositories/primary/access.repo';
import { AssignShiftRepository } from '~/repositories/primary/assign-shift.repo';
const AWS = require('aws-sdk');

@Injectable()
export class RejectionMonitoringService {
  constructor() {}

  @BindRepo(ProductionRejectionRepo)
  private readonly repo: ProductionRejectionRepo;

  @BindRepo(OrganizationUnitRepo)
  private readonly organizationUnitRepo: OrganizationUnitRepo;

  @BindRepo(ProcessRepo)
  private readonly processRepo: ProcessRepo;

  @BindRepo(ProcessMachineRepo)
  private readonly processMachineRepo: ProcessMachineRepo;

  @BindRepo(MachineRepo)
  private readonly machineRepo: MachineRepo;

  @BindRepo(MachineParameterRepo)
  private readonly machineParameterRepo: MachineParameterRepo;

  @BindRepo(MesDxMeasurementRepo)
  private readonly mesDxMeasurementRepo: MesDxMeasurementRepo;

  @BindRepo(GeneralDataDetailRepo)
  private readonly generalDataDetailRepo: GeneralDataDetailRepo;

  @BindRepo(GeneralDataRepo)
  private readonly generalDataRepo: GeneralDataRepo;

  @BindRepo(ProductionOrderRepo)
  private readonly productionOrderRepo: ProductionOrderRepo;

  @BindRepo(ShiftRepo)
  private readonly shiftRepo: ShiftRepo;

  @BindRepo(ItemRepo)
  private readonly itemRepo: ItemRepo;

  @BindRepo(ProductionOrderMaterialRepo)
  private readonly productionOrderMaterialRepo: ProductionOrderMaterialRepo;

  @BindRepo(UomConventionRepo)
  private readonly uomConventionRepo: UomConventionRepo;

  @BindRepo(RejectionReasonRepo)
  private readonly rejectionReasonRepo: RejectionReasonRepo;

  @BindRepo(AccessRepo)
  private readonly accessRepo: AccessRepo;

  @BindRepo(AssignShiftRepository)
  private readonly assignShiftRepo: AssignShiftRepository;

  sitewise = new AWS.IoTSiteWise({
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    region: process.env.AWS_REGION,
  });

  async getAssetPropertyValue(assetId: string, propertyId: string) {
    try {
      const params = {
        assetId: assetId,
        propertyId: propertyId,
      };
      const data = await this.sitewise.getAssetPropertyValue(params).promise();

      // console.log('Asset Property Value: ', data);
      return data;
    } catch (err) {
      console.log('Error getting Asset Property Value: ', err);
    }
  }

  async getAssetPropertyValueHistory(
    assetId: string,
    propertyId: string,
    startDate: Date,
    endDate: Date,
  ) {
    try {
      const params = {
        assetId: assetId,
        propertyId: propertyId,
        startDate: startDate,
        endDate: endDate,
        timeOrdering: 'DESCENDING',
      };
      const data = await this.sitewise.getAssetPropertyValueHistory(params).promise();

      // console.log('Asset Property Value: ', data);
      return data;
    } catch (err) {
      console.log('Error getting Asset Property Value: ', err);
    }
  }

  /**
   * Tìm tất cả lý do với bộ lọc tùy chọn
   * @param filterDto Tiêu chí lọc tùy chọn
   * @returns Danh sách phân trang
   */
  async pagination(params: ListProductionRejection) {
    if (!params.siteId || !params.factoryId)
      return {
        data: [],
        total: 0,
      };
    const whereCon: any = {};
    if (params.startDate && params.endDate)
      whereCon.transactionDate = Between(new Date(params.startDate), new Date(params.endDate));
    if (params.siteId) whereCon.siteId = params.siteId;
    if (params.factoryId) whereCon.factoryId = params.factoryId;
    if (params.lineId) whereCon.productionLineId = params.lineId;
    const result = await this.repo.findPagination(
      { where: whereCon, order: { transactionDate: 'DESC' } },
      { pageIndex: params.pageIndex, pageSize: params.pageSize },
    );
    const lstSiteId = result.data.map((i) => i.siteId);
    const lstFactoryId = result.data.map((i) => i.factoryId);
    const lstLineId = result.data.map((i) => i.productionLineId);
    const organization = await this.organizationUnitRepo.find({
      where: { id: In([...lstSiteId, ...lstFactoryId, ...lstLineId]) },
    });
    const lstProcessAreaId = result.data.map((i) => i.processAreaId);
    const lstProcessArea = await this.organizationUnitRepo.find({
      where: { id: In(lstProcessAreaId) },
    });
    const isValidUUID = (id: string) =>
      /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i.test(id);
    const lstProductionOrderId = result.data
      .map((i) => i.productionOrderId)
      .filter((id) => typeof id === 'string' && isValidUUID(id));
    const lstProductionOrder = await this.productionOrderRepo.find({
      where: { id: In(lstProductionOrderId) },
      select: ['id', 'orderNo'],
    });
    const lstShiftId = result.data.map((i) => i.shiftId);
    const lstShift = await this.shiftRepo.find({ where: { id: In(lstShiftId) } });
    const lstTransactionTypeCode = result.data.map((i) => i.transactionTypeCode);
    const lstTransactionType = await this.generalDataRepo.find({
      where: { code: In(lstTransactionTypeCode) },
    });
    const lstTransactionTypeDetail = await this.generalDataDetailRepo.find({
      where: { generalId: In(lstTransactionType.map((i) => i.id)) },
    });
    const lstUomCode = result.data.map((i) => i.uomCode);
    const lstItemId = result.data.map((i) => i.itemId);
    const lstItem = await this.itemRepo.find({ where: { id: In(lstItemId) } });
    const dictRejectionReason: any = {};
    {
      const lstRejectionReason = await this.rejectionReasonRepo.find({
        where: { id: In(result.data.map((i) => i.rejectionReasonId)) },
      });
      lstRejectionReason.forEach((x) => (dictRejectionReason[x.id] = x));
    }
    result.data.forEach((item: any) => {
      const site = organization.find((i) => i.id === item.siteId);
      item.siteName = site?.code + ' - ' + site?.name;
      const factory = organization.find((i) => i.id === item.factoryId);
      item.factoryName = factory?.code + ' - ' + factory?.name;
      const line = organization.find((i) => i.id === item.productionLineId);
      item.lineName = line?.code + ' - ' + line?.name;
      const processArea = lstProcessArea.find((i) => i.id === item.processAreaId);
      item.processAreaName = processArea?.code + ' - ' + processArea?.name;
      const productionOrder = lstProductionOrder.find((i) => i.id === item.productionOrderId);
      item.productionOrderName = productionOrder?.orderNo;
      const shift = lstShift.find((i) => i.id === item.shiftId);
      item.shiftCode = shift?.code;
      item.shiftName = shift?.code + ' - ' + shift?.description;
      const transactionType = lstTransactionType.find((i) => i.code === item.transactionTypeCode);
      item.transactionTypeName = transactionType?.code + ' - ' + transactionType?.name;
      const transactionTypeDetail = lstTransactionTypeDetail.find(
        (i) => i.code === item.transactionTypeDetailCode,
      );
      item.transactionTypeDetailName =
        transactionTypeDetail?.code + ' - ' + transactionTypeDetail?.name;
      const Item = lstItem.find((i) => i.id === item.itemId);
      item.itemName = Item?.code + ' - ' + Item?.name;
      item.rejectionReasonName = dictRejectionReason[item.rejectionReasonId]
        ? dictRejectionReason[item.rejectionReasonId]?.code +
          ' - ' +
          dictRejectionReason[item.rejectionReasonId]?.name
        : '';
    });
    return result;
  }

  async listRejectionMonitoring(params: RejectionMonitoringReq) {
    if (!params.siteId || !params.factoryId)
      return {
        data: [],
        total: 0,
      };
    const whereSiteCon: any = { levelGeneralDataDetailCode: 'SITE', isActive: true };
    if (params.siteId) whereSiteCon.id = params.siteId;
    const site = await this.organizationUnitRepo.find({ where: whereSiteCon });
    const whereFactoryCon: any = {
      parentId: In(site.map((i) => i.id)),
      levelGeneralDataDetailCode: 'FACTORY',
      isActive: true,
    };
    if (params.factoryId) whereFactoryCon.id = params.factoryId;
    const factory = await this.organizationUnitRepo.find({ where: whereFactoryCon });
    const whereLineCon: any = {
      parentId: In(factory.map((i) => i.id)),
      levelGeneralDataDetailCode: 'LINE',
      isActive: true,
    };
    if (params.lineId) whereLineCon.id = params.lineId;
    const line = await this.organizationUnitRepo.find({
      where: whereLineCon,
      select: ['id', 'name', 'code'],
    });
    const lstProcessArea = await this.organizationUnitRepo.find({
      where: { parentId: In(line.map((i) => i.id)) },
      select: ['id', 'name', 'code', 'levelGeneralDataDetailCode', 'parentId'],
    });
    const lstProcess = await this.processRepo.find({
      where: { organizationUnitId: In(lstProcessArea.map((i) => i.id)) },
      select: ['id', 'name', 'organizationUnitId'],
    });
    const lstProcessMachine = await this.processMachineRepo.find({
      where: { processId: In(lstProcess.map((i) => i.id)) },
    });
    const lstMachine = await this.machineRepo.find({
      where: { id: In(lstProcessMachine.map((i) => i.machineId)) },
    });
    const lstMachineParam = await this.machineParameterRepo.find({
      where: { machineId: In(lstMachine.map((i) => i.id)), showOnRejectionMonitoring: true },
    });
    const result = [];
    lstProcessArea.forEach((processArea: any) => {
      const data = { ...processArea };
      const lstProcessFilter = lstProcess.filter((i) => i.organizationUnitId === data.id);
      lstProcessFilter.forEach((process: any) => {
        const lstProcessMachineFilter = lstProcessMachine.filter((i) => i.processId === process.id);
        lstProcessMachineFilter.forEach((processMachine: any) => {
          const machine: any = lstMachine.find((i) => i.id === processMachine.machineId);
          const lstMachinePara = lstMachineParam.filter((i) => i.machineId === machine.id);
          lstMachinePara.forEach((para: any) => {
            const dataCopy = { ...data };
            const param = { ...para };
            const machineCopy = { ...machine };
            dataCopy.propertyId = param.iotsitewisePropertyId;
            dataCopy.lineName = line.find((i) => i.id === processArea.parentId)?.name || '';
            dataCopy.machineName = machineCopy.name;
            dataCopy.uomCode = param.uomCode;
            dataCopy.value = '';
            dataCopy.propertyId = param.iotsitewisePropertyId;
            dataCopy.param = param.id;
            result.push(dataCopy);
          });
        });
      });
    });

    // Sắp xếp mảng result theo machineName
    result.sort((a, b) => {
      if (a.machineName < b.machineName) {
        return -1; // a trước b
      }
      if (a.machineName > b.machineName) {
        return 1; // b trước a
      }
      return 0; // Nếu tên máy giống nhau, không thay đổi vị trí
    });

    if (!params.startDate || !params.endDate) {
      return {
        data: result,
        total: result.length,
      };
    }

    const dictValue: any = {};
    {
      const lstMes = await this.mesDxMeasurementRepo.find({
        where: {
          measurementId: In(result.map((i) => i.propertyId)),
          datetime: Between(new Date(params.startDate), new Date(params.endDate)),
        },
        order: { datetime: 'DESC' },
      });
      lstMes.forEach(
        (x) =>
          !dictValue[x.measurementId] &&
          (dictValue[x.measurementId] = {
            value: x.value,
            date: x.datetime,
          }),
      );
    }
    result.forEach((item) => {
      item.value = dictValue[item.propertyId] ? dictValue[item.propertyId].value : null;
      item.date = dictValue[item.propertyId] ? dictValue[item.propertyId].date : null;
    });
    result.sort(
      (a, b) =>
        (b?.date ? new Date(b.date).getTime() : 0) - (a?.date ? new Date(a.date).getTime() : 0),
    );
    return {
      data: result,
      total: result.length,
    };
  }

  async getOptions(levelCode?: string, userId?: string): Promise<any[]> {
    let lstFactoryId = [];
    if (userId) {
      const lstAccess = await this.accessRepo.find({
        where: { user: { id: userId }, status: true },
        relations: ['organizationUnit', 'user'],
      });
      if (lstAccess.length === 0) return [];

      lstFactoryId = lstAccess.map((i) => i.organizationUnit.id);
    }
    levelCode = levelCode?.replace(/\?$/, '');
    const whereCon: any = { levelGeneralDataDetailCode: levelCode, isActive: true };

    if (lstFactoryId.length > 0 && levelCode === EOrganizationLevel.Factory) {
      whereCon.id = In(lstFactoryId);
    }
    if (lstFactoryId.length > 0 && levelCode === EOrganizationLevel.Site) {
      const lstFactory = await this.organizationUnitRepo.find({
        where: { id: In(lstFactoryId), isActive: true },
        select: ['parentId'],
      });
      const lstSiteId = lstFactory.map((i) => i.parentId);
      whereCon.id = In(lstSiteId);
    }
    try {
      const data = await this.organizationUnitRepo.find({
        where: whereCon,
      });
      return data.map((i) => ({
        label: i.code + ' - ' + i.name,
        value: i.id,
        parentId: i.parentId,
        code: i.code,
        name: i.name,
        categoryId: i.categoryId,
      }));
    } catch (error) {
      console.error('Error in getOptions:', error);
      return [];
    }
  }

  async create(body: ProductionRejectionReq) {
    const { userId: userId } = adminSessionContext;
    const today = new Date();
    const area = await this.organizationUnitRepo.findOne({
      where: { id: body.processAreaId, levelGeneralDataDetailCode: 'PROCESS' },
    });
    if (!area) throw new Error('Not Found Process Area');
    const line = await this.organizationUnitRepo.findOne({
      where: { id: body.productionLineId, levelGeneralDataDetailCode: 'LINE' },
    });
    if (!line) throw new Error('Not Found Production Line');
    const factory = await this.organizationUnitRepo.findOne({
      where: { id: line.parentId, levelGeneralDataDetailCode: 'FACTORY' },
    });
    if (!factory) throw new Error('Not Found Factory');
    const site = await this.organizationUnitRepo.findOne({
      where: { id: factory.parentId, levelGeneralDataDetailCode: 'SITE' },
    });
    if (!site) throw new Error('Not Found Site');
    const transactionType = await this.generalDataDetailRepo.findOne({
      where: { id: body.transactionTypeId, isActive: true },
    });
    if (!transactionType) throw new Error('Not Found Transaction Type');
    const parentTransactionType = await this.generalDataRepo.findOne({
      where: { id: transactionType.generalId },
    });
    if (!transactionType) throw new Error('Not Found Transaction Type Parent');
    const { uomCode, transactionTypeId, ...rest } = body;
    const data = await this.repo.save({
      ...rest,
      transactionDate: dayjs(body?.transactionDate).isValid()
        ? dayjs(body?.transactionDate).tz('Asia/Ho_Chi_Minh').startOf('day').toDate()
        : null,
      postedTrx: 0,
      productionLineId: line.id,
      factoryId: factory.id,
      siteId: site.id,
      transactionTypeCode: parentTransactionType.code,
      transactionTypeDetailCode: transactionType.code,
      uomCode: uomCode,
      createdBy: userId,
      updatedBy: userId,
      createdDate: today,
    });
    return { message: 'Create Successfully!', data: data };
  }

  async update(id: string, body: ProductionRejectionReq) {
    const checkExist = await this.repo.findOne({ where: { id: id } });
    if (!checkExist) throw new Error('Not Fount Production Rejection!');
    const { userId: userId } = adminSessionContext;
    const today = new Date();
    const area = await this.organizationUnitRepo.findOne({
      where: { id: body.processAreaId, levelGeneralDataDetailCode: 'PROCESS' },
    });
    if (!area) throw new Error('Not Found Process Area');
    const line = await this.organizationUnitRepo.findOne({
      where: { id: body.productionLineId, levelGeneralDataDetailCode: 'LINE' },
    });
    if (!line) throw new Error('Not Found Production Line');
    const factory = await this.organizationUnitRepo.findOne({
      where: { id: line.parentId, levelGeneralDataDetailCode: 'FACTORY' },
    });
    if (!factory) throw new Error('Not Found Factory');
    const site = await this.organizationUnitRepo.findOne({
      where: { id: factory.parentId, levelGeneralDataDetailCode: 'SITE' },
    });
    if (!site) throw new Error('Not Found Site');
    const transactionType = await this.generalDataDetailRepo.findOne({
      where: { id: body.transactionTypeId, isActive: true },
    });
    if (!transactionType) throw new Error('Not Found Transaction Type');
    const parentTransactionType = await this.generalDataRepo.findOne({
      where: { id: transactionType.generalId },
    });
    if (!transactionType) throw new Error('Not Found Transaction Type Parent');
    const { uomCode, transactionTypeId, ...rest } = body;
    const data = await this.repo.update(
      { id: id },
      {
        ...rest,
        transactionDate: dayjs(body?.transactionDate).isValid()
          ? dayjs(body?.transactionDate).tz('Asia/Ho_Chi_Minh').startOf('day').toDate()
          : null,
        uomCode: uomCode,
        productionLineId: line.id,
        factoryId: factory.id,
        siteId: site.id,
        transactionTypeCode: parentTransactionType.code,
        transactionTypeDetailCode: transactionType.code,
        rejectionReasonId: body?.rejectionReasonId || null,
        updatedBy: userId,
        updatedDate: today,
      },
    );
    return { message: 'Update Successfully!', data: data };
  }

  async detail(id: string) {
    if (!id) throw new BadRequestException('ID don’t exist');
    const data: any = await this.repo.findOneOrFail({ where: { id: id } });
    const generalData = await this.generalDataRepo.findOne({
      where: { code: data.transactionTypeCode, isActive: true },
    });
    const generalDataDetail = await this.generalDataDetailRepo.findOne({
      where: { generalId: generalData.id, code: data.transactionTypeDetailCode, isActive: true },
    });
    data.transactionTypeId = generalDataDetail.id;
    delete data.transactionTypeCode;
    delete data.transactionTypeDetailCode;
    return data;
  }

  async delete(id: string) {
    const checkExist = await this.repo.findOne({ where: { id: id } });
    if (!checkExist) throw new Error('Not Fount Production Rejection!');
    return await this.repo.delete({ id: id });
  }

  async loadProcessArea(lineId?: string) {
    const whereCon: any = { levelGeneralDataDetailCode: 'PROCESS', isActive: true };
    if (lineId) whereCon.parentId = lineId;
    return await this.organizationUnitRepo.find({ where: whereCon });
  }

  async loadProductionOrder() {
    return await this.productionOrderRepo.find();
  }

  async loadShift() {
    return await this.shiftRepo.find();
  }

  async loadTransactionType() {
    const general = await this.generalDataRepo.findOne({
      where: { code: 'REJECTION_TRANSACTION_TYPE', isActive: true },
    });
    if (!general) throw new Error('Not Found General Data!');
    return await this.generalDataDetailRepo.find({
      where: { generalId: general.id, isActive: true },
    });
  }

  async loadItem() {
    return await this.itemRepo.find();
  }

  async loadUom() {
    const lstItem = await this.itemRepo.find({
      where: { status: 'ACTIVE' },
    });
    const lstItemId = lstItem.map((i) => i.id);
    const lstUomCon = await this.uomConventionRepo.find({ where: { itemId: In(lstItemId) } });
    let data = [];
    for (const item of lstItem) {
      data.push({
        itemId: { ...item }.id,
        uom: { ...item }.baseUnit,
      });
      data.push({
        itemId: { ...item }.id,
        uom: { ...item }.inventoryUnit,
      });
      for (const uom of lstUomCon.filter((i) => i.itemId === item.id)) {
        data.push({
          itemId: { ...item }.id,
          uom: { ...uom }.toUnit,
        });
      }
    }
    // Dùng Set để loại bỏ trùng lặp, chuyển đối tượng thành chuỗi
    const uniqueData = Array.from(new Set(data.map((item) => JSON.stringify(item)))).map((item) =>
      JSON.parse(item),
    ); // Chuyển lại chuỗi thành đối tượng

    return uniqueData;
  }

  async loadRejectionReason() {
    return await this.rejectionReasonRepo.find({ where: { status: 1 } });
  }

  async loadAssignShiftByFactoryId(factoryId: string) {
    if (!factoryId) throw new BadRequestException('Factory ID is required');
    const factory = await this.organizationUnitRepo.findOne({
      where: { id: factoryId, levelGeneralDataDetailCode: 'FACTORY' },
    });
    if (!factory) throw new BadRequestException('Factory not found');
    return await this.assignShiftRepo.find({ where: { organizationId: factory.id, status: true } });
  }
}
