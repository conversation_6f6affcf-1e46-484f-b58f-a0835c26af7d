import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { HttpMethod } from '~/@core/network';

export class PathMethodReq {
  @ApiProperty({ description: 'Đường dẫn', example: '/integration/erp/recipe' })
  url: string;

  @ApiProperty({ description: 'Phương thức', example: HttpMethod.POST, enum: HttpMethod })
  method: HttpMethod;
}

export class CreateApiTokenReq {
  @ApiProperty({
    description: 'Danh sách các đường dẫn và phương thức được phép truy cập',
    type: () => PathMethodReq,
    isArray: true,
  })
  paths: Array<PathMethodReq>;

  @ApiPropertyOptional({
    description: 'Ngày hết hạn của token',
    type: Date,
  })
  expiredAt?: Date;
}
