import { ValidateNested, isDefined, IsDefined } from 'class-validator';
import { ApiExtraModels, ApiOkResponse, ApiProperty, getSchemaPath } from '@nestjs/swagger';
import { applyDecorators, Type } from '@nestjs/common';
type TOperatorText = '=' | 'LIKE' | 'NOT LIKE';
type TOperatorNumber = '=' | '!=' | '<' | '>' | '>=' | '<=';
type TOperatorDate = '=' | '!=' | '<' | '>' | '>=' | '<=';
type TOperatorSelect = 'IN' | 'NOT IN';
type TOperatorBoolean = '=';

export class FilterItemText {
  type: 'TEXT' = 'TEXT';
  value: string = '';
  compare?: TOperatorText = 'LIKE';
}
export class FilterItemNumber {
  type: 'NUMBER' = 'NUMBER';
  value: number;
  compare?: TOperatorNumber = '=';
}
export class FilterItemDate {
  type: 'DATE' = 'DATE';
  value: Date;
  compare?: TOperatorDate = '=';
}
export class FilterItemBoolean {
  type: 'BOOLEAN' = 'BOOLEAN';
  value: boolean;
  compare?: TOperatorBoolean = '=';
}

export class FilterItemSelect {
  type: 'SELECT' = 'SELECT';
  value: any[] = [];
  compare?: TOperatorSelect = 'IN';
}
export type FilterItem =
  | FilterItemText
  | FilterItemNumber
  | FilterItemDate
  | FilterItemBoolean
  | FilterItemSelect;

export type FilterOption<T = any> = {
  [k in keyof T]?: FilterItem;
};
export type OrderOption<T> = {
  [k in keyof T]?: 'ASC' | 'DESC';
};

export class PageRequest<T = any> {
  @ApiProperty({
    example: 10,
  })
  pageSize: number = 10;
  @ApiProperty({
    example: 1,
  })
  pageIndex: number = 1;

  // @ValidateNested()
  // @IsDefined()
  @ApiProperty({
    type: Object,
  })
  orders?: OrderOption<T>;
  // @ValidateNested()
  // @IsDefined()
  @ApiProperty({
    type: Object,
  })
  filters?: FilterOption<T>;
}

export class PageResponse<T = any> {
  // @ApiProperty()
  data: T[];

  @ApiProperty()
  total?: number;

  @ApiProperty({
    type: Object,
    description: 'Additional metadata about the response',
    example: { totalRecords: 101, totalPages: 11, request: {}, extra: {} },
  })
  meta?: {
    totalRecords?: number;
    totalPages?: number;
    request?: PageRequest<T>;
    extra?: any;
  };
}
export class SuccessResponse {
  @ApiProperty({
    example: 'success',
  })
  message: string = 'success';
}

export class DataSuccessResponse {
  @ApiProperty({
    example: 'success',
  })
  message: string = 'success';
  data: any;

  constructor(data: any) {
    this.data = data;
  }
}
// SQL injection
// function mapPostgresQueryFilter<T>(filters: FilterOption<T> = {}) {
//   let where = ' where 1 = 1 ';
//   Object.keys(filters).forEach((key) => {
//     const filterItem: FilterItem = filters[key] || [];

//     if (filterItem.type === 'TEXT') {
//       const { value, compare = 'LIKE' } = filterItem;
//       if (value) {
//         where += ` AND ( "${key}" ${compare} '%${value}%'  ) `;
//       }
//     }
//     if (filterItem.type === 'DATE') {
//       const { value, compare = '=' } = filterItem;
//       if (value) {
//         switch (compare) {
//           case '=':
//             where += ` AND ( "${key}" >= '${value}'::date AND  "${key}" < ('${value}'::date + '1 day'::interval) ) `;
//             break;
//           default:
//             where += ` AND ( "${key}" ${compare} '%${value}%'  ) `;
//             break;
//         }
//       }
//     }

//     if (filterItem.type === 'NUMBER') {
//       const { value, compare = '=' } = filterItem;
//       if (value !== undefined && value !== null && typeof value === 'number') {
//         where += ` AND ( "${key}" ${compare} ${value} ) `;
//       }
//     }

//     if (filterItem.type === 'BOOLEAN') {
//       const { value, compare = '=' } = filterItem;
//       if (value === true || value === false) {
//         where += ` AND ( "${key}" ${compare} ${value} ) `;
//       }
//     }

//     if (filterItem.type === 'SELECT') {
//       const { value, compare = 'IN' } = filterItem;
//       const makeValue = value.filter((v) => v);
//       if (makeValue && makeValue.length > 0) {
//         where += ` AND ( "${key}" ${compare} ('${value.join(',')}')  ) `;
//       }
//     }
//   });
//   return where;
// }

function mapPostgresQueryFilter<T>(filters: FilterOption<T> = {}) {
  let where = ' WHERE 1 = 1 ';
  const values: any[] = []; // Mảng để chứa các giá trị tham số
  let valueIndex = 1; // Biến đếm số thứ tự tham số (Postgres sử dụng $1, $2,...)

  Object.keys(filters).forEach((key) => {
    const filterItem: FilterItem = filters[key] || [];

    if (filterItem.type === 'TEXT') {
      const { value, compare = 'LIKE' } = filterItem;
      if (value) {
        where += ` AND "${key}" ${compare} $${valueIndex++}`;
        values.push(`%${value}%`); // Thêm giá trị vào mảng tham số
      }
    }

    if (filterItem.type === 'DATE') {
      const { value, compare = '=' } = filterItem;
      if (value) {
        switch (compare) {
          case '=':
            where += ` AND "${key}" >= $${valueIndex++}::date AND "${key}" < ($${valueIndex++}::date + '1 day'::interval) `;
            values.push(value); // Thêm giá trị vào mảng tham số
            values.push(value); // Thêm giá trị ngày thứ hai vào mảng tham số
            break;
          default:
            where += ` AND "${key}" ${compare} $${valueIndex++}`;
            values.push(value); // Thêm giá trị vào mảng tham số
            break;
        }
      }
    }

    if (filterItem.type === 'NUMBER') {
      const { value, compare = '=' } = filterItem;
      if (value !== undefined && value !== null && typeof value === 'number') {
        where += ` AND "${key}" ${compare} $${valueIndex++}`;
        values.push(value); // Thêm giá trị vào mảng tham số
      }
    }

    if (filterItem.type === 'BOOLEAN') {
      const { value, compare = '=' } = filterItem;
      if (value === true || value === false) {
        where += ` AND "${key}" ${compare} $${valueIndex++}`;
        values.push(value); // Thêm giá trị vào mảng tham số
      }
    }

    if (filterItem.type === 'SELECT') {
      const { value, compare = 'IN' } = filterItem;
      const makeValue = value.filter((v) => v);
      if (makeValue && makeValue.length > 0) {
        const placeholders = makeValue.map(() => `$${valueIndex++}`).join(', ');
        where += ` AND "${key}" ${compare} (${placeholders}) `;
        values.push(...makeValue); // Thêm giá trị vào mảng tham số
      }
    }
  });

  return { where, values };
}

export const ApiPaginatedDto = <TModel extends Type<any>>(model: TModel) => {
  return applyDecorators(
    ApiOkResponse({
      schema: {
        allOf: [
          { $ref: getSchemaPath(PageResponse) },
          {
            properties: {
              data: {
                type: 'array',
                items: { $ref: getSchemaPath(model) },
              },
            },
          },
        ],
      },
    }),
  );
};
export const ApiOkResponsePaginated = <DataDto extends Type<unknown>>(dataDto: DataDto) => {
  return applyDecorators(
    ApiExtraModels(PageResponse, dataDto),
    ApiOkResponse({
      schema: {
        allOf: [
          { $ref: getSchemaPath(PageResponse) },
          {
            properties: {
              data: {
                type: 'array',
                items: { $ref: getSchemaPath(dataDto) },
              },
            },
          },
        ],
      },
    }),
  );
};
export default {
  mapPostgresQueryFilter,
};
