import { Column, Entity, PrimaryColumn } from 'typeorm';
import { IsUUID } from 'class-validator';
import { PrimaryBaseEntity } from '~/entities/primary-base.entity';

@Entity('sitewise_properties')
export class SitewisePropertiesEntity extends PrimaryBaseEntity {
  @Column({ nullable: true })
  name: string;

  /** Mã sitewise properties */
  @Column({ nullable: true })
  code: string;

  // /** Mô tả sitewise properties */
  // @Column({ nullable: true })
  // description: string;

  /** Mã AWS */
  @PrimaryColumn()
  @IsUUID(4)
  awsId: string;

  /** Loại data*/
  @Column({ nullable: true })
  type: string;

  /** Loại giá trị*/
  @Column({ type: 'jsonb', nullable: true })
  typeValue: any;

  /** Loại data*/
  @Column({ nullable: true })
  dataType: string;

  /** Loại data*/
  @Column({ nullable: true })
  dataTypeSpec: string;

  /** <PERSON><PERSON><PERSON> vị */
  @Column({ nullable: true })
  unit: string;

  // @Column({ nullable: true })
  // notificationState: string;

  // @Column({ nullable: true })
  // notificationTopic: string;

  /** Mã externalId */
  @Column({ name: 'externalId', nullable: true })
  externalId: string;

  /** Mã sitewise asset */
  @Column({ nullable: true })
  sitewiseAssetId: string;

  /** Mã sitewise model */
  @Column({ nullable: true })
  sitewiseModelId: string;
}
