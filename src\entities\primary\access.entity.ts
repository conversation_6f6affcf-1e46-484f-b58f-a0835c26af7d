import { <PERSON>um<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ToOne } from 'typeorm';
import { PrimaryBaseEntity } from '../primary-base.entity';
import { OrganizationUnitEntity } from './organization-unit.entity';
import { UserEntity } from './user.entity';
import { ApiProperty } from '@nestjs/swagger';

@Entity('access')
export class AccessEntity extends PrimaryBaseEntity {
  @ManyToOne(() => UserEntity, (user) => user.access)
  user: UserEntity;

  @ManyToOne(() => OrganizationUnitEntity)
  @JoinColumn()
  organizationUnit: OrganizationUnitEntity;

  /** Trạng thái hoạt động */
  @ApiProperty({ description: 'Trạng thái hoạt động' })
  @Column({ default: true, nullable: true })
  status: boolean;

  /** Mặc định*/
  @ApiProperty({ description: '' })
  @Column({ default: false, nullable: true })
  default: boolean;
}
