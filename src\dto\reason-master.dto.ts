import { ApiProperty, PartialType } from '@nestjs/swagger';
import { IsBoolean, IsNotEmpty, IsOptional, IsString, MaxLength } from 'class-validator';
import { PageRequest } from '~/@systems/utils';
import { ReasonMasterEntity } from '~/entities/primary/reason-master.entity';

export class CreateReasonMasterDto {
  @ApiProperty({ description: 'Mã lý do duy nhất' })
  @IsString()
  @IsNotEmpty()
  code: string;

  @ApiProperty({ description: 'Tên của lý do', nullable: true })
  @IsString()
  @IsOptional()
  name?: string;

  @ApiProperty({ description: 'Danh mục của lý do' })
  @IsString()
  @IsNotEmpty()
  categoryId: string;

  @ApiProperty({ description: 'Nhóm của lý do' })
  @IsString()
  @IsNotEmpty()
  groupId: string;

  @ApiProperty({ description: 'Nhóm con của lý do', nullable: true })
  @IsString()
  @IsOptional()
  subGroupId?: string;

  @ApiProperty({ description: 'Mô tả chi tiết về lý do', nullable: true })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiProperty({ description: 'Trạng thái hoạt động của lý do', default: true })
  @IsBoolean()
  @IsOptional()
  isActive?: boolean;

  @ApiProperty({ description: 'ID của lý do cha', nullable: true })
  @IsString()
  @IsOptional()
  parentId?: string;

  @ApiProperty({ description: 'Ghi chú bổ sung', nullable: true, maxLength: 255 })
  @IsString()
  @IsOptional()
  @MaxLength(255)
  note?: string;
}

export class UpdateReasonMasterDto extends PartialType(CreateReasonMasterDto) {
  @ApiProperty({ description: 'Id của lý do', nullable: true, required: false })
  @IsString()
  @IsNotEmpty()
  id: string;
}

export class ReasonMasterPaginationDto extends PageRequest {
  where: any;
}

export class ReasonMasterResponseDto {
  @ApiProperty({ description: 'Unique identifier' })
  id: string;

  @ApiProperty({ description: 'Mã lý do duy nhất' })
  code: string;

  @ApiProperty({ description: 'Tên của lý do', nullable: true })
  name: string;

  @ApiProperty({ description: 'Danh mục của lý do' })
  categoryId: string;

  @ApiProperty({ description: 'Nhóm của lý do' })
  groupId: string;

  @ApiProperty({ description: 'Nhóm con của lý do', nullable: true })
  subGroupId: string;

  @ApiProperty({ description: 'Danh mục của lý do' })
  category: string;

  @ApiProperty({ description: 'Nhóm của lý do' })
  group: string;

  @ApiProperty({ description: 'Nhóm con của lý do', nullable: true })
  subGroup: string;

  @ApiProperty({ description: 'Mô tả chi tiết về lý do', nullable: true })
  description: string;

  @ApiProperty({ description: 'Trạng thái hoạt động của lý do' })
  isActive: boolean;

  @ApiProperty({ description: 'ID của lý do cha', nullable: true })
  parentId: string;

  @ApiProperty({ description: 'Ghi chú bổ sung', nullable: true })
  note: string;

  @ApiProperty({ description: 'Thời gian tạo' })
  createdDate: Date;

  @ApiProperty({ description: 'Thời gian cập nhật' })
  updatedDate: Date;

  @ApiProperty({ description: 'Danh sách lý do con', nullable: true })
  child: Array<ReasonMasterResponseDto>;

  constructor(entity: ReasonMasterEntity) {
    this.id = entity.id;
    this.code = entity.code;
    this.name = entity.name;
    this.categoryId = entity.category;
    this.groupId = entity.group;
    this.subGroupId = entity.subGroup;
    this.group = entity.groupCode;
    this.category = entity.categoryCode;
    this.subGroup = entity.subGroupCode;
    this.description = entity.description;
    this.isActive = entity.isActive;
    this.parentId = entity.parent?.id;
    this.note = entity.note;
    this.createdDate = entity.createdDate;
    this.updatedDate = entity.updatedDate;
    this.child = ((entity as any).child as any) || [];
  }
}
