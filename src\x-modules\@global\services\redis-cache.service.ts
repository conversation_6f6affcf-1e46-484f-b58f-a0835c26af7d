import { Injectable, Logger } from '@nestjs/common';
import Redis from 'ioredis';

@Injectable()
export class RedisCacheService {
  private readonly logger = new Logger(RedisCacheService.name);
  private redisClient: Redis;

  constructor() {
    // Khởi tạo Redis client
    // this.redisClient = new Redis({
    //   host: process.env.REDIS_HOST || 'localhost',
    //   port: Number(process.env.REDIS_PORT) || 6379,
    //   password: process.env.REDIS_PASSWORD || '',
    //   db: 0,
    // });
    // this.redisClient.on('connect', () => {
    //   this.logger.log('Connected to Redis');
    // });
    // this.redisClient.on('error', (err) => {
    //   this.logger.error('Redis connection error:', err);
    // });
  }

  /**
   * Tạo key cache với tên bảng và ID duy nhất
   * @param tableName: Tên bảng
   * @param id: ID của đối tượng
   */
  private generateCacheKey(tableName: string, id: string): string {
    return `${tableName}:${id}`;
  }

  /**
   * Thêm dữ liệu vào cache
   * @param tableName: Tên bảng
   * @param id: ID đối tượng
   * @param value: Dữ liệu cần lưu vào cache
   * @param ttl: Thời gian tồn tại cache (số giây)
   */
  async setCache(tableName: string, id: string, value: string, ttl: number = 3600): Promise<void> {
    const key = this.generateCacheKey(tableName, id); // Tạo key duy nhất
    try {
      await this.redisClient.setex(key, ttl, value);
      this.logger.log(`Cache set successfully for key: ${key}`);
    } catch (error) {
      this.logger.error(`Failed to set cache for key: ${key}`, error.stack);
      throw new Error('Failed to set cache');
    }
  }

  /**
   * Lấy dữ liệu từ cache
   * @param tableName: Tên bảng
   * @param id: ID của đối tượng
   */
  async getCache(tableName: string, id: string): Promise<string | null> {
    const key = this.generateCacheKey(tableName, id); // Tạo key duy nhất
    try {
      const value = await this.redisClient.get(key);
      if (value) {
        this.logger.log(`Cache retrieved successfully for key: ${key}`);
        return value;
      }
      this.logger.warn(`No cache found for key: ${key}`);
      return null;
    } catch (error) {
      this.logger.error(`Failed to get cache for key: ${key}`, error.stack);
      throw new Error('Failed to get cache');
    }
  }

  /**
   * Xóa dữ liệu cache theo key duy nhất
   * @param tableName: Tên bảng
   * @param id: ID đối tượng
   */
  async removeCache(tableName: string, id: string): Promise<void> {
    const key = this.generateCacheKey(tableName, id); // Tạo key duy nhất
    try {
      const result = await this.redisClient.del(key);
      if (result === 1) {
        this.logger.log(`Cache removed successfully for key: ${key}`);
      } else {
        this.logger.warn(`No cache found to remove for key: ${key}`);
      }
    } catch (error) {
      this.logger.error(`Failed to remove cache for key: ${key}`, error.stack);
      throw new Error('Failed to remove cache');
    }
  }

  /**
   * Kiểm tra kết nối Redis
   */
  async ping(): Promise<void> {
    try {
      const response = await this.redisClient.ping();
      if (response === 'PONG') {
        this.logger.log('Redis is alive');
      } else {
        this.logger.error('Redis is not responding properly');
      }
    } catch (error) {
      this.logger.error('Failed to ping Redis', error.stack);
      throw new Error('Redis is not responding');
    }
  }
}
