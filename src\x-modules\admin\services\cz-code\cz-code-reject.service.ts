import { Injectable } from '@nestjs/common';
import moment from 'moment';
import { Connection } from 'typeorm';
import { BindRepo } from '~/@core/decorator';
import { CzCodeCheckReq } from '~/dto/cz-code.dto'
import { UniqueCodeActivationRepo } from '~/repositories/primary';
import { CzCodeService } from '../cz-code'

@Injectable()
export class CzCodeRejectService {
  constructor( 
    private readonly connection: Connection,
    private readonly czCodeService: CzCodeService
  ) {}

  @BindRepo(UniqueCodeActivationRepo)
  private uniqueCodeActivationRepo: UniqueCodeActivationRepo;

  async findCzCodeByCode(code: string) {
    if(!code || code === '') return undefined;

    const query = `
      SELECT 
        poucd.*,
        po."orderNo",
        po."lotNumber",
        po."planStartDate",
        i."shelfLifeDay"
      FROM production_order_unique_code_detail poucd 
      INNER JOIN production_order po ON po.id = poucd."orderId" 
      INNER join item i on i.id = po."itemId" 
      WHERE poucd."uniqueCode" = $1
    `
    const result = await this.connection.query(query,[code]);
    return result?.length > 0 ? result[0] : undefined
  }

  handleExpiredDate(productionDate:Date | null, shelfLifeDay: number) {
      if (!productionDate) return '';
      const shelfLifeDayValue = shelfLifeDay ? shelfLifeDay : 0
  
      const newDate = moment(productionDate, 'DD/MM/YYYY').add(shelfLifeDayValue, 'days');
      return newDate.toDate();
    }

  async handleSuccessRep(req: CzCodeCheckReq, code: Number, message: string) {
    let czCodeBody = {}
    if(code !== 400){ 
      const czCodeInfo = await this.czCodeService.czCodeCheckService(req);
      czCodeBody = czCodeInfo?.body || {};
    }  
    return {
        statusCode: code,
        message: message || 'Reject CzCode successfull !',
        czCode: req?.czCode || '',
        body: czCodeBody
    }
  }

  async czCodeRejectService(data: CzCodeCheckReq) {
    try {
      const czCodeReq = data?.czCode || '';

      // Tìm cz code trong bảng ProductionOrderUniqueCodeDetailRepo 
      const czCodeData = await this.findCzCodeByCode(czCodeReq)
      // Nếu không tìm thấy cz code được khai báo thì trả về lỗi 
      if(!czCodeData) return this.handleSuccessRep(data, 400, 'CzCode is not found !');

      // Tìm tiếp trong bảng uniqueCodeActivationRepo: czCode này đã được xử lý chưa.
      const uniqueCodeActivationData = await this.uniqueCodeActivationRepo.findOne({
        where: { uniqueCode : czCodeReq }
      })

      if(uniqueCodeActivationData){
        // Nếu czCode đã bị reject thì không update thêm.
        if(uniqueCodeActivationData?.status === -1) return this.handleSuccessRep(data, 409, 'CzCode has been rejected');

        let rejectObj = { status: -1 }
        // xét posted in (1,2) (đang, đã tạo trx) thì update về -1 (ouput po 60.5 sẽ tính lại), ngược lại update về 0
        const postedChecker = [1,2].includes(uniqueCodeActivationData?.posted);
        if(postedChecker) rejectObj['posted'] = -1;

        await this.uniqueCodeActivationRepo.update({id: uniqueCodeActivationData?.id}, rejectObj);
      }else{
        return this.handleSuccessRep(data, 409, 'CzCode not activated, cannot reject');
      }
      return this.handleSuccessRep(data, 200, 'CzCode is reject successfully !');
    } catch (error) {
      return this.handleSuccessRep(data, 400, error.message || 'Failed to reject CzCode')
    }
  }
}
