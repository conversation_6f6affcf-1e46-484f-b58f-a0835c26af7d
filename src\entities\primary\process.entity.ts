import { Column, Entity, Index, JoinColumn, ManyToOne, OneToMany } from 'typeorm';
import { PrimaryBaseEntity } from '../primary-base.entity';
import { OrganizationUnitEntity } from './organization-unit.entity';
import { RecipeProcessEntity } from './recipe-process.entity';
import { ProcessMachineEntity } from './process-machine.entity';
import { SetPointEntity } from './set-point.entity';

@Entity('process')
export class ProcessEntity extends PrimaryBaseEntity {
  @Column({ unique: true, length: 50 })
  @Index()
  code: string;

  @Column({ nullable: true })
  version: number;

  @Column({ nullable: true })
  name: string;

  @Column({ type: 'text', nullable: true })
  description: string;

  @Column({ nullable: true, length: 15 })
  ipAddress: string;

  @Column({ nullable: true })
  gatewayProtocol: string;

  @Column({ nullable: true })
  category: string;

  @Column({ nullable: true })
  outputCalculationMethodCode: string;

  @Column({ default: true })
  isActive: boolean;

  @Column({ type: 'text', nullable: true })
  note: string;

  @OneToMany(() => RecipeProcessEntity, (rp) => rp.process)
  recipeProcesses: Array<RecipeProcessEntity>;

  @Column({ type: 'uuid', nullable: true })
  organizationUnitId: string;

  @ManyToOne(() => OrganizationUnitEntity, (organization) => organization.processes)
  @JoinColumn({ name: 'organizationUnitId', referencedColumnName: 'id' })
  organization?: OrganizationUnitEntity;

  @OneToMany(() => ProcessMachineEntity, (pm) => pm.process)
  processMachines: Promise<ProcessMachineEntity[]>;

  @OneToMany(() => SetPointEntity, (setPoint) => setPoint.process)
  setPoints: SetPointEntity[];
}
