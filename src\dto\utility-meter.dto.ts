// src/x-modules/admin/dtos/system-configuration/utility-meter.dto.ts
import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { IsBoolean, IsNotEmpty, IsOptional, IsString, IsUUID } from 'class-validator';

export class UtilityMeterQueryDto {
  @ApiPropertyOptional({ description: 'Page number', default: 1 })
  @IsOptional()
  page?: number = 1;

  @ApiPropertyOptional({ description: 'Items per page', default: 10 })
  @IsOptional()
  limit?: number = 10;

  @ApiPropertyOptional({ description: 'Search by name' })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({ description: 'Filter by factory ID' })
  @IsOptional()
  @IsUUID()
  factoryId?: string;

  @ApiProperty({ description: 'Utility Meter Status' })
  @IsOptional()
  isActive?: boolean;

  @ApiProperty({ description: 'Utility Meter Code' })
  @IsOptional()
  code?: string;

  @ApiProperty({ description: 'Utility Meter Name' })
  @IsOptional()
  name?: string;

  @ApiProperty({ description: 'Utility Meter Type' })
  @IsOptional()
  utilityMeterTypeDetailCode?: string;
}

export class UtilityMeterReq {
  @ApiProperty({ description: 'Meter id' })
  @IsOptional()
  id?: string;

  @ApiProperty({ description: 'Meter code' })
  @IsOptional()
  code?: string;

  @ApiProperty({ description: 'Meter name', required: false })
  @IsOptional()
  @IsString()
  name?: string;

  @ApiProperty({ description: 'Factory ID' })
  @IsUUID()
  @IsNotEmpty()
  factoryId: string;

  @ApiProperty({ description: 'Tag name', required: false })
  @IsOptional()
  @IsString()
  tagName?: string;

  @ApiProperty({ description: 'Tag address', required: false })
  @IsOptional()
  @IsString()
  tagAddress?: string;

  @ApiProperty({ description: 'UOM code', required: false })
  @IsOptional()
  @IsString()
  uomCode?: string;

  @ApiProperty({ description: 'UOM detail code', required: false })
  @IsOptional()
  @IsString()
  uomDetailCode?: string;

  @ApiProperty({ description: 'Utility meter type code' })
  @IsString()
  @IsNotEmpty()
  utilityMeterTypeCode: string;

  @ApiProperty({ description: 'Utility meter type detail code' })
  @IsString()
  @IsNotEmpty()
  utilityMeterTypeDetailCode: string;

  @ApiProperty({ description: 'Data type code' })
  @IsString()
  @IsNotEmpty()
  dataTypeCode: string;

  @ApiProperty({ description: 'Data type detail code' })
  @IsString()
  @IsNotEmpty()
  dataTypeDetailCode: string;

  @ApiProperty({ description: 'Datetime unit code', required: false })
  @IsOptional()
  @IsString()
  datetimeUnitCode?: string;

  @ApiProperty({ description: 'Datetime unit detail code', required: false })
  @IsOptional()
  @IsString()
  datetimeUnitDetailCode?: string;

  @ApiProperty({ description: 'Calculation method code', required: false })
  @IsOptional()
  @IsString()
  calMethodCode?: string;

  @ApiProperty({ description: 'UOM calculation method code', required: false })
  @IsOptional()
  @IsString()
  uomCalMethodCode?: string;

  @ApiProperty({ description: 'Resource code', required: false })
  @IsOptional()
  @IsString()
  resourceCode?: string;

  @ApiProperty({ description: 'Asset ID', required: false })
  @IsOptional()
  @IsString()
  assetId?: string;

  @ApiProperty({ description: 'Measurement ID', required: false })
  @IsOptional()
  @IsString()
  measurementId?: string;

  @ApiProperty({ description: 'Metric ID', required: false })
  @IsOptional()
  @IsString()
  metricId?: string;

  @ApiProperty({ description: 'Process', required: false })
  @IsOptional()
  processAreaDetails?: ProcessAreaReq[];
}

export class ProcessAreaReq {
  @ApiProperty({ description: 'Process area ID' })
  @IsUUID()
  @IsNotEmpty()
  processAreaId: string;

  @ApiProperty({ description: 'Is assigned' })
  @IsBoolean()
  @IsNotEmpty()
  isAssigned: boolean;
}
