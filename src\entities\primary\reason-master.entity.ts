import { ApiProperty } from '@nestjs/swagger';
import { Column, Entity, JoinColumn, ManyToOne, OneToMany } from 'typeorm';
import { PrimaryBaseEntity } from '../primary-base.entity';

@Entity('reason_master')
export class ReasonMasterEntity extends PrimaryBaseEntity {
  @ApiProperty({ description: 'Mã lý do duy nhất' })
  @Column({ unique: true })
  code: string;

  @ApiProperty({ description: 'Tên của lý do' })
  @Column({ type: 'text', nullable: true })
  name: string;

  @ApiProperty({ description: 'Danh mục của lý do' })
  @Column({ nullable: true, default: '' })
  category: string;

  @ApiProperty({ description: 'Code của danh mục của lý do' })
  @Column({ nullable: true, default: '' })
  categoryCode: string;

  @ApiProperty({ description: 'Id của danh mục của lý do' })
  @Column({ nullable: true, default: '' })
  categoryId: string;

  @ApiProperty({ description: 'Nhóm của lý do' })
  @Column({ nullable: true, default: '' })
  group: string;

  @ApiProperty({ description: 'Code của nhóm của lý do' })
  @Column({ nullable: true, default: '' })
  groupCode: string;

  @ApiProperty({ description: 'Id của nhóm của lý do' })
  @Column({ nullable: true, default: '' })
  groupId: string;

  @ApiProperty({ description: 'Nhóm con của lý do' })
  @Column({ nullable: true, default: '' })
  subGroup: string;

  @ApiProperty({ description: 'Code của nhóm con của lý do' })
  @Column({ nullable: true, default: '' })
  subGroupCode: string;

  @ApiProperty({ description: 'Id của nhóm con của lý do' })
  @Column({ nullable: true, default: '' })
  subGroupId: string;

  @ApiProperty({ description: 'Mô tả chi tiết về lý do' })
  @Column({ nullable: true, type: 'text' })
  description: string;

  @ApiProperty({ description: 'Trạng thái hoạt động của lý do' })
  @Column({ default: true })
  isActive: boolean;

  @ApiProperty({ description: 'ID của lý do cha', nullable: true })
  @Column({ type: 'varchar', nullable: true })
  parentId: string;

  @ApiProperty({ description: 'Lý do cha', nullable: true })
  @ManyToOne(() => ReasonMasterEntity, reason => reason.children, { nullable: true })
  @JoinColumn({ name: 'parentId', referencedColumnName: 'id' })
  parent: ReasonMasterEntity;

  @ApiProperty({ description: 'Danh sách lý do con' })
  @OneToMany(() => ReasonMasterEntity, reason => reason.parent)
  children: ReasonMasterEntity[];

  @ApiProperty({ description: 'Ghi chú bổ sung', nullable: true })
  @Column({ nullable: true, type: 'text' })
  note: string;
}
