import { Request } from 'express';

const requestHelper = (req: Request) => {
  const originalForwarded = req.headers['x-original-forwarded-for'];
  const forwardedFor = req.headers['x-forwarded-for'];
  const realIP = req.headers['x-real-ip'];
  const cfIP = req.headers['cf-connecting-ip'];

  const ipHeaders = {
    'x-original-forwarded-for': req.headers['x-original-forwarded-for'],
    'cf-connecting-ip': req.headers['cf-connecting-ip'],
    'x-real-ip': req.headers['x-real-ip'],
    'x-forwarded-for': req.headers['x-forwarded-for'],
    forwarded: req.headers['forwarded'],
    'true-client-ip': req.headers['true-client-ip'],
    'fastly-client-ip': req.headers['fastly-client-ip'],
    'x-cluster-client-ip': req.headers['x-cluster-client-ip'],
    'x-forwarded': req.headers['x-forwarded'],
    via: req.headers['via'],
    remoteAddress: req.socket?.remoteAddress || req.connection?.remoteAddress,
    'req.ip': req.ip,
  };

  // Ưu tiên theo thứ tự: originalForwarded > cfIP > realIP > x-forwarded-for > req.ip
  const clientIP =
    (Array.isArray(originalForwarded)
      ? originalForwarded[0]
      : originalForwarded?.split(',')[0]?.trim()) ||
    (Array.isArray(cfIP) ? cfIP[0] : cfIP?.split(',')[0]?.trim()) ||
    (Array.isArray(realIP) ? realIP[0] : realIP?.split(',')[0]?.trim()) ||
    (Array.isArray(forwardedFor) ? forwardedFor[0] : forwardedFor?.split(',')[0]?.trim()) ||
    req.ip;

  return {
    clientIP,
    ipHeaders: ipHeaders,
  };
};

export default requestHelper;
