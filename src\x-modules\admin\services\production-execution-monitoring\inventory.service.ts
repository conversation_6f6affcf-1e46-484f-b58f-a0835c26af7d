import { Injectable, NotFoundException } from '@nestjs/common';
import { ILike } from 'typeorm';
import { configEnv } from '~/@config/env';
import { BindRepo } from '~/@core/decorator';
import { NSGeneralData, NSSQSS } from '~/common/enums';
import { EOrganizationLevel } from '~/common/enums/organization-level.enum';
import {
  RequestGetManualGPCInventory,
  RequestListFactoryInventory,
  RequestListSiteInventory,
  RequestPaginationInventory,
} from '~/dto/inventory.dto';
import { InventoryEntity } from '~/entities/primary';
import { ItemRepo, OrganizationUnitRepo } from '~/repositories/primary';
import { InventoryRepository } from '~/repositories/primary/inventory.repo';
import { SnsService } from '~/x-modules/sqs/sns.service';
const { TOKEN_ERP_URL, ERP_INVENTORY, EBS_MES_USERNAME, EBS_MES_PASSWORD } = configEnv();

@Injectable()
export class InventoryService {
  constructor(private readonly snsService: SnsService) {}
  @BindRepo(InventoryRepository)
  private InventoryRepo: InventoryRepository;

  @BindRepo(OrganizationUnitRepo)
  private OrganUnit: OrganizationUnitRepo;

  @BindRepo(ItemRepo)
  private ItemRepo: ItemRepo;

  /**
   * Lấy token GPC
   * @param params
   * @returns
   */

  async getTokenERP() {
    try {
      const response = await fetch(TOKEN_ERP_URL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username: EBS_MES_USERNAME,
          password: EBS_MES_PASSWORD,
          api_url: ERP_INVENTORY,
        }),
      });

      const result = await response.json();

      return result.Token;
    } catch (error) {
      console.log(error);
      throw new Error(`Failed to get token from ERP: ${error}`);
    }
  }

  /**
   * Lấy danh sách inventory GPC
   * @param params
   * @returns
   */

  async getManualERP(params: RequestGetManualGPCInventory) {
    const site = await this.OrganUnit.findOne({
      where: {
        id: params.siteId,
      },
    });

    const factory = await this.OrganUnit.findOne({
      where: {
        id: params.factoryId,
        parentId: params.siteId,
      },
    });

    if (!site) {
      throw new NotFoundException('Can not find the site');
    }

    if (!factory) {
      throw new NotFoundException('Can not find the factory');
    }

    return await this.storeGPCRecords(site.code, factory.code);
  }

  /**
   * Lưu danh sách inventory GPC
   * @param params
   * @returns
   */

  async storeGPCRecords(plantCode: string, factoryCode: string) {
    const tokenERP = await this.getTokenERP();
    const urlInventory = `${ERP_INVENTORY}?plant=${plantCode}&Sub_inventory=${factoryCode}`;

    const response = await fetch(urlInventory, {
      method: 'GET',
      headers: {
        Authorization: `Bearer ${tokenERP}`,
        'Content-Type': 'application/json',
      },
    });

    if (!response.ok) {
      throw new Error(`Failed to fetch ERP data: ${response.statusText}`);
    }

    const data = await response.json();

    if (data.code != 200) {
      throw new Error(`Failed to fetch ERP data: ${data.message}`);
    }

    const inventoryArray: any = data.data[0].refs;
    const records = [];

    for (let inventory of inventoryArray) {
      const entity = new InventoryEntity();
      entity.materialCode = inventory?.MaterialCode ?? '';
      entity.manufacture = inventory?.Manufacture ?? '';
      entity.materialLotNumber = inventory?.MaterialLotNumber ?? '';
      entity.plant = inventory?.Plant ?? '';
      entity.subInventory = inventory?.Sub_inventory ?? '';
      entity.locator = inventory?.Locator ?? '';
      entity.lotStatus = inventory?.LotStatus ?? '';
      entity.expiredDate = inventory?.Exprired_Date ? new Date(inventory?.Exprired_Date) : null;
      entity.productionDate = inventory?.Production_Date
        ? new Date(inventory?.Production_Date)
        : null;
      entity.receiptDate = inventory?.Receipt_Date ? new Date(inventory?.Receipt_Date) : null;
      entity.baseQty = inventory?.Base_qty ?? '';
      entity.baseUom = inventory?.Base_uom ?? '';
      entity.inventoryQty = inventory?.Inventory_qty ?? '';
      entity.inventoryUom = inventory?.Inventory_uom ?? '';
      entity.palletQty = inventory?.Pallet_qty ?? '';
      entity.supplier = inventory?.Supplier ?? '';

      let materialName = '';

      if (inventory?.MaterialCode) {
        const item = await this.ItemRepo.findOne({
          code: inventory?.MaterialCode,
        });

        if (item && item?.name) {
          materialName = item.name;
        }
      }

      entity.materialName = materialName;

      records.push(entity);
    }

    const deleteOldRecords = await this.InventoryRepo.delete({
      plant: plantCode,
      subInventory: factoryCode,
    });

    const result = await this.InventoryRepo.save(records, { chunk: 100 });
    this.snsService.sendMessage(
      {
        message: 'SyncInventoryToScada Manual',
        data: {
          lstId: result.map((item) => item.id),
        },
      },
      NSSQSS.EMessageType.SyncInventoryToScada,
    );
    return result;
  }

  /**
   * Lấy danh sách pagination
   * @param params
   * @returns
   */

  async pagination(params: RequestPaginationInventory) {
    let whereCon: any = {};

    if (params?.siteCode) {
      whereCon.plant = params.siteCode;
    }

    if (params?.factoryCode) {
      whereCon.subInventory = params.factoryCode;
    }

    if (params?.locatorCode) {
      whereCon.locator = ILike(`%${params.locatorCode}%`);
    }

    if (params?.materialLotNumber) {
      whereCon.materialLotNumber = ILike(`%${params.materialLotNumber}%`);
    }

    if (params?.materialCode) {
      whereCon.materialCode = ILike(`%${params.materialCode}%`);
    }

    return await this.InventoryRepo.findPagination(
      { where: whereCon, order: { createdDate: 'DESC' } },
      params,
    );
  }

  /**
   * Lấy danh sách site
   * @param params
   * @returns
   */

  async listSite(params: RequestListSiteInventory) {
    return await this.OrganUnit.find({
      where: {
        levelGeneralDataParentCode: NSGeneralData.EGeneralDataCode.ORG_LEVEL,
        levelGeneralDataDetailCode: EOrganizationLevel.Site,
        isActive: true,
      },
    });
  }

  /**
   * Lấy danh sách factory
   * @param params
   * @returns
   */

  async listFactory(params: RequestListFactoryInventory) {
    if (params.siteId == 'All') {
      return await this.OrganUnit.find({
        where: {
          isActive: true,
        },
      });
    }

    return await this.OrganUnit.find({
      where: {
        parentId: params.siteId,
        levelGeneralDataParentCode: NSGeneralData.EGeneralDataCode.ORG_LEVEL,
        levelGeneralDataDetailCode: EOrganizationLevel.Factory,
        isActive: true,
      },
    });
  }

  /**
   * Lấy data cho filters
   * @param params
   * @returns
   */

  async listFilters() {
    const listInventory = await this.InventoryRepo.find({});

    const filters = listInventory.reduce(
      (acc, cur) => {
        if (cur.plant && !acc.plant.some((p) => p.code === cur.plant)) {
          acc.plant.push({
            code: cur.plant,
            name: cur.plant,
          });
        }

        if (cur.subInventory && !acc.subInventory.some((s) => s.code === cur.subInventory)) {
          acc.subInventory.push({
            code: cur.subInventory,
            name: cur.subInventory,
          });
        }

        if (cur.materialCode && !acc.materialCode.some((m) => m.code === cur.materialCode)) {
          acc.materialCode.push({
            code: cur.materialCode,
            name: cur.materialCode,
          });
        }

        if (cur.locator && !acc.locator.some((l) => l.code === cur.locator)) {
          acc.locator.push({
            code: cur.locator,
            name: cur.locator,
          });
        }

        if (
          cur.materialLotNumber &&
          !acc.materialLotNumber.some((l) => l.code === cur.materialLotNumber)
        ) {
          acc.materialLotNumber.push({
            code: cur.materialLotNumber,
            name: cur.materialLotNumber,
          });
        }

        return acc;
      },
      {
        plant: [],
        subInventory: [],
        materialCode: [],
        locator: [],
        materialLotNumber: [],
      },
    );

    return filters;
  }
}
