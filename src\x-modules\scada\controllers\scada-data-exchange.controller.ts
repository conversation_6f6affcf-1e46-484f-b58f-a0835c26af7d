import { Controller, Post, Body, Get, UseGuards } from '@nestjs/common';
import { AcceptMaterialConsumptionReq, ScadaDataExchangeReq } from '~/dto/scada.dto';
import { ApiOperation } from '@nestjs/swagger';
import { DockerHookService } from './../services/docker-hook.service';
import { ScadaAuthGuard } from '~/@systems/guard/scada-auth.guard';
@Controller('')
@UseGuards(ScadaAuthGuard)
export class ScadaDataExchangeController {
  constructor(private readonly dockerHookService: DockerHookService) {}

  @ApiOperation({ summary: 'Tính năng tích hợp dữ liệu downtime từ hệ thống SCADA' })
  @Post('accept-end-downtime')
  acceptEndDowntime(@Body() body: ScadaDataExchangeReq) {
    return this.dockerHookService.scadaDataExchange(body);
  }

  @ApiOperation({ summary: '<PERSON><PERSON><PERSON> năng ghi nhận dữ liệu material consumption từ hệ thống SCADA' })
  @Post('accept-material-consumption')
  acceptMaterialConsumption(@Body() body: AcceptMaterialConsumptionReq) {
    return this.dockerHookService.acceptMaterialConsumption(body);
  }
}
