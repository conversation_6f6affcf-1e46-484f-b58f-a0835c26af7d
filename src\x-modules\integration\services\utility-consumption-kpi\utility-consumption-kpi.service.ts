import { Injectable } from '@nestjs/common';
import { BindRepo } from '~/@core/decorator';
import moment from 'moment';
import { JwtService } from '@nestjs/jwt';
import { Connection } from 'typeorm';
import { promises } from 'dns';
import { dateHelper } from '~/common/helpers/date.helper';
import {
  KpiRepo,
  KpiSetDetailRepo,
  KpiSetGroupRepo,
  KpiSetHeaderRepo,
  KpiSetProductAreaRepo,
  KpiScoreRepo,
} from '~/repositories/primary';

@Injectable()

export class UtilityConsumptionKpi {
  constructor(
    private jwtService: JwtService,
    private readonly connection: Connection
  ) {}
  @BindRepo(KpiRepo)
  private readonly kpiRepo: KpiRepo;
  @BindRepo(KpiScoreRepo)
  private readonly kpiScoreRepo: KpiScoreRepo;
  @BindRepo(KpiSetHeaderRepo)
  private readonly kpiSetHeaderRepo: KpiSetHeaderRepo;
  @BindRepo(KpiSetGroupRepo)
  private readonly kpiSetGroupRepo: KpiSetGroupRepo;
  @BindRepo(KpiSetDetailRepo)
  private readonly kpiSetDetailRepo: KpiSetDetailRepo;
  @BindRepo(KpiSetProductAreaRepo)
  private readonly kpiSetProductAreaRepo: KpiSetProductAreaRepo;

  async onGetKpiPeriodId (date: any) {
    const getDate = moment(date, "YYYY-MM-DD HH:mm:ss.SSS Z").format('YYYY-MM-DD');
    const KpiPeriodData = await this.connection.query(`
      SELECT kp.*
      FROM kpi_period kp
      join kpi_period_rule kpr on kp."kpiPeriodRuleId" = kpr."id"
      WHERE DATE($1) BETWEEN DATE(kp."startDate") AND DATE(kp."endDate")
      and kpr."cycleCode" = '1'
    `, [getDate]);
    return KpiPeriodData[0] ? KpiPeriodData[0]?.id : null;
  }

  async onHandleTotal (getDateFrom: any, getDateTo: any) {
    let whereDateFilter: string;
    let queryParams: any[] = [];

    if (getDateFrom && getDateTo) {
      whereDateFilter = `ut."date" BETWEEN $1 AND $2`;
      queryParams.push(getDateFrom, getDateTo);
    } else {
      whereDateFilter = `DATE(ut."date") BETWEEN CURRENT_DATE - INTERVAL '2 days' AND CURRENT_DATE`;
    }
    const result = await this.connection.query(`
      WITH  
        df_kpivalue AS (
        select DATE(ut."date") as podate ,um.factory_id ,ut."shiftId" , um.utility_meter_type_detail_code , 
        SUM(ut.value) as value , MAX(poe.actualqty) totalqty, 
        CASE 
            WHEN MAX(poe.actualqty) IS NULL OR MAX(poe.actualqty) = 0 THEN 0
            ELSE SUM(ut.value)*1000 / MAX(poe.actualqty)
        END --SUM(ut.value)*1000/MAX(poe.actualqty) 
        as KPIValue --,umd.process_area_id,um.resource_code, ut."meterId" , ut.posted --, ut."postedAt" 
        from utility_transaction ut 
        inner join utility_meter um on ut."meterId"  = um.id
        inner join utility_meter_detail umd on um.id = umd.utility_meter_id
        inner join production_area_detail pad2  on pad2."organizationUnitId" =umd.process_area_id
        inner join 
          (select po."shiftId", s.code , SUM(po."actualProductQty") - SUM(po."defectiveProducts") actualQTy, ou."parentId" as Factory_id,  date(po."productionDate") as date --po."productionLineId", ou_fak.code 
          from production_oee po 
          inner join organization_units ou  on ou.id = cast(po."productionLineId" as uuid )
          inner join organization_units ou_fak on ou."parentId"=ou_fak.id
          inner join shift s on po."shiftId" = s.id--::Text
          group by po."shiftId",ou."parentId",po."productionDate", s.code ,ou_fak.code ) as poe 
        on poe."shiftId" = ut."shiftId" and poe.factory_id = um.factory_id and poe."date" = DATE(ut."date")
        where pad2."isActive" = true and ${whereDateFilter}
        group by DATE(ut."date") ,um.factory_id , ut."shiftId" , um.utility_meter_type_detail_code
        having MAX(poe.actualqty) <>0
        ),

        df_kpi_set as(
        SELECT ksh.* ,'||' "||", ksg."kpiGroupId",ksg."kpiGroupCode", ksg."kpiGroupName", ksg."kpiSetHeaderId", ksg.id as idbanggroup,
        '||' "||", ksd."kpiId", ksd."kpiSetGroupId" ,'||' "||", k.code, k."shortName", k."utilityType" ,'||' "||", 
        kspa."factoryId" , ou.code , kspa."productionAreaId", pa.code , pa."name" , kspa."siteId", k."inputFrequency", k.unit 
        --kp.kpivalue , kp."shiftId" , kp.podate , ksd."id" AS "kpiSetDetailId", 
        from kpi_set_header ksh 
        join kpi_set_group ksg  on ksh.id = ksg."kpiSetHeaderId"
        join kpi_set_detail ksd on ksd."kpiSetGroupId" = ksg.id 
        join kpi k  on k.id = ksd."kpiId"
        join kpi_set_production_area kspa  on ksh.id = kspa."kpiSetHeaderId"
        join organization_units ou on kspa."factoryId" = ou.id
        join production_area pa on pa.id = kspa."productionAreaId"
        --left join kpivalue as kp on kp.factory_id = kspa."factoryId" and kp."productionAreaId" =kspa."productionAreaId" and kp.utility_meter_type_detail_code =k."utilityType"
        where ksh.status = 1 and k."kpiMethod" = 'A' and k."autoFunction"  = 'F_KPI_UTILITY' and k."utilityType" is not null  and kspa.status =1 and k.status =1 --and ksh.code ='TTSET08'
        )

        select dks.*,'break' "break",dkv.* 
        from df_kpi_set dks
        inner join df_kpivalue dkv 
        on dks."utilityType"=dkv.utility_meter_type_detail_code and dks."factoryId" = dkv.factory_id
    `, queryParams);
    return result;
  }

  async createUtilityConsumptionKpi(data: { fromDate?: string; toDate?: string }) {
    try {
      let getDateFrom = null
      let getDateTo = null
      if(data.fromDate && data.toDate){
        getDateFrom = dateHelper.formatDateByJobManual(data.fromDate)
        getDateTo = dateHelper.formatDateByJobManual(data.toDate)
      }

      const onHandleTotalValue = await this.onHandleTotal(getDateFrom, getDateTo)
      if(!onHandleTotalValue || onHandleTotalValue?.length === 0) return;
      await Promise.all(
        onHandleTotalValue.map(async (totalValue:any) => {
          const getKpiPeriodId = await this.onGetKpiPeriodId(totalValue?.podate) || null;
          if(!getKpiPeriodId) return;

          const kpiScoreObj = {
              kpiSetHeaderId: totalValue?.kpiSetHeaderId,
              siteId: totalValue?.siteId,
              factoryId: totalValue?.factory_id,
              productionAreaId: totalValue?.productionAreaId,
              kpiSetGroupId: totalValue?.kpiSetGroupId,
              kpiId: totalValue?.kpiId,
              shiftId: totalValue?.shiftId,
              kpiSetDetailId: totalValue?.kpiSetDetailId,
              kpiPeriodId: getKpiPeriodId,
              actualScore: totalValue?.kpivalue,
              actualValue: totalValue?.value,
              actualQty: totalValue?.totalqty,
              scoreDate: totalValue?.podate,
              source: 'Job41',
              createdByUser: "-1",
              updatedByUser: '-1'
            };

            const existing = await this.kpiScoreRepo.findOne({
              where: {
                productionAreaId: kpiScoreObj?.productionAreaId,
                kpiSetHeaderId: kpiScoreObj?.kpiSetHeaderId,
                shiftId: kpiScoreObj?.shiftId,
                scoreDate: kpiScoreObj?.scoreDate,
                source: 'Job41',
              },
            });
            // Nếu đã có record trùng bộ key thì không insert tiếp.
            if(existing){
              await this.kpiScoreRepo.update({ id: existing?.id },
                {
                  actualScore: kpiScoreObj.actualScore,
                  actualValue: kpiScoreObj?.actualValue,
                  actualQty : kpiScoreObj?.actualQty
                }
              )
            }else{
              await this.kpiScoreRepo.save(kpiScoreObj);
            }
        })
      )
    } catch (error) {
      console.log(error, 'error');
    }
  }
}
