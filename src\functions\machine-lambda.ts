import { Context, Handler } from 'aws-lambda';
import { NestFactory } from '@nestjs/core';
import { plainToInstance } from 'class-transformer';
import { validate } from 'class-validator';
import { AppModule } from '~/app.module';
import { MachineReq, ParameterReq } from '~/dto/machine.dto';
import { MachineService } from '~/x-modules/admin/services';
let cachedApp: any = null;

async function bootstrap() {
  if (!cachedApp) {
    const app = await NestFactory.createApplicationContext(AppModule);
    // const app = await NestFactory.create(AppModule);
    await app.init();
    cachedApp = app;
  }
  return cachedApp;
}

// 🆕 Hàm validate dữ liệu đầu vào
async function validateInput(dtoClass: any, data: any) {
  const instance = plainToInstance(dtoClass, data);
  const errors = await validate(instance);
  if (errors.length > 0) {
    throw new Error(JSON.stringify(errors));
  }
}

// 🛠 Tạo máy mới
export const create: Handler = async (event: any, context: Context) => {
  const app = await bootstrap();
  const machineService = app.get(MachineService);
  const body = JSON.parse(event.body);

  await validateInput(MachineReq, body);
  const result = await machineService.create(body);

  return {
    statusCode: 201,
    body: JSON.stringify({ message: 'Create machine success', data: result }),
  };
};

// 📜 Lấy danh sách máy
export const list: Handler = async (event: any, context: Context) => {
  const app = await bootstrap();
  const machineService = app.get(MachineService);
  const params = event.queryStringParameters || {};

  const result = await machineService.list(params);
  return {
    statusCode: 200,
    body: JSON.stringify({ message: 'List machines success', data: result }),
  };
};

// 🔍 Lấy chi tiết máy
export const detail: Handler = async (event: any, context: Context) => {
  const app = await bootstrap();
  const machineService = app.get(MachineService);
  const { id } = event.pathParameters;

  const result = await machineService.detail(id);
  if (!result) {
    return {
      statusCode: 404,
      body: JSON.stringify({ message: 'Machine not found' }),
    };
  }

  return {
    statusCode: 200,
    body: JSON.stringify({ message: 'Get machine success', data: result }),
  };
};

// ✏️ Cập nhật máy
export const update: Handler = async (event: any, context: Context) => {
  const app = await bootstrap();
  const machineService = app.get(MachineService);
  const { id } = event.pathParameters;
  const body = JSON.parse(event.body);

  await validateInput(MachineReq, body);
  const result = await machineService.update(id, body);

  return {
    statusCode: 200,
    body: JSON.stringify({ message: 'Update machine success', data: result }),
  };
};

// ❌ Xóa máy
export const deleteMachine: Handler = async (event: any, context: Context) => {
  const app = await bootstrap();
  const machineService = app.get(MachineService);
  const { id } = event.pathParameters;

  await machineService.delete(id);
  return {
    statusCode: 200,
    body: JSON.stringify({ message: 'Delete machine success' }),
  };
};

// 📌 Lấy danh sách parameters của máy
export const listMachineParameter: Handler = async (event: any, context: Context) => {
  const app = await bootstrap();
  const machineService = app.get(MachineService);
  const { machineId } = event.pathParameters;
  const params = event.queryStringParameters || {};

  const result = await machineService.listMachineParameter(machineId, params);
  return {
    statusCode: 200,
    body: JSON.stringify({ message: 'List machine parameters success', data: result }),
  };
};

// 🔄 Batch update parameters của máy
export const batchUpdateMachineParameters: Handler = async (event: any, context: Context) => {
  const app = await bootstrap();
  const machineService = app.get(MachineService);
  const { machineId } = event.pathParameters;
  const body = JSON.parse(event.body);

  await validateInput(ParameterReq, body);
  const result = await machineService.batchUpdateMachineParameters(machineId, body);

  return {
    statusCode: 200,
    body: JSON.stringify({ message: 'Batch update parameters success', data: result }),
  };
};
