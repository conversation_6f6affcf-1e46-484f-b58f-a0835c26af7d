// x_module/integration/controller/my-controller.controller.ts

import {
  Controller,
  Post,
  Body,
  Headers,
  Res,
  HttpStatus,
  Inject,
  forwardRef,
  UseGuards,
} from '@nestjs/common';
import { Response } from 'express';
import { NSSQSS } from '~/common/enums/NSSQS';
import { DataCalculationByJobReq } from '~/dto/calculation.dto';
import { SQSService } from '~/x-modules/sqs/sqs.service';
import { UtilityConsumptionKpi } from '~/x-modules/integration/services';
import { ScheduleAuthGuard } from '~/@systems/guard/schedule-auth.guard';

@UseGuards(ScheduleAuthGuard)
@Controller('data-calculation-by-job')
export class DataCalculationByJob {
  constructor(
    private readonly sqsService: SQSService,
    private readonly utilityConsumptionKpi: UtilityConsumptionKpi,
  ) {}

  // Handle job by task 36.1
  @Post('prod-batch-paramaters')
  async onProdBatchParamaters(
    @Body() data: DataCalculationByJobReq,
    @Headers() headers: any,
    @Res() res: Response,
  ) {
    this.sqsService.sendMessage(
      {
        message: 'OnProdBatchParamaters',
        data: data,
      },
      NSSQSS.EMessageType.OnProdBatchParamaters,
    );
    return res.status(HttpStatus.OK).json({ message: 'Data received successfully' });
  }
  // Handle job by task 36.3
  @Post('product-output-by-rejection')
  async onProductOutputByRejection(@Body() data: DataCalculationByJobReq, @Res() res: Response) {
    this.sqsService.sendMessage(
      {
        message: 'OnProductOutputByRejection',
        data: data,
      },
      NSSQSS.EMessageType.OnProductOutputByRejection,
    );
    return res.status(HttpStatus.OK).json({ message: 'Data received successfully' });
  }
  // Handle job by task 36.2
  @Post('production-order-material-transaction')
  async onProductionOrderMaterialTransaction(
    @Body() data: DataCalculationByJobReq,
    @Res() res: Response,
  ) {
    this.sqsService.sendMessage(
      {
        message: 'OnProductionOrderMaterialTransaction',
        data: data,
      },
      NSSQSS.EMessageType.OnProductionOrderMaterialTransaction,
    );
    return res.status(HttpStatus.OK).json({ message: 'Data received successfully' });
  }
  // Handle job by task 36.13
  @Post('calculate-utility-usage')
  async onCalculateUtilityUsage(@Body() data: DataCalculationByJobReq, @Res() res: Response) {
    this.sqsService.sendMessage(
      {
        message: 'OnCalculateUtilityUsage',
        data: data,
      },
      NSSQSS.EMessageType.OnCalculateUtilityUsage,
    );
    return res.status(HttpStatus.OK).json({ message: 'Data received successfully' });
  }
  // Handle job by task 36.71
  @Post('production-oee')
  async onOroductionOee(@Body() data: DataCalculationByJobReq, @Res() res: Response) {
    this.sqsService.sendMessage(
      {
        message: 'OnOeeCalculation',
        data: data,
      },
      NSSQSS.EMessageType.OnOeeCalculation,
    );
    return res.status(HttpStatus.OK).json({ message: 'Data received successfully' });
  }
  // Handle job by task 36.4
  @Post('calculate-kansui-and-seasoning')
  async onKansuiAndSeasoningCalculation(
    @Body() data: DataCalculationByJobReq,
    @Res() res: Response,
  ) {
    this.sqsService.sendMessage(
      {
        message: 'OnKansuiAndSeasoningCalculation',
        data: data,
      },
      NSSQSS.EMessageType.OnKansuiAndSeasoningCalculation,
    );
    return res.status(HttpStatus.OK).json({ message: 'Data received successfully' });
  }
  // Handle job by task 36.5
  @Post('transaction-product-output')
  async onTransactionProductOutput(@Body() data: DataCalculationByJobReq, @Res() res: Response) {
    this.sqsService.sendMessage(
      {
        message: 'OnTransactionProductOutput',
        data: data,
      },
      NSSQSS.EMessageType.OnTransactionProductOutput,
    );
    return res.status(HttpStatus.OK).json({ message: 'Data received successfully' });
  }

  // Handle job by task 36.11
  @Post('utility-transaction-allocation')
  async onUtilityTransactionAllocation(
    @Body() data: DataCalculationByJobReq,
    @Res() res: Response,
  ) {
    this.sqsService.sendMessage(
      {
        message: 'OnUtilityTransactionAllocation',
        data: data,
      },
      NSSQSS.EMessageType.OnUtilityTransactionAllocation,
    );
    return res.status(HttpStatus.OK).json({ message: 'Data received successfully' });
  }

  // Handle job by task 36.12
  @Post('manhour-transaction-allocation')
  async onManhourTransactionAllocation(
    @Body() data: DataCalculationByJobReq,
    @Res() res: Response,
  ) {
    this.sqsService.sendMessage(
      {
        message: 'OnManhourTransactionAllocation',
        data: data,
      },
      NSSQSS.EMessageType.OnManhourTransactionAllocation,
    );
    return res.status(HttpStatus.OK).json({ message: 'Data received successfully' });
  }

  // Handle job by task 36.15
  @Post('create-manhour-transactions')
  async onCreateManhourTransactions(@Body() data: DataCalculationByJobReq, @Res() res: Response) {
    this.sqsService.sendMessage(
      {
        message: 'OnCreateManhourTransactions',
        data: data,
      },
      NSSQSS.EMessageType.OnCreateManhourTransactions,
    );
    return res.status(HttpStatus.OK).json({ message: 'Data received successfully' });
  }

  // Handle job by task 41
  @Post('utility-consumption-kpi')
  async onCreateUtilityConsumptionKpi(@Body() data: DataCalculationByJobReq, @Res() res: Response) {
    this.sqsService.sendMessage(
      {
        message: 'OnCreateUtilityConsumptionKpi',
        data: data,
      },
      NSSQSS.EMessageType.OnCreateUtilityConsumptionKpi,
    );
    return res.status(HttpStatus.OK).json({ message: 'Data received successfully' });
  }

  // Handle job by task 43
  @Post('manhour-kpi')
  async onCreateManhourKpi(@Body() data: DataCalculationByJobReq, @Res() res: Response) {
    this.sqsService.sendMessage(
      {
        message: 'OnCreateManhourKpi',
        data: data,
      },
      NSSQSS.EMessageType.OnCreateManhourKpi,
    );
    return res.status(HttpStatus.OK).json({ message: 'Data received successfully' });
  }

  // Handle job by task 46
  @Post('oee-kpi')
  async onCreateOeeKpi(@Body() data: DataCalculationByJobReq, @Res() res: Response) {
    this.sqsService.sendMessage(
      {
        message: 'OnCreateOeeKpi',
        data: data,
      },
      NSSQSS.EMessageType.OnCreateOeeKpi,
    );
    return res.status(HttpStatus.OK).json({ message: 'Data received successfully' });
  }

  // Handle job by task 40.2
  @Post('loss-powder-kpi')
  async onLossPowderKpi(@Body() data: DataCalculationByJobReq, @Res() res: Response) {
    this.sqsService.sendMessage(
      {
        message: 'OnLossPowderKpi',
        data: data,
      },
      NSSQSS.EMessageType.OnLossPowderKpi,
    );
    return res.status(HttpStatus.OK).json({ message: 'Data received successfully' });
  }

  // Handle job by task 47
  @Post('rejection-kpi')
  async onRejectionKpi(@Body() data: DataCalculationByJobReq, @Res() res: Response) {
    this.sqsService.sendMessage(
      {
        message: 'OnRejectionKpi',
        data: data,
      },
      NSSQSS.EMessageType.OnRejectionKpi,
    );
    return res.status(HttpStatus.OK).json({ message: 'Data received successfully' });
  }
}
