import { BadRequestException, Injectable } from '@nestjs/common';
import moment from 'moment';
import { Between, EntityManager, ILike, In, LessThanOrEqual, Like, MoreThanOrEqual } from 'typeorm';
import { BindRepo } from '~/@core/decorator';
import { ListProductionRejection, ProductionRejectionReq } from '~/dto/rejection-monitoring.dto';
import { UtilityMetersRepo, UtilityTransactionRepo } from '~/repositories/primary';
import { adminSessionContext } from '../../admin-session.context';
import { ShiftRepo } from '~/repositories/primary/shift.repo';
import {
  ListUtilityTransaction,
  UtilityMonitoringReq,
  UtilityTransactionReq,
} from '~/dto/utility-transaction.dto';
import { AssignShiftRepository } from '~/repositories/primary/assign-shift.repo';
import { MesDxMetricsRepo } from '~/repositories/scada';
import dayjs from 'dayjs';
import { BusinessException } from '~/@systems/exceptions';
const AWS = require('aws-sdk');

@Injectable()
export class UtilityTransactionService {
  constructor(private readonly entityManager: EntityManager) {}

  @BindRepo(UtilityTransactionRepo)
  private readonly repo: UtilityTransactionRepo;

  @BindRepo(UtilityMetersRepo)
  private readonly utilityMetersRepo: UtilityMetersRepo;

  @BindRepo(ShiftRepo)
  private readonly shiftRepo: ShiftRepo;

  @BindRepo(AssignShiftRepository)
  private readonly assignShiftRepo: AssignShiftRepository;

  @BindRepo(MesDxMetricsRepo)
  private readonly mesDxMetricsRepo: MesDxMetricsRepo;

  sitewise = new AWS.IoTSiteWise({
    accessKeyId: process.env.AWS_ACCESS_KEY_ID,
    secretAccessKey: process.env.AWS_SECRET_ACCESS_KEY,
    region: process.env.AWS_REGION,
  });

  async getAssetPropertyValue(assetId: string, propertyId: string) {
    try {
      const params = {
        assetId: assetId,
        propertyId: propertyId,
      };
      const data = await this.sitewise.getAssetPropertyValue(params).promise();

      // console.log('Asset Property Value: ', data);
      return data;
    } catch (err) {
      console.log('Error getting Asset Property Value: ', err);
    }
  }

  async getAssetPropertyValueHistory(
    assetId: string,
    propertyId: string,
    startDate: Date,
    endDate: Date,
  ) {
    try {
      const params = {
        assetId: assetId,
        propertyId: propertyId,
        startDate: startDate,
        endDate: endDate,
        timeOrdering: 'DESCENDING',
      };
      const data = await this.sitewise.getAssetPropertyValueHistory(params).promise();

      // console.log('Asset Property Value: ', data);
      return data;
    } catch (err) {
      console.log('Error getting Asset Property Value: ', err);
    }
  }

  async getUtilityMonitor(params: UtilityMonitoringReq) {
    if (!params.factoryId)
      return {
        data: [],
        total: 0,
      };
    const whereMeter: any = { isActive: true };
    if (params.factoryId) whereMeter.factoryId = params.factoryId;
    if (params.meterCode) whereMeter.code = ILike(`%${params.meterCode}%`);
    if (params.meterName) whereMeter.name = ILike(`%${params.meterName}%`);
    if (params.type) whereMeter.utilityMeterTypeDetailCode = ILike(`%${params.type}%`);
    const now = dayjs().tz('Asia/Ho_Chi_Minh').format('HH:mm:ss');
    const whereCon: any = {};
    whereCon.startTime = LessThanOrEqual(now);
    whereCon.endTime = MoreThanOrEqual(now);
    const shift = await this.shiftRepo.findOne({
      where: whereCon,
      order: { startTime: 'DESC' },
    });
    if (!shift) throw new BusinessException('Not Found Shift!');
    const startTime = dayjs()
      .tz('Asia/Ho_Chi_Minh', true)
      .hour(Number(shift.startTime.split(':')[0]))
      .minute(Number(shift.startTime.split(':')[1]))
      .second(Number(shift.startTime.split(':')[2]))
      .format('YYYY-MM-DD HH:mm:ss');

    const endTime = dayjs().tz('Asia/Ho_Chi_Minh').format('YYYY-MM-DD HH:mm:ss');
    const queryParams: any[] = [];
    let baseSQL = `
    WITH df_utility AS (
      SELECT
        ou_site.code "site",
        um.* 
      FROM
        utility_meter um
        INNER JOIN organization_units ou_factory ON um.factory_id = ou_factory.
        ID INNER JOIN organization_units ou_site ON ou_factory."parentId" = ou_site.ID 
      WHERE
        um.is_active = TRUE 
        AND ou_factory.id = $1`;
    queryParams.push(params.factoryId);
    if (params.meterCode) {
      baseSQL += ` AND um.code ILike $${queryParams.length + 1}`;
      queryParams.push(`%${params.meterCode}%`);
    }
    if (params.meterName) {
      baseSQL += ` AND um.name ILike $${queryParams.length + 1}`;
      queryParams.push(`%${params.meterName}%`);
    }
    if (params.type) {
      baseSQL += ` AND um."utility_meter_type_detail_code" ILike $${queryParams.length + 1}`;
      queryParams.push(`%${params.type}%`);
    }

    baseSQL += `),
    df_metric_utility AS (
      SELECT
        smdm.*,
        du.ID AS utility_id,
        du.site,
        du.cal_method_code,
        du.code,
        du."name",
        du.tag_name,
        du.utility_meter_type_detail_code
      FROM
        scada_mes_dx_metrics smdm
        INNER JOIN ( SELECT DISTINCT site, ID, asset_id, metric_id, cal_method_code, code, "name", tag_name, utility_meter_type_detail_code FROM df_utility ) AS du ON smdm."assetId" = du.asset_id 
        AND smdm."metricId" = du.metric_id 
      WHERE
        smdm.datetime BETWEEN $${queryParams.length + 1} AND $${queryParams.length + 2}
    ),
    marked AS (
      SELECT
        *,
      CASE
          
          WHEN 
          VALUE
            :: NUMERIC != 0 
            AND (
              LAG( VALUE :: NUMERIC, 1 ) OVER ( PARTITION BY site, "assetId", "metricId", utility_id, tag_name ORDER BY datetime ) = 0 
              OR LAG( VALUE :: NUMERIC, 1 ) OVER ( PARTITION BY site, "assetId", "metricId", utility_id, tag_name ORDER BY datetime ) IS NULL 
              OR ABS(
                LAG( VALUE :: NUMERIC, 1 ) OVER ( PARTITION BY site, "assetId", "metricId", utility_id, tag_name ORDER BY datetime ) - 
                VALUE
                  :: NUMERIC 
              ) > 1000 
              ) THEN
              1 ELSE 0 
            END AS new_group 
          FROM
            df_metric_utility 
          WHERE
            cal_method_code = 'DIFFERENCE_BASED' 
        ),
        numbered AS (
          SELECT
            *,
            SUM( new_group ) OVER ( PARTITION BY site, "assetId", "metricId", utility_id, tag_name ORDER BY datetime ROWS UNBOUNDED PRECEDING ) AS segment_id 
          FROM
            marked 
        ),
        filtered AS ( SELECT * FROM numbered WHERE VALUE :: NUMERIC != 0 ),
        range_values AS (
          SELECT DISTINCT
            site,
            "assetId",
            "metricId",
            utility_id,
            code,
            "name",
            tag_name,
            segment_id,
            utility_meter_type_detail_code,
    --  MIN(value::numeric) AS min_val, MAX(value::numeric) AS max_val
            FIRST_VALUE( VALUE :: NUMERIC ) OVER w AS min_val,
            LAST_VALUE( VALUE :: NUMERIC ) OVER w2 AS max_val 
          FROM
            filtered --  GROUP BY site,"assetId","metricId",utility_id,code,"name",tag_name, segment_id
            WINDOW w AS (
              PARTITION BY site,
              "assetId",
              "metricId",
              utility_id,
              code,
              "name",
              tag_name,
              segment_id 
              ORDER BY
                datetime ROWS BETWEEN UNBOUNDED PRECEDING 
              AND UNBOUNDED FOLLOWING 
            ),
            w2 AS (
              PARTITION BY site,
              "assetId",
              "metricId",
              utility_id,
              code,
              "name",
              tag_name,
              segment_id 
              ORDER BY
                datetime ROWS BETWEEN UNBOUNDED PRECEDING 
              AND UNBOUNDED FOLLOWING 
            ) 
        ),
        final_diff AS (
          SELECT
            site,
            "assetId",
            "metricId",
            utility_id,
            code,
            "name",
            tag_name,
            SUM( max_val - min_val ) AS total_diff,
            utility_meter_type_detail_code
          FROM
            range_values 
          GROUP BY
            site,
            "assetId",
            "metricId",
            utility_id,
            code,
            "name",
            tag_name,
            utility_meter_type_detail_code 
        ),
        df_utility_flow AS (
          SELECT
            dmu.site,
            dmu.utility_id,
            b.code,
            b.NAME,
            b.tag_name,
            b.utility_meter_type_detail_code,
            dmu."assetId",
            dmu."metricId",
            dmu.cal_method_code,
            SUM( val_per_min ) AS utility_val 
          FROM
            ( SELECT dmus.*, dmus.VALUE :: NUMERIC / 60 AS val_per_min FROM df_metric_utility dmus WHERE dmus.cal_method_code = 'FLOWRATE-BASED' ) AS dmu
            INNER JOIN df_utility b ON dmu.utility_id = b.ID 
          GROUP BY
            dmu.site,
            dmu.utility_id,
            b.code,
            b.NAME,
            b.tag_name,
            b.utility_meter_type_detail_code,
            dmu."assetId",
            dmu."metricId",
            dmu.cal_method_code 
        ), all_data AS ( SELECT
        site "Site",
        code "code",
        "name" "name",
        tag_name "Tag",
        'DIFFERENCE_BASED' "Method",
        total_diff "value" ,
        utility_meter_type_detail_code "utilityMeterTypeDetailCode"
      FROM
        final_diff UNION ALL
      SELECT
        b.site "siteName",
        b.code "code",
        b.NAME "name",
        b.tag_name "Tag",
        b.cal_method_code "Method",
        b.utility_val "value",
         b.utility_meter_type_detail_code "utilityMeterTypeDetailCode"
      FROM
        df_utility_flow b
      )
    `;
    const selectSQL =
      baseSQL +
      `SELECT * FROM all_data
    ORDER BY "Site", "code", "Method"
    LIMIT $${queryParams.length + 3} OFFSET $${queryParams.length + 4};
    `;
    const countSQL = baseSQL + `SELECT COUNT(*) FROM all_data`;
    queryParams.push(startTime);
    queryParams.push(endTime);
    queryParams.push(params.pageSize || 10);
    queryParams.push(params.pageIndex ? (params.pageIndex - 1) * (params.pageSize || 10) : 0);
    const result = await this.entityManager.query(selectSQL, queryParams);
    const total = await this.entityManager.query(
      countSQL,
      queryParams.slice(0, queryParams.length - 2),
    );
    if (total.length === 0) return { data: [], total: 0 };
    return { data: result, total: Number(total[0]?.count) || 0 };
  }

  /**
   * Tìm tất cả lý do với bộ lọc tùy chọn
   * @param filterDto Tiêu chí lọc tùy chọn
   * @returns Danh sách phân trang
   */
  async pagination(params: ListUtilityTransaction) {
    const whereCon: any = {};
    const whereMeter: any = {};
    if (!params.factoryId)
      return {
        data: [],
        total: 0,
      };
    if (params.factoryId) whereMeter.factoryId = params.factoryId;
    if (params.factoryId && params.type) whereMeter.utilityMeterTypeDetailCode = params.type;
    const meter = await this.utilityMetersRepo.find({ where: whereMeter });
    whereCon.meterId = In(meter.map((i) => i.id));

    if (params.startTime && params.endTime)
      whereCon.date = Between(dayjs(params.startTime), dayjs(params.endTime));
    const result = await this.repo.findPagination(
      { where: whereCon, order: { date: 'DESC', createdDate: 'DESC' } },
      { pageSize: params.pageSize, pageIndex: params.pageIndex },
    );
    const shifts = await this.shiftRepo.find({
      where: { id: In(result.data.map((i) => i.shiftId)) },
    });
    const meters = await this.utilityMetersRepo.find({
      where: { id: In(result.data.map((i) => i.meterId)) },
    });
    result.data.forEach((item: any) => {
      const shift = shifts.find((i) => i.id === item.shiftId);
      item.shiftName = shift?.code;
      const met = meters.find((i) => i.id === item.meterId);
      item.type = met?.utilityMeterTypeDetailCode || '';
      item.meterCode = met?.code || '';
      item.meterName = met?.name || '';
    });

    result.data.sort((a: any, b: any) => {
      if (a.type < b.type) return -1;
      if (a.type > b.type) return 1;
      // Nếu type giống nhau, so sánh shiftName
      if (a.shiftName < b.shiftName) return -1;
      if (a.shiftName > b.shiftName) return 1;
      return 0;
    });
    return result;
  }

  async getOptions(levelCode?: string): Promise<any[]> {
    return [];
  }

  async create(body: UtilityTransactionReq) {
    const { userId: userId } = adminSessionContext;
    const today = new Date();
    const shift = await this.shiftRepo.findOne({ where: { id: body.shiftId, status: true } });
    if (!shift) throw new Error('Not Found Shift!');
    const meter = await this.utilityMetersRepo.findOne({
      where: { id: body.meterId, isActive: true },
    });
    if (!meter) throw new Error('Not Found Meter');
    const data = await this.repo.save({
      ...body,
      createdBy: userId,
      createdDate: today,
      updatedBy: userId,
      updatedDate: today,
    });
    return { message: 'Create Successfully!', data: data };
  }

  async loadUtilityMeter(factoryId?: string) {
    const whereCon: any = { isActive: true };
    if (factoryId) whereCon.factoryId = factoryId;
    return await this.utilityMetersRepo.find({ where: whereCon });
  }
  async loadShift(factoryId: string) {
    const assignShift = await this.assignShiftRepo.find({
      where: { organizationId: factoryId, status: true },
    });
    return await this.shiftRepo.find({
      where: { id: In(assignShift.map((i) => i.shiftId)), status: true },
    });
  }
}
