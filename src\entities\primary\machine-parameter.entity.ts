import { Column, Entity, Index, JoinColumn, ManyToOne, OneToMany } from 'typeorm';
import { ApiPropertyOptional } from '@nestjs/swagger';
import { PrimaryBaseEntity } from '~/entities/primary-base.entity';
import { GeneralDataDetailEntity, MachineEntity } from '~/entities/primary';
import { SetPointEntity } from './set-point.entity';

// Machine Parameter entity
/** Thông số máy móc */
@Entity('machine_parameter')
export class MachineParameterEntity extends PrimaryBaseEntity {
  /** @RelationId(() => MachineEntity), */
  @Column({})
  machineId: string;
  @ManyToOne(() => MachineEntity, p => p.parameters)
  @JoinColumn({ name: 'machineId', referencedColumnName: 'id' })
  machine: Promise<MachineEntity>;

  /** Mã tham số, tự động tăng từ 1000001  */
  @Column({ unique: true })
  @Index()
  code: number;

  /** <PERSON><PERSON> tả tham số */
  @Column({ type: 'text', default: '' })
  desc?: string;

  /** Địa chỉ IP của máy (Default từ Machine.IPAddress) */
  @Column({ type: 'text', default: '' })
  ipAddress: string;

  /** Tên tag */
  @Column({})
  tagName: string;

  /** Địa chỉ tag */
  @Column({ type: 'text', default: '' })
  tagAddress: string;

  /** Có tính toán theo khoảng thời gian không? (true = có, false = lấy realtime) */
  @Column({ type: 'boolean', default: false })
  interval: boolean;

  /** Giá trị thời gian (chỉ nhập khi interval = true) */
  @Column({ type: 'decimal', precision: 10, scale: 2, nullable: true })
  timeValue?: number;

  /** ID của Node */
  @Column({ type: 'text', default: '' })
  nodeId?: string;

  /** Ghi chú */
  @Column({ type: 'text', nullable: true })
  note?: string;

  /** IOT Sitewise Asset ID */
  @Column({ type: 'text', default: '' })
  iotsitewiseAssetId?: string; // TODO: chỉnh lại thành required

  /** iotsitewisePropertyId từ aws */
  @Column({ default: '' })
  iotsitewisePropertyId: string;

  /** OEE_Cal */
  @Column({ type: 'smallint', default: 0 })
  oeeCal: number;

  /** Trạng thái batch control (-1=undefined, 0=stop, 1=start, 2=start&stop) */
  @Column({ type: 'smallint', default: -1 })
  batchStatusControl: number;

  /** is show on Dashboard  */
  @Column({ default: false })
  showOnDashboard: boolean;

  /** is show on Rejection Monitoring  */
  @Column({ default: false })
  showOnRejectionMonitoring: boolean;

  /** Trạng thái kích hoạt (true = active, false = inactive) */
  @Column({ default: true })
  isActive: boolean;

  // ========================= Type =========================

  /**
   * Mã GeneralData của Type.
   */
  @ApiPropertyOptional({ description: 'Mã GeneralData của Type' })
  @Column({ default: '' })
  typeGeneralCode?: string;

  /**
   * Mã GeneralDataDetail của Type.
   */
  @ApiPropertyOptional({ description: 'Mã GeneralDataDetail của Type' })
  @Column({ default: '' })
  typeCode?: string;

  /**
   * ID của Type, liên kết với GeneralDataDetailEntity.
   */
  @ApiPropertyOptional({ description: 'ID của Type' })
  @Column({ nullable: true })
  typeId: string;
  @ManyToOne(() => GeneralDataDetailEntity)
  @JoinColumn({ name: 'typeId', referencedColumnName: 'id' })
  type: Promise<GeneralDataDetailEntity>;

  // ========================= Data Type =========================

  /**
   * Mã GeneralData của Data Type.
   */
  @ApiPropertyOptional({ description: 'Mã GeneralData của Data Type' })
  @Column({ default: '' })
  dataTypeGeneralCode?: string;

  /**
   * Mã GeneralDataDetail của Data Type.
   */
  @ApiPropertyOptional({ description: 'Mã GeneralDataDetail của Data Type' })
  @Column({ default: '' })
  dataTypeCode?: string;

  /**
   * ID của Data Type, liên kết với GeneralDataDetailEntity.
   */
  @ApiPropertyOptional({ description: 'ID của Data Type' })
  @Column({ nullable: true })
  dataTypeId?: string;
  @ManyToOne(() => GeneralDataDetailEntity)
  @JoinColumn({ name: 'dataTypeId', referencedColumnName: 'id' })
  dataType?: Promise<GeneralDataDetailEntity>;

  // ========================= UOM (Đơn vị đo lường) =========================

  /**
   * Mã GeneralData của UOM.
   */
  @ApiPropertyOptional({ description: 'Mã GeneralData của UOM' })
  @Column({ default: '' })
  uomGeneralCode?: string;

  /**
   * Mã GeneralDataDetail của UOM.
   */
  @ApiPropertyOptional({ description: 'Mã GeneralDataDetail của UOM' })
  @Column({ default: '' })
  uomCode?: string;

  /**
   * ID của UOM, liên kết với GeneralDataDetailEntity.
   */
  @ApiPropertyOptional({ description: 'ID của UOM' })
  @Column({ nullable: true })
  uomId?: string;
  @ManyToOne(() => GeneralDataDetailEntity)
  @JoinColumn({ name: 'uomId', referencedColumnName: 'id' })
  uom?: Promise<GeneralDataDetailEntity>;

  // ========================= Datetime Unit =========================

  /**
   * Mã GeneralData của Datetime Unit.
   */
  @ApiPropertyOptional({ description: 'Mã GeneralData của Datetime Unit' })
  @Column({ default: '' })
  datetimeUnitGeneralCode?: string;

  /**
   * Mã GeneralDataDetail của Datetime Unit.
   */
  @ApiPropertyOptional({ description: 'Mã GeneralDataDetail của Datetime Unit' })
  @Column({ default: '' })
  datetimeUnitCode?: string;

  /**
   * ID của Datetime Unit, liên kết với GeneralDataDetailEntity.
   */
  @ApiPropertyOptional({ description: 'ID của Datetime Unit' })
  @Column({ nullable: true })
  datetimeUnitId?: string;
  @ManyToOne(() => GeneralDataDetailEntity)
  @JoinColumn({ name: 'datetimeUnitId', referencedColumnName: 'id' })
  datetimeUnit?: Promise<GeneralDataDetailEntity>;

  // ========================= IoT Sitewise Model Property Type =========================

  /**
   * Mã GeneralData của IoT Sitewise  Property Type.
   */
  @ApiPropertyOptional({ description: 'Mã GeneralData của IoT Sitewise  Property Type' })
  @Column({ default: '' })
  iotsitewisePropertyTypeGeneralCode?: string;

  /**
   * Mã GeneralDataDetail của IoT Sitewise  Property Type.
   */
  @ApiPropertyOptional({ description: 'Mã GeneralDataDetail của IoT Sitewise  Property Type' })
  @Column({ default: '' })
  iotsitewisePropertyTypeCode?: string;

  /**
   * ID của IoT Sitewise  Property Type, liên kết với GeneralDataDetailEntity.
   */
  @ApiPropertyOptional({ description: 'ID của IoT Sitewise  Property Type' })
  @Column({ nullable: true })
  iotsitewisePropertyTypeId?: string;
  @ManyToOne(() => GeneralDataDetailEntity)
  @JoinColumn({ name: 'iotsitewisePropertyTypeId', referencedColumnName: 'id' })
  iotsitewisePropertyType?: GeneralDataDetailEntity;

  @OneToMany(() => SetPointEntity, setPoint => setPoint.machineParameter)
  setPoints: Promise<SetPointEntity[]>;
}
