import { Injectable } from '@nestjs/common';
import { delay } from 'rxjs';
import { Between, ILike } from 'typeorm';
import { BindRepo } from '~/@core/decorator';
import { NSSQSS } from '~/common/enums';
import { LogScheduleRepo } from '~/repositories/primary';
import { LogJobRepo } from '~/repositories/primary/log-job.repo';

@Injectable()
export class LogScheduleService {
  constructor() {}
  @BindRepo(LogScheduleRepo)
  private readonly logScheduleRepo: LogScheduleRepo;

  async pagination(params: any) {
    const whereCon: any = {};
    if (params.type) whereCon.type = ILike(`%${params.type}%`);
    if (params.status) whereCon.status = ILike(`%${params.status}%`);
    if (params.startTime && params.endTime)
      whereCon.createdDate = Between(params.startTime, params.endTime);
    const res = await this.logScheduleRepo.findPagination(
      { where: whereCon, order: { createdDate: 'DESC' } },
      { pageSize: params.pageSize, pageIndex: params.pageIndex },
    );
    res.data.forEach((i: any) => (i.metadata = JSON.stringify(i.metadata)));
    return res;
  }
}
