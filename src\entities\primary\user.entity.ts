import { ApiProperty } from '@nestjs/swagger';
import { Column, Entity, JoinColumn, ManyToMany, ManyToOne, OneToMany } from 'typeorm';
import { LOGIN_PREFERENCES } from '~/common/constants/User.constants';
import { PrimaryBaseEntity } from '~/entities/primary-base.entity';
import { AccessEntity } from './access.entity';
import { ProductionAreaEntity } from './production-area.entity';
import { TimeSheetRecordEntity } from './timesheet-records.entity';
import { PermissionUserEntity } from './permission-user.entity';
import { UserGroupEntity } from '~/entities/primary/user-group.entity';
import { PermissionEntity } from '~/entities/primary/permission.entity';

/** User Entity */
@Entity('user')
export class UserEntity extends PrimaryBaseEntity {
  /** Unique employee Code  */
  @Column({
    type: 'varchar',
    length: 50,
    unique: true,
    nullable: false,
  })
  @ApiProperty({ description: 'Employee Code', type: 'string', uniqueItems: true, required: true })
  employeeCode: string;

  /** Production Area ID */
  @Column({
    name: 'productionAreaId',
    nullable: true,
  })
  productionAreaId: string;

  /** Production Area */
  @ManyToOne(() => ProductionAreaEntity)
  @JoinColumn({ name: 'productionAreaId' })
  productionArea: ProductionAreaEntity;

  /** Full name */
  @Column({
    type: 'text',
    nullable: false,
  })
  @ApiProperty({ description: 'Full name', type: 'string', required: true })
  fullName: string;

  /** Email */
  @Column({
    type: 'text',
    nullable: true,
  })
  @ApiProperty({ description: 'Email', type: 'string' })
  email: string;

  /** Phone */
  @Column({
    type: 'varchar',
    nullable: false,
    length: 20,
  })
  @ApiProperty({ description: 'Phone', type: 'string', required: true })
  phone: string;

  /** Login Preference */
  @Column({
    type: 'enum',
    nullable: false,
    enum: LOGIN_PREFERENCES,
    default: LOGIN_PREFERENCES.MSSSO,
  })
  @ApiProperty({
    description: 'Login Preference',
    enum: LOGIN_PREFERENCES,
    default: LOGIN_PREFERENCES.MSSSO,
  })
  loginPreference: LOGIN_PREFERENCES;

  /** Activity status */
  @Column({
    type: 'boolean',
    default: false,
  })
  @ApiProperty({ description: 'User activity status', type: 'boolean' })
  isActive: boolean;

  /** Account valid from date */
  @Column({
    type: 'date',
    nullable: false,
  })
  @ApiProperty({ description: 'Account valid from date', type: 'string', required: true })
  validFrom: Date;

  /** Account valid to date */
  @Column({
    type: 'date',
    nullable: true,
  })
  @ApiProperty({ description: 'Account valid to date', type: 'string', required: false })
  validTo: Date;

  @Column({
    type: 'varchar',
    nullable: true,
  })
  @ApiProperty({
    description: 'Image url',
    example: 'https://masanbi.s3.ap-southeast-1.amazonaws.com/upload/Meal_21SI23629.jpg',
  })
  imageUrl?: string;

  @Column({
    type: 'int',
    nullable: true,
  })
  @ApiProperty({ description: 'Pin ZKT', type: 'number' })
  userNo: number;

  @Column({
    type: 'varchar',
    nullable: true,
  })
  @ApiProperty({ description: 'Status sync', type: 'string' })
  syncStatus: string;

  @Column({
    type: 'date',
    nullable: true,
  })
  @ApiProperty({ description: 'Sync date', type: 'date' })
  syncDate: Date;

  /* Xoa */

  @Column({
    type: 'varchar',
    nullable: true,
  })
  @ApiProperty({ description: 'Status sync', type: 'string' })
  hanetSyncStatus?: string;

  @Column({
    type: 'date',
    nullable: true,
  })
  @ApiProperty({ description: 'Sync date', type: 'string' })
  hanetSyncDate: Date;

  @Column({
    type: 'varchar',
    nullable: true,
  })
  @ApiProperty({
    description: 'Default route for the user',
    example: '/dashboard',
  })
  defaultRoute: string;

  /* End Xoa */

  /** Id nhóm quyền */
  @Column({
    name: 'permissionId',
    nullable: true,
  })
  permissionId: string;

  /** Permission */
  @ManyToOne(() => PermissionEntity, (p) => p.users)
  @JoinColumn({ name: 'permissionId', referencedColumnName: 'id' })
  permission: Promise<PermissionEntity>;

  @OneToMany(() => AccessEntity, (access) => access.user)
  access: AccessEntity[];

  /** Relation với bảng TimeSheetRecord */
  @OneToMany(() => TimeSheetRecordEntity, (timesheet) => timesheet.user)
  timesheets!: TimeSheetRecordEntity[];

  @OneToMany(() => PermissionUserEntity, (e) => e.user)
  permissionUsers: Promise<PermissionUserEntity[]>;

  @ManyToMany(() => UserGroupEntity, (group) => group.users)
  groups?: UserGroupEntity[];
}
