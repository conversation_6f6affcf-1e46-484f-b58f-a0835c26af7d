import { NestFactory } from '@nestjs/core';
import { Handler, Context } from 'aws-lambda';
import { AppModule } from '~/app.module';
import { MaterialGroupDto, MaterialGroupReq } from '~/dto/material-group.dto'; // <PERSON>i<PERSON>u chỉnh theo đường dẫn DTO của bạn
import { MaterialGroupService } from '~/x-modules/admin/services';

let cachedApp: any = null;

async function bootstrap() {
  if (!cachedApp) {
    const app = await NestFactory.createApplicationContext(AppModule);
    await app.init();
    cachedApp = app;
  }
  return cachedApp;
}

// Lambda handler cho việc phân trang danh sách nhóm nguyên vật liệu
export const listMaterialGroup: Handler = async (event: any, context: Context) => {
  const app = await bootstrap(); // Khởi tạo NestJS app
  const service = app.get(MaterialGroupService);

  const params: MaterialGroupReq = event.queryStringParameters; // <PERSON><PERSON><PERSON> tham số từ query string

  try {
    const result = await service.pagination(params); // Gọi phương thức phân trang
    return {
      statusCode: 200,
      body: JSON.stringify(result),
      headers: {
        'Content-Type': 'application/json',
      },
    };
  } catch (error) {
    return {
      statusCode: error.status || 500,
      body: JSON.stringify({
        message: error.message || 'Internal server error',
        error: error.response?.error || error.name,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    };
  }
};

// Lambda handler cho việc lấy danh sách item
export const loadItem: Handler = async (event: any, context: Context) => {
  const app = await bootstrap(); // Khởi tạo NestJS app
  const service = app.get(MaterialGroupService);

  try {
    const result = await service.loadItem(); // Gọi phương thức lấy danh sách item
    return {
      statusCode: 200,
      body: JSON.stringify(result),
      headers: {
        'Content-Type': 'application/json',
      },
    };
  } catch (error) {
    return {
      statusCode: error.status || 500,
      body: JSON.stringify({
        message: error.message || 'Internal server error',
        error: error.response?.error || error.name,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    };
  }
};

// Lambda handler cho việc lấy chi tiết nhóm nguyên vật liệu
export const getMaterialDetail: Handler = async (event: any, context: Context) => {
  const app = await bootstrap(); // Khởi tạo NestJS app
  const service = app.get(MaterialGroupService);

  const id: string = event.pathParameters.id; // Lấy id từ path parameters

  try {
    const result = await service.getMaterialDetail(id); // Gọi phương thức lấy chi tiết nhóm nguyên vật liệu
    return {
      statusCode: 200,
      body: JSON.stringify(result),
      headers: {
        'Content-Type': 'application/json',
      },
    };
  } catch (error) {
    return {
      statusCode: error.status || 500,
      body: JSON.stringify({
        message: error.message || 'Internal server error',
        error: error.response?.error || error.name,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    };
  }
};

// Lambda handler cho việc tạo mới nhóm nguyên vật liệu
export const createMaterialGroup: Handler = async (event: any, context: Context) => {
  const app = await bootstrap(); // Khởi tạo NestJS app
  const service = app.get(MaterialGroupService);

  const body: MaterialGroupDto = JSON.parse(event.body); // Lấy dữ liệu từ body

  try {
    const result = await service.create(body); // Gọi phương thức tạo mới nhóm nguyên vật liệu
    return {
      statusCode: 201,
      body: JSON.stringify(result),
      headers: {
        'Content-Type': 'application/json',
      },
    };
  } catch (error) {
    return {
      statusCode: error.status || 500,
      body: JSON.stringify({
        message: error.message || 'Internal server error',
        error: error.response?.error || error.name,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    };
  }
};

// Lambda handler cho việc cập nhật nhóm nguyên vật liệu
export const updateMaterialGroup: Handler = async (event: any, context: Context) => {
  const app = await bootstrap(); // Khởi tạo NestJS app
  const service = app.get(MaterialGroupService);

  const id: string = event.pathParameters.id; // Lấy id từ path parameters
  const body: MaterialGroupDto = JSON.parse(event.body); // Lấy dữ liệu từ body

  try {
    const result = await service.update(id, body); // Gọi phương thức cập nhật nhóm nguyên vật liệu
    return {
      statusCode: 200,
      body: JSON.stringify(result),
      headers: {
        'Content-Type': 'application/json',
      },
    };
  } catch (error) {
    return {
      statusCode: error.status || 500,
      body: JSON.stringify({
        message: error.message || 'Internal server error',
        error: error.response?.error || error.name,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    };
  }
};

// Lambda handler cho việc cập nhật trạng thái nhóm nguyên vật liệu
export const updateActiveMaterialGroup: Handler = async (event: any, context: Context) => {
  const app = await bootstrap(); // Khởi tạo NestJS app
  const service = app.get(MaterialGroupService);

  const id: string = event.pathParameters.id; // Lấy id từ path parameters

  try {
    const result = await service.updateActive(id); // Gọi phương thức cập nhật trạng thái nhóm nguyên vật liệu
    return {
      statusCode: 200,
      body: JSON.stringify(result),
      headers: {
        'Content-Type': 'application/json',
      },
    };
  } catch (error) {
    return {
      statusCode: error.status || 500,
      body: JSON.stringify({
        message: error.message || 'Internal server error',
        error: error.response?.error || error.name,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    };
  }
};
