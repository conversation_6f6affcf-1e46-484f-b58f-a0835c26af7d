import { Injectable } from '@nestjs/common';
import * as AWS from 'aws-sdk';
import { configEnv } from '~/@config/env';

const {
  AWS_S3_UPLOAD_FOLDER,
  AWS_S3_BUCKET_NAME,
  AWS_S3_ACCESS_KEY_ID,
  AWS_S3_SECRET_ACCESS_KEY,
  AWS_S3_REGION,
} = configEnv();

@Injectable()
export class S3PrivateService {
  spot: string;
  s3: AWS.S3;

  constructor() {
    this.spot = `__${AWS_S3_UPLOAD_FOLDER}/`;

    this.s3 = new AWS.S3({
      accessKeyId: AWS_S3_ACCESS_KEY_ID,
      secretAccessKey: AWS_S3_SECRET_ACCESS_KEY,
      region: AWS_S3_REGION,
    });
  }

  async uploadS3(
    file: Express.Multer.File,
    fileName: string,
    ACL: 'public-read' | 'private',
    config?: {},
  ): Promise<{ fileName: string; fileUrl: string }> {
    const temp: string[] = file?.originalname ? file.originalname.split('.') : [];

    const ext = temp.length > 1 ? `.${temp[temp.length - 1]}` : '';

    const KEY_PRIVATE = `${this.spot}${fileName}${ext}`;

    const params: AWS.S3.PutObjectRequest = {
      Bucket: AWS_S3_BUCKET_NAME,
      Key: KEY_PRIVATE,
      Body: file.buffer,
      ACL,
      ...config,
    };

    return new Promise<{ fileName: string; fileUrl: string }>((resolve, reject) => {
      this.s3.upload(params, (err, data) => {
        if (err) {
          reject(err);
        } else {
          resolve({ fileName, fileUrl: data.Location });
        }
      });
    });
  }

  async getS3Link(fileUrl: string, expires?: string): Promise<string> {
    let expiresParams = Number(expires) || 3600;

    const PRIVATE_KEY = `${this.spot}${fileUrl.split(this.spot)[1]}`;

    const params = {
      Bucket: AWS_S3_BUCKET_NAME,
      Key: PRIVATE_KEY,
      Expires: Number(expiresParams),
    };

    return new Promise((resolve, reject) => {
      this.s3.getSignedUrl('getObject', params, (err: any, url: any) => {
        if (err) {
          reject(err);
        } else {
          resolve(url);
        }
      });
    });
  }
}
