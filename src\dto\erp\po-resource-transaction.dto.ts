import { PartialType } from '@nestjs/mapped-types';
import { ApiPropertyOptional, ApiProperty } from '@nestjs/swagger';
import { IsOptional, IsString } from 'class-validator';
import { PageRequest } from '~/@systems/utils/page.utils';

export class POResourceTransactionReq extends PartialType(PageRequest) {
  @ApiPropertyOptional({ example: 'PO-20240501' })
  OrderNo?: string;

  @ApiPropertyOptional({ example: 'WIP' })
  Site?: string;

  @ApiPropertyOptional({ example: 'WP3' })
  @IsOptional()
  @IsString()
  Factory?: string;

  @ApiProperty({ example: '2025-05-01T00:00:00.000Z' })
  fromProductionDate: Date | string;

  @ApiProperty({ example: '2025-05-09T23:59:59.999Z' })
  toProductionDate: Date | string;
}

/**
 * DTO cho các bản ghi giao dịch (refs) bên trong data
 */
export class POResourceTransactionRefRes {
  @ApiPropertyOptional({
    description: 'Mã của giao dịch RS',
    example: 'RS_12345',
  })
  LineId?: string;

  @ApiProperty({
    description:
      'production_order_resource.orderId (Chỉ trả kết quả các production order có status=3)',
    example: 'A67890',
  })
  orderID: string;

  @ApiProperty({
    description: 'production_order_resource.orderNo',
    example: 'ORD001',
  })
  OrderNo: string;

  @ApiProperty({
    description:
      'production_order_resource.processId liên kết với bảng process để lấy process.code',
    example: 'OP001',
  })
  Operationcode: string;

  @ApiProperty({
    description: 'production_order_resource.resourceCode',
    example: 'RESOURCE1',
  })
  resourcecode: string;

  @ApiProperty({
    description: 'production_order_resource.actualResourceUsage',
    example: 50.5,
  })
  actualResourceUsage: number;

  @ApiProperty({
    description: 'production_order_resource.uom',
    example: 'hrs',
  })
  UOM: string;

  @ApiProperty({
    description: 'production_order.planstartdate',
    example: '2025-05-05T10:00:00Z',
  })
  transactionDate: Date;

  @ApiProperty({
    description: 'production_order.createdDate',
    example: '2024-11-08T05:05:21.953Z',
  })
  createdDate: Date;

  @ApiProperty({
    description: 'production_order.updatedDate',
    example: '2024-11-08T05:05:21.953Z',
  })
  updatedDate: Date;
}
