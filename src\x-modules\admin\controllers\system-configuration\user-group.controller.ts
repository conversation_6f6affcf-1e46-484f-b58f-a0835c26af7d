import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';
import {
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  Query,
  Patch,
  HttpException,
  UseGuards,
} from '@nestjs/common';
import { DefController, DefGet, DefPost } from '~/@core/decorator';
import { UserGroupEntity } from '~/entities/primary/user-group.entity';
import { UserGroupService } from '../../services/system-configuration/user-group.service';
import { UserService } from '../../services/system-configuration/user.service';
import { PageRequest } from '~/@systems/utils/page.utils';
import { Roles } from '~/@core/decorator/roles.decorator';
import { RoleGuard } from '~/@systems/guard/role.guard';

@DefController('user-group')
export class UserGroupController {
  constructor(
    private readonly userGroupService: UserGroupService,
    private readonly userService: UserService,
  ) {}

  @Get()
  @Roles('/system-configuration/user-group', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: '<PERSON><PERSON><PERSON> danh sách nhóm người dùng' })
  async getUserList(
    @Query('pageIndex') pageIndex: number = 1,
    @Query('pageSize') pageSize: number = 10,
    @Query('searchField') searchField?: string,
    @Query('searchTerm') searchTerm?: string,
  ) {
    try {
      const filters: any = {};

      if (searchField && searchTerm) {
        filters[searchField] = searchTerm;
      }

      const result = await this.userGroupService.findAll({
        pageIndex: pageIndex,
        pageSize: pageSize,
        filters,
        orders: {
          createdDate: 'DESC',
        },
      });

      return {
        data: result.data,
        pagination: {
          total: result.total,
          pageIndex,
          pageSize,
          totalPages: Math.ceil(result.total / pageSize),
        },
      };
    } catch (error) {
      throw new HttpException(
        error.message || 'Có lỗi xảy ra khi lấy danh sách nhóm người dùng',
        500,
      );
    }
  }

  @DefPost('list')
  @Roles('/system-configuration/user-group', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Lấy danh sách nhóm người dùng' })
  async search(
    @Body()
    body: {
      name?: string;
      description?: string;
      isActive?: boolean;
    } & PageRequest,
  ) {
    return this.userGroupService.search(body);
  }

  @Get('status')
  @Roles('/system-configuration/user-group', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Lấy danh sách nhóm người dùng đang hoạt động' })
  async findGroupsByStatus(
    @Query('isActive') isActive: boolean = true,
    @Query('pageIndex') pageIndex: number = 1,
    @Query('pageSize') pageSize: number = 10,
  ) {
    try {
      const activeGroups = await this.userGroupService.findGroupsByStatus(isActive);

      // Thực hiện phân trang thủ công
      const startIndex = (pageIndex - 1) * pageSize;
      const endIndex = pageIndex * pageSize;
      const items = activeGroups.slice(startIndex, endIndex);
      const total = activeGroups.length;
      const totalPages = Math.ceil(total / pageSize);

      return {
        data: items,
        pagination: {
          total,
          pageIndex,
          pageSize,
          totalPages,
        },
      };
    } catch (error) {
      throw new HttpException(
        error.message || 'Có lỗi xảy ra khi lấy danh sách nhóm người dùng đang hoạt động',
        500,
      );
    }
  }

  @Get('available-users')
  @Roles('/system-configuration/user-group', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Lấy danh sách người dùng có thể thêm vào nhóm' })
  async getAvailableUsers(
    @Query('pageIndex') pageIndex: number = 1,
    @Query('pageSize') pageSize: number = 9999,
    @Query('employeeCode') employeeCode?: string,
    @Query('fullName') fullName?: string,
    @Query('email') email?: string,
    @Query('phone') phone?: string,
    @Query('groupId') groupId?: string,
  ) {
    try {
      // Tạo filter params từ các tham số tìm kiếm
      const filterParams: any = { isActive: 'yes' };

      if (employeeCode) filterParams.employeeCode = employeeCode;
      if (fullName) filterParams.fullName = fullName;
      if (email) filterParams.email = email;
      if (phone) filterParams.phone = phone;

      // Gọi service để lấy danh sách người dùng với các bộ lọc
      const result = await this.userService.getUserList({
        page: pageIndex,
        limit: pageSize,
        ...filterParams,
      });

      // Nếu có groupId, lọc ra những người dùng chưa thuộc nhóm
      if (groupId) {
        const group = await this.userGroupService.findGroupWithUsers(groupId);
        if (group && group.users) {
          const existingUserIds = group.users.map((user) => user.id);
          result.items = result.items.filter((user) => !existingUserIds.includes(user.id));
          result.total = result.items.length;
        }
      }

      return {
        data: result.items,
        pagination: {
          total: result.total,
          pageIndex,
          pageSize,
          totalPages: Math.ceil(result.total / pageSize),
        },
      };
    } catch (error) {
      throw new HttpException(error.message || 'Có lỗi xảy ra khi lấy danh sách người dùng', 500);
    }
  }

  @Get(':id')
  @Roles('/system-configuration/user-group', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Lấy thông tin chi tiết nhóm người dùng' })
  async getUserGroup(@Param('id') id: string) {
    try {
      const result = await this.userGroupService.findOne(id);
      if (!result) {
        throw new HttpException('Nhóm người dùng không tồn tại!', 404);
      }
      return { data: result };
    } catch (error) {
      throw new HttpException(
        error.message || 'Có lỗi xảy ra khi lấy thông tin nhóm người dùng',
        error.status || 500,
      );
    }
  }

  @Get(':id/with-users')
  @Roles('/system-configuration/user-group', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Lấy thông tin nhóm người dùng kèm thành viên' })
  async getUserGroupWithUsers(@Param('id') id: string) {
    try {
      const result = await this.userGroupService.findGroupWithUsers(id);
      if (!result) {
        throw new HttpException('Nhóm người dùng không tồn tại!', 404);
      }
      return { data: result };
    } catch (error) {
      throw new HttpException(
        error.message || 'Có lỗi xảy ra khi lấy thông tin nhóm người dùng kèm thành viên',
        error.status || 500,
      );
    }
  }

  @Post()
  @Roles('/system-configuration/user-group', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Tạo mới nhóm người dùng' })
  async createUserGroup(@Body() createData: Partial<UserGroupEntity>) {
    try {
      const result = await this.userGroupService.create(createData);
      return result;
    } catch (error) {
      throw new HttpException(
        error.message || 'Có lỗi xảy ra khi tạo nhóm người dùng',
        error.status || 500,
      );
    }
  }

  @Post(':id/users')
  @Roles('/system-configuration/user-group', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Thêm thành viên vào nhóm' })
  async addUsersToGroup(@Param('id') id: string, @Body('userIds') userIds: string[]) {
    try {
      const group = await this.userGroupService.findOne(id);
      if (!group) {
        throw new HttpException('Nhóm người dùng không tồn tại!', 404);
      }

      const result = await this.userGroupService.addUsers(id, userIds);
      return { message: 'success', data: result };
    } catch (error) {
      throw new HttpException(
        error.message || 'Có lỗi xảy ra khi thêm thành viên vào nhóm',
        error.status || 500,
      );
    }
  }

  @Put(':id')
  @Roles('/system-configuration/user-group', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Cập nhật thông tin nhóm người dùng' })
  async updateUserGroup(@Param('id') id: string, @Body() updateData: Partial<UserGroupEntity>) {
    try {
      const group = await this.userGroupService.findOne(id);
      if (!group) {
        throw new HttpException('Nhóm người dùng không tồn tại!', 404);
      }

      const result = await this.userGroupService.update(id, updateData);
      return { message: 'success', data: result };
    } catch (error) {
      throw new HttpException(
        error.message || 'Có lỗi xảy ra khi cập nhật nhóm người dùng',
        error.status || 500,
      );
    }
  }

  @Delete(':id')
  @Roles('/system-configuration/user-group', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Xóa nhóm người dùng' })
  async deleteUserGroup(@Param('id') id: string) {
    try {
      const group = await this.userGroupService.findOne(id);
      if (!group) {
        throw new HttpException('Nhóm người dùng không tồn tại!', 404);
      }

      await this.userGroupService.remove(id);
      return { message: 'success' };
    } catch (error) {
      throw new HttpException(
        error.message || 'Có lỗi xảy ra khi xóa nhóm người dùng',
        error.status || 500,
      );
    }
  }

  @Delete(':id/users')
  @Roles('/system-configuration/user-group', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Xóa thành viên khỏi nhóm' })
  async removeUsersFromGroup(@Param('id') id: string, @Body('userIds') userIds: string[]) {
    try {
      const group = await this.userGroupService.findOne(id);
      if (!group) {
        throw new HttpException('Nhóm người dùng không tồn tại!', 404);
      }

      const result = await this.userGroupService.removeUsers(id, userIds);
      return { message: 'success', data: result };
    } catch (error) {
      throw new HttpException(
        error.message || 'Có lỗi xảy ra khi xóa thành viên khỏi nhóm',
        error.status || 500,
      );
    }
  }

  @Patch(':id/toggle-status')
  @Roles('/system-configuration/user-group', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Đảo trạng thái nhóm người dùng' })
  async toggleUserGroupStatus(@Param('id') id: string) {
    try {
      const group = await this.userGroupService.findOne(id);
      if (!group) {
        throw new HttpException('Nhóm người dùng không tồn tại!', 404);
      }

      const result = await this.userGroupService.toggleStatus(id);
      return { message: 'success', data: result };
    } catch (error) {
      throw new HttpException(
        error.message || 'Có lỗi xảy ra khi đảo trạng thái nhóm người dùng',
        error.status || 500,
      );
    }
  }

  @DefPost('meta/fetch-types')
  @Roles('/system-configuration/user-group', 'View')
  @UseGuards(RoleGuard)
  @ApiOperation({ summary: 'Lấy danh sách nhà máy theo site' })
  async getMetaTypes(@Body() body: any) {
    return this.userGroupService.getMetaTypes();
  }
}
