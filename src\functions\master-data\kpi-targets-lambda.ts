import { pagination } from './sitewise-properties-lambda';
import { NestFactory } from '@nestjs/core';
import { Context, Handler } from 'aws-lambda';
import { AppModule } from '../../app.module';
import { KPIStandardService } from '~/x-modules/admin/services';

let cachedApp: any = null;

async function bootstrap() {
  if (!cachedApp) {
    const app = await NestFactory.createApplicationContext(AppModule);
    await app.init();
    cachedApp = app;
  }
  return cachedApp;
}

/**
 * Create a new Kpi Targets
 */
export const createKpiTargets: Handler = async (event: any, context: Context) => {
  const app = await bootstrap();
  const service = app.get(KPIStandardService);
  const body = JSON.parse(event.body);

  try {
    const result = await service.create(body);
    return {
      statusCode: 201,
      body: JSON.stringify(result),
      headers: {
        'Content-Type': 'application/json',
      },
      message: result.message,
    };
  } catch (error) {
    return {
      statusCode: error.status || 500,
      body: JSON.stringify({
        message: error.message || 'Internal server error',
        error: error.response?.error || error.name,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    };
  }
};

/**
 * Copy Kpi Targets
 */
export const copyKpiTargets: Handler = async (event: any, context: Context) => {
  const app = await bootstrap();
  const service = app.get(KPIStandardService);
  const body = JSON.parse(event.body);

  try {
    const result = await service.copyKpiTargets(body);
    return {
      statusCode: 200,
      body: JSON.stringify(result),
      headers: {
        'Content-Type': 'application/json',
      },
      message: result.message,
    };
  } catch (error) {
    return {
      statusCode: error.status || 500,
      body: JSON.stringify({
        message: error.message || 'Internal server error',
        error: error.response?.error || error.name,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    };
  }
};
/**
 * Update Kpi Targets
 */
export const updateKpiTargets: Handler = async (event: any, context: Context) => {
  const app = await bootstrap();
  const service = app.get(KPIStandardService);
  const body = JSON.parse(event.body);

  try {
    const result = await service.update(body);
    return {
      statusCode: 200,
      body: JSON.stringify(result),
      headers: {
        'Content-Type': 'application/json',
      },
      message: result.message,
    };
  } catch (error) {
    return {
      statusCode: error.status || 500,
      body: JSON.stringify({
        message: error.message || 'Internal server error',
        error: error.response?.error || error.name,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    };
  }
};

/**
 * Get paginated list
 */
export const paginationKpiTarget = async (event: any) => {
  const service = cachedApp.get(KPIStandardService);
  const params = event.queryStringParameters?.params;
  try {
    const result = await service.pagination(params);
    return {
      statusCode: 200,
      body: JSON.stringify(result),
      headers: {
        'Content-Type': 'application/json',
      },
      message: result.message,
    };
  } catch (error) {
    return {
      statusCode: error.status || 500,
      body: JSON.stringify({
        message: error.message || 'Internal server error',
        error: error.response?.error || error.name,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    };
  }
};

/**
 * Load Data Kpi Set by ID
 */
export const loadDataKpiSetById: Handler = async (event: any, context: Context) => {
  const app = await bootstrap();
  const service = app.get(KPIStandardService);

  try {
    const kpiSetId = event.pathParameters?.kpiSetId;
    if (!kpiSetId) {
      return {
        statusCode: 400,
        body: JSON.stringify({
          message: 'Missing ID parameter',
        }),
        headers: {
          'Content-Type': 'application/json',
        },
      };
    }

    const result = await service.loadDataKpiSetFirst(kpiSetId);
    return {
      statusCode: 200,
      body: JSON.stringify(result),
      headers: {
        'Content-Type': 'application/json',
      },
      message: 'Get Kpi Set successfully!',
    };
  } catch (error) {
    return {
      statusCode: error.status || 500,
      body: JSON.stringify({
        message: error.message || 'Internal server error',
        error: error.response?.error || error.name,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    };
  }
};

/**
 * Find Data Kpi Set by ID
 */
export const findDataKpiSetById: Handler = async (event: any, context: Context) => {
  const app = await bootstrap();
  const service = app.get(KPIStandardService);

  try {
    const kpiSetId = event.pathParameters?.kpiSetId;
    if (!kpiSetId) {
      return {
        statusCode: 400,
        body: JSON.stringify({
          message: 'Missing ID parameter',
        }),
        headers: {
          'Content-Type': 'application/json',
        },
      };
    }

    const result = await service.find(kpiSetId);
    return {
      statusCode: 200,
      body: JSON.stringify(result),
      headers: {
        'Content-Type': 'application/json',
      },
      message: 'Find Data Kpi Set successfully!',
    };
  } catch (error) {
    return {
      statusCode: error.status || 500,
      body: JSON.stringify({
        message: error.message || 'Internal server error',
        error: error.response?.error || error.name,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    };
  }
};
export const deleteKpiTargetById: Handler = async (event: any, context: Context) => {
  const app = await bootstrap();
  const service = app.get(KPIStandardService);

  try {
    const groupId = event.pathParameters?.groupId;
    if (!groupId) {
      return {
        statusCode: 400,
        body: JSON.stringify({
          message: 'Missing ID parameter',
        }),
        headers: {
          'Content-Type': 'application/json',
        },
      };
    }

    const result = await service.delete(groupId);
    return {
      statusCode: 200,
      body: JSON.stringify(result),
      headers: {
        'Content-Type': 'application/json',
      },
      message: 'Delete Kpi Target successfully!',
    };
  } catch (error) {
    return {
      statusCode: error.status || 500,
      body: JSON.stringify({
        message: error.message || 'Internal server error',
        error: error.response?.error || error.name,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    };
  }
};
export const findDataKpiSetByGroupId: Handler = async (event: any, context: Context) => {
  const app = await bootstrap();
  const service = app.get(KPIStandardService);

  try {
    const kpiSetId = event.pathParameters?.kpiSetId;
    const groupId = event.pathParameters?.groupId;
    if (!kpiSetId && groupId) {
      return {
        statusCode: 400,
        body: JSON.stringify({
          message: 'Missing ID parameter',
        }),
        headers: {
          'Content-Type': 'application/json',
        },
      };
    }

    const result = await service.findByGroupId(kpiSetId, groupId);
    return {
      statusCode: 200,
      body: JSON.stringify(result),
      headers: {
        'Content-Type': 'application/json',
      },
      message: 'Find Data Kpi Set successfully!',
    };
  } catch (error) {
    return {
      statusCode: error.status || 500,
      body: JSON.stringify({
        message: error.message || 'Internal server error',
        error: error.response?.error || error.name,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    };
  }
};

/**
 * Load data select for declare Kpi Target
 */
export const loadDataKpiSet: Handler = async (event: any, context: Context) => {
  const app = await bootstrap();
  const service = app.get(KPIStandardService);

  try {
    const result = await service.loadDataKpiSet();
    return {
      statusCode: 200,
      body: JSON.stringify(result),
      headers: {
        'Content-Type': 'application/json',
      },
      message: 'Get data successfully!',
    };
  } catch (error) {
    return {
      statusCode: error.status || 500,
      body: JSON.stringify({
        message: error.message || 'Internal server error',
        error: error.response?.error || error.name,
      }),
      headers: {
        'Content-Type': 'application/json',
      },
    };
  }
};
