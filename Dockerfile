# FROM node:20.9.0-alpine AS dist
# WORKDIR /tmp/
# USER root
# RUN apk --no-cache add --virtual builds-deps giflib-dev build-base python3 cairo-dev pango-dev g++ make libc6-compat libpng-dev libjpeg-turbo-dev libtool autoconf automake nasm
# RUN npm install -g node-gyp
# COPY package.json yarn.lock tsconfig.json tsconfig.build.json ./
# COPY src/ src/
# ENV NODE_OPTIONS=--max_old_space_size=4096
# RUN yarn
# RUN yarn build

# FROM node:20.9.0-alpine AS node_modules
# WORKDIR /tmp/
# USER root
# RUN apk --no-cache add --virtual builds-deps giflib-dev build-base python3 cairo-dev pango-dev g++ make libc6-compat libpng-dev libjpeg-turbo-dev libtool autoconf automake nasm
# RUN npm install -g node-gyp
# COPY package.json yarn.lock ./
# RUN yarn install --production

# FROM node:20.9.0-alpine
# WORKDIR /usr/local/nub-api

# RUN apk --no-cache add \
#     cairo \
#     pango \
#     giflib \
#     libjpeg-turbo \
#     pixman \
#     freetype \
#     glib

# COPY --from=node_modules /tmp/node_modules ./node_modules
# COPY --from=dist /tmp/dist ./dist
# # COPY resources/ resources/
# COPY src/assets ./dist/assets/
# USER root
# CMD exec node --max_old_space_size=4096 dist/main.js

# ------------------------------------------------------------

FROM node:20.9.0-alpine as dist
WORKDIR /tmp/
RUN apk --no-cache add --virtual builds-deps build-base python3
COPY package.json yarn.lock tsconfig.json tsconfig.build.json ./
COPY src/ src/
ENV NODE_OPTIONS=--max_old_space_size=4096
RUN yarn
RUN yarn build

FROM node:20.9.0-alpine as node_modules
WORKDIR /tmp/
RUN apk --no-cache add --virtual builds-deps build-base python3
COPY package.json yarn.lock ./
RUN yarn install --production

FROM node:20.9.0-alpine
WORKDIR /usr/local/nub-api
COPY --from=node_modules /tmp/node_modules ./node_modules
COPY --from=dist /tmp/dist ./dist
RUN mkdir /uploads
# COPY resources/ resources/
COPY src/assets ./dist/assets/
CMD exec node --max_old_space_size=4096 dist/main.js