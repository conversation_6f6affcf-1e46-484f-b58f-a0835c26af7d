import { Injectable } from '@nestjs/common';
import moment from 'moment';
import { Connection } from 'typeorm';
import { BindRepo } from '~/@core/decorator';
import { CzCodeCheckReq, CzCodeActiveReq } from '~/dto/cz-code.dto'
import { UniqueCodeActivationRepo } from '~/repositories/primary';
import { CzCodeService } from '../cz-code'

@Injectable()
export class CzCodeActiveService {
  constructor( 
    private readonly connection: Connection,
    private readonly czCodeService: CzCodeService
   ) {}

  @BindRepo(UniqueCodeActivationRepo)
  private uniqueCodeActivationRepo: UniqueCodeActivationRepo;

  async findCzCodeByCode(requirement: CzCodeActiveReq) {
    const query = `
      SELECT 
        poucd.*,
        po."orderNo",
        po."lotNumber",
        po."planStartDate",
        i."shelfLifeDay"
      FROM production_order_unique_code_detail poucd 
      INNER JOIN production_order po ON po.id = poucd."orderId" 
      INNER join item i on i.id = po."itemId" 
      WHERE poucd."uniqueCode" = $1
        AND po."lineId" = $2
        AND po."siteId" = $3
        AND po."factoryId" = $4
        AND po."processAreaId" = $5
        AND po."id" = $6
    `
    const result = await this.connection.query(
      query,
      [
        requirement.czCode,
        requirement.lineId,
        requirement.siteId,
        requirement.factoryId,
        requirement.processAreaId,
        requirement.productionOrderId
      ]
    );
    return result?.length > 0 ? result[0] : undefined
  }

  handleExpiredDate(productionDate:Date | null, shelfLifeDay: number) {
      if (!productionDate) return '';
      const shelfLifeDayValue = shelfLifeDay ? shelfLifeDay : 0
  
      const newDate = moment(productionDate, 'DD/MM/YYYY').add(shelfLifeDayValue, 'days');
      return newDate.toDate();
    }

  async handleSuccessRep(req: CzCodeCheckReq, code: Number, message: string) {
    let czCodeBody = {}
    if(code !== 400){ 
      const czCodeInfo = await this.czCodeService.czCodeCheckService(req);
      czCodeBody = czCodeInfo?.body || {};
    }      
    return {
        statusCode: code,
        message: message || 'Active CzCode successfull !',
        czCode: req?.czCode || '',
        body: czCodeBody
    }
  }

  async czCodeActiveService(data: CzCodeActiveReq) {
    try {
      // Handle get requirement:
      const czCodeReq = data?.czCode || '';
      const getLineId = data?.lineId ? data.lineId : undefined;
      const getSiteId = data?.siteId ? data.siteId : undefined;
      const getFactoryId = data?.factoryId ? data.factoryId : undefined;
      const getProcessAreaId = data?.processAreaId ? data.processAreaId : undefined;
      const getProductionOrderId = data?.productionOrderId ? data.productionOrderId : undefined;

      if(czCodeReq === '' || !getLineId || !getSiteId || !getFactoryId || !getProcessAreaId || !getProductionOrderId) return this.handleSuccessRep(data, 400, 'Missing required filters !');

      // Tìm cz code trong bảng ProductionOrderUniqueCodeDetailRepo 
      const czCodeData = await this.findCzCodeByCode(data)
      // Nếu không tìm thấy cz code được khai báo thì trả về lỗi 
      if(!czCodeData) return this.handleSuccessRep(data, 400, 'CzCode is not found !');

      // Tìm tiếp trong bảng uniqueCodeActivationRepo: czCode này đã được xử lý chưa.
      const uniqueCodeActivationData = await this.uniqueCodeActivationRepo.findOne({
        where: { uniqueCode : czCodeReq }
      })

      // Nếu có uniqueCodeActivationData: return czCode đã được xử lý
      if(uniqueCodeActivationData) return this.handleSuccessRep(data, 409, 'This code has already been used !');
 
      const czCodeActiveObj = {
        orderId: czCodeData.orderId,
        orderNo: czCodeData.orderNo,
        uniqueCode: czCodeData.uniqueCode,
        status: 1,
        productionDate: czCodeData.planStartDate,
        expiredDate: this.handleExpiredDate(czCodeData.planStartDate, czCodeData.shelfLifeDay),
        source: 'M'
      }
      await this.uniqueCodeActivationRepo.save({...czCodeActiveObj});

      return this.handleSuccessRep(data, 200, 'CzCode is active successfully !');
    } catch (error) {
      return this.handleSuccessRep(data, 400, error.message || 'Failed to active CzCode')
    }
  }
}
