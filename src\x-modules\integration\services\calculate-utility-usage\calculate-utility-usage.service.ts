import { ConsoleLogger, Injectable } from '@nestjs/common';
import { BindRepo } from '~/@core/decorator';
import { MesDxMetricsRepo } from '~/repositories/scada';
import { JwtService } from '@nestjs/jwt';
import {
    UtilityMetersRepo,
    OrganizationUnitRepo,
    UtilityTransactionRepo
} from '~/repositories/primary';
import { AssignShiftRepository } from '~/repositories/primary/assign-shift.repo';
import { ShiftRepo } from '~/repositories/primary/shift.repo';
import { Between, In, Connection } from 'typeorm';
import moment from 'moment';
import { dateHelper } from '~/common/helpers/date.helper';

@Injectable()
export class CalculateUtilityUsage {
    constructor(
        private jwtService: JwtService,
        private readonly connection: Connection
    ) { }

    // Admin Database
    @BindRepo(UtilityMetersRepo)
    private readonly utilityMetersRepo: UtilityMetersRepo;

    @BindRepo(OrganizationUnitRepo)
    private readonly organizationUnitRepo: OrganizationUnitRepo;

    @BindRepo(AssignShiftRepository)
    private readonly assignShiftRepository: AssignShiftRepository;

    @BindRepo(ShiftRepo)
    private readonly shiftRepo: ShiftRepo;

    @BindRepo(MesDxMetricsRepo)
    private readonly mesDxMetricsRepo: MesDxMetricsRepo;

    @BindRepo(UtilityTransactionRepo)
    private readonly utilityTransactionRepo: UtilityTransactionRepo;

    private getNumericValue(value: string) {
        let num = Number(value);
        return isNaN(num) ? 0 : num;
    }

    private async isUtilityTransactionExists(meterDate: any, shiftIdInfo: string, meterIdInfo: string) {
        const utilityTransactionData = await this.utilityTransactionRepo.findOne(
            { date: meterDate, shiftId: shiftIdInfo, meterId: meterIdInfo, createdByUser: '-1' }
        );
        if (!utilityTransactionData) return '';

        return utilityTransactionData?.id
    }

    private async onStoredUtilityTransaction(
        date: any,
        shiftId: string,
        meterId: string,
        value: number
    ) {
        const utilityTransactionObj = {
            date,
            value,
            shiftId,
            meterId,
            createdByUser: '-1',
            postedAt: new Date(),
            updatedByUser: '-1'
        }

        await this.utilityTransactionRepo.save({ ...utilityTransactionObj })
    }

    private onGetMetricsData(
        timeDate: string, 
        indexDate: number,
        getDateTo: any
    ) {
        const [hours, minutes, seconds] = timeDate.split(":").map(Number);
        let getTime = moment().utcOffset(7 * 60).subtract(indexDate, 'days');
        if(getDateTo) getTime = moment(getDateTo, "YYYY-MM-DD HH:mm:ss.SSS Z").utcOffset(7 * 60).subtract(indexDate, 'days');
        getTime.set({
            hour: hours,
            minute: minutes,
            second: seconds,
            millisecond: 0
        });
        return getTime.format('YYYY-MM-DD HH:mm:ss.SSS Z')
    }

    private async onUtilityTransaction(
        assetIdInfo: string,
        metricIdInfo: string,
        startTime: any,
        endTime: any,
        calMethodCode: string,
        shiftIdInfo: string,
        meterIdInfo: string,
        isNightShift: boolean,
        uomCalMethodCodeInfo: string,
        inclusiveDays:number,
        getDateTo: any,
    ) {
        const limitLopp = inclusiveDays ? inclusiveDays : 3
        // Xử lý tìm dữ liệu từ ngày hiện tại và 2 ngày trước.
        for (let i = 0; i < limitLopp; i++) {
            // Nếu đã có record rồi thì ko xử lý tiếp
            // const dateMeter = dateHelper.getTodayWithCustomTime('00:00:00', 'Asia/Ho_Chi_Minh', 'Asia/Ho_Chi_Minh')
            // dateMeter.setDate(dateMeter.getDate() - (!isNightShift ? i : i + 1));
            const dateMeter = this.onGetMetricsData('00:00:00', (!isNightShift ? i : i + 1), getDateTo)
            const isCheckUtilityTransaction = await this.isUtilityTransactionExists(dateMeter, shiftIdInfo, meterIdInfo)

            // Nếu isNightShift thì startDate phải lùi thêm 1 ngày
            const getStartTimeFind = this.onGetMetricsData(startTime, (!isNightShift ? i : i + 1), getDateTo)
            const getEndTimeFind = this.onGetMetricsData(endTime, i, getDateTo)
            const result = await this.connection.query(
                `SELECT calculate_utility_value($1, $2) AS data`,
                [getStartTimeFind, getEndTimeFind]
            );

            let getValue = 0
            if(result[0] && result[0]?.data){
                const sumMetricsValue = result[0].data.find(item => 
                    item.assetId === assetIdInfo &&
                    item.metricId === metricIdInfo
                )
                if(sumMetricsValue){ getValue = sumMetricsValue['total_diff'] }
            }

            if(getValue === 0) return;

            // // Làm tròn đến số thập phân thứ 5
            getValue = Math.round(getValue * 100000) / 100000;
            // if (isCheckUtilityTransaction) return;
            if (isCheckUtilityTransaction === '') {
                // nếu chưa có thì insert giá trị
                await this.onStoredUtilityTransaction(dateMeter, shiftIdInfo, meterIdInfo, getValue)
            }else{
                // nếu có thì update lại giá trị value
                await this.utilityTransactionRepo.update(
                    { id: isCheckUtilityTransaction },
                    { value: getValue }
                );
            }
        }
    }

    async createCalculateUtilityUsage(data: { fromDate?: string; toDate?: string }) {
        try {
            // set-up data
            let getDateFrom = null
            let getDateTo = null
            if(data.fromDate && data.toDate){
                getDateFrom = dateHelper.formatDateByJobManual(data.fromDate)
                getDateTo = dateHelper.formatDateByJobManual(data.toDate)
            }
            // Tính số ngày chênh lệch
            const getDateFromTamp = moment(getDateFrom, "YYYY-MM-DD HH:mm:ss.SSS Z");
            const getDateToTamp = moment(getDateTo, "YYYY-MM-DD HH:mm:ss.SSS Z");
    
            const inclusiveDays = getDateToTamp.startOf('day').diff(getDateFromTamp.startOf('day'), 'days') + 1;

            // Quét tất cả các utility meter còn đang active
            const utilityMasterData = await this.utilityMetersRepo.find({ where: { isActive: true } })
            if (!utilityMasterData) return;

            await Promise.all(
                utilityMasterData.map(async (utilityMaster) => {
                    const getFactoryId = utilityMaster?.factoryId || '';
                    const getAssetId = utilityMaster?.assetId || '';
                    const getMetricId = utilityMaster?.metricId || '';
                    const getCalMethodCode = utilityMaster?.calMethodCode || ''
                    const getMeterId = utilityMaster?.id || ''
                    const getUomCalMethodCode = utilityMaster?.uomCalMethodCode || ''

                    // Liên kết với bảng assign shift cho organization unit để lấy tất cả các ca đang active theo factory
                    const assignShiftRepositoryData = await this.assignShiftRepository.find({ where: { organizationId: getFactoryId, status: true } })
                    if (!assignShiftRepositoryData) return;

                        await Promise.all(
                            assignShiftRepositoryData.map(async (assignShiftRepository) => {
                                // Thông tin shift, start time, end time của shift
                                const getShiftId = assignShiftRepository?.shiftId || '';
                                const shiftData = await this.shiftRepo.findOne({ where: { id: getShiftId } })
                                if (!shiftData) return;
    
                                const getStartTime = shiftData?.startTime;
                                const getEndTime = shiftData?.endTime;
                                const isNightShift = shiftData?.nightShift;
                                await this.onUtilityTransaction(
                                    getAssetId, 
                                    getMetricId, 
                                    getStartTime, 
                                    getEndTime, 
                                    getCalMethodCode, 
                                    getShiftId, 
                                    getMeterId, 
                                    isNightShift, 
                                    getUomCalMethodCode,
                                    inclusiveDays,
                                    getDateTo
                                )
                            })
                        )
                })
            );
        } catch (error) {
            console.log(error, 'error');
        }
    }
}
