# Admin <PERSON>le (`admin`)

## 📋 Tổng quan

Mo<PERSON>le `admin` đảm nhiệm các chức năng quản trị hệ thống

## 📁 Directory Structure

admin 
│   │
│   ├── data-calculation-by-job             # Tính toán dữ liệu theo job
│   │ 
│   ├── master-data                         #  Quản lý Master Data
│   │         
│   ├── permission                          #  Quản lý phân quyền role cho từng User
│   │        
│   ├── production-execution-monitoring     #  Giám sát thực hiện sản xuất
│   │        
│   ├── production-order-batch              #  Sản xuất lô sản phẩm 
│   │        
│   ├── recipe                              #  Công thức 
│   │        
│   ├── report-dashboard                    #  Dashboard KPI
│   │        
│   ├── scale-scan                          #  Cân và Scan
│   │        
│   ├── system-configuration                #  Cấu hình hệ thống 
│   │        
│   ├── api-token                           #  Quản lý các token để truy cập các API (Bên Scada gọi qua)
│   │        
│   ├── auth                                #  dùng để xác thực đăng nhập người dùng thông qua hệ thống Microsoft
│   │        
│   ├── sitewise                            #  Dùng để quản lý và xử lý dữ liệu liên quan đến hệ thống AWS SiteWise và hệ thống giám sát sản xuất (OEE)
│   │        
│   ├── admin-session.context               #  Lấy thông tin phiên làm việc (session) của admin hoặc user từ RequestContext
│   │        
│   ├── admin.middleware                    #  Xác thực token Microsoft SSO và ngăn chặn truy cập trái phép vào hệ thống.



---

## ⚙️ Sơ đồ luồng xử lý                                        
Client Request                                              
      │
      ▼
┌─────────────────────────────┐
│        RequestContext       │
└────────────┬────────────────┘
             │
             ▼
┌─────────────────────────────┐
│       admin.middleware      │◄────────────┐
│  - Xác thực Microsoft token │             │
│  - Lấy user nếu hợp lệ      │             │
└────────────┬────────────────┘             │
             ▼                              │
┌─────────────────────────────┐             │
│  admin-session.context      │             │
│  - Retrieve user session    │             │
│    (userId, email, ...)     │             │
└────────────┬────────────────┘             │
             ▼                              │
        ┌────────────┐                      │
        │ Controller │◄─────────────────────┘
        └────┬───────┘
             ▼
        ┌────────────────────────────────────┐
        │       Business Logic Modules       │
        │    (data, permission, config,...)  │
        └────────────┬───────────────────────┘
                     ▼
                 Response

---

### 🛠️. Tổng quan sơ đồ luồng xử lý 
- Khi Client gửi một yêu cầu (request) đến hệ thống, đầu tiên request đó được xử lý qua lớp RequestContext – nơi thu thập thông tin cơ bản về request (như headers, IP, v.v...). Sau đó, request tiếp tục đi qua tầng admin.middleware, nơi thực hiện việc xác thực người dùng. Middleware sẽ kiểm tra token Microsoft mà người dùng gửi lên, nếu token hợp lệ thì lấy thông tin người dùng tương ứng.

- Tiếp theo, request đi đến admin-session.context, nơi lấy session người dùng, cụ thể như userId, email, vai trò (role), hoặc các quyền truy cập cần thiết. Thông tin này sẽ được giữ lại để các phần xử lý phía sau có thể sử dụng.

- Từ đó, request được chuyển đến Controller – nơi định tuyến request đến đúng chức năng trong hệ thống. Dựa vào API endpoint, controller sẽ gọi đến module nghiệp vụ tương ứng.

- Các module nghiệp vụ (business logic) sẽ thực hiện xử lý logic cần thiết. Mỗi module tương ứng với một nhóm chức năng riêng biệt trong hệ thống

- Cuối cùng, sau khi logic được xử lý xong, kết quả được trả ngược lại qua controller và gửi về phía client


