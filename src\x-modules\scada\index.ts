import { MiddlewareConsumer, NestModule, RequestMethod, forwardRef } from '@nestjs/common';
import { ChildModule } from '~/@core/decorator';
import { RefixModule } from '../config-module';
import * as allController from './controllers';
import * as allService from './services';
import { ScadaMiddleware } from './scada.middleware';
import { SQSModule } from '~/x-modules/sqs';
@ChildModule({
  prefix: RefixModule.scada,
  providers: [...Object.values(allService)],
  controllers: [...Object.values(allController)],
  imports: [forwardRef(() => SQSModule)],
})
export class ScadaModule implements NestModule {
  configure(consumer: MiddlewareConsumer) {
    consumer
      .apply(ScadaMiddleware)
      .exclude()
      .forRoutes({ path: `${RefixModule.scada}*`, method: RequestMethod.ALL });
  }
}
