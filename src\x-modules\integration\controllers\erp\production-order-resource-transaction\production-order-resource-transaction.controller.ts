import { Query, UseGuards, HttpStatus, Inject } from '@nestjs/common';
import { Api<PERSON>aram, ApiQuery, ApiResponse } from '@nestjs/swagger';
import { DefController, DefGet, DefPost } from '~/@core/decorator';
import { ApiTokenGuard } from '~/@core/decorator/api-token.decorator';
import { PageRequest, PageResponse } from '~/@systems/utils';
import { ERPDataPayloadRes, ERPMessagePayloadRes } from '~/dto/erp/payload.dto';
import {
  POResourceTransactionRefRes,
  POResourceTransactionReq,
} from '~/dto/erp/po-resource-transaction.dto';
import { POResourceTransactionERPIntegrationService } from '~/x-modules/integration/services/production-order-resource-transaction';

@UseGuards(ApiTokenGuard)
@DefController('erp/production-order-resource-transaction')
export class POResourceTransactionERPIntegrationController {
  constructor(private readonly integrateService: POResourceTransactionERPIntegrationService) {}

  @DefGet('list', {
    summary: 'ERP gọi API của MES để lấy thông tin các dữ liệu Resource với mỗi lệnh sản xuất',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: 'Success response',
    schema: {
      example: {
        code: 200,
        status: 'success',
        message: 'successfully',
        data: [
          {
            created_at: '2024-11-08T05:05:21.953Z',
            updated_at: '2024-11-08T05:05:21.953Z',
            refs: [
              {
                LineID: '1',
                orderID: 'A67890',
                OrderNo: 'ORD001',
                OperationCode: 'OP001',
                ResourceCode: 'RESOURCE1',
                ActualResourceUsage: 50.5,
                UOM: 'hrs',
                TransactionDate: '2025-05-05T10:00:00Z',
              },
            ],
          },
        ],
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.UNPROCESSABLE_ENTITY,
    description: 'Error response',
    schema: {
      example: {
        code: 422,
        status: 'Error',
        message: 'Unprocessable',
        errors: [
          { field: 'OrderNo', message: 'Invalid' },
          { field: 'OperationCode', message: 'Invalid' },
        ],
      },
    },
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description:
      'Bad Request - Máy khách gửi yêu cầu với thông số không đúng (ví dụ: một giá trị bị thiếu hoặc sai kiểu)',
  })
  @ApiResponse({
    status: HttpStatus.UNAUTHORIZED,
    description:
      'Unauthorized - API yêu cầu người dùng cung cấp token xác thực nhưng token không hợp lệ',
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: 'Not Found - API không tìm thấy tài nguyên mà đang yêu cầu',
  })
  @ApiResponse({
    status: HttpStatus.FORBIDDEN,
    description: 'Forbidden - Người dùng không có quyền truy cập tài nguyên',
  })
  @ApiResponse({
    status: HttpStatus.METHOD_NOT_ALLOWED,
    description: 'Method Not Allowed - Phương thức không được hỗ trợ',
  })
  @ApiResponse({
    status: HttpStatus.REQUEST_TIMEOUT,
    description: 'Request Timeout - Yêu cầu của máy khách quá thời gian chờ',
  })
  @ApiResponse({
    status: HttpStatus.INTERNAL_SERVER_ERROR,
    description: 'Internal Server Error - Máy chủ gặp lỗi không xác định khi xử lý yêu cầu',
  })
  async list(
    @Query() param: POResourceTransactionReq,
  ): Promise<ERPMessagePayloadRes<ERPDataPayloadRes<POResourceTransactionRefRes>> | any> {
    //
    return await this.integrateService.list(param);
  }
}
