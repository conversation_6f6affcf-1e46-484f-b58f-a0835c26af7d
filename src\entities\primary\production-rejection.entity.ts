import { ApiProperty } from '@nestjs/swagger';
import { Column, Entity } from 'typeorm';
import { PrimaryBaseEntity } from '../primary-base.entity';

@Entity('production_rejection')
export class ProductionRejectionEntity extends PrimaryBaseEntity {
  @ApiProperty({ description: 'Site id' })
  @Column({ nullable: true, type: 'uuid' })
  siteId: string;

  @ApiProperty({ description: 'Factory id' })
  @Column({ nullable: true, type: 'uuid' })
  factoryId: string;

  @ApiProperty({ description: 'Production Line id' })
  @Column({ nullable: true, type: 'uuid' })
  productionLineId: string;

  @ApiProperty({ description: 'Process Area id' })
  @Column({ nullable: true, type: 'uuid' })
  processAreaId: string;

  @ApiProperty({ description: 'Production Order id' })
  @Column({ nullable: true, type: 'uuid' })
  productionOrderId: string;

  @ApiProperty({ description: 'Item id' })
  @Column({ nullable: true, type: 'uuid' })
  itemId: string;

  @ApiProperty({ description: 'Transaction date' })
  @Column({ nullable: true, type: 'timestamptz' })
  transactionDate: Date;

  @ApiProperty({ description: 'Shift id' })
  @Column({ nullable: true, type: 'uuid' })
  shiftId: string;

  @ApiProperty({ description: 'Transaction Type Code' })
  @Column({ nullable: false })
  transactionTypeCode: string;

  @ApiProperty({ description: 'Transaction Type Detail Code' })
  @Column({ nullable: false })
  transactionTypeDetailCode: string;

  @ApiProperty({ description: 'quantity' })
  @Column({ nullable: false, type: 'numeric' })
  quantity: number;

  @ApiProperty({ description: 'UOM Code' })
  @Column({ nullable: false })
  uomCode: string;

  @ApiProperty({ description: 'Posted Trx' })
  @Column({ nullable: false })
  postedTrx: number;

  @ApiProperty({ description: 'Note' })
  @Column({ nullable: true })
  note: string;

  @ApiProperty({ description: 'Description' })
  @Column({ nullable: true })
  description: string;

  @ApiProperty({ description: 'OEE Cal' })
  @Column({ nullable: true, default: 1 })
  oeeCal: number;

  @ApiProperty({ description: 'Rejection Reason id' })
  @Column({ nullable: true, type: 'uuid' })
  rejectionReasonId: string;
}
