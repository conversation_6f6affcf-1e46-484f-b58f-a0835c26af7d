import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { PageRequest } from '~/@systems/utils';
// import { PrimaryBaseDto } from '../../../@core/base/primary-base.dto';

export class ListOrganizationUnitReq extends PageRequest {
  @ApiPropertyOptional({ description: 'Page number', default: 1 })
  page: number;

  @ApiPropertyOptional({ description: 'Number of items per page', default: 10 })
  take: number;

  @ApiPropertyOptional({ description: 'Number of items to skip' })
  skip: number;

  @ApiPropertyOptional({ description: 'Skip pagination and return all results' })
  skipPagination?: boolean;

  @ApiPropertyOptional({ description: 'Organization unit level ID' })
  levelId?: string;

  @ApiPropertyOptional({ description: 'Organization unit level code' })
  levelCode?: string;

  @ApiPropertyOptional({ description: 'Level general data parent code' })
  levelGeneralDataParentCode?: string;

  @ApiPropertyOptional({ description: 'Level general data detail code' })
  levelGeneralDataDetailCode?: string;

  @ApiPropertyOptional({ description: 'Organization unit category ID' })
  categoryId?: string;

  @ApiPropertyOptional({ description: 'Organization unit category code' })
  categoryCode?: string;

  @ApiPropertyOptional({ description: 'Category general data parent code' })
  categoryGeneralDataParentCode?: string;

  @ApiPropertyOptional({ description: 'Category general data detail code' })
  categoryGeneralDataDetailCode?: string;

  @ApiPropertyOptional({ description: 'Active/Inactive', example: true })
  isActive?: boolean | string;

  @ApiPropertyOptional({ description: 'Organization unit code', example: 'ORG-001' })
  code?: string;

  @ApiPropertyOptional({ description: 'Organization unit name', example: 'Marketing' })
  name?: string;

  @ApiPropertyOptional({ description: 'Storage Location', example: 'Warehouse A' })
  storageLocation?: string;

  @ApiPropertyOptional({ description: 'Parent organization unit ID' })
  parentId?: string;

  @ApiPropertyOptional({
    description: 'Created date from (ISO format)',
    example: '2023-01-01T00:00:00.000Z',
  })
  createdDateFrom?: string;

  @ApiPropertyOptional({
    description: 'Created date to (ISO format)',
    example: '2023-12-31T23:59:59.999Z',
  })
  createdDateTo?: string;
}

export class OrganizationUnitDto {
  @ApiProperty({ description: 'Organization unit code', example: 'ORG-001' })
  code: string;

  @ApiProperty({ description: 'Organization unit name', example: 'Marketing Department' })
  name: string;

  @ApiPropertyOptional({
    description: 'Organization unit description',
    example: 'Marketing department for product promotion',
  })
  description?: string;

  @ApiPropertyOptional({ description: 'Organization unit level ID' })
  levelId?: string;

  @ApiPropertyOptional({ description: 'Level general data parent code' })
  levelGeneralDataParentCode?: string;

  @ApiPropertyOptional({ description: 'Level general data detail code' })
  levelGeneralDataDetailCode?: string;

  @ApiPropertyOptional({ description: 'Organization unit level' })
  level?: {
    id: string;
    code: string;
    name: string;
  };

  @ApiPropertyOptional({ description: 'Organization unit category ID' })
  categoryId?: string;

  @ApiPropertyOptional({ description: 'Category general data parent code' })
  categoryGeneralDataParentCode?: string;

  @ApiPropertyOptional({ description: 'Category general data detail code' })
  categoryGeneralDataDetailCode?: string;

  @ApiPropertyOptional({ description: 'Organization unit category' })
  category?: {
    id: string;
    code: string;
    name: string;
  };

  @ApiPropertyOptional({ description: 'Organization unit capacity', example: 100.5 })
  capacity?: number;

  @ApiPropertyOptional({ description: 'Weighting distribution', example: 0.75 })
  weightingDistribution?: number;

  @ApiPropertyOptional({ description: 'Ingredient locator', example: 'SECTION-A' })
  ingredientLocator?: string;

  @ApiPropertyOptional({ description: 'Products locator', example: 'SECTION-B' })
  productsLocator?: string;

  @ApiPropertyOptional({ description: 'Storage Location', example: 'Warehouse A, Shelf B3' })
  storageLocation?: string;

  @ApiPropertyOptional({ description: 'Parent organization unit ID', example: 'uuid' })
  parentId?: string;

  @ApiPropertyOptional({ description: 'Scada ID', example: 'uuid' })
  ipScada?: string;

  @ApiPropertyOptional({
    description: 'Active/Inactive',
    example: true,
    default: true,
  })
  isActive: boolean;

  @ApiPropertyOptional({ description: 'Material Handle Type', nullable: true })
  materialHandleType?: string;

  @ApiPropertyOptional({ description: 'Additional notes', example: 'Special handling required' })
  note?: string;

  @ApiPropertyOptional({ description: 'Order number for sorting', example: 1 })
  orderNo?: number;

  @ApiPropertyOptional({ description: 'Has children', example: true })
  hasChildren?: boolean;

  @ApiPropertyOptional({ description: 'Parent organization unit', type: () => OrganizationUnitDto })
  parent?: OrganizationUnitDto;

  @ApiPropertyOptional({
    description: 'Child organization units',
    type: () => [OrganizationUnitDto],
  })
  children?: OrganizationUnitDto[];
}
