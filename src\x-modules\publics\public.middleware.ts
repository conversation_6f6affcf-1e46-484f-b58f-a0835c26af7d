import {
  BadRequestException,
  Injectable,
  NestMiddleware,
  UnauthorizedException,
} from '@nestjs/common';
import { NextFunction, Request, Response } from 'express';
import { configEnv } from '~/@config/env';
import { SystemValue } from '~/common/constants/SystemValue';
import requestHelper from '~/common/helpers/request.helper';
import { TimeSheetRecordService } from '~/x-modules/admin/services';

const { ACCEPT_PUBLIC_IP = '' } = configEnv();

@Injectable()
export class PublicMiddleware implements NestMiddleware {
  constructor(private timeSheetRecordService: TimeSheetRecordService) {}

  async use(req: Request, res: Response, next: NextFunction) {
    try {
      const checkObject = (obj: any): boolean => {
        if (!obj || typeof obj !== 'object') return false;
        for (const value of Object.values(obj)) {
          if (typeof value === 'string' && SystemValue.FORBIDDEN_PATTERN.test(value)) {
            return true;
          } else if (typeof value === 'object') {
            if (checkObject(value)) return true;
          }
        }
        return false;
      };

      if (checkObject(req.query) || checkObject(req.body)) {
        throw new BadRequestException('SQL Injection detected!');
      }

      const request = requestHelper(req);

      let IPS = [];

      const PUBLIC_IPS = ACCEPT_PUBLIC_IP ? ACCEPT_PUBLIC_IP.split('|').map((ip) => ip.trim()) : [];

      if (PUBLIC_IPS) {
        IPS = [...IPS, ...PUBLIC_IPS];
      }

      const STATION_IPS = await this.timeSheetRecordService.getListStation({}, request.clientIP);

      if (STATION_IPS) {
        IPS = [
          ...IPS,
          ...STATION_IPS.filter((station) => station.ipcIP).map((station) => station.ipcIP),
        ];
      }

      if (IPS.includes(request.clientIP as string)) {
        return next();
      } else {
        throw new UnauthorizedException(
          JSON.stringify({
            IPS,
            PUBLIC_IPS,
            STATION_IPS,
            request,
          }),
        );
      }
    } catch (error) {
      next(error);
    }
  }
}
