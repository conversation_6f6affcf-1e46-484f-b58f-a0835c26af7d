import { Body, Delete, Inject, Param, Query, UseGuards } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { DefController, DefGet, DefPost, DefPut } from '~/@core/decorator';
import { PermissionAdminService } from '~/x-modules/publics/services/permission-admin/permission-admin.service';
import { EventHubService } from '~/x-modules/@global/services/event-hub.service';
@ApiTags('Permission-admin')
@DefController('permission-admin')
export class PermissionAdminController {
  constructor(
    @Inject(PermissionAdminService) private readonly PermissionAdminService: PermissionAdminService,
    private readonly eventHubService: EventHubService,
  ) {}

  // @DefPost('create-user-admin')
  // async createUser(@Body('email') email: string) {
  //   return this.PermissionAdminService.createUserWithFullPermissions(email);
  // }

  // @DefPost('test')
  // async test() {
  //   try {
  //     // Bạn có thể gửi một event đơn lẻ
  //     await this.eventHubService.sendEvents([
  //       {
  //         message: 'Hello from NestJS!',
  //         data: {
  //           recipeId: 'debbda40-fd-4040-adf3-aaa989846bec',
  //         },
  //         timestamp: new Date(),
  //       },
  //     ]);
  //     return { message: 'Event accepted for publishing.' };
  //   } catch (error) {
  //     // Trả về lỗi nếu không gửi được
  //     // Có thể sử dụng HttpException của NestJS để trả về lỗi cụ thể hơn
  //     return { message: 'Failed to publish event.', error: error.message };
  //   }
  // }
}
