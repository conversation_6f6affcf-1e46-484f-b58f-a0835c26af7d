import { EntityRepository } from 'typeorm';
import { PrimaryRepo } from '~/repositories/primary.repo';
import { WeighingTareEntity } from '~/entities/primary/weighing-tare.entity';
import { WeighingTareDetailEntity } from '~/entities/primary/weighing-tare-detail.entity';

@EntityRepository(WeighingTareEntity)
export class WeighingTareRepo extends PrimaryRepo<WeighingTareEntity> {}

@EntityRepository(WeighingTareDetailEntity)
export class WeighingTareDetailRepo extends PrimaryRepo<WeighingTareDetailEntity> {}
