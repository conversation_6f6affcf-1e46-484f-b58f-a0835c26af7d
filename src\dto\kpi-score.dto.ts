import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsNotEmpty,
  IsN<PERSON>ber,
  IsOptional,
  IsString,
  IsDate,
  IsEnum,
  IsUUID,
} from 'class-validator';
import dayjs from 'dayjs';
import { PageRequest } from '~/@systems/utils';

export enum KpiType {
  MANUAL = 'M',
  AUTO = 'A',
}

export enum ShiftType {
  MORNING = 'MORNING',
  AFTERNOON = 'AFTERNOON',
  NIGHT = 'NIGHT',
}

export class KpiScoreFilterDto extends PageRequest {
  @ApiProperty({ required: true })
  @IsNotEmpty()
  @IsString()
  siteId: string;

  @ApiProperty({ required: true })
  @IsNotEmpty()
  @IsString()
  factoryId: string;

  @ApiProperty({ required: true })
  @IsNotEmpty()
  @IsString()
  productionAreaId: string;

  @ApiProperty({ required: true })
  @IsNotEmpty()
  @IsString()
  kpiSetId: string;

  @ApiProperty({ required: true })
  @IsNotEmpty()
  startDate: Date;

  @ApiProperty({ required: true })
  @IsNotEmpty()
  endDate: Date;

  @ApiProperty({ required: false, enum: KpiType })
  @IsOptional()
  @IsEnum(KpiType)
  kpiType?: KpiType;

  @ApiProperty({ required: false, enum: ShiftType })
  @IsOptional()
  @IsEnum(ShiftType)
  shift?: ShiftType;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  searchText?: string;
}

export class KpiScoreItemDto {
  @ApiProperty()
  @IsString()
  kpiId: string;

  @ApiProperty()
  @IsString()
  kpiCode: string;

  @ApiProperty()
  @IsString()
  kpiName: string;

  @ApiProperty()
  @IsString()
  kpiType: KpiType;

  @ApiProperty()
  @IsString()
  unit: string;

  @ApiProperty()
  @IsNumber()
  target: number;

  @ApiProperty()
  @IsNumber()
  weight: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  actual?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  score?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsNumber()
  weightedScore?: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  note?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsEnum(ShiftType)
  shift?: ShiftType;
}

export class SaveKpiScoreDto {
  @ApiProperty()
  @IsString()
  kpiId: string;

  @ApiProperty()
  @IsString()
  productionAreaId: string;

  @ApiProperty()
  @IsNumber()
  actual: number;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsString()
  note?: string;

  @ApiProperty({ required: false })
  @IsOptional()
  @IsEnum(ShiftType)
  shift?: ShiftType;
}

export class KPIScoreDto {
  @ApiProperty()
  id: string;

  @ApiProperty()
  siteId: string;

  @ApiProperty()
  siteCode: string;

  @ApiProperty()
  siteName: string;

  @ApiProperty()
  factoryId: string;

  @ApiProperty()
  factoryCode: string;

  @ApiProperty()
  factoryName: string;

  @ApiProperty()
  productionAreaId: string;

  @ApiProperty()
  productionAreaCode: string;

  @ApiProperty()
  productionAreaName: string;

  @ApiProperty()
  kpiSetId: string;

  @ApiProperty()
  kpiSetCode: string;

  @ApiProperty()
  kpiSetName: string;

  @ApiProperty()
  kpiSetGroupId: string;

  @ApiProperty()
  kpiSetGroupCode: string;

  @ApiProperty()
  kpiSetGroupName: string;

  @ApiProperty()
  shiftId: string;

  @ApiProperty()
  shiftCode: string;

  @ApiProperty()
  shiftName: string;

  @ApiProperty()
  scoreDate: string;

  @ApiProperty()
  actualScore: number;

  @ApiProperty()
  createdBy: string;

  @ApiProperty()
  createdAt: string;

  @ApiProperty()
  updatedBy: string;

  @ApiProperty()
  updatedAt: string;
}

export class KPIScoreReq {
  @ApiProperty()
  @IsOptional()
  @IsUUID()
  siteId?: string;

  @ApiProperty()
  @IsOptional()
  @IsUUID()
  factoryId?: string;

  @ApiProperty()
  @IsUUID()
  productionAreaId: string;

  @ApiProperty()
  @IsUUID()
  kpiSetId: string;

  @ApiProperty()
  @IsString()
  startDate: string;

  @ApiProperty()
  @IsString()
  endDate: string;

  @ApiProperty()
  @IsOptional()
  @IsString()
  shift?: string;
}

export class KpiScoreReq {
  @ApiProperty()
  @IsOptional()
  // @IsUUID()
  id?: string;

  siteId?: string;
  factoryId?: string;
  productionAreaId?: string;
  kpiSetHeaderId?: string;
  kpiSetGroupId?: string;
  kpiSetDetailId?: string;
  kpiId?: string;
  shiftId?: string;
  scoreDate: string;
  actualScore?: number;
}

export class DataOptionsDto {
  @ApiProperty({ type: [Object] })
  productionAreas: any[];

  @ApiProperty({ type: [Object] })
  factories: any[];

  @ApiProperty({ type: [Object] })
  sites: any[];
}

export class KpiSetOptionsReq {
  @ApiProperty()
  @IsString()
  organizationUnitId: string;
}
