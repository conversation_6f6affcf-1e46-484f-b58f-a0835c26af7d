import { ApiProperty } from '@nestjs/swagger';
import { IsArray, IsBoolean, IsNotEmpty, IsNumber, IsString } from 'class-validator';
import { PageRequest } from '~/@systems/utils';

export class SetPointPaginationDTO extends PageRequest {
  where: any;
}

export class SetPointCreateDTO {
  @ApiProperty({ description: 'Id của recipe' })
  @IsString()
  @IsNotEmpty()
  recipeId: string;
  @ApiProperty({ description: 'Type cho biết tạo thông qua recipe , process hay product' })
  @IsString()
  @IsNotEmpty()
  type: string;
  @ApiProperty({ description: 'Danh sách Set Point' })
  @IsArray()
  lstSetPoint: SubSetPointCreateDTO[];
}
export class SetPointCreateByProcessDTO {
  @ApiProperty({ description: 'Id của process' })
  @IsString()
  @IsNotEmpty()
  processId: string;
  @ApiProperty({ description: 'Type cho biết tạo thông qua recipe , process hay product' })
  @IsString()
  @IsNotEmpty()
  type: string;
  @ApiProperty({ description: 'Danh sách Set Point' })
  @IsArray()
  lstSetPoint: SubSetPointCreateDTO[];
}

export class SetPointCreateByProductDTO {
  @ApiProperty({ description: 'Id của product' })
  @IsString()
  @IsNotEmpty()
  productId: string;
  @ApiProperty({ description: 'Type cho biết tạo thông qua recipe , process hay product' })
  @IsString()
  @IsNotEmpty()
  type: string;
  @ApiProperty({ description: 'Danh sách Set Point' })
  @IsArray()
  lstSetPoint: SubSetPointCreateDTO[];
}

export class SubSetPointCreateDTO {
  @ApiProperty({ description: 'Id của máy' })
  @IsString()
  @IsNotEmpty()
  machineId: string;

  @ApiProperty({ description: 'Id của process' })
  @IsString()
  @IsNotEmpty()
  processId: string;

  @ApiProperty({ description: 'Id của machine parameter' })
  @IsString()
  @IsNotEmpty()
  parameterId: string;

  @ApiProperty({ description: 'Target value' })
  @IsNumber()
  @IsNotEmpty({ message: 'Target value should not be empty' })
  targetValue: number;

  @ApiProperty({ description: 'Min' })
  @IsNumber()
  @IsNotEmpty({ message: 'Min value should not be empty' })
  min: number;

  @ApiProperty({ description: 'Max' })
  @IsNumber()
  @IsNotEmpty({ message: 'Max value should not be empty' })
  max: number;
}

export class DuplicateDTO {
  @ApiProperty({ description: 'Id của recipe' })
  @IsString()
  @IsNotEmpty()
  recipeId: string;

  @ApiProperty({ description: 'Danh sách ID set point' })
  @IsArray()
  @IsNotEmpty()
  setPointIds: string[];
}
export class DuplicateByProcessDTO {
  @ApiProperty({ description: 'Id của process' })
  @IsString()
  @IsNotEmpty()
  processId: string;

  @ApiProperty({ description: 'Danh sách ID set point' })
  @IsArray()
  @IsNotEmpty()
  setPointIds: string[];
}
export class DuplicateByProductDTO {
  @ApiProperty({ description: 'Id của product' })
  @IsString()
  @IsNotEmpty()
  productId: string;

  @ApiProperty({ description: 'Danh sách ID set point' })
  @IsArray()
  @IsNotEmpty()
  setPointIds: string[];
}
export class UpdateSetPointDTO {
  @ApiProperty({ description: 'Id của set point' })
  @IsString()
  @IsNotEmpty()
  id: string;

  @ApiProperty({ description: 'Target value' })
  @IsNumber()
  @IsNotEmpty()
  targetValue: number;

  @ApiProperty({ description: 'Min' })
  @IsNumber()
  @IsNotEmpty()
  min: number;

  @ApiProperty({ description: 'Max' })
  @IsNumber()
  @IsNotEmpty()
  max: number;

  @ApiProperty({ description: 'IsActive / Active' })
  @IsBoolean()
  @IsNotEmpty()
  isActive: number;
}
