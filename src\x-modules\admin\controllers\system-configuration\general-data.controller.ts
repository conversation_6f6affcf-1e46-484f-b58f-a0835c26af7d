import { Body, Param, Query, UseGuards } from '@nestjs/common';
import { ApiOperation, ApiTags } from '@nestjs/swagger';
import { DefController, DefD<PERSON>te, DefGet, DefPatch, DefPost, DefPut } from '~/@core/decorator';
import {
  GeneralMasterDataCreateDto,
  GeneralMasterDataUpdateDto,
  ListGeneralReq,
} from '~/dto/general-data.dto';
import { GeneralDataService } from '../../services';
import { Roles } from '~/@core/decorator/roles.decorator';
import { RoleGuard } from '~/@systems/guard/role.guard';

@ApiTags('GeneralData')
@DefController('general-data')
export class GeneralDataController {
  constructor(private readonly generalDataService: GeneralDataService) {}

  @ApiOperation({
    summary: 'Find general data by code',
    description: 'Find general data and its details by code',
  })
  @DefGet('by-code/:code')
  @Roles('/report/oee-dashboard', 'View')
  @Roles('/system-configuration/general-data', 'View')
  @UseGuards(RoleGuard)
  async findByCode(@Param('code') code: string) {
    return this.generalDataService.findByCode(code);
  }

  @DefGet('list')
  @Roles('/system-configuration/general-data', 'View')
  @UseGuards(RoleGuard)
  async getGeneralDataList(@Query() params: ListGeneralReq) {
    return this.generalDataService.list(params);
  }

  @ApiOperation({
    summary: 'Get general data detail list',
    description: 'Get details of a general data by ID with filtering options',
  })
  @DefGet('list-details/:id')
  @Roles('/system-configuration/general-data', 'View')
  @UseGuards(RoleGuard)
  async getGeneralDataDetailList(
    @Param('id') id: string,
    @Query('code') code?: string,
    @Query('name') name?: string,
    @Query('description') description?: string,
    @Query('note') note?: string,
    @Query('isActive') isActive?: string,
  ) {
    const filter: any = {};
    if (code) filter.code = code;
    if (name) filter.name = name;
    if (description) filter.description = description;
    if (note) filter.note = note;
    if (isActive !== undefined) {
      filter.isActive = isActive === 'true';
    }

    return this.generalDataService.listDetails(id, filter);
  }

  @DefPost('create')
  @Roles('/system-configuration/general-data', 'Create')
  @UseGuards(RoleGuard)
  async create(@Body() data: GeneralMasterDataCreateDto) {
    return this.generalDataService.create(data);
  }

  @DefPost('create-details/:id')
  @Roles('/system-configuration/general-data', 'View')
  @UseGuards(RoleGuard)
  async createDetails(@Param('id') id: string, @Body() data: GeneralMasterDataCreateDto) {
    return this.generalDataService.createDetails(id, data);
  }

  @ApiOperation({ summary: 'Cập nhật', description: 'Cập nhật thông tin của general data hiện có' })
  @DefPut('update')
  @Roles('/system-configuration/general-data', 'Edit')
  @UseGuards(RoleGuard)
  async update(@Body() body: GeneralMasterDataUpdateDto) {
    return this.generalDataService.update(body);
  }

  @ApiOperation({
    summary: 'Cập nhật chi tiết',
    description: 'Cập nhật thông tin chi tiết của general data',
  })
  @DefPut(':id/update-details')
  @Roles('/system-configuration/general-data', 'View')
  @UseGuards(RoleGuard)
  async updateDetails(@Param('id') id: string, @Body() data: GeneralMasterDataCreateDto) {
    return this.generalDataService.updateDetails(id, data);
  }

  @ApiOperation({ summary: 'Create general data', description: 'Create a new general data' })
  @DefPost()
  @Roles('/system-configuration/general-data', 'View')
  @UseGuards(RoleGuard)
  async createGeneralData(@Body() data: GeneralMasterDataCreateDto) {
    return this.generalDataService.create(data);
  }

  @ApiOperation({
    summary: 'Get all general data',
    description: 'Get all general data with pagination',
  })
  @DefGet()
  @Roles('/system-configuration/general-data', 'View')
  @UseGuards(RoleGuard)
  async findAll(@Query() params: ListGeneralReq) {
    return this.generalDataService.list(params);
  }

  @ApiOperation({
    summary: 'Get all general data without pagination',
    description: 'Get all general data without pagination',
  })
  @DefGet('all')
  @Roles('/system-configuration/general-data', 'View')
  @UseGuards(RoleGuard)
  async findAllWithoutPagination(@Query() params: ListGeneralReq) {
    return this.generalDataService.findAll(params);
  }

  @ApiOperation({ summary: 'Get general data by ID', description: 'Get general data by ID' })
  @DefGet(':id')
  @Roles('/system-configuration/general-data', 'View')
  @UseGuards(RoleGuard)
  async findOne(@Param('id') id: string) {
    return this.generalDataService.findOne(id);
  }

  @ApiOperation({ summary: 'Update general data', description: 'Update an existing general data' })
  @DefPatch(':id')
  @Roles('/system-configuration/general-data', 'View')
  @UseGuards(RoleGuard)
  async updateById(@Param('id') id: string, @Body() data: any) {
    const updateData: GeneralMasterDataUpdateDto = {
      ...data,
      id: id,
    };
    return this.generalDataService.update(updateData);
  }

  @ApiOperation({
    summary: 'Update general data (PUT)',
    description: 'Update an existing general data using PUT method',
  })
  @DefPut(':id')
  @Roles('/system-configuration/general-data', 'View')
  @UseGuards(RoleGuard)
  async updateByIdPut(@Param('id') id: string, @Body() data: any) {
    const updateData: GeneralMasterDataUpdateDto = {
      ...data,
      id: id,
    };
    return this.generalDataService.update(updateData);
  }

  @ApiOperation({ summary: 'Delete general data', description: 'Delete a general data by ID' })
  @DefDelete(':id')
  @Roles('/system-configuration/general-data', 'View')
  @UseGuards(RoleGuard)
  async remove(@Param('id') id: string) {
    return this.generalDataService.remove(id);
  }

  @ApiOperation({
    summary: 'Get general data details',
    description:
      'Get details of a general data by ID with filtering options for code, name, description, note, and active status',
  })
  @DefGet(':id/details')
  @Roles('/system-configuration/general-data', 'View')
  @UseGuards(RoleGuard)
  async getDetails(
    @Param('id') id: string,
    @Query('code') code?: string,
    @Query('name') name?: string,
    @Query('description') description?: string,
    @Query('note') note?: string,
    @Query('isActive') isActive?: string,
  ) {
    const filter: any = {};
    if (code) filter.code = code;
    if (name) filter.name = name;
    if (description) filter.description = description;
    if (note) filter.note = note;
    if (isActive !== undefined) {
      filter.isActive = isActive === 'true';
    }

    return this.generalDataService.listDetails(id, filter);
  }

  @ApiOperation({
    summary: 'Create general data detail',
    description: 'Create a new detail for a general data',
  })
  @DefPost(':id/details')
  @Roles('/system-configuration/general-data', 'View')
  @UseGuards(RoleGuard)
  async createDetail(@Param('id') id: string, @Body() data: GeneralMasterDataCreateDto) {
    return this.generalDataService.createDetails(id, data);
  }

  @ApiOperation({
    summary: 'Get general data detail by ID',
    description: 'Get a specific detail of a general data',
  })
  @DefGet(':generalId/details/:detailId')
  @Roles('/system-configuration/general-data', 'View')
  @UseGuards(RoleGuard)
  async findDetailById(@Param('generalId') generalId: string, @Param('detailId') detailId: string) {
    return this.generalDataService.findDetailById(detailId);
  }

  @ApiOperation({
    summary: 'Update general data detail',
    description: 'Update a specific detail of a general data',
  })
  @DefPatch(':generalId/details/:detailId')
  @Roles('/system-configuration/general-data', 'View')
  @UseGuards(RoleGuard)
  async updateDetailById(
    @Param('generalId') generalId: string,
    @Param('detailId') detailId: string,
    @Body() data: GeneralMasterDataCreateDto,
  ) {
    const updateData = data as unknown as GeneralMasterDataUpdateDto;
    updateData.id = detailId;
    return this.generalDataService.updateDetails(detailId, data);
  }

  @ApiOperation({
    summary: 'Update general data detail (PUT)',
    description: 'Update a specific detail of a general data using PUT method',
  })
  @DefPut(':generalId/details/:detailId')
  @Roles('/system-configuration/general-data', 'View')
  @UseGuards(RoleGuard)
  async updateDetailByIdPut(
    @Param('generalId') generalId: string,
    @Param('detailId') detailId: string,
    @Body() data: GeneralMasterDataCreateDto,
  ) {
    const updateData = data as unknown as GeneralMasterDataUpdateDto;
    updateData.id = detailId;
    return this.generalDataService.updateDetails(detailId, data);
  }

  @ApiOperation({
    summary: 'Delete general data detail',
    description: 'Delete a specific detail of a general data',
  })
  @DefDelete(':generalId/details/:detailId')
  @Roles('/system-configuration/general-data', 'View')
  @UseGuards(RoleGuard)
  async removeDetail(@Param('generalId') generalId: string, @Param('detailId') detailId: string) {
    return this.generalDataService.removeDetail(detailId);
  }
}
