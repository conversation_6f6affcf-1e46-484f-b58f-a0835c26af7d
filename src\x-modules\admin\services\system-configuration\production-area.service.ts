import { Injectable } from '@nestjs/common';
import { In, Not } from 'typeorm';
import { v4 } from 'uuid';
import { BindRepo, DefTransaction } from '~/@core/decorator';
import { PageRequest } from '~/@systems/utils';
import { ORGANIZATION_LEVEL } from '~/common/constants/organizationLevel.constant';
import { ProcessAreaActiveStatus } from '~/dto/production-area.dto';
import {
  OrganizationUnitEntity,
  ProductionAreaDetailEntity,
  ProductionAreaEntity,
} from '~/entities/primary';
import { IGetProductionAreaRequest } from '~/functions/production-area-lambda';
import {
  GeneralDataDetailRepo,
  GeneralDataRepo,
  ProductionAreaDetailRepo,
  ProductionAreaRepo,
} from '~/repositories/primary';
import { OrganizationUnitRepo } from '~/repositories/primary/organization-unit.repo';

export interface IProcessAreaDetailItem {
  id: string;
  organizationUnitId: string;
  processAreaId: string;
  processArea: string;
  line: string;
  isActive: boolean;
  createdBy: string;
  createdDate: string;
  status?: 'added' | 'modified' | 'deleted';
}

export interface IProductionAreaSubmitData {
  code: string;
  name: string;
  factoryId: string;
  locationX: number;
  locationY: number;
  uomGeneralCode?: string;
  uomGeneralDetailCode?: string;
  tolerance?: number;
  isActive: boolean;
  note?: string;
  description?: string;
  manHourResourceCode?: string;
  processAreaDetails: {
    added: IProcessAreaDetailItem[];
    modified: IProcessAreaDetailItem[];
    deleted: IProcessAreaDetailItem[];
  };
}

export interface IProductionAreaFilter {
  code?: string;
  factoryId?: string;
  name?: string;
  activeStatus?: ProcessAreaActiveStatus;
  createdDate?: string;
  updatedDate?: string;
  page?: number;
  limit?: number;
}

@Injectable()
export class ProductionAreaService {
  constructor() {}

  @BindRepo(ProductionAreaRepo)
  private readonly repo: ProductionAreaRepo;

  @BindRepo(ProductionAreaDetailRepo)
  private readonly detailRepo: ProductionAreaDetailRepo;

  @BindRepo(OrganizationUnitRepo)
  private readonly organizationUnitRepo: OrganizationUnitRepo;

  @BindRepo(GeneralDataDetailRepo)
  private readonly generalDataDetailRepo: GeneralDataDetailRepo;

  @BindRepo(GeneralDataRepo)
  private readonly generalDataRepo: GeneralDataRepo;

  async list() {
    const result = await this.repo.find({
      order: { isActive: 'DESC', createdDate: 'DESC' },
      relations: ['organization'],
    });
    return result;
  }

  async pagination(params: PageRequest) {
    const result = await this.repo.findPagination(
      { order: { isActive: 'DESC', createdDate: 'DESC' } },
      params,
    );
    return result;
  }

  async getProductionArea(params: IGetProductionAreaRequest) {
    const productionArea = await this.repo.findOne({
      where: {
        id: params.id,
      },
      relations: ['details', 'details.organization', 'organization', 'uom'],
    });
    if (!productionArea) throw new Error('Không tìm thấy khu vực sản xuất cần tìm!');
    return productionArea;
  }

  async getFactoryList() {
    const factoryId = await this.generalDataDetailRepo.findOne({
      where: { code: ORGANIZATION_LEVEL.FACTORY, isActive: true },
    });

    if (!factoryId) {
      return [];
    }

    const factoryList = await this.organizationUnitRepo.find({
      where: { levelId: factoryId.id, isActive: true },
      relations: ['parent'],
    });

    return factoryList;
  }

  async getAllGeneralDataDetail() {
    // UOM
    const generalUOM = await this.generalDataRepo.findOne({
      where: { code: 'UOM', isActive: true },
    });

    let result = [];
    if (generalUOM) {
      result = await this.generalDataDetailRepo.find({
        where: { generalId: generalUOM.id, isActive: true },
      });
    }

    return result;
  }

  async getProcessAreaByFactoryId(
    factoryId: string,
    processAreaCode?: string,
    processAreaName?: string,
    lineId?: string,
    activeStatus?: ProcessAreaActiveStatus,
    page: number = 1,
    limit: number = 10,
  ) {
    // Lấy level ID của Process Area từ GeneralDataDetail
    const processAreaLevel = await this.generalDataDetailRepo.findOne({
      where: { code: ORGANIZATION_LEVEL.PROCESS, isActive: true },
    });

    if (!processAreaLevel) {
      return {
        items: [],
        total: 0,
        page,
        limit,
        totalPages: 0,
      };
    }

    // Tạo query builder
    const queryBuilder = this.organizationUnitRepo
      .createQueryBuilder('processArea')
      .leftJoinAndSelect('processArea.parent', 'line')
      .leftJoinAndSelect('line.parent', 'factory')
      .where('processArea.levelId = :levelId', { levelId: processAreaLevel.id })
      .andWhere('factory.id = :factoryId', { factoryId });

    // Tìm kiếm gần đúng processAreaCode
    if (processAreaCode) {
      queryBuilder.andWhere('UPPER(processArea.code) LIKE UPPER(:processAreaCode)', {
        processAreaCode: `%${processAreaCode}%`,
      });
    }

    // Tìm kiếm gần đúng processAreaName
    if (processAreaName) {
      queryBuilder.andWhere('LOWER(processArea.name) LIKE LOWER(:processAreaName)', {
        processAreaName: `%${processAreaName}%`,
      });
    }

    // Filter theo Line nếu có
    if (lineId) {
      queryBuilder.andWhere('line.id = :lineId', { lineId });
    }

    // Filter theo activeStatus
    if (activeStatus && activeStatus !== ProcessAreaActiveStatus.FULL) {
      const isActive = activeStatus === ProcessAreaActiveStatus.ACTIVE;
      queryBuilder.andWhere('processArea.isActive = :isActive', { isActive });
    }

    // Sắp xếp theo tên
    queryBuilder.orderBy('processArea.name', 'ASC');

    // Tính toán skip và take cho phân trang
    const skip = (page - 1) * limit;

    // Thực hiện đếm tổng số bản ghi
    const total = await queryBuilder.getCount();

    // Thêm phân trang vào query
    queryBuilder.skip(skip).take(limit);

    const items = await queryBuilder.getMany();

    return {
      items,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  @DefTransaction()
  async createProductionArea(data: IProductionAreaSubmitData) {
    // 1. Kiểm tra code có tồn tại
    const existingCode = await this.repo.findOne({
      where: { code: data.code },
    });
    if (existingCode) {
      throw new Error('Mã khu vực sản xuất đã tồn tại trong hệ thống!');
    }

    // 2. Kiểm tra factoryId có tồn tại
    // Lấy level Factory từ general data
    const factoryLevel = await this.generalDataDetailRepo.findOne({
      where: {
        code: ORGANIZATION_LEVEL.FACTORY,
        isActive: true,
      },
    });

    if (!factoryLevel) {
      throw new Error('Không tìm thấy cấu hình Factory level!');
    }

    const existingFactory = await this.organizationUnitRepo.findOne({
      where: {
        id: data.factoryId,
        isActive: true,
        levelId: factoryLevel.id,
      },
    });

    if (!existingFactory) {
      throw new Error('Factory không tồn tại hoặc không hoạt động!');
    }

    // 3. Kiểm tra uomId có tồn tại
    let existingUom = null;
    if (data.uomGeneralDetailCode) {
      existingUom = await this.generalDataDetailRepo.findOne({
        where: {
          code: data.uomGeneralDetailCode,
          isActive: true,
        },
      });
      if (!existingUom) {
        throw new Error('Đơn vị đo không tồn tại hoặc không hoạt động!');
      }
    }

    // 4. Kiểm tra processAreaId trong array added có tồn tại
    if (data.processAreaDetails.added.length > 0) {
      const processAreaIds = data.processAreaDetails.added.map(item => item.processAreaId);
      const existingProcessAreas = await this.organizationUnitRepo.find({
        where: {
          id: In(processAreaIds),
        },
      });

      if (existingProcessAreas.length !== processAreaIds.length) {
        throw new Error('Một số Process Area không tồn tại hoặc không hoạt động!');
      }
    }

    // Lưu Production Area
    const productionArea = new ProductionAreaEntity();
    Object.assign(productionArea, {
      id: v4(),
      code: data.code,
      name: data.name,
      organizationUnitId: data.factoryId,
      locationX: data.locationX,
      locationY: data.locationY,
      uomGeneralCode: data.uomGeneralCode,
      uomGeneralDetailCode: data.uomGeneralDetailCode,
      uomGeneralDetailName: existingUom ? existingUom.name : null,
      tolerance: data.tolerance,
      manHourResourceCode: data.manHourResourceCode,
      isActive: data.isActive,
      note: data.note,
      description: data.description,
    });

    const savedProductionArea = await this.repo.save(productionArea);

    // Lưu Production Area Details từ array added
    if (data.processAreaDetails.added.length > 0) {
      const productionAreaDetails = data.processAreaDetails.added.map(item => {
        const detail = new ProductionAreaDetailEntity();
        return Object.assign(detail, {
          isActive: true,
          organization: { id: item.organizationUnitId } as OrganizationUnitEntity,
          productionArea: { id: productionArea.id } as ProductionAreaEntity,
        });
      });

      await this.detailRepo.saves(productionAreaDetails);
    }

    return savedProductionArea;
  }

  async getProductionAreaList(filter?: IProductionAreaFilter) {
    const queryBuilder = this.repo
      .createQueryBuilder('productionArea')
      .leftJoinAndSelect('productionArea.organization', 'organization')
      .leftJoinAndSelect('productionArea.uom', 'uom');

    if (filter) {
      if (filter.code) {
        queryBuilder.andWhere('productionArea.code LIKE :code', { code: `%${filter.code}%` });
      }
      if (filter.name) {
        queryBuilder.andWhere('productionArea.name LIKE :name', { name: `%${filter.name}%` });
      }
      if (filter.factoryId) {
        queryBuilder.andWhere('productionArea.organizationUnitId = :factoryId', {
          factoryId: filter.factoryId,
        });
      }
      if (filter.activeStatus && filter.activeStatus !== ProcessAreaActiveStatus.FULL) {
        const isActive = filter.activeStatus === ProcessAreaActiveStatus.ACTIVE;
        queryBuilder.andWhere('productionArea.isActive = :isActive', { isActive });
      }
      if (filter.createdDate) {
        queryBuilder.andWhere('DATE(productionArea.createdDate) = DATE(:createdDate)', {
          createdDate: filter.createdDate,
        });
      }
      if (filter.updatedDate) {
        queryBuilder.andWhere('DATE(productionArea.updatedDate) = DATE(:updatedDate)', {
          updatedDate: filter.updatedDate,
        });
      }
    }

    queryBuilder.orderBy({
      'productionArea.isActive': 'DESC',
      'productionArea.createdDate': 'DESC',
    });

    // Tính toán skip và take cho phân trang
    const page = filter?.page || 1;
    const limit = filter?.limit || 10;
    const skip = (page - 1) * limit;

    // Thực hiện đếm tổng số bản ghi
    const total = await queryBuilder.getCount();

    // Thêm phân trang vào query
    queryBuilder.skip(skip).take(limit);

    const items = await queryBuilder.getMany();

    return {
      items,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async getProductionAreaInfo(id: string) {
    const productionArea = await this.repo.findOne({
      where: { id },
      relations: ['organization', 'uom'],
    });

    if (!productionArea) {
      throw new Error('Không tìm thấy khu vực sản xuất!');
    }

    return productionArea;
  }

  async getProductionAreaDetails(id: string, page: number = 1, limit: number = 10) {
    // Kiểm tra production area có tồn tại không
    const productionArea = await this.repo.findOne({
      where: { id },
    });

    if (!productionArea) {
      throw new Error('Không tìm thấy khu vực sản xuất!');
    }

    // Tạo query builder
    const queryBuilder = this.detailRepo
      .createQueryBuilder('detail')
      .leftJoinAndSelect('detail.productionArea', 'productionArea')
      .leftJoinAndSelect('detail.organization', 'organization')
      .leftJoinAndSelect('organization.parent', 'line')
      .where('detail.productionAreaId = :id', { id });

    // Sắp xếp theo tên organization
    queryBuilder.orderBy('organization.name', 'ASC');

    // Tính toán skip và take cho phân trang
    const skip = (page - 1) * limit;

    // Thực hiện đếm tổng số bản ghi
    const total = await queryBuilder.getCount();

    // Thêm phân trang vào query
    queryBuilder.skip(skip).take(limit);

    // Lấy kết quả
    const details = await queryBuilder.getMany();

    // Format kết quả
    const items = details.map(item => ({
      ...item,
      processAreaName: item.organization.name,
      line: item.organization.parent.name,
    }));

    return {
      items,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  @DefTransaction()
  async updateProductionArea(id: string, data: IProductionAreaSubmitData) {
    // 1. Kiểm tra production area có tồn tại
    const existingProductionArea = await this.repo.findOne({
      where: { id },
      relations: ['details'],
    });

    if (!existingProductionArea) {
      throw new Error('Không tìm thấy khu vực sản xuất!');
    }

    // 2. Kiểm tra code có trùng với production area khác
    const duplicateCode = await this.repo.findOne({
      where: {
        code: data.code,
        id: Not(id),
      },
    });

    if (duplicateCode) {
      throw new Error('Mã khu vực sản xuất đã tồn tại trong hệ thống!');
    }

    // 3. Kiểm tra factoryId có tồn tại
    const factoryLevel = await this.generalDataDetailRepo.findOne({
      where: {
        code: ORGANIZATION_LEVEL.FACTORY,
        isActive: true,
      },
    });

    if (!factoryLevel) {
      throw new Error('Không tìm thấy cấu hình Factory level!');
    }

    const existingFactory = await this.organizationUnitRepo.findOne({
      where: {
        id: data.factoryId,
        isActive: true,
        levelId: factoryLevel.id,
      },
    });

    if (!existingFactory) {
      throw new Error('Factory không tồn tại hoặc không hoạt động!');
    }

    // 4. Kiểm tra uomGeneralDetailCode có tồn tại
    let existingUom = null;
    if (data.uomGeneralDetailCode) {
      existingUom = await this.generalDataDetailRepo.findOne({
        where: {
          code: data.uomGeneralDetailCode,
          isActive: true,
        },
      });
      if (!existingUom) {
        throw new Error('Đơn vị đo không tồn tại hoặc không hoạt động!');
      }
    }

    // 5. Kiểm tra processAreaId trong array added có tồn tại
    if (data.processAreaDetails.added.length > 0) {
      const processAreaIds = data.processAreaDetails.added.map(item => item.processAreaId);
      const existingProcessAreas = await this.organizationUnitRepo.find({
        where: {
          id: In(processAreaIds),
        },
      });

      if (existingProcessAreas.length !== processAreaIds.length) {
        throw new Error('Một số Process Area không tồn tại hoặc không hoạt động!');
      }
    }

    // 6. Kiểm tra ID của production area details cần sửa có tồn tại
    if (data.processAreaDetails.modified.length > 0) {
      const modifiedIds = data.processAreaDetails.modified.map(item => item.id);
      const existingModifiedDetails = await this.detailRepo.find({
        where: { id: In(modifiedIds) },
      });

      if (existingModifiedDetails.length !== modifiedIds.length) {
        throw new Error('Một số Production Area Detail cần sửa không tồn tại!');
      }
    }

    // 7. Kiểm tra ID của production area details cần xóa có tồn tại
    if (data.processAreaDetails.deleted.length > 0) {
      const deletedIds = data.processAreaDetails.deleted.map(item => item.id);
      const existingDeletedDetails = await this.detailRepo.find({
        where: { id: In(deletedIds) },
      });

      if (existingDeletedDetails.length !== deletedIds.length) {
        throw new Error('Một số Production Area Detail cần xóa không tồn tại!');
      }
    }

    // 8. Cập nhật thông tin Production Area
    Object.assign(existingProductionArea, {
      code: data.code,
      name: data.name,
      organizationUnitId: data.factoryId,
      locationX: data.locationX,
      locationY: data.locationY,
      uomGeneralCode: data.uomGeneralCode ?? null,
      uomGeneralDetailCode: data.uomGeneralDetailCode ?? null,
      uomGeneralDetailName: existingUom ? existingUom.name : null,
      tolerance: data.tolerance,
      manHourResourceCode: data.manHourResourceCode,
      isActive: data.isActive,
      note: data.note,
      description: data.description,
    } as ProductionAreaEntity);

    await this.repo.save(existingProductionArea);

    // 9. Xử lý Production Area Details đã xóa
    if (data.processAreaDetails.deleted.length > 0) {
      const deletedIds = data.processAreaDetails.deleted.map(item => item.id);
      await this.detailRepo.delete({ id: In(deletedIds) });
    }

    // 10. Xử lý Production Area Details đã sửa
    if (data.processAreaDetails.modified.length > 0) {
      const modifiedDetails = data.processAreaDetails.modified.map(item => {
        return this.detailRepo.update(
          { id: item.id },
          {
            organizationUnitId: item.organizationUnitId,
            isActive: item.isActive,
          },
        );
      });
      await Promise.all(modifiedDetails);
    }

    // 11. Thêm mới Production Area Details
    if (data.processAreaDetails.added.length > 0) {
      const newDetails = data.processAreaDetails.added.map(item => {
        const detail = new ProductionAreaDetailEntity();
        return Object.assign(detail, {
          isActive: true,
          organizationUnitId: item.organizationUnitId,
          productionAreaId: existingProductionArea.id,
        });
      });

      await this.detailRepo.saves(newDetails);
    }

    return existingProductionArea;
  }

  async getLinesByFactoryId(factoryId: string) {
    // Lấy level ID của Line từ GeneralDataDetail
    const lineLevel = await this.generalDataDetailRepo.findOne({
      where: { code: ORGANIZATION_LEVEL.LINE, isActive: true },
    });

    if (!lineLevel) {
      return [];
    }

    // Lấy tất cả Line thuộc Factory
    const lines = await this.organizationUnitRepo.find({
      where: {
        levelId: lineLevel.id,
        isActive: true,
      },
      relations: ['parent', 'parent.parent', 'parent.parent.parent'],
      order: {
        name: 'ASC',
      },
    });

    // Lọc các Line theo Factory
    const filteredLines = lines.filter(line => {
      let current = line;
      let isInFactory = false;

      while (current.parent) {
        if (current.parent.id === factoryId) {
          isInFactory = true;
          break;
        }
        current = current.parent;
      }

      return isInFactory;
    });

    return filteredLines;
  }

  async getAllProductionAreas() {
    const productionAreas = await this.repo.find();

    return {
      items: productionAreas,
      total: productionAreas.length,
    };
  }
}
