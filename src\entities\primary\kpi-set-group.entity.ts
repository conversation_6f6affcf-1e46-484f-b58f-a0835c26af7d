import { <PERSON><PERSON><PERSON>, PrimaryColumn, Column, <PERSON>To<PERSON>ne, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, OneToMany } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { PrimaryBaseEntity } from '../primary-base.entity';
import { KpiSetHeaderEntity } from './kpi-set-header.entity';
import { KpiSetDetailEntity } from './kpi-set-detail.entity';

@Entity('kpi_set_group')
export class KpiSetGroupEntity extends PrimaryBaseEntity {
  /** KPI set id: lấy id từ bảng KPI_SET_HEADER */
  @ApiProperty({ description: 'id KPI set header' })
  @Column({ nullable: true })
  kpiSetHeaderId: string;
  @ManyToOne(() => KpiSetHeaderEntity, org => org.id)
  @JoinColumn({ name: 'kpiSetHeaderId', referencedColumnName: 'id' })
  kpiSetHeader?: KpiSetHeaderEntity;

  /** Id ngành hàng: Chọn từ trường code của bảng Master General Data với điều kiện mã có parent code = PRODUCTION CATEGORY */
  @ApiProperty({ description: 'Mã ngành hàng' })
  @Column({ type: 'varchar', length: 36, nullable: true })
  kpiCategoryId: string;

  /** Mã ngành hàng: Chọn từ trường code của bảng Master General Data với điều kiện mã có parent code = PRODUCTION CATEGORY */
  @ApiProperty({ description: 'Mã ngành hàng' })
  @Column({ type: 'varchar', length: 50, nullable: true })
  kpiCategoryCode: string;

  /** Tên ngành hàng: Chọn từ trường code của bảng Master General Data với điều kiện mã có parent code = PRODUCTION CATEGORY */
  @ApiProperty({ description: 'Mã ngành hàng' })
  @Column({ type: 'varchar', length: 250, nullable: true })
  kpiCategoryName: string;

  /** Id nhóm KPI: Chọn từ trường code của bảng Master General Data với điều kiện mã có parent code = KPI GROUP */
  @ApiProperty({ description: 'Mã nhóm KPI' })
  @Column({ type: 'uuid', nullable: true })
  kpiGroupId: string;

  /** Mã nhóm KPI: Chọn từ trường code của bảng Master General Data với điều kiện mã có parent code = KPI GROUP */
  @ApiProperty({ description: 'Mã nhóm KPI' })
  @Column({ type: 'varchar', length: 50, nullable: true })
  kpiGroupCode: string;

  /** Tên KPI group được valid theo kpi group từ bảng Master General Data */
  @ApiProperty({ description: 'Tên nhóm KPI' })
  @Column({ type: 'varchar', length: 250, nullable: true })
  kpiGroupName: string;

  /** Tỷ trọng chia thưởng cho KPI group */
  @ApiProperty({ description: 'Tỷ trọng chia thưởng' })
  @Column({ type: 'numeric', nullable: true, default: 0 })
  weighTarget: number;

  /** Số tiền thưởng cho group theo tỷ trọng */
  @ApiProperty({ description: 'Số tiền thưởng cho nhóm' })
  @Column({ type: 'numeric', nullable: true, default: 0 })
  budget: number;

  /** KPI set group id, là key duy nhất, hệ thống tự generate theo số tự nhiên đại diện cho 1 nhóm kpi set code, kpi category, kpi group */
  @ApiProperty({ description: 'ID nhóm bộ KPI' })
  @Column({ type: 'int', generated: 'increment' })
  kpiSetGroupId: number;

  /** Danh sách chi tiết */
  @OneToMany(() => KpiSetDetailEntity, pro => pro.kpiSetGroup)
  details: KpiSetDetailEntity[];

  //   // Mối quan hệ với KpiSetHeader
  //   @ManyToOne(() => KpiSetHeader, kpiSetHeader => kpiSetHeader.kpiSetGroups)
  //   @JoinColumn({ name: 'kpiSetId' })
  //   kpiSetHeader: KpiSetHeader;
}
