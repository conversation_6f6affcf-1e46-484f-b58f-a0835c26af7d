import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { Between, EntityManager, ILike, In } from 'typeorm';
import * as ExcelJS from 'exceljs';
import * as path from 'path';
import * as os from 'os';
import * as fs from 'fs';
import { BindRepo } from '~/@core/decorator';
import { PageResponse } from '~/@systems/utils/page.utils';
import { NSGeneralData } from '~/common/enums';
import { dayjs, TZ } from '~/common/helpers/date.helper';
import { adminSessionContext } from '~/x-modules/admin/admin-session.context';
import { S3PrivateService } from '~/x-modules/@global/services';
import { ProductionOrderEntity } from '~/entities/primary';
import {
  GeneralDataDetailRepo,
  GeneralDataRepo,
  OrganizationUnitRepo,
  ProductionBatchRepo,
  ProductionOrderMaterialRepo,
  ProductionOrderMaterialTransactionRepo,
  ProductionOrderRepo,
  ReportHistoryRepo,
  ShiftRepo,
  UserRepo,
} from '~/repositories/primary';
import { CreateReportDto, ListFilterReportDto } from '~/dto/report-history.dto';

const REPORT = { reportCode: '1', reportName: 'Production Order Material' };

@Injectable()
export class ProductionOrderMaterialReportService {
  constructor(
    private readonly entityManager: EntityManager,
    private readonly s3PrivateService: S3PrivateService,
  ) {}

  @BindRepo(ReportHistoryRepo)
  private reportHistoryRepo: ReportHistoryRepo;
  @BindRepo(ProductionOrderRepo)
  private productionOrderRepo: ProductionOrderRepo;
  @BindRepo(ProductionOrderMaterialRepo)
  private productionOrderMaterialRepo: ProductionOrderMaterialRepo;
  @BindRepo(ProductionOrderMaterialTransactionRepo)
  private productionOrderMaterialTransactionRepo: ProductionOrderMaterialTransactionRepo;
  @BindRepo(ProductionBatchRepo)
  private productionBatchRepo: ProductionBatchRepo;
  @BindRepo(ShiftRepo)
  private shiftRepo: ShiftRepo;
  @BindRepo(OrganizationUnitRepo)
  private organizationUnitRepo: OrganizationUnitRepo;
  @BindRepo(GeneralDataRepo)
  private generalDataRepo: GeneralDataRepo;
  @BindRepo(GeneralDataDetailRepo)
  private generalDataDetailRepo: GeneralDataDetailRepo;
  @BindRepo(UserRepo)
  private userRepo: UserRepo;

  async search(req: ListFilterReportDto): Promise<PageResponse> {
    const { createdByUser, email, fromDate, toDate, pageIndex, pageSize } = req;
    // Parse date dạng ISO -> Date object để truy vấn chuẩn
    const start = fromDate ? dayjs(fromDate).toDate() : undefined;
    const end = toDate ? dayjs(toDate).toDate() : undefined;

    const [data, total] = await this.reportHistoryRepo.findAndCount({
      where: {
        reportCode: REPORT.reportCode,
        ...(createdByUser && { createdByUser: ILike(`%${createdByUser}%`) }),
        ...(email && { email: ILike(`%${email}%`) }),
        ...(start && end && { createdDate: Between(start, end) }),
      },
      order: { createdDate: 'DESC' },
      skip: pageIndex && pageSize ? (pageIndex - 1) * pageSize : undefined,
      take: pageIndex && pageSize ? pageSize : undefined,
    });

    const signedUrls = await Promise.all(
      data.map(async (item) => {
        const signedUrl = await this.s3PrivateService.getS3Link(item.linkDownload ?? ''); // 1h
        return signedUrl;
      }),
    );
    data.forEach((item, index) => {
      item.linkDownload = signedUrls[index];
    });

    return { data: data, meta: { totalRecords: total, request: req } };
  }

  async submitReport(req: CreateReportDto) {
    const beginAt = dayjs.utc(Date.now()).tz(TZ).format('YYYYMMDDHHmmss');
    const {
      siteIdLst,
      factoryIdLst,
      lineIdLst,
      orderNo,
      statusIdLst,
      fromDate,
      toDate,
      batchNumber,
    } = req;

    const { userId, userName, userEmail } = adminSessionContext;

    const report = this.reportHistoryRepo.create({
      reportCode: REPORT.reportCode,
      reportName: REPORT.reportName,
      status: NSGeneralData.EReportStatus.RUNNING,
      input: req,
      createdBy: userId,
      createdByUser: userName,
      email: userEmail,
    });
    const reportSaved = await this.reportHistoryRepo.save(report);

    try {
      let result: any[] = [];

      // Parse date dạng ISO -> Date object để truy vấn chuẩn
      const start = fromDate ? dayjs(fromDate).toDate() : undefined;
      const end = toDate ? dayjs(toDate).toDate() : undefined;

      const productionOrderLst = await this.productionOrderRepo.find({
        where: {
          ...(siteIdLst?.length && { siteId: In(siteIdLst) }),
          ...(factoryIdLst?.length && { factoryId: In(factoryIdLst) }),
          ...(lineIdLst?.length && { lineId: In(lineIdLst) }),
          ...(orderNo && { orderNo: ILike(`%${orderNo}%`) }),
          ...(statusIdLst?.length && { orderStatus: In(statusIdLst) }),
          ...(start && end && { planStartDate: Between(start, end) }),
        },
      });

      const data = productionOrderLst.length
        ? await this.buildPOMaterialReport(productionOrderLst)
        : [];

      // Xuất dữ liệu ra file excel (giả sử return đường dẫn file tạm local)
      const localFilePath = await this.exportExcel(data);

      // Upload file lên S3
      const fileName = `report_${reportSaved.reportCode}_${beginAt}`;

      const { fileUrl } = await this.s3PrivateService.uploadS3(
        {
          // Giả sử exportExcel trả về Buffer hoặc Stream,
          // Nếu exportExcel trả về path, bạn có thể đọc file thành Buffer ở đây
          buffer: await fs.promises.readFile(localFilePath),
          originalname: fileName + '.xlsx',
        } as Express.Multer.File,
        fileName,
        'private',
      );

      const reportCompleted = await this.reportHistoryRepo.save({
        ...reportSaved,
        linkDownload: fileUrl,
        output: result,
        outputTotal: result.length,
        status: NSGeneralData.EReportStatus.COMPLETED,
        completedDate: new Date(),
      });

      return { data: [reportCompleted], meta: { request: req } };
    } catch (err) {
      Logger.error(err);
      await this.reportHistoryRepo.save({
        ...reportSaved,
        status: NSGeneralData.EReportStatus.ERROR,
        output: {
          errorMessage: err?.message ?? 'Unknown Error',
        },
        completedDate: new Date(),
      });
      throw new BadRequestException(`Submit report failed: ${err.message}`);
    }
  }

  private async buildPOMaterialReport(
    productionOrderLst: ProductionOrderEntity[],
  ): Promise<POMaterialReportOutput[]> {
    // Lấy danh sách orderId để làm điều kiện truy vấn
    const orderIds = productionOrderLst.map((order) => order.id);

    // Chạy song song nhiều truy vấn để lấy dữ liệu liên quan cho báo cáo
    const [
      siteMap,
      factoryMap,
      lineMap,
      processAreaMap,
      shiftMap,
      statusMap,
      operatorMap,
      orderMaterials,
      transactions,
    ] = await Promise.all([
      // Lấy danh sách site tương ứng với các đơn hàng
      this.organizationUnitRepo.find({
        where: { id: In(productionOrderLst.map((order) => order.siteId)) },
      }),
      // Lấy danh sách factory tương ứng với các đơn hàng
      this.organizationUnitRepo.find({
        where: { id: In(productionOrderLst.map((order) => order.factoryId)) },
      }),
      // Lấy danh sách line (dây chuyền) tương ứng với các đơn hàng
      this.organizationUnitRepo.find({
        where: { id: In(productionOrderLst.map((order) => order.lineId)) },
      }),
      // Lấy danh sách khu vực quy trình tương ứng với các đơn hàng
      this.organizationUnitRepo.find({
        where: { id: In(productionOrderLst.map((order) => order.processAreaId)) },
      }),
      // Lấy danh sách ca làm việc tương ứng với các đơn hàng
      this.shiftRepo.find({
        where: { id: In(productionOrderLst.map((order) => order.shiftId)) },
      }),
      // Lấy danh sách trạng thái đơn hàng
      this.generalDataDetailRepo.find({
        where: { id: In(productionOrderLst.map((order) => order.orderStatus)) },
      }),
      // Lấy danh sách người vận hành (operator)
      this.userRepo.find({
        where: { id: In(productionOrderLst.map((order) => order.operatorUserId)) },
      }),
      // Lấy danh sách vật liệu của các đơn hàng, kèm quan hệ process
      this.productionOrderMaterialRepo.find({
        where: { orderId: In(orderIds) },
        relations: ['process'],
      }),
      // Lấy danh sách các giao dịch vật liệu, sắp xếp mới nhất trước
      this.productionOrderMaterialTransactionRepo.find({
        where: { orderId: In(orderIds) },
        order: {
          transactionDate: 'DESC',
        },
      }),
    ]);

    // Tạo Map để tra cứu nhanh theo id, tránh filter mảng nhiều lần
    const siteMapById = new Map(siteMap.map((site) => [site.id, site]));
    const factoryMapById = new Map(factoryMap.map((factory) => [factory.id, factory]));
    const lineMapById = new Map(lineMap.map((line) => [line.id, line]));
    const processAreaMapById = new Map(
      processAreaMap.map((processArea) => [processArea.id, processArea]),
    );
    const shiftMapById = new Map(shiftMap.map((shift) => [shift.id, shift]));
    const statusMapById = new Map(statusMap.map((status) => [status.id, status]));
    const operatorMapById = new Map(operatorMap.map((operator) => [operator.id, operator]));

    // Mảng chứa kết quả đầu ra cuối cùng
    const outputs: POMaterialReportOutput[] = [];

    // Duyệt từng đơn hàng
    for (const order of productionOrderLst) {
      // Lọc vật liệu chỉ thuộc đơn hàng hiện tại
      const orderMaterialsForOrder = orderMaterials.filter((om) => om.orderId === order.id);

      // Duyệt từng vật liệu trong đơn hàng
      for (const material of orderMaterialsForOrder) {
        // Lọc các giao dịch của vật liệu này trong đơn hàng
        const transactionsForMaterial = transactions.filter(
          (trx) => trx.orderId === order.id && trx.materialId === material.materialId,
        );

        // Tính tổng số lượng giao dịch đã thực hiện, chú ý trường hợp giao dịch trả lại (số âm)
        const sumTransactionQty = transactionsForMaterial.reduce((sum, trx) => {
          let signedQty = Number(trx.transactionQty);

          if (
            (material.lineTypeCode === NSGeneralData.EMatterialLineType.ING &&
              trx.transactionType === NSGeneralData.ETransactionType.WIP_ISSUE_RETURN) ||
            (material.lineTypeCode === NSGeneralData.EMatterialLineType.PRO &&
              trx.transactionType === NSGeneralData.ETransactionType.WIP_COMPLETION_RETURN) ||
            (material.lineTypeCode === NSGeneralData.EMatterialLineType.BY_PRO &&
              trx.transactionType === NSGeneralData.ETransactionType.BYPRODUCT_COMPLETION_RETURN)
          ) {
            signedQty = -Number(trx.transactionQty);
          }

          return sum + signedQty;
        }, 0);

        for (const trx of transactionsForMaterial) {
          // Tạo đối tượng output cho dòng báo cáo vật liệu hiện tại
          const output = new POMaterialReportOutput();

          // Gán thông tin site, factory, line, khu vực quy trình (process area)
          output.site = `${siteMapById.get(order.siteId)?.code} - ${siteMapById.get(order.siteId)?.name}`;
          output.factory = `${factoryMapById.get(order.factoryId)?.code} - ${factoryMapById.get(order.factoryId)?.name}`;
          output.line = `${lineMapById.get(order.lineId)?.code} - ${lineMapById.get(order.lineId)?.name}`;
          output.processArea = `${processAreaMapById.get(order.processAreaId)?.code} - ${processAreaMapById.get(order.processAreaId)?.name}`;

          // Gán các thông tin cơ bản đơn hàng
          output.productionOrderNo = order.orderNo;
          output.recipeCode = order.recipeCode;
          output.recipeVer = order.recipeVersion;
          output.orderQty = order.quantity;
          output.orderUom = order.trxUom;

          // Chuyển đổi timezone cho ngày sản xuất
          output.productionDate = order.planStartDate;

          // Gán thông tin ca làm việc, trạng thái đơn hàng, người tạo và ngày tạo
          output.shift = `${shiftMapById.get(order.shiftId)?.code} - ${shiftMapById.get(order.shiftId)?.description}`;
          output.orderStatus = `${statusMapById.get(order.orderStatus)?.code} - ${statusMapById.get(order.orderStatus)?.name}`;
          output.createdBy = order.createdBy;
          output.creationDate = order.createdDate;

          // Gán thông tin người vận hành
          output.operator = `${operatorMapById.get(order.operatorUserId)?.employeeCode} - ${operatorMapById.get(order.operatorUserId)?.fullName}`;

          // Gán thông tin quy trình của vật liệu
          output.processCode = material.process?.code;
          output.processName = material.process?.name;

          // Gán các thông tin vật liệu
          output.materialCode = material.materialCode;
          output.materialName = material.materialName;
          output.type = material.lineTypeCode;
          output.uom = material.trxUomCode;
          output.planQty = material.planTrxQty;
          output.actualQty = material.actualTrxQty;

          // Tổng số lượng giao dịch đã tính
          output.sumTransactionQty = sumTransactionQty;

          // Nếu có giao dịch mới nhất thì gán thêm thông tin chi tiết giao dịch
          const batch = await this.productionBatchRepo.findOne({
            where: { ...(trx.batchId && { id: trx.batchId }) },
          });

          output.batchNumber = batch?.batchNumber;
          output.transactionId = trx.materialTransactionId;
          output.transactionType = trx.transactionType;
          output.transactionUom = trx.transactionUom;
          output.transactionQty = trx.transactionQty;
          output.transactionDate = trx.transactionDate;
          output.lot = trx.lotNumber;

          // Lấy tên phương pháp tính toán OEE nếu có (chỗ này có thể optimize tránh await trong loop)
          const method = await this.generalDataDetailRepo.findOne({
            where: {
              ...(material.process.outputCalculationMethodCode && {
                code: material.process.outputCalculationMethodCode,
              }),
            },
          });
          output.oeeCalculation = method?.name;

          // Đẩy dòng báo cáo hiện tại vào mảng kết quả
          outputs.push(output);
        }
      }
    }

    // Trả về mảng báo cáo hoàn chỉnh
    return outputs;
  }

  private async exportExcel(data: POMaterialReportOutput[]): Promise<string> {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Report');

    // Định nghĩa header
    worksheet.columns = [
      { header: 'No', key: 'index', width: 5 },
      { header: 'Site', key: 'site', width: 20 },
      { header: 'Factory', key: 'factory', width: 20 },
      { header: 'Line', key: 'line', width: 20 },
      { header: 'Process Area', key: 'processArea', width: 20 },
      { header: 'Production Order No', key: 'productionOrderNo', width: 25 },
      { header: 'Recipe Code', key: 'recipeCode', width: 20 },
      { header: 'Recipe Version', key: 'recipeVer', width: 15 },
      { header: 'Order Quantity', key: 'orderQty', width: 15 },
      { header: 'Order UOM', key: 'orderUom', width: 10 },
      { header: 'Production Date', key: 'productionDate', width: 20 },
      { header: 'Shift', key: 'shift', width: 15 },
      { header: 'Order Status', key: 'orderStatus', width: 20 },
      { header: 'Created By', key: 'createdBy', width: 20 },
      { header: 'Creation Date', key: 'creationDate', width: 20 },
      { header: 'Operator', key: 'operator', width: 25 },
      { header: 'Process Code', key: 'processCode', width: 20 },
      { header: 'Process Name', key: 'processName', width: 25 },
      { header: 'Material Code', key: 'materialCode', width: 20 },
      { header: 'Material Name', key: 'materialName', width: 30 },
      { header: 'Type', key: 'type', width: 10 },
      { header: 'UOM', key: 'uom', width: 10 },
      { header: 'Planned Quantity', key: 'planQty', width: 15 },
      { header: 'Actual Quantity', key: 'actualQty', width: 15 },
      { header: 'Sum Transaction Quantity', key: 'sumTransactionQty', width: 20 },
      { header: 'Batch Number', key: 'batchNumber', width: 20 },
      { header: 'Transaction ID', key: 'transactionId', width: 20 },
      { header: 'Transaction Type', key: 'transactionType', width: 20 },
      { header: 'Transaction UOM', key: 'transactionUom', width: 10 },
      { header: 'Transaction Quantity', key: 'transactionQty', width: 15 },
      { header: 'Transaction Date', key: 'transactionDate', width: 20 },
      { header: 'Lot Number', key: 'lot', width: 20 },
      { header: 'OEE Calculation', key: 'oeeCalculation', width: 20 },
    ];

    // Thêm dữ liệu
    data.forEach((item, index) => {
      worksheet.addRow({
        index: index + 1,
        site: item.site,
        factory: item.factory,
        line: item.line,
        processArea: item.processArea,
        productionOrderNo: item.productionOrderNo,
        recipeCode: item.recipeCode,
        recipeVer: item.recipeVer,
        orderQty: item.orderQty,
        orderUom: item.orderUom,
        productionDate: dayjs.utc(item.productionDate).tz(TZ).format('DD-MM-YYYY HH:mm:ss'),
        shift: item.shift,
        orderStatus: item.orderStatus,
        createdBy: item.createdBy,
        creationDate: dayjs.utc(item.creationDate).tz(TZ).format('DD-MM-YYYY HH:mm:ss'),
        operator: item.operator,
        processCode: item.processCode,
        processName: item.processName,
        materialCode: item.materialCode,
        materialName: item.materialName,
        type: item.type,
        uom: item.uom,
        planQty: item.planQty,
        actualQty: item.actualQty,
        sumTransactionQty: item.sumTransactionQty,
        batchNumber: item.batchNumber,
        transactionId: item.transactionId,
        transactionType: item.transactionType,
        transactionUom: item.transactionUom,
        transactionQty: item.transactionQty,
        transactionDate: dayjs.utc(item.transactionDate).tz(TZ).format('DD-MM-YYYY HH:mm:ss'),
        lot: item.lot,
        oeeCalculation: item.oeeCalculation,
      });
    });

    // Tạo đường dẫn file tạm thời
    const filePath = path.join(os.tmpdir(), `report-${Date.now()}.xlsx`);

    // Ghi file
    await workbook.xlsx.writeFile(filePath);

    return filePath;
  }

  async getMetaLines(req: { factoryIdLst?: string[] }): Promise<PageResponse<any>> {
    const lines = await this.organizationUnitRepo.find({
      where: {
        isActive: true,
        levelGeneralDataDetailCode: NSGeneralData.EOrgLevel.LINE,
        ...(req?.factoryIdLst && { parentId: In(req.factoryIdLst) }),
      },
      order: { code: 'ASC', createdDate: 'DESC' },
    });
    return { data: lines };
  }

  async getMetaOrderStatus(): Promise<PageResponse<any>> {
    const genData = await this.generalDataRepo.find({
      where: {
        isActive: true,
        code: NSGeneralData.EGeneralDataCode.ORDER_STATUS,
      },
      select: ['id', 'code'],
    });

    const details = await this.generalDataDetailRepo.find({
      where: {
        isActive: true,
        generalId: In(genData.map((item) => item.id)),
      },
      order: { code: 'ASC' },
      select: ['id', 'code', 'name'],
    });

    return { data: details, total: details.length };
  }
}

class POMaterialReportOutput {
  site: string;
  factory: string;
  line: string;
  processArea: string;
  productionOrderNo: string;
  recipeCode: string;
  recipeVer: number;
  orderQty: number;
  orderUom: string;
  productionDate: Date;
  shift: string;
  orderStatus: string;
  createdBy: string;
  creationDate: Date;
  operator: string;
  processCode: string;
  processName: string;
  materialCode: string;
  materialName: string;
  type: string; // e.g., ING, PKG, etc.
  uom: string;
  planQty: number;
  actualQty: number;
  sumTransactionQty: number;
  transactionId: string;
  batchNumber: number | string;
  transactionType: string;
  transactionUom: string;
  transactionQty: number;
  transactionDate: Date;
  lot: string;
  oeeCalculation: string; // OEE tổng hợp, nếu null thì dùng undefined hoặc nullable
}
