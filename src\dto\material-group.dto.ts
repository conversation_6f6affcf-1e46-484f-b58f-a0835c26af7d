import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsBoolean, IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';
import { PageRequest } from '~/@systems/utils';

export class MaterialGroupReq extends PageRequest {
  /** Mã nhóm nguyên vật liệu */
  @ApiPropertyOptional({ description: 'Mã nhóm nguyên vật liệu' })
  @IsOptional()
  code?: string;

  /** Mô tả nhóm nguyên vật liệu */
  @ApiPropertyOptional({ description: 'Mô tả nhóm nguyên vật liệu' })
  @IsOptional()
  description?: string;

  /** Trạng thái nhóm nguyên vật liệu */
  @ApiPropertyOptional({ description: 'Trạng thái nhóm nguyên vật liệu' })
  @IsOptional()
  active?: boolean;
}

export class ItemDto {
  /** Mã sản phẩm */
  @ApiProperty({ description: 'Mã sản phẩm' })
  id: string;

  /** Trạng thái sản phẩm */
  @ApiProperty({ description: 'Trạng thái sản phẩm' })
  active: boolean;

  /** Người tạo */
  @ApiPropertyOptional({ description: 'Người tạo' })
  @IsOptional()
  createdBy?: string;

  /** Ngày tạo */
  @ApiPropertyOptional({ description: 'Ngày tạo' })
  @IsOptional()
  createdDate?: string;
}

export class MaterialGroupDto {
  /** Mã nhóm nguyên vật liệu */
  @ApiProperty({ description: 'Mã nhóm nguyên vật liệu' })
  code: string;

  /** Mô tả nhóm nguyên vật liệu */
  @ApiProperty({ description: 'Mô tả nhóm nguyên vật liệu' })
  description: string;

  /** Danh sách sản phẩm */
  @ApiProperty({ description: 'Danh sách sản phẩm nguyên vật liệu' })
  lstItem: ItemDto[];

  /** Trạng thái nhóm nguyên vật liệu */
  @ApiProperty({ description: 'Trạng thái nhóm nguyên vật liệu' })
  active: boolean;
}
