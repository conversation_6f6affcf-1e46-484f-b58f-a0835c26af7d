import { Body, Param, Query } from '@nestjs/common';
import { ApiTags } from '@nestjs/swagger';
import { DefController, DefGet, DefPost, DefPut } from '~/@core/decorator';
import { ListSitewiseModelReq, SitewiseModelReq } from '~/dto/sitewise.dto';
import { SitewiseModelService } from '../../services';

@ApiTags('Sitewise Model')
@DefController('sitewise-model')
export class SitewiseModelController {
  constructor(private readonly service: SitewiseModelService) {}

  @DefGet('find')
  async find(@Query() data: any) {
    return await this.service.find(data);
  }

  @DefGet(':id')
  async findOne(@Param('id') id: string) {
    return await this.service.findOne(id);
  }

  @DefGet('')
  async pagination(@Query() data: ListSitewiseModelReq) {
    return await this.service.pagination(data);
  }

  @DefPost('')
  async createData(@Body() data: SitewiseModelReq) {
    return await this.service.createData(data);
  }

  @DefPut(':id')
  async updateData(@Param('id') id: string, @Body() data: SitewiseModelReq) {
    return await this.service.updateData(id, data);
  }

  @DefPut('update-active/:id')
  async updateActive(@Param('id') id: string) {
    return await this.service.updateIsDelete(id);
  }

  @DefPost('import')
  async createDataExcel(@Body() data: SitewiseModelReq[]) {
    return await this.service.createDataExcel(data);
  }
}
