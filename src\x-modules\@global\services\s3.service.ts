import { Injectable } from '@nestjs/common';
import { configEnv } from '~/@config/env';
import * as AWS from 'aws-sdk';
import * as ExcelJS from 'exceljs';
import * as fs from 'fs';
import { v4 as uuidv4 } from 'uuid';
import * as path from 'path';
const { LINK_UPLOAD_S3, AWS_S3_BUCKET_NAME, AWS_S3_ACCESS_KEY_ID, AWS_S3_SECRET_ACCESS_KEY } =
  configEnv();
@Injectable()
export class S3Service {
  AWS_S3_BUCKET_NAME: string;
  s3: AWS.S3;
  constructor() {
    this.AWS_S3_BUCKET_NAME = AWS_S3_BUCKET_NAME;
    const ACCESS_KEY_ID = AWS_S3_ACCESS_KEY_ID;
    const SECRET_ACCESS_KEY = AWS_S3_SECRET_ACCESS_KEY;

    this.s3 = new AWS.S3({
      accessKeyId: ACCESS_KEY_ID,
      secretAccessKey: SECRET_ACCESS_KEY,
      region: 'ap-southeast-1',
    });
  }

  async uploadSingle(
    file: Express.Multer.File,
    fileName: string,
  ): Promise<{ fileName: string; fileUrl: string }> {
    const temp: string[] = file?.originalname ? file.originalname.split('.') : [];
    const ext = temp.length > 1 ? `.${temp[temp.length - 1]}` : '';
    const key = `${LINK_UPLOAD_S3}/${fileName}${ext}`;

    const params: AWS.S3.PutObjectRequest = {
      Bucket: AWS_S3_BUCKET_NAME,
      Key: key,
      Body: file.buffer,
      ACL: 'public-read',
    };

    return new Promise<{ fileName: string; fileUrl: string }>((resolve, reject) => {
      this.s3.upload(params, (err, data) => {
        if (err) {
          reject(err);
        } else {
          resolve({ fileName, fileUrl: data.Location });
        }
      });
    });
  }

  async exportAndUploadToS3(data: any[], bucketName: string): Promise<string> {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Data');

    // Add header
    const headers = Object.keys(data[0] || {});
    worksheet.addRow(headers);

    // Add rows
    data.forEach((item) => {
      const row = headers.map((h) => item[h]);
      worksheet.addRow(row);
    });

    // Tạo file tạm
    const fileName = `export-${uuidv4()}.xlsx`;
    const filePath = path.join('/tmp', fileName); // Unix tmp folder, có thể dùng folder khác trên Windows

    await workbook.xlsx.writeFile(filePath);

    // Đọc file buffer
    const fileBuffer = fs.readFileSync(filePath);

    // Upload lên S3
    const s3Key = `exports/${fileName}`;
    await this.s3
      .upload({
        Bucket: bucketName,
        Key: s3Key,
        Body: fileBuffer,
        ContentType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      })
      .promise();

    // Xoá file tạm
    fs.unlinkSync(filePath);

    return `https://${bucketName}.s3.${process.env.AWS_REGION}.amazonaws.com/${s3Key}`;
  }
}
