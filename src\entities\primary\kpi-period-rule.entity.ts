import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Exclude } from 'class-transformer';
import {
  Column,
  CreateDateColumn,
  Entity,
  Index,
  PrimaryGeneratedColumn,
  UpdateDateColumn,
  VersionColumn,
} from 'typeorm';
import { PrimaryBaseEntity } from '~/entities/primary-base.entity';

/** Bảng kỳ KPI rule */
@Entity('kpi_period_rule')
export class KPIPeriodRuleEntity extends PrimaryBaseEntity {
  /** Tên rule kỳ KPI */
  @Column({ nullable: true })
  periodRuleName: string;

  /** <PERSON><PERSON><PERSON> bắt đầu */
  @Column({ nullable: false })
  startDate: number;

  /** Ng<PERSON>y kết thúc */
  @Column({ nullable: false })
  endDate: number;

  /** Ca bắt đầu */
  @Column({ nullable: true })
  startShift: string;

  /** Ca kết thúc */
  @Column({ nullable: true })
  endShift: string;

  /** Th<PERSON>g bắt đầu */
  @Column({ nullable: true })
  startMonth: number;

  /** <PERSON> kỳ lặp lại */
  @Column({ nullable: true })
  cycleType: string;

  /** Mã chu kỳ  */
  @Column({ nullable: true })
  cycleCode: string;

  /** Tên chu kỳ*/
  @Column({ nullable: true })
  cycleName: string;

  /** Mã ca bắt đầu */
  @Column({ nullable: true })
  startShiftCode: string;

  /** Mã ca kết thúc */
  @Column({ nullable: true })
  endShiftCode: string;

  /** Trạng thái rule kỳ KPI */
  @Column({ nullable: true })
  status: number;
}
