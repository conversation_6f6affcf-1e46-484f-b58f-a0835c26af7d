import { Injectable, NotFoundException } from '@nestjs/common';
import { ILike } from 'typeorm';
import { BindRepo } from '~/@core/decorator';
import {
  ORGANIZATION_GENERAL_DATA,
  ORGANIZATION_LEVEL,
  ORGANIZATION_LEVEL_ORDER,
} from '~/common/constants/organizationLevel.constant';
import { EOrganizationLevel, getParentLevelCode } from '~/common/enums/organization-level.enum';
import { ListOrganizationUnitReq } from '~/dto/organization-unit.dto';
import { GeneralDataDetailRepo, GeneralDataRepo } from '~/repositories/primary/general-data.repo';
import { OrganizationUnitRepo } from '~/repositories/primary/organization-unit.repo';

@Injectable()
export class OrganizationUnitService {
  constructor() {}

  @BindRepo(OrganizationUnitRepo)
  organizationUnitRepo: OrganizationUnitRepo;

  @BindRepo(GeneralDataDetailRepo)
  private readonly generalDataDetailRepo: GeneralDataDetailRepo;

  @BindRepo(GeneralDataRepo)
  private readonly generalDataRepo: GeneralDataRepo;

  private isValidUUID(uuid: string): boolean {
    const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-4[0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(uuid);
  }

  private async getGeneralDataDetail(id: string | null | undefined) {
    if (!id || !this.isValidUUID(id)) {
      return null;
    }

    try {
      const detail = await this.generalDataDetailRepo
        .createQueryBuilder('detail')
        .leftJoinAndSelect('detail.general', 'general')
        .where('detail.id = :id', { id })
        .getOne();

      if (!detail) {
        return null;
      }

      const general = await detail.general;
      if (!general) {
        return null;
      }

      return {
        detail,
        general,
      };
    } catch (error) {
      console.error('Error fetching general data detail:', error);
      return null;
    }
  }

  private async getChildrenRecursive(
    unit: any,
    currentLevel: number = 1,
    maxLevel: number = 5,
  ): Promise<any> {
    if (currentLevel > maxLevel) {
      return unit;
    }

    const children = await this.organizationUnitRepo.findChildren(unit.id);

    if (children.length > 0) {
      const processedChildren = await Promise.all(
        children.map(async (child) => {
          const hasChildren = await this.organizationUnitRepo.hasChildren(child.id);

          let level = null;
          if (child.levelId) {
            const levelDetail = await this.generalDataDetailRepo.findOne({
              where: { id: child.levelId },
            });
            if (levelDetail) {
              level = {
                id: levelDetail.id,
                code: levelDetail.code,
                name: levelDetail.name,
              };
            }
          }

          let category = null;
          if (child.categoryId) {
            const categoryDetail = await this.generalDataDetailRepo.findOne({
              where: { id: child.categoryId },
            });
            if (categoryDetail) {
              category = {
                id: categoryDetail.id,
                code: categoryDetail.code,
                name: categoryDetail.name,
              };
            }
          }

          // Get parent name
          let parentName = null;
          if (child.parentId) {
            const parentUnit = await this.organizationUnitRepo.findOne({
              where: { id: child.parentId },
            });
            if (parentUnit) {
              parentName = parentUnit.name;
            }
          }

          const processedChild = {
            ...child,
            hasChildren,
            level,
            category,
            parentName,
          };

          // Recursively get children for the next level
          return this.getChildrenRecursive(processedChild, currentLevel + 1, maxLevel);
        }),
      );

      unit.children = processedChildren;
    } else {
      unit.children = [];
    }

    return unit;
  }

  async create(createData: any): Promise<any> {
    // Handle parent field if it's sent as a string instead of an object
    if (createData.parent && typeof createData.parent === 'string') {
      createData.parentId = createData.parent;
      delete createData.parent;
    }

    // Remove any non-entity fields that might cause issues
    const fieldsToRemove = ['hasChildren', 'level', 'category', 'parentName', 'children'];
    fieldsToRemove.forEach((field) => {
      if (field in createData) {
        delete createData[field];
      }
    });

    // Handle level reference
    if (createData.levelId) {
      const levelData = await this.getGeneralDataDetail(createData.levelId);
      if (levelData) {
        createData.levelGeneralDataParentCode = levelData.general.code;
        createData.levelGeneralDataDetailCode = levelData.detail.code;
      }
    }

    // Handle category reference
    if (createData.categoryId) {
      const categoryData = await this.getGeneralDataDetail(createData.categoryId);
      if (categoryData) {
        createData.categoryGeneralDataParentCode = categoryData.general.code;
        createData.categoryGeneralDataDetailCode = categoryData.detail.code;
      }
    }

    // Create new organization unit
    const newUnit = this.organizationUnitRepo.create(createData);
    return await this.organizationUnitRepo.save(newUnit);
  }

  async list(query: ListOrganizationUnitReq): Promise<any> {
    const qb = this.organizationUnitRepo
      .createQueryBuilder('ou')
      .select([
        'ou.id',
        'ou.createdDate',
        'ou.updatedDate',
        'ou.createdBy',
        'ou.updatedBy',
        'ou.version',
        'ou.code',
        'ou.name',
        'ou.description',
        'ou.levelId',
        'ou.levelGeneralDataParentCode',
        'ou.levelGeneralDataDetailCode',
        'ou.categoryId',
        'ou.categoryGeneralDataParentCode',
        'ou.categoryGeneralDataDetailCode',
        'ou.capacity',
        'ou.weightingDistribution',
        'ou.ingredientLocator',
        'ou.productsLocator',
        'ou.storageLocation',
        'ou.parentId',
        'ou.isActive',
        'ou.note',
        'ou.orderNo',
        'ou.ipScada',
        'ou.materialHandleType',
      ])
      .addSelect(
        `RTRIM(CONCAT_WS('/', 
          CASE WHEN ou_p5.id IS NOT NULL THEN
            CASE 
              WHEN ou_p5."levelGeneralDataDetailCode" IS NULL THEN CONCAT('9_', LPAD(COALESCE(CAST(ou_p5."orderNo" AS VARCHAR), '0'), 7, '0'), '_', LOWER(ou_p5.code))
              WHEN UPPER(ou_p5."levelGeneralDataDetailCode") = '${ORGANIZATION_LEVEL.GROUP}' THEN CONCAT('${ORGANIZATION_LEVEL_ORDER[ORGANIZATION_LEVEL.GROUP]}_', LPAD(COALESCE(CAST(ou_p5."orderNo" AS VARCHAR), '0'), 7, '0'), '_', LOWER(ou_p5.code))
              WHEN UPPER(ou_p5."levelGeneralDataDetailCode") = '${ORGANIZATION_LEVEL.SITE}' THEN CONCAT('${ORGANIZATION_LEVEL_ORDER[ORGANIZATION_LEVEL.SITE]}_', LPAD(COALESCE(CAST(ou_p5."orderNo" AS VARCHAR), '0'), 7, '0'), '_', LOWER(ou_p5.code))
              WHEN UPPER(ou_p5."levelGeneralDataDetailCode") = '${ORGANIZATION_LEVEL.FACTORY}' THEN CONCAT('${ORGANIZATION_LEVEL_ORDER[ORGANIZATION_LEVEL.FACTORY]}_', LPAD(COALESCE(CAST(ou_p5."orderNo" AS VARCHAR), '0'), 7, '0'), '_', LOWER(ou_p5.code))
              WHEN UPPER(ou_p5."levelGeneralDataDetailCode") = '${ORGANIZATION_LEVEL.LINE}' THEN CONCAT('${ORGANIZATION_LEVEL_ORDER[ORGANIZATION_LEVEL.LINE]}_', LPAD(COALESCE(CAST(ou_p5."orderNo" AS VARCHAR), '0'), 7, '0'), '_', LOWER(ou_p5.code))
              WHEN UPPER(ou_p5."levelGeneralDataDetailCode") = '${ORGANIZATION_LEVEL.PROCESS}' THEN CONCAT('${ORGANIZATION_LEVEL_ORDER[ORGANIZATION_LEVEL.PROCESS]}_', LPAD(COALESCE(CAST(ou_p5."orderNo" AS VARCHAR), '0'), 7, '0'), '_', LOWER(ou_p5.code))
              ELSE CONCAT('9_', LPAD(COALESCE(CAST(ou_p5."orderNo" AS VARCHAR), '0'), 7, '0'), '_', LOWER(ou_p5.code))
            END
          END,
          CASE WHEN ou_p4.id IS NOT NULL THEN
            CASE 
              WHEN ou_p4."levelGeneralDataDetailCode" IS NULL THEN CONCAT('9_', LPAD(COALESCE(CAST(ou_p4."orderNo" AS VARCHAR), '0'), 7, '0'), '_', LOWER(ou_p4.code))
              WHEN UPPER(ou_p4."levelGeneralDataDetailCode") = '${ORGANIZATION_LEVEL.GROUP}' THEN CONCAT('${ORGANIZATION_LEVEL_ORDER[ORGANIZATION_LEVEL.GROUP]}_', LPAD(COALESCE(CAST(ou_p4."orderNo" AS VARCHAR), '0'), 7, '0'), '_', LOWER(ou_p4.code))
              WHEN UPPER(ou_p4."levelGeneralDataDetailCode") = '${ORGANIZATION_LEVEL.SITE}' THEN CONCAT('${ORGANIZATION_LEVEL_ORDER[ORGANIZATION_LEVEL.SITE]}_', LPAD(COALESCE(CAST(ou_p4."orderNo" AS VARCHAR), '0'), 7, '0'), '_', LOWER(ou_p4.code))
              WHEN UPPER(ou_p4."levelGeneralDataDetailCode") = '${ORGANIZATION_LEVEL.FACTORY}' THEN CONCAT('${ORGANIZATION_LEVEL_ORDER[ORGANIZATION_LEVEL.FACTORY]}_', LPAD(COALESCE(CAST(ou_p4."orderNo" AS VARCHAR), '0'), 7, '0'), '_', LOWER(ou_p4.code))
              WHEN UPPER(ou_p4."levelGeneralDataDetailCode") = '${ORGANIZATION_LEVEL.LINE}' THEN CONCAT('${ORGANIZATION_LEVEL_ORDER[ORGANIZATION_LEVEL.LINE]}_', LPAD(COALESCE(CAST(ou_p4."orderNo" AS VARCHAR), '0'), 7, '0'), '_', LOWER(ou_p4.code))
              WHEN UPPER(ou_p4."levelGeneralDataDetailCode") = '${ORGANIZATION_LEVEL.PROCESS}' THEN CONCAT('${ORGANIZATION_LEVEL_ORDER[ORGANIZATION_LEVEL.PROCESS]}_', LPAD(COALESCE(CAST(ou_p4."orderNo" AS VARCHAR), '0'), 7, '0'), '_', LOWER(ou_p4.code))
              ELSE CONCAT('9_', LPAD(COALESCE(CAST(ou_p4."orderNo" AS VARCHAR), '0'), 7, '0'), '_', LOWER(ou_p4.code))
            END
          END,
          CASE WHEN ou_p3.id IS NOT NULL THEN
            CASE 
              WHEN ou_p3."levelGeneralDataDetailCode" IS NULL THEN CONCAT('9_', LPAD(COALESCE(CAST(ou_p3."orderNo" AS VARCHAR), '0'), 7, '0'), '_', LOWER(ou_p3.code))
              WHEN UPPER(ou_p3."levelGeneralDataDetailCode") = '${ORGANIZATION_LEVEL.GROUP}' THEN CONCAT('${ORGANIZATION_LEVEL_ORDER[ORGANIZATION_LEVEL.GROUP]}_', LPAD(COALESCE(CAST(ou_p3."orderNo" AS VARCHAR), '0'), 7, '0'), '_', LOWER(ou_p3.code))
              WHEN UPPER(ou_p3."levelGeneralDataDetailCode") = '${ORGANIZATION_LEVEL.SITE}' THEN CONCAT('${ORGANIZATION_LEVEL_ORDER[ORGANIZATION_LEVEL.SITE]}_', LPAD(COALESCE(CAST(ou_p3."orderNo" AS VARCHAR), '0'), 7, '0'), '_', LOWER(ou_p3.code))
              WHEN UPPER(ou_p3."levelGeneralDataDetailCode") = '${ORGANIZATION_LEVEL.FACTORY}' THEN CONCAT('${ORGANIZATION_LEVEL_ORDER[ORGANIZATION_LEVEL.FACTORY]}_', LPAD(COALESCE(CAST(ou_p3."orderNo" AS VARCHAR), '0'), 7, '0'), '_', LOWER(ou_p3.code))
              WHEN UPPER(ou_p3."levelGeneralDataDetailCode") = '${ORGANIZATION_LEVEL.LINE}' THEN CONCAT('${ORGANIZATION_LEVEL_ORDER[ORGANIZATION_LEVEL.LINE]}_', LPAD(COALESCE(CAST(ou_p3."orderNo" AS VARCHAR), '0'), 7, '0'), '_', LOWER(ou_p3.code))
              WHEN UPPER(ou_p3."levelGeneralDataDetailCode") = '${ORGANIZATION_LEVEL.PROCESS}' THEN CONCAT('${ORGANIZATION_LEVEL_ORDER[ORGANIZATION_LEVEL.PROCESS]}_', LPAD(COALESCE(CAST(ou_p3."orderNo" AS VARCHAR), '0'), 7, '0'), '_', LOWER(ou_p3.code))
              ELSE CONCAT('9_', LPAD(COALESCE(CAST(ou_p3."orderNo" AS VARCHAR), '0'), 7, '0'), '_', LOWER(ou_p3.code))
            END
          END,
          CASE WHEN ou_p2.id IS NOT NULL THEN
            CASE 
              WHEN ou_p2."levelGeneralDataDetailCode" IS NULL THEN CONCAT('9_', LPAD(COALESCE(CAST(ou_p2."orderNo" AS VARCHAR), '0'), 7, '0'), '_', LOWER(ou_p2.code))
              WHEN UPPER(ou_p2."levelGeneralDataDetailCode") = '${ORGANIZATION_LEVEL.GROUP}' THEN CONCAT('${ORGANIZATION_LEVEL_ORDER[ORGANIZATION_LEVEL.GROUP]}_', LPAD(COALESCE(CAST(ou_p2."orderNo" AS VARCHAR), '0'), 7, '0'), '_', LOWER(ou_p2.code))
              WHEN UPPER(ou_p2."levelGeneralDataDetailCode") = '${ORGANIZATION_LEVEL.SITE}' THEN CONCAT('${ORGANIZATION_LEVEL_ORDER[ORGANIZATION_LEVEL.SITE]}_', LPAD(COALESCE(CAST(ou_p2."orderNo" AS VARCHAR), '0'), 7, '0'), '_', LOWER(ou_p2.code))
              WHEN UPPER(ou_p2."levelGeneralDataDetailCode") = '${ORGANIZATION_LEVEL.FACTORY}' THEN CONCAT('${ORGANIZATION_LEVEL_ORDER[ORGANIZATION_LEVEL.FACTORY]}_', LPAD(COALESCE(CAST(ou_p2."orderNo" AS VARCHAR), '0'), 7, '0'), '_', LOWER(ou_p2.code))
              WHEN UPPER(ou_p2."levelGeneralDataDetailCode") = '${ORGANIZATION_LEVEL.LINE}' THEN CONCAT('${ORGANIZATION_LEVEL_ORDER[ORGANIZATION_LEVEL.LINE]}_', LPAD(COALESCE(CAST(ou_p2."orderNo" AS VARCHAR), '0'), 7, '0'), '_', LOWER(ou_p2.code))
              WHEN UPPER(ou_p2."levelGeneralDataDetailCode") = '${ORGANIZATION_LEVEL.PROCESS}' THEN CONCAT('${ORGANIZATION_LEVEL_ORDER[ORGANIZATION_LEVEL.PROCESS]}_', LPAD(COALESCE(CAST(ou_p2."orderNo" AS VARCHAR), '0'), 7, '0'), '_', LOWER(ou_p2.code))
              ELSE CONCAT('9_', LPAD(COALESCE(CAST(ou_p2."orderNo" AS VARCHAR), '0'), 7, '0'), '_', LOWER(ou_p2.code))
            END
          END,
          CASE WHEN ou_p1.id IS NOT NULL THEN
            CASE 
              WHEN ou_p1."levelGeneralDataDetailCode" IS NULL THEN CONCAT('9_', LPAD(COALESCE(CAST(ou_p1."orderNo" AS VARCHAR), '0'), 7, '0'), '_', LOWER(ou_p1.code))
              WHEN UPPER(ou_p1."levelGeneralDataDetailCode") = '${ORGANIZATION_LEVEL.GROUP}' THEN CONCAT('${ORGANIZATION_LEVEL_ORDER[ORGANIZATION_LEVEL.GROUP]}_', LPAD(COALESCE(CAST(ou_p1."orderNo" AS VARCHAR), '0'), 7, '0'), '_', LOWER(ou_p1.code))
              WHEN UPPER(ou_p1."levelGeneralDataDetailCode") = '${ORGANIZATION_LEVEL.SITE}' THEN CONCAT('${ORGANIZATION_LEVEL_ORDER[ORGANIZATION_LEVEL.SITE]}_', LPAD(COALESCE(CAST(ou_p1."orderNo" AS VARCHAR), '0'), 7, '0'), '_', LOWER(ou_p1.code))
              WHEN UPPER(ou_p1."levelGeneralDataDetailCode") = '${ORGANIZATION_LEVEL.FACTORY}' THEN CONCAT('${ORGANIZATION_LEVEL_ORDER[ORGANIZATION_LEVEL.FACTORY]}_', LPAD(COALESCE(CAST(ou_p1."orderNo" AS VARCHAR), '0'), 7, '0'), '_', LOWER(ou_p1.code))
              WHEN UPPER(ou_p1."levelGeneralDataDetailCode") = '${ORGANIZATION_LEVEL.LINE}' THEN CONCAT('${ORGANIZATION_LEVEL_ORDER[ORGANIZATION_LEVEL.LINE]}_', LPAD(COALESCE(CAST(ou_p1."orderNo" AS VARCHAR), '0'), 7, '0'), '_', LOWER(ou_p1.code))
              WHEN UPPER(ou_p1."levelGeneralDataDetailCode") = '${ORGANIZATION_LEVEL.PROCESS}' THEN CONCAT('${ORGANIZATION_LEVEL_ORDER[ORGANIZATION_LEVEL.PROCESS]}_', LPAD(COALESCE(CAST(ou_p1."orderNo" AS VARCHAR), '0'), 7, '0'), '_', LOWER(ou_p1.code))
              ELSE CONCAT('9_', LPAD(COALESCE(CAST(ou_p1."orderNo" AS VARCHAR), '0'), 7, '0'), '_', LOWER(ou_p1.code))
            END
          END,
          CASE 
            WHEN ou."levelGeneralDataDetailCode" IS NULL THEN CONCAT('9_', LPAD(COALESCE(CAST(ou."orderNo" AS VARCHAR), '0'), 7, '0'), '_', LOWER(ou.code))
            WHEN UPPER(ou."levelGeneralDataDetailCode") = '${ORGANIZATION_LEVEL.GROUP}' THEN CONCAT('${ORGANIZATION_LEVEL_ORDER[ORGANIZATION_LEVEL.GROUP]}_', LPAD(COALESCE(CAST(ou."orderNo" AS VARCHAR), '0'), 7, '0'), '_', LOWER(ou.code))
            WHEN UPPER(ou."levelGeneralDataDetailCode") = '${ORGANIZATION_LEVEL.SITE}' THEN CONCAT('${ORGANIZATION_LEVEL_ORDER[ORGANIZATION_LEVEL.SITE]}_', LPAD(COALESCE(CAST(ou."orderNo" AS VARCHAR), '0'), 7, '0'), '_', LOWER(ou.code))
            WHEN UPPER(ou."levelGeneralDataDetailCode") = '${ORGANIZATION_LEVEL.FACTORY}' THEN CONCAT('${ORGANIZATION_LEVEL_ORDER[ORGANIZATION_LEVEL.FACTORY]}_', LPAD(COALESCE(CAST(ou."orderNo" AS VARCHAR), '0'), 7, '0'), '_', LOWER(ou.code))
            WHEN UPPER(ou."levelGeneralDataDetailCode") = '${ORGANIZATION_LEVEL.LINE}' THEN CONCAT('${ORGANIZATION_LEVEL_ORDER[ORGANIZATION_LEVEL.LINE]}_', LPAD(COALESCE(CAST(ou."orderNo" AS VARCHAR), '0'), 7, '0'), '_', LOWER(ou.code))
            WHEN UPPER(ou."levelGeneralDataDetailCode") = '${ORGANIZATION_LEVEL.PROCESS}' THEN CONCAT('${ORGANIZATION_LEVEL_ORDER[ORGANIZATION_LEVEL.PROCESS]}_', LPAD(COALESCE(CAST(ou."orderNo" AS VARCHAR), '0'), 7, '0'), '_', LOWER(ou.code))
            ELSE CONCAT('9_', LPAD(COALESCE(CAST(ou."orderNo" AS VARCHAR), '0'), 7, '0'), '_', LOWER(ou.code))
          END
        ), '/')`,
        'full_path',
      );

    // Join with parent tables
    qb.leftJoin('organization_units', 'ou_p1', 'ou.parentId = ou_p1.id')
      .leftJoin('organization_units', 'ou_p2', 'ou_p1.parentId = ou_p2.id')
      .leftJoin('organization_units', 'ou_p3', 'ou_p2.parentId = ou_p3.id')
      .leftJoin('organization_units', 'ou_p4', 'ou_p3.parentId = ou_p4.id')
      .leftJoin('organization_units', 'ou_p5', 'ou_p4.parentId = ou_p5.id');

    // Apply filters
    if (query.levelId && query.levelId !== 'undefined') {
      const levelData = await this.getGeneralDataDetail(query.levelId);
      if (levelData) {
        if (levelData.general.code) {
          qb.andWhere('ou.levelGeneralDataParentCode = :levelParentCode', {
            levelParentCode: levelData.general.code,
          });
        }
        if (levelData.detail.code) {
          qb.andWhere('ou.levelGeneralDataDetailCode = :levelDetailCode', {
            levelDetailCode: levelData.detail.code,
          });
        }
      }
    }

    if (query.categoryId && query.categoryId !== 'undefined') {
      const categoryData = await this.getGeneralDataDetail(query.categoryId);
      if (categoryData) {
        if (categoryData.general.code) {
          qb.andWhere('ou.categoryGeneralDataParentCode = :categoryParentCode', {
            categoryParentCode: categoryData.general.code,
          });
        }
        if (categoryData.detail.code) {
          qb.andWhere('ou.categoryGeneralDataDetailCode = :categoryDetailCode', {
            categoryDetailCode: categoryData.detail.code,
          });
        }
      }
    }

    if (query.isActive !== undefined && query.isActive !== 'undefined') {
      qb.andWhere('ou.isActive = :isActive', {
        isActive: query.isActive === 'true' || query.isActive === true,
      });
    }

    if (query.code && query.code !== 'undefined') {
      qb.andWhere('LOWER(ou.code) LIKE LOWER(:code)', { code: `%${query.code}%` });
    }

    if (query.name && query.name !== 'undefined') {
      qb.andWhere('LOWER(ou.name) LIKE LOWER(:name)', { name: `%${query.name}%` });
    }

    if (query.storageLocation && query.storageLocation !== 'undefined') {
      qb.andWhere('LOWER(ou.storageLocation) LIKE LOWER(:storageLocation)', {
        storageLocation: `%${query.storageLocation}%`,
      });
    }

    if (query.parentId && query.parentId !== 'undefined') {
      qb.andWhere('ou.parentId = :parentId', { parentId: query.parentId });
    }

    if (query.createdDateFrom) {
      qb.andWhere('ou.createdDate >= :createdDateFrom', {
        createdDateFrom: new Date(query.createdDateFrom),
      });
    }

    if (query.createdDateTo) {
      qb.andWhere('ou.createdDate <= :createdDateTo', {
        createdDateTo: new Date(query.createdDateTo),
      });
    }

    // Order by the constructed path
    qb.orderBy('full_path', 'ASC');

    // Apply pagination if needed
    if (!query.skipPagination) {
      const pageSize = Number(query.pageSize) || 10;
      const pageIndex = Number(query.pageIndex) || 1;
      const skip = (pageIndex - 1) * pageSize;
      qb.skip(skip).take(pageSize);
    }

    const [items, total] = await qb.getManyAndCount();

    const dictMaterialHandleType: any = {};
    {
      const lstMaterialHandleType = await this.generalDataRepo.findOne({
        where: { code: 'MATERIAL_HANDLING_TYPE' },
      });
      const lstMaterialHandleTypeDetail = await this.generalDataDetailRepo.find({
        generalId: lstMaterialHandleType.id,
      });
      lstMaterialHandleTypeDetail.forEach((i) => (dictMaterialHandleType[i.code] = i));
    }
    // Transform results
    const transformedItems = await Promise.all(
      items.map(async (item) => {
        const hasChildren = await this.organizationUnitRepo.hasChildren(item.id);

        // Get level information
        let level = null;
        if (item.levelId) {
          const levelDetail = await this.generalDataDetailRepo.findOne({
            where: { id: item.levelId },
          });
          if (levelDetail) {
            level = {
              id: levelDetail.id,
              code: levelDetail.code,
              name: levelDetail.name,
            };
          }
        }

        // Get category information
        let category = null;
        if (item.categoryId) {
          const categoryDetail = await this.generalDataDetailRepo.findOne({
            where: { id: item.categoryId },
          });
          if (categoryDetail) {
            category = {
              id: categoryDetail.id,
              code: categoryDetail.code,
              name: categoryDetail.name,
            };
          }
        }

        // Get parent name
        let parentName = null;
        if (item.parentId) {
          const parentUnit = await this.organizationUnitRepo.findOne({
            where: { id: item.parentId },
          });
          if (parentUnit) {
            parentName = parentUnit.name;
          }
        }

        // Initialize level-specific codes
        const levelCodes = {
          codeGroup: '',
          codeSite: '',
          codeFactory: '',
          codeLine: '',
          codeProcess: '',
        };

        // Set code based on levelGeneralDataDetailCode
        if (item.levelGeneralDataDetailCode) {
          const levelCode = item.levelGeneralDataDetailCode.toUpperCase();
          switch (levelCode) {
            case ORGANIZATION_LEVEL.GROUP:
              levelCodes.codeGroup = item.code;
              break;
            case ORGANIZATION_LEVEL.SITE:
              levelCodes.codeSite = item.code;
              break;
            case ORGANIZATION_LEVEL.FACTORY:
              levelCodes.codeFactory = item.code;
              break;
            case ORGANIZATION_LEVEL.LINE:
              levelCodes.codeLine = item.code;
              break;
            case ORGANIZATION_LEVEL.PROCESS:
              levelCodes.codeProcess = item.code;
              break;
          }
        }

        return {
          ...item,
          hasChildren,
          level,
          category,
          parentName,
          materialHandleTypeName: dictMaterialHandleType[item.materialHandleType]
            ? dictMaterialHandleType[item.materialHandleType]?.code +
              ' - ' +
              dictMaterialHandleType[item.materialHandleType]?.name
            : null,
          ...levelCodes,
          full_path: (item as any).full_path,
        };
      }),
    );

    return {
      data: transformedItems,
      total,
      pageSize: Number(query.pageSize) || 10,
      pageIndex: Number(query.pageIndex) || 1,
    };
  }

  async findAll(): Promise<any> {
    const units = await this.organizationUnitRepo.find();

    // Add parentName to each unit
    const unitsWithParentName = await Promise.all(
      units.map(async (unit) => {
        let parentName = null;
        if (unit.parentId) {
          const parentUnit = await this.organizationUnitRepo.findOne({
            where: { id: unit.parentId },
          });
          if (parentUnit) {
            parentName = parentUnit.name;
          }
        }

        return {
          ...unit,
          parentName,
        };
      }),
    );

    return unitsWithParentName;
  }

  async findRoots(): Promise<any[]> {
    const roots = await this.organizationUnitRepo.findRoots();
    return roots;
  }

  async findChildren(parentId: string): Promise<any[]> {
    const children = await this.organizationUnitRepo.findChildren(parentId);

    // Get parent name
    let parentName = null;
    const parentUnit = await this.organizationUnitRepo.findOne({
      where: { id: parentId },
    });
    if (parentUnit) {
      parentName = parentUnit.name;
    }

    // Add parentName to each child
    return children.map((child) => ({
      ...child,
      parentName,
    }));
  }

  async search(query: string): Promise<any[]> {
    const results = await this.organizationUnitRepo.searchByTerm(query);

    // Add parentName to each result
    const resultsWithParentName = await Promise.all(
      results.map(async (unit) => {
        let parentName = null;
        if (unit.parentId) {
          const parentUnit = await this.organizationUnitRepo.findOne({
            where: { id: unit.parentId },
          });
          if (parentUnit) {
            parentName = parentUnit.name;
          }
        }

        return {
          ...unit,
          parentName,
        };
      }),
    );

    return resultsWithParentName;
  }

  async findOne(id: string): Promise<any> {
    const unit = await this.organizationUnitRepo.findOne({ where: { id } });

    if (!unit) {
      throw new NotFoundException(`Organization unit with ID ${id} not found`);
    }

    const hasChildren = await this.organizationUnitRepo.hasChildren(id);

    // Get parent name if parentId exists
    let parentName = null;
    if (unit.parentId) {
      const parentUnit = await this.organizationUnitRepo.findOne({
        where: { id: unit.parentId },
      });
      if (parentUnit) {
        parentName = parentUnit.name;
      }
    }

    // Get level information if levelId exists
    let level = null;
    if (unit.levelId) {
      const levelDetail = await this.generalDataDetailRepo.findOne({
        where: { id: unit.levelId },
      });
      if (levelDetail) {
        level = {
          id: levelDetail.id,
          code: levelDetail.code,
          name: levelDetail.name,
        };
      }
    }

    // Get category information if categoryId exists
    let category = null;
    if (unit.categoryId) {
      const categoryDetail = await this.generalDataDetailRepo.findOne({
        where: { id: unit.categoryId },
      });
      if (categoryDetail) {
        category = {
          id: categoryDetail.id,
          code: categoryDetail.code,
          name: categoryDetail.name,
        };
      }
    }

    // Return a new object with the additional properties
    return {
      ...unit,
      hasChildren,
      parentName,
      level,
      category,
    };
  }

  async findByCode(code: string): Promise<any> {
    const unit = await this.organizationUnitRepo.findByCode(code);

    if (!unit) {
      throw new NotFoundException(`Organization unit with code ${code} not found`);
    }

    const hasChildren = await this.organizationUnitRepo.hasChildren(unit.id);

    // Get parent name if parentId exists
    let parentName = null;
    if (unit.parentId) {
      const parentUnit = await this.organizationUnitRepo.findOne({
        where: { id: unit.parentId },
      });
      if (parentUnit) {
        parentName = parentUnit.name;
      }
    }

    // Get level information if levelId exists
    let level = null;
    if (unit.levelId) {
      const levelDetail = await this.generalDataDetailRepo.findOne({
        where: { id: unit.levelId },
      });
      if (levelDetail) {
        level = {
          id: levelDetail.id,
          code: levelDetail.code,
          name: levelDetail.name,
        };
      }
    }

    // Get category information if categoryId exists
    let category = null;
    if (unit.categoryId) {
      const categoryDetail = await this.generalDataDetailRepo.findOne({
        where: { id: unit.categoryId },
      });
      if (categoryDetail) {
        category = {
          id: categoryDetail.id,
          code: categoryDetail.code,
          name: categoryDetail.name,
        };
      }
    }

    // Return a new object with the additional properties
    return {
      ...unit,
      hasChildren,
      parentName,
      level,
      category,
    };
  }

  async update(id: string, updateData: any): Promise<any> {
    // Check if organization unit exists
    const existingUnit = await this.organizationUnitRepo.findOne({ where: { id } });
    if (!existingUnit) {
      throw new NotFoundException(`Organization unit with ID ${id} not found`);
    }

    // Handle parent field if it's sent as a string instead of an object
    if (updateData.parent && typeof updateData.parent === 'string') {
      updateData.parentId = updateData.parent;
      delete updateData.parent;
    }

    // Convert empty string UUID fields to null
    if (updateData.parentId === '') {
      updateData.parentId = null;
    }
    if (updateData.levelId === '') {
      updateData.levelId = null;
      updateData.levelGeneralDataParentCode = null;
      updateData.levelGeneralDataDetailCode = null;
    }
    if (updateData.categoryId === '') {
      updateData.categoryId = null;
      updateData.categoryGeneralDataParentCode = null;
      updateData.categoryGeneralDataDetailCode = null;
    }

    // Remove any non-entity fields that might cause issues
    const fieldsToRemove = ['hasChildren', 'level', 'category', 'parentName', 'children'];
    fieldsToRemove.forEach((field) => {
      if (field in updateData) {
        delete updateData[field];
      }
    });

    // Handle level reference
    if (updateData.levelId) {
      const levelData = await this.getGeneralDataDetail(updateData.levelId);
      if (levelData) {
        updateData.levelGeneralDataParentCode = levelData.general.code;
        updateData.levelGeneralDataDetailCode = levelData.detail.code;
      }
    }

    // Handle category reference
    if (updateData.categoryId) {
      const categoryData = await this.getGeneralDataDetail(updateData.categoryId);
      if (categoryData) {
        updateData.categoryGeneralDataParentCode = categoryData.general.code;
        updateData.categoryGeneralDataDetailCode = categoryData.detail.code;
      }
    }
    updateData.materialHandleType = updateData?.materialHandleType || null;

    // Update organization unit
    Object.assign(existingUnit, updateData);
    const updatedUnit = await this.organizationUnitRepo.save(existingUnit);

    // Get parent name if parentId exists
    let parentName = null;
    if (updatedUnit.parentId) {
      const parentUnit = await this.organizationUnitRepo.findOne({
        where: { id: updatedUnit.parentId },
      });
      if (parentUnit) {
        parentName = parentUnit.name;
      }
    }

    return {
      ...updatedUnit,
      parentName,
    };
  }

  async remove(id: string): Promise<void> {
    // Check if organization unit exists
    const existingUnit = await this.organizationUnitRepo.findOne({ where: { id } });
    if (!existingUnit) {
      throw new NotFoundException(`Organization unit with ID ${id} not found`);
    }

    // Check if organization unit has children
    const hasChildren = await this.organizationUnitRepo.hasChildren(id);
    if (hasChildren) {
      throw new Error(`Cannot delete organization unit with ID ${id} because it has children`);
    }

    // Delete organization unit
    await this.organizationUnitRepo.remove(existingUnit);
  }

  async getOptions(levelCode?: string): Promise<any[]> {
    // remove if last is ?
    levelCode = levelCode?.replace(/\?$/, '');
    try {
      // Get the ORG_LEVEL general data
      const orgLevelData = await this.generalDataRepo.findOne({
        where: { code: ORGANIZATION_GENERAL_DATA.ORG_LEVEL },
      });

      if (!orgLevelData) {
        return [];
      }

      if (!levelCode) {
        // If no levelCode provided, return all active organization units
        const options = await this.organizationUnitRepo.find({
          where: { isActive: true },
          order: { name: 'ASC' },
        });
        return options.map((option) => ({
          value: option.id,
          label: `${option.code} - ${option.name}`,
          code: option.code,
        }));
      }

      // Get the parent level code based on the provided level code
      const parentLevelCode = getParentLevelCode(levelCode as EOrganizationLevel);

      if (!parentLevelCode) {
        return [];
      }

      // Get the parent level detail
      const parentLevelDetail = await this.generalDataDetailRepo.findOne({
        where: {
          code: ILike(parentLevelCode),
          generalId: orgLevelData.id,
          isActive: true,
        },
      });

      if (!parentLevelDetail) {
        return [];
      }

      // Find all active organizations at the parent level
      const parentOrgs = await this.organizationUnitRepo.find({
        where: {
          levelId: parentLevelDetail.id,
          isActive: true,
        },
        order: { name: 'ASC' },
      });

      // Format label based on level
      const formattedOrgs = await Promise.all(
        parentOrgs.map(async (org) => {
          let label = '';
          let parentSite = null;
          let parentFactory = null;

          switch (levelCode) {
            case EOrganizationLevel.Group:
            case EOrganizationLevel.Site:
              // For Group and Site, show: code-name of Group
              label = `${org.code} - ${org.name}`;
              break;

            case EOrganizationLevel.Factory:
              // For Factory, show: code-name of Site
              label = `${org.code} - ${org.name}`;
              break;

            case EOrganizationLevel.Line:
              // For Line, show: codeSite-code-name of Factory
              parentSite = await this.findParentByLevel(org.parentId, EOrganizationLevel.Site);
              label = parentSite
                ? `${parentSite.code} - ${org.code} - ${org.name}`
                : `${org.code} - ${org.name}`;
              break;

            case EOrganizationLevel.Process:
              // For Process, show: codeSite+codeFactory-code-name of Line
              parentFactory = await this.findParentByLevel(
                org.parentId,
                EOrganizationLevel.Factory,
              );
              if (parentFactory) {
                parentSite = await this.findParentByLevel(
                  parentFactory.parentId,
                  EOrganizationLevel.Site,
                );
              }

              if (parentSite && parentFactory) {
                label = `${parentSite.code}${parentFactory.code} - ${org.code} - ${org.name}`;
              } else {
                label = `${org.code} - ${org.name}`;
              }
              break;

            default:
              label = `${org.code} - ${org.name}`;
          }

          return {
            value: org.id,
            label,
            levelId: org.levelId,

            code: org.code,
          };
        }),
      );

      return formattedOrgs;
    } catch (error) {
      console.error('Error in getOptions:', error);
      return [];
    }
  }

  private async findParentByLevel(
    parentId: string | null,
    levelCode: EOrganizationLevel,
  ): Promise<any | null> {
    if (!parentId) return null;

    const levelDetail = await this.generalDataDetailRepo.findOne({
      where: {
        code: levelCode,
        isActive: true,
      },
    });

    if (!levelDetail) return null;

    const parent = await this.organizationUnitRepo.findOne({
      where: {
        id: parentId,
        levelId: levelDetail.id,
        isActive: true,
      },
    });

    return parent;
  }

  async getHierarchyFromProcess(processAreaId: string) {
    const current = await this.organizationUnitRepo.findOne({
      where: { id: processAreaId },
      relations: ['parent', 'parent.parent', 'parent.parent.parent', 'parent.parent.parent.parent'],
    });

    const line = current?.parent;
    const factory = line?.parent;
    const site = factory?.parent;
    const group = site?.parent;

    return {
      data: {
        processAreaId: current?.id,
        processAreaCode: current?.code,
        processAreaName: current?.name,
        lineId: line?.id ?? null,
        lineCode: line?.code ?? null,
        lineName: line?.name ?? null,
        factoryId: factory?.id ?? null,
        factoryCode: factory?.code ?? null,
        factoryName: factory?.name ?? null,
        siteId: site?.id ?? null,
        siteCode: site?.code ?? null,
        siteName: site?.name ?? null,
        groupId: group?.id ?? null,
        groupCode: group?.code ?? null,
        groupName: group?.name ?? null,
      },
      meta: {
        request: { processAreaId },
      },
    };
  }
}
