import { Injectable, NotFoundException } from '@nestjs/common';
import axios from 'axios';
import { Between, Raw } from 'typeorm';
import { BindRepo, DefTransaction } from '~/@core/decorator';
import { BusinessException } from '~/@systems/exceptions';
import { getFirstDayTz, getLastDayTz } from '~/@systems/utils';
import { NSSetting } from '~/common/enums/NSSetting.enum';
import { RejectionReasonPageReq } from '~/dto/rejection-reason.dto';
import { RejectionReasonEntity } from '~/entities/primary';
import { update } from '~/functions/general-data';
import { RejectionReasonRepo, SettingStringRepo } from '~/repositories/primary';
import { configEnv } from '~/@config/env';
const { QLONE_URL } = configEnv();
@Injectable()
export class RejectionReasonService {
  @BindRepo(RejectionReasonRepo)
  private repo: RejectionReasonRepo;

  @BindRepo(SettingStringRepo)
  private settingStringRepo: SettingStringRepo;

  private qloneUrl = QLONE_URL;

  constructor() {}

  @DefTransaction()
  async refresh(): Promise<any> {
    const { IdToken } = await this.getTokenQloneCognito();
    const res = await axios.get(this.qloneUrl, {
      headers: {
        Authorization: `Bearer ${IdToken}`,
      },
    });
    const lstDataRefresh = res?.data?.body || [];
    const lstDataTaken = await this.repo.find({ where: {}, select: ['id', 'code'] });
    const mapDataTaken = new Map(lstDataTaken.map((item) => [item.code, item]));

    const lstInsert = [];
    const lstUpdate = [];
    for (let item of lstDataRefresh) {
      const curTaken = mapDataTaken.get(item.code);
      // Nếu chưa có thì thêm mới
      if (!curTaken) {
        const newRejReason = new RejectionReasonEntity();
        newRejReason.code = item.code;
        newRejReason.name = item.description;
        newRejReason.status = 1;
        // newRejReason.createdBy = '-1';
        lstInsert.push(newRejReason);
      } else {
        // Nếu đã có thì cập nhật lại
        lstUpdate.push(
          this.repo.update(curTaken.id, {
            status: 1,
            name: item.description,
            // updatedBy: '-1',
            updatedDate: new Date(),
          }),
        );
      }
    }

    await Promise.all(lstUpdate);
    await this.repo.insert(lstInsert);
    return { message: 'data refresh successful' };
  }

  async pagination(params: RejectionReasonPageReq) {
    const whereCon: any = {};
    if (params.code) {
      whereCon.code = Raw((alias) => `LOWER(${alias}) LIKE LOWER(:code)`, {
        code: `%${params.code}%`,
      });
    }
    if (params.name) {
      whereCon.name = Raw((alias) => `LOWER(${alias}) LIKE LOWER(:name)`, {
        name: `%${params.name}%`,
      });
    }
    if (params.updatedDate?.length === 2) {
      const fd = getFirstDayTz(params.updatedDate[0]);
      const ld = getLastDayTz(params.updatedDate[1]);
      whereCon.updatedDate = Between(fd, ld);
    }
    if ([true, false, 0, 1].includes(params.status)) whereCon.status = params.status ? 1 : 0;

    const res: any = await this.repo.findPagination(
      {
        where: whereCon,
        order: { code: 'ASC' },
      },
      params,
    );
    return res;
  }

  findAll(): Promise<RejectionReasonEntity[]> {
    return this.repo.find();
  }

  async findOne(id: string): Promise<RejectionReasonEntity> {
    const reason = await this.repo.findOne({ where: { id } });
    if (!reason) throw new NotFoundException(`Không tìm thấy lý do với id ${id}`);
    return reason;
  }

  /** Lấy thông tin token để xác thực qua hệ thống QLONE */
  async getTokenQloneCognito() {
    const checkSetting = await this.settingStringRepo.findOne({
      where: { code: NSSetting.ESettingCode.COGNITO_TOKEN },
    });

    // Nếu token lưu trong db còn hạn thì lấy từ db ra
    if (checkSetting?.value) {
      const objValue = JSON.parse(checkSetting.value);
      const { expiredTime, IdToken } = objValue;
      const curTime = new Date().getTime();
      if (expiredTime > curTime && IdToken) {
        return { IdToken };
      }
    }

    const payload = {
      AuthFlow: 'USER_PASSWORD_AUTH',
      ClientId: process.env.CLIENT_ID,
      AuthParameters: {
        USERNAME: process.env.USERNAME_COGNITO,
        PASSWORD: process.env.PASSWORD_COGNITO,
      },
    };
    const cognitoEndpoint = `https://cognito-idp.${process.env.REGION}.amazonaws.com/`;

    try {
      const res = await axios.post(cognitoEndpoint, payload, {
        headers: {
          'X-Amz-Target': 'AWSCognitoIdentityProviderService.InitiateAuth',
          'Content-Type': 'application/x-amz-json-1.1',
        },
      });
      const { AccessToken, IdToken, RefreshToken, ExpiresIn } = res?.data?.AuthenticationResult;
      if (!IdToken) throw new BusinessException('Hệ thống QLONE không trả về IdToken');
      const expiredTime = new Date().getTime() + (ExpiresIn - 5 * 60) * 1000; // 5 phút trước khi hết hạn

      // Lưu lại token vào db sử dụng cho lần gọi sau
      if (checkSetting) {
        await this.settingStringRepo.update(checkSetting.id, {
          value: JSON.stringify({ AccessToken, IdToken, RefreshToken, expiredTime }),
          updatedDate: new Date(),
        });
      } else {
        await this.settingStringRepo.insert({
          code: NSSetting.ESettingCode.COGNITO_TOKEN,
          type: NSSetting.EDataType.STRING,
          value: JSON.stringify({ AccessToken, IdToken, RefreshToken, expiredTime }),
          createdBy: '-1',
        });
      }
      return { IdToken };
    } catch (error) {
      throw new BusinessException(
        'Lỗi khi lấy token từ hệ thống QLONE, chi tiết: ' + error.toString(),
      );
    }
  }
}
