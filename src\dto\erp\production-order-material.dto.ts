import { IsOptional, IsString } from 'class-validator';

export class GetProductionOrderMaterialTransactionsRequest {
  @IsOptional()
  @IsString()
  orderNo?: string;

  @IsOptional()
  @IsString()
  site?: string;

  @IsOptional()
  @IsString()
  factory?: string;

  @IsOptional()
  @IsString()
  fromtransactionDate?: string;

  @IsOptional()
  @IsString()
  totransactionDate?: string;

  @IsOptional()
  @IsString()
  fromUpdatedDate?: string;

  @IsOptional()
  @IsString()
  toUpdatedDate?: string;
}
