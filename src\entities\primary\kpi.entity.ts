import { Column, Entity } from 'typeorm';
import { PrimaryBaseEntity } from '../primary-base.entity';

/** Bảng KPI Rule */
@Entity('kpi')
export class KpiEntity extends PrimaryBaseEntity {
  /** Mã ngành hàng */
  @Column({ nullable: false, type: 'varchar', length: 15 })
  kpiCategory: string;

  /** ID ngành hàng */
  @Column({ nullable: true, type: 'uuid' })
  kpiCategoryId: string;

  /** Mã nhóm KPI */
  @Column({ nullable: true, type: 'varchar', length: 15 })
  kpiGroup: string;

  /** ID nhóm KPI */
  @Column({ nullable: true, type: 'uuid' })
  kpiGroupId: string;

  /** Mã KPI */
  @Column({ nullable: true, type: 'varchar', length: 15 })
  code: string;

  /** Tên ngắn KPI */
  @Column({ nullable: true, type: 'varchar', length: 128 })
  shortName: string;

  /** Tên đầy đủ KPI */
  @Column({ nullable: true, type: 'varchar', length: 256 })
  longName: string;

  /** Mã Đơn vị KPI */
  @Column({ nullable: true, type: 'varchar', length: 15 })
  unit: string;

  /** Id đơn vị KPI */
  @Column({ nullable: true, type: 'uuid' })
  unitId: string;

  /** Mã phương thức ghi nhận KPI */
  @Column({ nullable: true, type: 'varchar', length: 15 })
  kpiMethod: string;

  /** ID phương thức ghi nhận KPI */
  @Column({ nullable: true, type: 'uuid' })
  kpiMethodId: string;

  /** Mã chức năng tự động */
  @Column({ nullable: true, type: 'varchar', length: 128 })
  autoFunction?: string;

  /** ID chức năng tự động */
  @Column({ nullable: true, type: 'uuid' })
  autoFunctionId?: string;

  /** Mã Loại tính toán */
  @Column({ nullable: true, type: 'varchar', length: 15 })
  calType?: string;

  /** ID loại tính toán */
  @Column({ nullable: true, type: 'uuid' })
  calTypeId?: string;

  /** Mã tần suất nhập liệu */
  @Column({ nullable: true, type: 'varchar', length: 15 })
  inputFrequency: string;

  /** ID tần suất nhập liệu */
  @Column({ nullable: true, type: 'uuid' })
  inputFrequencyId: string;

  /** Mã phương pháp tính toán */
  @Column({ nullable: true, type: 'varchar', length: 15 })
  calMethod: string;

  /** ID phương pháp tính toán */
  @Column({ nullable: true, type: 'uuid' })
  calMethodId: string;

  /** Trạng thái */
  @Column({ nullable: true, default: 1 })
  status: number;

  /** Các tham số */
  @Column({ nullable: true, type: 'varchar', length: 128 })
  materialGroup?: string;

  @Column({ nullable: true, type: 'uuid' })
  materialGroupId?: string;

  @Column({ nullable: true, type: 'varchar', length: 128 })
  utilityType?: string;

  @Column({ nullable: true, type: 'uuid' })
  utilityTypeId?: string;

  @Column({ nullable: true, type: 'varchar', length: 128 })
  parameter3?: string;

  @Column({ nullable: true, type: 'varchar', length: 128 })
  parameter4?: string;

  @Column({ nullable: true, type: 'varchar', length: 128 })
  parameter5?: string;

  @Column({ nullable: true, type: 'varchar', length: 128 })
  parameter6?: string;
}
