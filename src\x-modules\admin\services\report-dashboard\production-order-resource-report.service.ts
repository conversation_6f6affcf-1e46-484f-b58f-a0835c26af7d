import { BadRequestException, Injectable, Logger } from '@nestjs/common';
import { Between, ILike, In } from 'typeorm';
import * as ExcelJS from 'exceljs';
import * as path from 'path';
import * as os from 'os';
import * as fs from 'fs';
import { BindRepo } from '~/@core/decorator';
import { PageResponse } from '~/@systems/utils/page.utils';
import { NSGeneralData } from '~/common/enums';
import { dayjs, TZ } from '~/common/helpers/date.helper';
import { adminSessionContext } from '~/x-modules/admin/admin-session.context';
import { S3PrivateService } from '~/x-modules/@global/services';
import { ProductionOrderEntity, ProductionOrderResourceEntity } from '~/entities/primary';
import {
  GeneralDataDetailRepo,
  GeneralDataRepo,
  OrganizationUnitRepo,
  ProcessRepo,
  ProductionOrderRepo,
  ProductionOrderResourceRepo,
  ReportHistoryRepo,
  ShiftRepo,
  UserRepo,
} from '~/repositories/primary';
import { CreateReportDto, ListFilterReportDto } from '~/dto/report-history.dto';

const REPORT = { reportCode: '2', reportName: 'Production Order Resource' };

@Injectable()
export class ProductionOrderResourceReportService {
  constructor(private readonly s3PrivateService: S3PrivateService) {}

  @BindRepo(ReportHistoryRepo)
  private reportHistoryRepo: ReportHistoryRepo;
  @BindRepo(ProductionOrderRepo)
  private productionOrderRepo: ProductionOrderRepo;
  @BindRepo(ProductionOrderResourceRepo)
  private productionOrderResourceRepo: ProductionOrderResourceRepo;
  @BindRepo(ShiftRepo)
  private shiftRepo: ShiftRepo;
  @BindRepo(OrganizationUnitRepo)
  private organizationUnitRepo: OrganizationUnitRepo;
  @BindRepo(GeneralDataRepo)
  private generalDataRepo: GeneralDataRepo;
  @BindRepo(ProcessRepo)
  private processRepo: ProcessRepo;
  @BindRepo(GeneralDataDetailRepo)
  private generalDataDetailRepo: GeneralDataDetailRepo;
  @BindRepo(UserRepo)
  private userRepo: UserRepo;

  async search(req: ListFilterReportDto): Promise<PageResponse> {
    const { createdByUser, email, fromDate, toDate, pageIndex, pageSize } = req;
    // Parse date dạng ISO -> Date object để truy vấn chuẩn
    const start = fromDate ? dayjs(fromDate).toDate() : undefined;
    const end = toDate ? dayjs(toDate).toDate() : undefined;

    const [data, total] = await this.reportHistoryRepo.findAndCount({
      where: {
        reportCode: REPORT.reportCode,
        ...(createdByUser && { createdByUser: ILike(`%${createdByUser}%`) }),
        ...(email && { email: ILike(`%${email}%`) }),
        ...(start && end && { createdDate: Between(start, end) }),
      },
      order: { createdDate: 'DESC' },
      skip: pageIndex && pageSize ? (pageIndex - 1) * pageSize : undefined,
      take: pageIndex && pageSize ? pageSize : undefined,
    });

    const signedUrls = await Promise.all(
      data.map(async (item) => {
        const signedUrl = await this.s3PrivateService.getS3Link(item.linkDownload ?? ''); // 1h
        return signedUrl;
      }),
    );
    data.forEach((item, index) => {
      item.linkDownload = signedUrls[index];
    });

    return { data: data, meta: { totalRecords: total, request: req } };
  }

  async submitReport(req: CreateReportDto) {
    const beginAt = dayjs.utc(Date.now()).tz(TZ).format('YYYYMMDDHHmmss');
    const {
      siteIdLst,
      factoryIdLst,
      lineIdLst,
      orderNo,
      statusIdLst,
      fromDate,
      toDate,
      batchNumber,
    } = req;

    const { userId, userName, userEmail } = adminSessionContext;

    const report = this.reportHistoryRepo.create({
      reportCode: REPORT.reportCode,
      reportName: REPORT.reportName,
      status: NSGeneralData.EReportStatus.RUNNING,
      input: req,
      createdBy: userId,
      createdByUser: userName,
      email: userEmail,
    });
    const reportSaved = await this.reportHistoryRepo.save(report);

    try {
      let result: any[] = [];

      // Parse date dạng ISO -> Date object để truy vấn chuẩn
      const start = fromDate ? dayjs(fromDate).toDate() : undefined;
      const end = toDate ? dayjs(toDate).toDate() : undefined;

      const productionOrderLst = await this.productionOrderRepo.find({
        where: {
          ...(siteIdLst?.length && { siteId: In(siteIdLst) }),
          ...(factoryIdLst?.length && { factoryId: In(factoryIdLst) }),
          ...(lineIdLst?.length && { lineId: In(lineIdLst) }),
          ...(orderNo && { orderNo: ILike(`%${orderNo}%`) }),
          ...(statusIdLst?.length && { orderStatus: In(statusIdLst) }),
          ...(start && end && { planStartDate: Between(start, end) }),
        },
      });

      const data = productionOrderLst.length
        ? await this.buildPOResourceReport(productionOrderLst)
        : [];

      // Xuất dữ liệu ra file excel (giả sử return đường dẫn file tạm local)
      const localFilePath = await this.exportExcel(data);

      // Upload file lên S3
      const fileName = `report_${reportSaved.reportCode}_${beginAt}`;

      const { fileUrl } = await this.s3PrivateService.uploadS3(
        {
          // Giả sử exportExcel trả về Buffer hoặc Stream,
          // Nếu exportExcel trả về path, bạn có thể đọc file thành Buffer ở đây
          buffer: await fs.promises.readFile(localFilePath),
          originalname: fileName + '.xlsx',
        } as Express.Multer.File,
        fileName,
        'private',
      );

      const reportCompleted = await this.reportHistoryRepo.save({
        ...reportSaved,
        linkDownload: fileUrl,
        output: result,
        outputTotal: result.length,
        status: NSGeneralData.EReportStatus.COMPLETED,
        completedDate: new Date(),
      });

      return { data: [reportCompleted], meta: { request: req } };
    } catch (err) {
      Logger.error(err);
      await this.reportHistoryRepo.save({
        ...reportSaved,
        status: NSGeneralData.EReportStatus.ERROR,
        output: {
          errorMessage: err?.message ?? 'Unknown Error',
        },
        completedDate: new Date(),
      });
      throw new BadRequestException(`Submit report failed: ${err.message}`);
    }
  }

  private async buildPOResourceReport(
    productionOrderLst: ProductionOrderEntity[],
  ): Promise<ProductionOrderResourceReportOutput[]> {
    // 1. Tạo map từ các thông tin có sẵn của các đơn hàng
    const siteIds = productionOrderLst.map((order) => order.siteId);
    const factoryIds = productionOrderLst.map((order) => order.factoryId);
    const lineIds = productionOrderLst.map((order) => order.lineId);
    const processAreaIds = productionOrderLst.map((order) => order.processAreaId);
    const shiftIds = productionOrderLst.map((order) => order.shiftId);
    const orderStatusIds = productionOrderLst.map((order) => order.orderStatus);
    const operatorIds = productionOrderLst.map((order) => order.operatorUserId);

    // Lấy các giá trị duy nhất
    const uniq = <T>(array: T[]) => Array.from(new Set(array));

    const [sites, factories, lines, processAreas, shifts, statuses, operators] = await Promise.all([
      this.organizationUnitRepo.find({ where: { id: In(uniq(siteIds)) } }),
      this.organizationUnitRepo.find({ where: { id: In(uniq(factoryIds)) } }),
      this.organizationUnitRepo.find({ where: { id: In(uniq(lineIds)) } }),
      this.organizationUnitRepo.find({ where: { id: In(uniq(processAreaIds)) } }),
      this.shiftRepo.find({ where: { id: In(uniq(shiftIds)) } }),
      this.generalDataDetailRepo.find({ where: { id: In(uniq(orderStatusIds)) } }),
      this.userRepo.find({ where: { id: In(uniq(operatorIds)) } }),
    ]);

    const siteMap = new Map(sites.map((site) => [site.id, site]));
    const factoryMap = new Map(factories.map((factory) => [factory.id, factory]));
    const lineMap = new Map(lines.map((line) => [line.id, line]));
    const processAreaMap = new Map(processAreas.map((pa) => [pa.id, pa]));
    const shiftMap = new Map(shifts.map((s) => [s.id, s]));
    const statusMap = new Map(statuses.map((st) => [st.id, st]));
    const operatorMap = new Map(operators.map((op) => [op.id, op]));

    // 2. Lấy tất cả production order resources của các đơn hàng cùng một lúc
    const orderIds = productionOrderLst.map((order) => order.id);
    const allOrderResources = await this.productionOrderResourceRepo.find({
      where: { orderId: In(orderIds) },
    });
    // Gom nhóm resources theo orderId
    const orderResourceMap = new Map<string, ProductionOrderResourceEntity[]>();
    allOrderResources.forEach((resource) => {
      const arr = orderResourceMap.get(resource.orderId) || [];
      arr.push(resource);
      orderResourceMap.set(resource.orderId, arr);
    });

    // 3. Thu thập các processId từ các resource và truy vấn nhanh tất cả
    const processIds = uniq(allOrderResources.map((resource) => resource.processId));
    const processes = processIds.length ? await this.processRepo.findByIds(processIds) : [];
    const processMap = new Map(processes.map((p) => [p.id, p]));

    const outputs: ProductionOrderResourceReportOutput[] = [];

    // 4. Lặp qua các Production Order
    for (const order of productionOrderLst) {
      const resources = orderResourceMap.get(order.id) || [];
      for (const resource of resources) {
        const output = new ProductionOrderResourceReportOutput();

        // Các thông tin lấy từ map
        const site = siteMap.get(order.siteId);
        const factory = factoryMap.get(order.factoryId);
        const line = lineMap.get(order.lineId);
        const processArea = processAreaMap.get(order.processAreaId);
        const shift = shiftMap.get(order.shiftId);
        const status = statusMap.get(order.orderStatus);
        const operator = operatorMap.get(order.operatorUserId);
        const process = processMap.get(resource.processId);

        output.site = site ? `${site.code} - ${site.name}` : null;
        output.factory = factory ? `${factory.code} - ${factory.name}` : null;
        output.line = line ? `${line.code} - ${line.name}` : null;
        output.processArea = processArea ? `${processArea.code} - ${processArea.name}` : null;
        output.productionOrderNo = order.orderNo;
        output.recipeCode = order.recipeCode;
        output.recipeVer = order.recipeVersion;
        output.orderQty = order.quantity;
        output.orderUom = order.trxUom;
        output.productionDate = order.planStartDate;
        output.shift = shift ? `${shift.code} - ${shift.description}` : null;
        output.orderStatus = status ? `${status.code} - ${status.name}` : null;
        output.createdBy = order.createdBy;
        output.creationDate = order.createdDate;
        output.operator = operator ? `${operator.employeeCode} - ${operator.fullName}` : null;

        // Lấy thông tin process của resource
        output.processCode = process ? process.code : null;
        output.processName = process ? process.name : null;

        // Các trường resource đặc thù
        output.resourceCode = resource.resourceCode;
        output.uom = resource.uom;
        output.planProdQty = resource.planProdQty;
        output.actualProdQty = resource.actualProdQty;
        output.planQty = resource.planResourceUsage;
        output.actualQty = resource.actualResourceUsage;

        outputs.push(output);
      }
    }

    return outputs;
  }

  private async exportExcel(data: ProductionOrderResourceReportOutput[]): Promise<string> {
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('Report');

    // Định nghĩa header
    worksheet.columns = [
      { header: 'No', key: 'index', width: 5 },
      { header: 'Site', key: 'site', width: 20 },
      { header: 'Factory', key: 'factory', width: 20 },
      { header: 'Line', key: 'line', width: 20 },
      { header: 'Process Area', key: 'processArea', width: 20 },
      { header: 'Production Order No', key: 'productionOrderNo', width: 25 },
      { header: 'Recipe Code', key: 'recipeCode', width: 20 },
      { header: 'Recipe Version', key: 'recipeVer', width: 15 },
      { header: 'Order Quantity', key: 'orderQty', width: 15 },
      { header: 'Order UOM', key: 'orderUom', width: 10 },
      { header: 'Production Date', key: 'productionDate', width: 20 },
      { header: 'Shift', key: 'shift', width: 15 },
      { header: 'Order Status', key: 'orderStatus', width: 20 },
      { header: 'Created By', key: 'createdBy', width: 20 },
      { header: 'Creation Date', key: 'creationDate', width: 20 },
      { header: 'Operator', key: 'operator', width: 25 },
      { header: 'Process Code', key: 'processCode', width: 20 },
      { header: 'Process Name', key: 'processName', width: 25 },
      { header: 'Resource Code', key: 'resourceCode', width: 20 },
      { header: 'UOM', key: 'uom', width: 10 },
      { header: 'Planned Qty', key: 'planQty', width: 15 },
      { header: 'Actual Qty', key: 'actualQty', width: 15 },
    ];

    // Thêm dữ liệu
    data.forEach((item, index) => {
      worksheet.addRow({
        index: index + 1,
        site: item.site,
        factory: item.factory,
        line: item.line,
        processArea: item.processArea,
        productionOrderNo: item.productionOrderNo,
        recipeCode: item.recipeCode,
        recipeVer: item.recipeVer,
        orderQty: item.orderQty,
        orderUom: item.orderUom,
        productionDate: dayjs.utc(item.productionDate).tz(TZ).format('DD-MM-YYYY HH:mm:ss'),
        shift: item.shift,
        orderStatus: item.orderStatus,
        createdBy: item.createdBy,
        creationDate: dayjs.utc(item.creationDate).tz(TZ).format('DD-MM-YYYY HH:mm:ss'),
        operator: item.operator,
        processCode: item.processCode,
        processName: item.processName,
        resourceCode: item.resourceCode,
        uom: item.uom,
        planQty: item.planQty,
        actualQty: item.actualQty,
      });
    });

    // Tạo đường dẫn file tạm thời
    const filePath = path.join(os.tmpdir(), `report-${Date.now()}.xlsx`);

    // Ghi file
    await workbook.xlsx.writeFile(filePath);

    return filePath;
  }

  async getMetaLines(req: { factoryIdLst?: string[] }): Promise<PageResponse<any>> {
    const lines = await this.organizationUnitRepo.find({
      where: {
        isActive: true,
        levelGeneralDataDetailCode: NSGeneralData.EOrgLevel.LINE,
        ...(req?.factoryIdLst && { parentId: In(req.factoryIdLst) }),
      },
      order: { code: 'ASC', createdDate: 'DESC' },
    });
    return { data: lines };
  }

  async getMetaOrderStatus(): Promise<PageResponse<any>> {
    const genData = await this.generalDataRepo.find({
      where: {
        isActive: true,
        code: NSGeneralData.EGeneralDataCode.ORDER_STATUS,
      },
      select: ['id', 'code'],
    });

    const details = await this.generalDataDetailRepo.find({
      where: {
        isActive: true,
        generalId: In(genData.map((item) => item.id)),
      },
      order: { code: 'ASC' },
      select: ['id', 'code', 'name'],
    });

    return { data: details, total: details.length };
  }
}

class ProductionOrderResourceReportOutput {
  site: string; // code - name
  factory: string; // code - name
  line: string; // code - name
  processArea: string; // code - name
  // production_order
  productionOrderNo: string;
  recipeCode: string;
  recipeVer: number;
  orderQty: number;
  orderUom: string;
  productionDate: Date; // YYYY-MM-DD
  // shift
  shift: string; // code - description
  // status
  orderStatus: string; // name
  createdBy: string;
  creationDate: Date;
  // operator
  operator: string; // employeeCode - employeeName
  // production_order_resource
  processCode: string;
  processName: string;
  resourceCode: string;
  resourceName: string;
  uom: string;
  planProdQty: number;
  actualProdQty: number;
  planQty: number;
  actualQty: number;
}
