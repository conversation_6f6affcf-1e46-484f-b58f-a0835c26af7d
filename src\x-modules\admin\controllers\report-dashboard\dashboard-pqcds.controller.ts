import { Body, Controller, UseGuards } from '@nestjs/common';
import { DefPost } from '~/@core/decorator';
import { Roles } from '~/@core/decorator/roles.decorator';
import { RoleGuard } from '~/@systems/guard/role.guard';
import { filterReq } from '~/dto/dashboard-pqcds.dto';
import { DashboardPqcdsService } from '~/x-modules/admin/services';

@Controller('dashboard-pqcds')
export class DashboardPqcdsController {
  constructor(private readonly dashboardPqcdsService: DashboardPqcdsService) {}

  @DefPost('calculate-oee')
  @Roles('/report/pqcds-dashboard', 'View')
  @UseGuards(RoleGuard)
  async calculateOEE(@Body() dto: filterReq) {
    return this.dashboardPqcdsService.getProductionOee(dto);
  }

  @DefPost('calculate-kpi')
  @Roles('/report/pqcds-dashboard', 'View')
  @UseGuards(RoleGuard)
  async getKpi(@Body() dto: filterReq) {
    return this.dashboardPqcdsService.getKpi(dto);
  }
  @DefPost('calculate-kpi-manual')
  @Roles('/report/pqcds-dashboard', 'View')
  @UseGuards(RoleGuard)
  async getKpiManual(@Body() dto: filterReq) {
    return this.dashboardPqcdsService.getKpiManual(dto);
  }

  @DefPost('calculate-kpi-product')
  @Roles('/report/pqcds-dashboard', 'View')
  @UseGuards(RoleGuard)
  async getKpiProductOutput(@Body() dto: filterReq) {
    return this.dashboardPqcdsService.getKpiProductOutput(dto);
  }
  @DefPost('calculate-he')
  @Roles('/report/pqcds-dashboard', 'View')
  @UseGuards(RoleGuard)
  async getHe(@Body() dto: filterReq) {
    return this.dashboardPqcdsService.calculateHe(dto);
  }
}
