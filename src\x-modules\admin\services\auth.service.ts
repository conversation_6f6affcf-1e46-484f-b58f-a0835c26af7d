import { RolePermission } from './../../../common/enums/enumRole.enum';
import { Injectable } from '@nestjs/common';
import { JwtPayload } from 'jsonwebtoken';
import * as jwt from 'jsonwebtoken';
import axios from 'axios';
import jwkToPem from 'jwk-to-pem';
import { configEnv } from '~/@config/env';
import { BindRepo } from '~/@core/decorator';
import { UserRepo } from '~/repositories/primary/user.repo';
import { TokenExpiredError } from 'jsonwebtoken';
@Injectable()
export class AuthService {
  @BindRepo(UserRepo)
  private userRepo: UserRepo;
  private readonly msConfig = {
    clientId: configEnv().MS_CLIENT_ID,
    clientSecret: configEnv().MS_CLIENT_SECRET,
    tenantId: configEnv().MS_TENANT_ID,
  };

  private readonly msDiscoveryUrl = `https://login.microsoftonline.com/${this.msConfig.tenantId}/discovery/v2.0/keys`;
  private jwksCache: any = null;
  private jwksCacheTime: number = 0;
  private readonly cacheDuration = 24 * 60 * 60 * 1000; // 24 hours

  async validateMsToken(token: string): Promise<{
    isValid: boolean;
    payload?: JwtPayload;
    error?: string;
  }> {
    try {
      // Decode token để lấy header và payload
      const decodedToken = jwt.decode(token, { complete: true });
      if (!decodedToken) {
        return { isValid: false, error: 'Token failed' };
      }

      // Kiểm tra clientId và tenantId
      const payload = decodedToken.payload as any;

      // Kiểm tra appId (có thể nằm trong aud hoặc aud)
      const appId = Array.isArray(payload.aud) ? payload.aud[0] : payload.aud;
      if (appId !== this.msConfig.clientId) {
        return { isValid: false, error: 'Client ID failed' };
      }

      // Kiểm tra tenantId (có thể nằm trong tid hoặc tenant id trong iss)
      const tokenTenantId = payload.tid || (payload.iss && payload.iss.split('/')[3]);
      if (tokenTenantId !== this.msConfig.tenantId) {
        return { isValid: false, error: 'Tenant ID failed' };
      }

      // Lấy JWKS từ Microsoft
      const jwks = await this.getMicrosoftJwks();

      // Tìm key matching với token
      const key = jwks.keys.find((k: any) => k.kid === decodedToken.header.kid);

      if (!key) {
        return { isValid: false, error: 'Key not found' };
      }

      // Convert JWK to PEM using jwk-to-pem library
      const pem = jwkToPem(key);

      // Verify token
      const verified = jwt.verify(token, pem, {
        algorithms: ['RS256'],
      }) as JwtPayload;

      return {
        isValid: true,
        payload: verified,
      };
    } catch (error) {
      console.error('Token validation error:', error);
      // // Kiểm tra lỗi có phải TokenExpiredError không
      // if (error instanceof TokenExpiredError) {
      //   // token expired, gọi hàm refresh token
      //   const refreshToken = await this.getRefreshTokenFromStore(); // Lấy refresh token từ DB hoặc session
      //   if (refreshToken) {
      //     const newToken = await this.refreshMsToken(refreshToken);
      //     if (newToken.access_token) {
      //       return {
      //         isValid: true,
      //         payload: jwt.decode(newToken.access_token, { complete: true }) as JwtPayload,
      //       };
      //     }
      //   }
      //   return { isValid: false, error: 'Token expired and refresh failed' };
      // }

      // // Các lỗi khác trả về thông báo lỗi
      // return {
      //   isValid: false,
      //   error: error.message || 'Token validation failed',
      // };
      return {
        isValid: false,
        error: error.message,
      };
    }
  }

  private async getMicrosoftJwks() {
    // Kiểm tra cache
    if (this.jwksCache && Date.now() - this.jwksCacheTime < this.cacheDuration) {
      return this.jwksCache;
    }

    // Lấy discovery document
    const { data } = await axios.get(this.msDiscoveryUrl);

    // Cập nhật cache
    this.jwksCache = data;
    this.jwksCacheTime = Date.now();

    return data;
  }

  async getUserRoles(userId: string) {
    const userPermission = await this.userRepo.findOne({
      where: { id: userId },
      relations: ['permission'],
    });

    if (!userPermission) {
      return [];
    }

    let lstEnumRole = [];

    try {
      const permission = await userPermission.permission;

      // Parse chỉ phần `treeNodes`
      const { treeNodes } = JSON.parse(permission.roleStringify) || {};

      if (!treeNodes) {
        return [];
      }

      lstEnumRole = this.getListRole(treeNodes, lstEnumRole);
      lstEnumRole = lstEnumRole.filter((item) => item.checked);

      return lstEnumRole;
    } catch (error) {
      console.error('Error parsing treeNodes:', error);
      return [];
    }
  }

  private getListRole(lstRole, lstEnumRole) {
    for (let role of lstRole) {
      const dataRole = role?.[0] || role;
      if (dataRole?.children?.length) {
        this.getListRole(dataRole.children, lstEnumRole);
      } else {
        lstEnumRole.push({
          ...dataRole,
        });
      }
    }
    return lstEnumRole;
  }

  async refreshMsToken(refreshToken: string): Promise<{ access_token?: string; error?: string }> {
    try {
      const params = new URLSearchParams();
      params.append('client_id', this.msConfig.clientId);
      params.append('scope', 'openid profile offline_access'); // scope cần thiết
      params.append('refresh_token', refreshToken);
      params.append('grant_type', 'refresh_token');
      params.append('client_secret', this.msConfig.clientSecret);

      const response = await axios.post(
        `https://login.microsoftonline.com/${this.msConfig.tenantId}/oauth2/v2.0/token`,
        params,
        { headers: { 'Content-Type': 'application/x-www-form-urlencoded' } },
      );

      return { access_token: response.data.access_token };
    } catch (error) {
      console.error('Refresh token error:', error.response?.data || error.message);
      return { error: 'Failed to refresh token' };
    }
  }
}
