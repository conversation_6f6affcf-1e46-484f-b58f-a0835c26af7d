import { DeleteMessageCommand, ReceiveMessageCommand, SQSClient } from '@aws-sdk/client-sqs';
import { Injectable, OnModuleInit } from '@nestjs/common';
import { configEnv } from '~/@config/env';
import { NSSQSS } from '~/common/enums';
import {
  SitewiseInService,
  ProdBatchParametersService,
  ProductOutputByRejection,
  ProdOrderMaterialTransactionService,
  CalculateUtilityUsage,
  ProductionOeeService,
  CalculateKansuiAndSeasoningService,
  TransactionProductOutput,
  CallProcedureService,
  UtilityConsumptionKpi,
  ManhourKpi,
  RejectionKpi,
  LossPowderKpi,
} from '~/x-modules/integration/services';
import { LogJobService } from './log-job.service';
import { Consumer } from 'sqs-consumer';
import { SQS } from 'aws-sdk';
import { EventHubService } from '~/x-modules/@global/services';
declare type SQSMessage = SQS.Types.Message;
const {
  AWS_SQS_URL,
  AWS_SQS_REGION,
  AWS_SQS_ACCESS_KEY_ID,
  AWS_SQS_SECRET_ACCESS_KEY,
  AWS_SQS_URL_CUSTOM,
  IS_DOCKER_SERVER,
  IS_DISABLE_SQS,
} = configEnv();
@Injectable()
export class SQSHandlerService implements OnModuleInit {
  private readonly sqsClient: SQSClient;
  constructor(
    private readonly sitewiseInService: SitewiseInService,
    private readonly prodBatchParametersService: ProdBatchParametersService,
    private readonly productOutputByRejection: ProductOutputByRejection,
    private readonly prodOrderMaterialTransactionService: ProdOrderMaterialTransactionService,
    private readonly calculateUtilityUsage: CalculateUtilityUsage,
    private readonly productionOeeService: ProductionOeeService,
    private readonly calculateKansuiAndSeasoningService: CalculateKansuiAndSeasoningService,
    private readonly transactionProductOutputService: TransactionProductOutput,
    private readonly callProcedureService: CallProcedureService,
    private readonly logJobService: LogJobService,
    private readonly utilityConsumptionKpi: UtilityConsumptionKpi,
    private readonly manhourKpi: ManhourKpi,
    private readonly eventHubService: EventHubService,
    private readonly rejectionKpi: RejectionKpi,
    private readonly lossPowderKpi: LossPowderKpi,
  ) {
    this.sqsClient = new SQSClient({
      region: AWS_SQS_REGION || 'ap-southeast-1',
      credentials: {
        accessKeyId: AWS_SQS_ACCESS_KEY_ID || '',
        secretAccessKey: AWS_SQS_SECRET_ACCESS_KEY || '',
      },
    });
    if (!IS_DOCKER_SERVER && !IS_DISABLE_SQS) {
      console.log(`===== Starting SQS consumer =====`);
      const app = Consumer.create({
        queueUrl: AWS_SQS_URL || '',
        sqs: this.sqsClient,
        messageAttributeNames: ['All'],
        handleMessage: async (message: SQSMessage) => {
          try {
            const msgObject = JSON.parse(message.Body ? message.Body : '');

            switch (msgObject.type) {
              case NSSQSS.EMessageType.OnProdBatchParamaters: {
                // Task 36.1
                const data = msgObject.data;
                if (data) {
                  await this.prodBatchParametersService.createProdBatchParamaters({
                    fromDate: data.fromDate,
                    toDate: data.toDate,
                  });
                }
                break;
              }
              case NSSQSS.EMessageType.OnProductOutputByRejection: {
                // Task 36.3
                const data = msgObject.data;
                if (data) {
                  await this.productOutputByRejection.createProductOutputByRejection();
                }
                break;
              }
              case NSSQSS.EMessageType.OnProductionOrderMaterialTransaction: {
                // Task 36.2
                const data = msgObject.data;
                if (data) {
                  await this.prodOrderMaterialTransactionService.createProdOrderMaterialTransaction();
                }
                break;
              }
              case NSSQSS.EMessageType.OnCalculateUtilityUsage: {
                // Task 36.13
                const data = msgObject.data;
                if (data) {
                  await this.calculateUtilityUsage.createCalculateUtilityUsage(data);
                }
                break;
              }
              case NSSQSS.EMessageType.OnOeeCalculation: {
                // Task 36.14
                const data = msgObject.data;
                if (data) {
                  await this.productionOeeService.createProductionOeeService({
                    fromDate: data.fromDate,
                    toDate: data.toDate,
                  });
                }
                break;
              }
              case NSSQSS.EMessageType.OnKansuiAndSeasoningCalculation: {
                // Task 36.15
                const data = msgObject.data;
                if (data) {
                  await this.calculateKansuiAndSeasoningService.createKansuiAndSeasoningCalculation();
                }
                break;
              }
              case NSSQSS.EMessageType.OnTransactionProductOutput: {
                // Task 36.5
                const data = msgObject.data;
                if (data) {
                  await this.transactionProductOutputService.createTransactionProductOutput(data);
                }
                break;
              }
              case NSSQSS.EMessageType.OnUtilityTransactionAllocation: {
                // Task 36.11
                const data = msgObject.data;
                if (data) {
                  await this.callProcedureService.utilityTransactionAllocation(data);
                }
                break;
              }
              case NSSQSS.EMessageType.OnManhourTransactionAllocation: {
                // Task 36.12
                const data = msgObject.data;
                if (data) {
                  await this.callProcedureService.manhourTransactionAllocation(data);
                }
                break;
              }
              case NSSQSS.EMessageType.OnCreateManhourTransactions: {
                // Task 36.15
                const data = msgObject.data;
                if (data) {
                  await this.callProcedureService.createManhourTransactions(data);
                }
                break;
              }
              case NSSQSS.EMessageType.OnCreateUtilityConsumptionKpi: {
                // Task 41
                const data = msgObject.data;
                if (data) {
                  await this.utilityConsumptionKpi.createUtilityConsumptionKpi(data);
                }
                break;
              }
              case NSSQSS.EMessageType.OnCreateManhourKpi: {
                // Task 43
                const data = msgObject.data;
                if (data) {
                  await this.manhourKpi.createManhourKpi(data);
                }
                break;
              }
              case NSSQSS.EMessageType.OnCreateOeeKpi: {
                // Task 46
                const data = msgObject.data;
                if (data) {
                  await this.callProcedureService.createOeekpi(data);
                }
                break;
              }
              case NSSQSS.EMessageType.OnRejectionKpi: {
                // Task 47
                const data = msgObject.data;
                if (data) {
                  await this.rejectionKpi.createRejectionKpi(data)
                }
              }
              case NSSQSS.EMessageType.OnLossPowderKpi: {
                // Task 40.2
                const data = msgObject.data;
                if (data) {
                  await this.lossPowderKpi.createLossPowderKpi(data)
                }
              }
              case NSSQSS.EMessageType.OnDockerEventHandling: {
                // Task 47
                const data = msgObject.data;
                if (data) {
                  await this.eventHubService.sendEvents(data);
                }
              }
              case NSSQSS.EMessageType.TestSNS: {
                const data = msgObject.data;
                if (data.recipeId) {
                  await this.sitewiseInService.testThrowError();
                }
                break;
              }
              default: {
                break;
              }
            }
            await this.logJobService.updateLogJobSuccess(message?.MessageId);
          } catch (error) {
            // console.error('Lỗi khi xử lý message từ SQS', error);
            await this.logJobService.updateLogJobError(message?.MessageId, error);
          }
        },
      });

      app.on('error', (err: any) => {
        console.error(err.message);
      });

      app.on('processing_error', (err: any) => {
        console.error(err.message);
      });

      app.on('timeout_error', (err) => {
        console.error(err.message);
      });

      app.start();
    }
  }

  async onModuleInit() {
    // console.log('Bắt đầu lắng nghe tin nhắn từ SQS...');
    if (IS_DOCKER_SERVER) this.pollMessagesSNS();
  }

  async pollMessagesSNS() {
    console.log(`===== Polling messages SNS =====`);
    while (true) {
      try {
        const result = await this.sqsClient.send(
          new ReceiveMessageCommand({
            QueueUrl: AWS_SQS_URL_CUSTOM || '',
            MaxNumberOfMessages: 1,
            WaitTimeSeconds: 10, // Long polling
          }),
        );

        const messages = result.Messages ?? [];

        for (const message of messages) {
          // 🧠 Xử lý logic ở đây
          await this.handleMessageSNSDocker(JSON.parse(message.Body));

          // ✅ Xóa message sau khi xử lý
          await this.sqsClient.send(
            new DeleteMessageCommand({
              QueueUrl: AWS_SQS_URL_CUSTOM || '',
              ReceiptHandle: message.ReceiptHandle!,
            }),
          );
        }
      } catch (error) {
        console.error('Lỗi khi nhận message từ SQS', error);
        await new Promise((r) => setTimeout(r, 5000)); // đợi 5s rồi retry
      }
    }
  }

  private async handleMessageSNSDocker(payload: any) {
    try {
      // console.log('payload', payload);
      if (payload.Type === 'Notification') {
        const snsMessage: {
          message: string;
          data: any;
        } = JSON.parse(payload.Message);
        const type = payload?.MessageAttributes?.type?.Value;
        switch (type) {
          case NSSQSS.EMessageType.SyncRecipeToScada: {
            // Task 24
            const data = snsMessage.data;
            if (data.recipeId) {
              await this.sitewiseInService.syncRecipeToScada(data.recipeId);
            }
            break;
          }
          case NSSQSS.EMessageType.SyncProductionOrderToScada: {
            // Task 27
            const data = snsMessage.data;
            if (data.lstId) {
              await this.sitewiseInService.syncProductionOrderToScada(data.lstId);
            }
            break;
          }
          case NSSQSS.EMessageType.SyncInventoryToScada: {
            // Task 29
            const data = snsMessage.data;
            if (data.lstId) {
              await this.sitewiseInService.syncInventoryToScada(data.lstId);
            }
            break;
          }
          case NSSQSS.EMessageType.TestSNS: {
            // console.log('TestSNS Handler', snsMessage);

            const data = snsMessage.data;
            if (data.recipeId) {
              await this.sitewiseInService.testThrowError();
              // console.log('TestSNS Handler', data.recipeId);
            }
            break;
          }
          default: {
            break;
          }
        }
        await this.logJobService.updateLogJobSuccess(payload?.MessageId);
      }
    } catch (error) {
      // console.error('Lỗi khi xử lý message từ SQS', error);
      if (error.message === '----- Khác IP Server -----') {
      } else await this.logJobService.updateLogJobError(payload?.MessageId, error);
    }
  }
}
