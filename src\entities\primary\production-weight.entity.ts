import { ApiProperty } from '@nestjs/swagger';
import { Column, Entity } from 'typeorm';
import { PrimaryBaseEntity } from '../primary-base.entity';

@Entity('production_weight')
export class ProductionWeightEntity extends PrimaryBaseEntity {
  @ApiProperty({ description: 'ID order' })
  @Column({ nullable: true, type: 'uuid' })
  orderId: string;

  @ApiProperty({ description: 'Order No' })
  @Column({ nullable: true })
  orderNo: string;

  @ApiProperty({ description: 'ID batch' })
  @Column({ nullable: true, type: 'uuid' })
  batchId: string;

  @ApiProperty({ description: 'Batch No' })
  @Column({ nullable: true })
  batchNo: string;

  @ApiProperty({ description: 'ID Production Material' })
  @Column({ nullable: true, type: 'uuid' })
  productionMaterialId: string;

  @ApiProperty({ description: 'ID Item' })
  @Column({ nullable: true, type: 'uuid' })
  itemId: string;

  @ApiProperty({ description: 'Item Code' })
  @Column({ nullable: true })
  itemCode: string;

  @ApiProperty({ description: 'Lot number' })
  @Column({ nullable: true })
  lotNumber: string;

  @ApiProperty({ description: 'Tare Quantity' })
  @Column({ nullable: true, type: 'numeric' })
  tareQty: number;

  @ApiProperty({ description: 'Tolerance' })
  @Column({ nullable: true, type: 'numeric' })
  tolerance: number;

  @ApiProperty({ description: 'Weighted Quantity' })
  @Column({ nullable: true, type: 'numeric' })
  weighedQty: number;

  @ApiProperty({ description: 'Weighted UOM' })
  @Column({ nullable: true })
  weightedUom: string;

  @ApiProperty({ description: 'Pack Quantity' })
  @Column({ nullable: true, type: 'numeric' })
  packQty: number;

  @ApiProperty({ description: 'Ngày giờ từng lần cân' })
  @Column({ nullable: true, type: 'timestamptz' })
  datetime: Date;

  @ApiProperty({ description: 'QRCode của từng dòng cân' })
  @Column({ nullable: true })
  qrcode: string;

  @ApiProperty({ description: 'Trạng thái' })
  @Column({ nullable: true, type: 'numeric' })
  status: number;

  @ApiProperty({ description: 'BatchId khi scan ghi nhận sử dụng' })
  @Column({ nullable: true, type: 'uuid' })
  batchConsumptId: string;

  @ApiProperty({ description: 'Batch No khi scan ghi nhận sử dụng' })
  @Column({ nullable: true })
  batchConsumptNo: string;
}
