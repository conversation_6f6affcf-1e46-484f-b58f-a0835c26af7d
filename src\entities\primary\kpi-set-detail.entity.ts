import { Enti<PERSON>, PrimaryColumn, Column, ManyTo<PERSON>ne, <PERSON><PERSON><PERSON><PERSON>um<PERSON> } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { PrimaryBaseEntity } from '../primary-base.entity';
import { KpiSetGroupEntity } from './kpi-set-group.entity';
import { KpiEntity } from './kpi.entity';
// import { KpiSetGroup } from './kpi-set-group.entity';

@Entity('kpi_set_detail')
export class KpiSetDetailEntity extends PrimaryBaseEntity {
  /** KPI set group id: lấy kpi_set_group_id từ bảng KPI_SET_HEADER */
  @ApiProperty({ description: 'ID nhóm bộ KPI (liên kết đến KpiSetGroup)' })
  @Column({ nullable: true })
  kpiSetGroupId: string;
  @ManyToOne(() => KpiSetGroupEntity, org => org.id)
  @JoinColumn({ name: 'kpiSetGroupId', referencedColumnName: 'id' })
  kpiSetGroup?: KpiSetGroupEntity;

  /** Lấy code từ bảng kpi list, chỉ lấy những kpi code có ngành, nhóm kpi = ngành, nhóm kpi được chọn ở kpi_set_group. Lưu ý: trên UI chọn và hiện code, nhưng lưu Database theo ID */
  @ApiProperty({ description: 'Mã KPI (liên kết đến bảng KPI list)' })
  @Column({ nullable: true })
  kpiId: string;
  @ManyToOne(() => KpiEntity, org => org.id)
  @JoinColumn({ name: 'kpiId', referencedColumnName: 'id' })
  kpi?: KpiEntity;

  /** Tỷ trọng chia thưởng cho các KPI chỉ tiết trong cùng KPI group */
  @ApiProperty({ description: 'Tỷ trọng chia thưởng cho KPI chi tiết' })
  @Column({ type: 'numeric', nullable: true })
  weighTarget: number;

  /** Số tiền thưởng cho các kpi chỉ tiết theo tỷ trọng */
  @ApiProperty({ description: 'Số tiền thưởng cho KPI chi tiết' })
  @Column({ type: 'numeric', nullable: true })
  budget: number;

  /** KPI set detail id, là key duy nhất, hệ thống tự generate theo số tự nhiên đại diện cho 1 nhóm kpi set group_id, kpi code_id */
  @ApiProperty({ description: 'ID chi tiết bộ KPI' })
  @Column({ type: 'int', generated: 'increment' })
  kpiSetDetailId: number;

  //   // Mối quan hệ với KpiSetGroup
  //   @ManyToOne(() => KpiSetGroup, kpiSetGroup => kpiSetGroup.kpiSetDetails)
  //   @JoinColumn({ name: 'kpiSetGroupId' })
  //   kpiSetGroup: KpiSetGroup;
}
