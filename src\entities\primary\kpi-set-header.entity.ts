import { Entity, Column, OneToMany, PrimaryGeneratedColumn } from 'typeorm';
import { ApiProperty } from '@nestjs/swagger';
import { PrimaryBaseEntity } from '../primary-base.entity';
import { KpiSetProductAreaEntity } from './kpi-set-production-area.entity';
import { KpiSetGroupEntity } from '~/entities/primary/kpi-set-group.entity';

@Entity('kpi_set_header')
export class KpiSetHeaderEntity extends PrimaryBaseEntity {
  /** KPI set code */
  @ApiProperty({ description: 'Mã bộ KPI' })
  @Column({ type: 'varchar', length: 50 })
  code: string;

  /** Tên ngắn (tên viết tắt) của KPI */
  @ApiProperty({ description: 'Tên ngắn (tên viết tắt) của KPI' })
  @Column({ type: 'varchar', length: 128, nullable: true })
  shortName: string;

  /** Tên <PERSON>PI đầy đủ */
  @ApiProperty({ description: 'Tên KPI đầy đủ' })
  @Column({ type: 'varchar', length: 256, nullable: true })
  longName: string;

  /** Số tiền ngân sách thưởng cho 1 process area hàng tháng */
  @ApiProperty({ description: 'Ngân sách thưởng hàng tháng' })
  @Column({ type: 'numeric', nullable: true })
  budget: number;

  /** Trạng thái sử dụng, dùng đánh dấu nhà máy, khu vực nào áp dụng kpi set, trường hợp ko sử dụng, bỏ check thi update lại trạng thái disable, check sử dụng là enable: 1-Enable 0-Disable */
  @ApiProperty({ description: 'Trạng thái sử dụng' })
  @Column({ type: 'int', nullable: true })
  enableFlag: number;

  /** Trạng thái sử dụng: 1-Active 0-Inactive */
  @ApiProperty({ description: 'Trạng thái sử dụng' })
  @Column({ type: 'int', nullable: true })
  status: number;

  /** Hệ thống tự sinh theo số thứ tự tăng dần, mỗi kpi set là 1 id duy nhất */
  @ApiProperty({ description: 'ID bộ KPI' })
  @Column({ type: 'int', generated: 'increment' })
  kpiSetId: number;

  /** Danh sách kv, nhà máy áp dụng */
  @OneToMany(() => KpiSetProductAreaEntity, pro => pro.kpiSetHeader)
  productionAreas: KpiSetProductAreaEntity[];

  /** Danh sách kpiSetGroup */
  @OneToMany(() => KpiSetGroupEntity, e => e.kpiSetHeader)
  kpiSetGroups: KpiSetGroupEntity[];
}
