import { Injectable } from '@nestjs/common';
import { BindRepo } from '~/@core/decorator';
import { JwtService } from '@nestjs/jwt';
import moment from 'moment';
import {
  MachineParameterRepo,
  MesDxProdBatchStatusRepo,
  MesDxProdBatchParamatersRepo,
} from '~/repositories/primary';
import { dateHelper } from '~/common/helpers/date.helper';
@Injectable()
export class ProdBatchParametersService {
  constructor() {}

  // Admin Database
  @BindRepo(MachineParameterRepo)
  private readonly machineParameterRepo: MachineParameterRepo;
  @BindRepo(MesDxProdBatchStatusRepo)
  private mesDxProdBatchStatusRepo: MesDxProdBatchStatusRepo;
  @BindRepo(MesDxProdBatchParamatersRepo)
  private mesDxProdBatchParamatersRepo: MesDxProdBatchParamatersRepo;

  private async onHandleStatus(status: any) {
    // 1. <PERSON><PERSON><PERSON> danh sách machine parameters theo machineId và typeCode
    const machineParameters = await this.machineParameterRepo
      .createQueryBuilder('param')
      .where('param.typeCode = :typeCode', { typeCode: '1' })
      .andWhere('param.machineId = :machineId', { machineId: status?.machineId })
      .distinctOn(['param.iotsitewisePropertyId'])
      .orderBy('param.iotsitewisePropertyId')
      .getMany();

    if (!machineParameters?.length) return;
    // console.log('machineParameters', machineParameters?.length);

    // 2. Định dạng thời gian
    const getStartTime = moment(status.startTime, 'YYYY-MM-DD HH:mm:ss.SSS Z')
      .utcOffset(7 * 60)
      .format('YYYY-MM-DD HH:mm:ss.SSS Z');
    const getEndTime = moment(status.endTime, 'YYYY-MM-DD HH:mm:ss.SSS Z')
      .utcOffset(7 * 60)
      .format('YYYY-MM-DD HH:mm:ss.SSS Z');

    for (const param of machineParameters) {
      // 3. JOIN measurements + paramaters, lấy tất cả kết quả
      const allResults: Array<{
        measurementId: string;
        datetime: string;
        value: string;
        exists: boolean;
      }> = await this.machineParameterRepo.query(
        `
      SELECT 
        m."measurementId",
        m."datetime",
        m."value",
        pbp."bacthProcessParameterId" IS NOT NULL AS "exists"
      FROM "scada_mes_dx_measurements" m
      LEFT JOIN "production_batch_paramaters" pbp
        ON pbp."measurementId" = m."measurementId"
        AND pbp."dateTime" = m."datetime"
        AND pbp."batchId" = $1
        AND pbp."machineId" = $2
      WHERE m."measurementId" = $3
        AND m."datetime" BETWEEN $4 AND $5
      ORDER BY m."datetime" ASC
      `,
        [status.batchId, status.machineId, param.iotsitewisePropertyId, getStartTime, getEndTime],
      );

      if (!allResults.length) continue;
      // console.log('Total Results:', allResults.length);

      // 4. Chunk kết quả theo số lượng dòng (ví dụ: 10,000 dòng mỗi lần)
      const resultChunks = this.chunkArray(allResults, 5000);

      const inserts = [];
      let prevValue: string | null = null;

      for (const chunk of resultChunks) {
        for (const row of chunk) {
          const { measurementId, datetime, value, exists } = row;

          if (prevValue !== value && !exists) {
            inserts.push({
              machineId: status.machineId,
              batchId: status.batchId,
              measurementId,
              value: value || '',
              dateTime: datetime,
              lastUpdate: new Date(),
              lastUpdateBy: -1,
              createdByUser: '-1',
            });
          }

          prevValue = value;
        }
      }

      // 5. Thực hiện insert nếu có dữ liệu mới
      if (inserts.length) {
        const insertChunks = this.chunkArray(inserts, 500);

        for (const chunk of insertChunks) {
          await this.mesDxProdBatchParamatersRepo
            .createQueryBuilder()
            .insert()
            .into('production_batch_paramaters')
            .values(chunk)
            .execute();
        }
      }

      // console.log('Inserted:', inserts.length);
      // console.log(`-----------------------------`);
    }
  }

  private chunkArray<T>(arr: T[], size: number): T[][] {
    const result: T[][] = [];
    for (let i = 0; i < arr.length; i += size) {
      result.push(arr.slice(i, i + size));
    }
    return result;
  }

  async createProdBatchParamaters(data: { fromDate?: string; toDate?: string }) {
    try {
      // Handle fromDate/toDate
      let getDateFrom = null;
      let getDateTo = null;
      if (data.fromDate && data.toDate) {
        getDateFrom = dateHelper.formatDateByJobManual(data.fromDate);
        getDateTo = dateHelper.formatDateByJobManual(data.toDate);
      }

      const query = await this.mesDxProdBatchStatusRepo
        .createQueryBuilder('batch')
        .where('batch.status = :status', { status: '3' })
        .distinctOn(['batch.batchId'])
        .orderBy('batch.batchId', 'ASC')
        .addOrderBy('batch.createdDate', 'DESC');

      if (getDateFrom && getDateTo) {
        query.andWhere('batch.startTime >= :getDateFrom', { getDateFrom });
        query.andWhere('batch.endTime <= :getDateTo', { getDateTo });
      }
      const getProBatchStatus = await query.getMany();

      if (!getProBatchStatus?.length) return;

      const chunkedStatusList = this.chunkArray(getProBatchStatus, 5); // 5 batch xử lý song song

      for (const chunk of chunkedStatusList) {
        await Promise.allSettled(chunk.map((status) => this.onHandleStatus(status)));
      }
      return true;
    } catch (error) {
      console.log(error, 'error');
    }
  }
}
