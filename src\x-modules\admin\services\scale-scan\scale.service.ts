import { BadRequestException, forwardRef, Inject } from '@nestjs/common';
import { In } from 'typeorm';
import { BindRepo } from '~/@core/decorator';
import {
  ConvertUomReq,
  CreateScaleRequestDto,
  GetLotRequestDto,
  ListScaleReq,
} from '~/dto/scale.dto';
import {
  InventoryRepository,
  ProductionAreaDetailRepo,
  ProductionAreaRepo,
  ProductionBatchRepo,
  ProductionOrderMaterialRepo,
  ProductionOrderRepo,
  ProductionWeightRepo,
  MaterialConsumptionScadaRepo,
  ProcessRepo,
  RecipeProcessItemRepo,
  RecipeProcessRepo,
  RecipeRepo,
  ItemRepo,
  UomConventionRepo,
  StationRepo,
  DeviceRepo,
  OrganizationUnitRepo,
} from '~/repositories/primary';
import { StationDetailRepo } from '~/repositories/primary/station-detail.repo';
import { PrinterService } from '~/x-modules/admin/services/print.service';

export class ScaleService {
  constructor(
    @Inject(forwardRef(() => PrinterService))
    private readonly printerService: PrinterService,
  ) {}
  @BindRepo(ProductionAreaRepo)
  private readonly productionAreaRepo: ProductionAreaRepo;

  @BindRepo(ProductionBatchRepo)
  private readonly productionBatchRepo: ProductionBatchRepo;

  @BindRepo(ProductionOrderRepo)
  private readonly productionOrderRepo: ProductionOrderRepo;

  @BindRepo(ProductionOrderMaterialRepo)
  private readonly productionOrderMaterialRepo: ProductionOrderMaterialRepo;

  @BindRepo(InventoryRepository)
  private readonly inventoryRepo: InventoryRepository;

  @BindRepo(ProductionWeightRepo)
  private readonly productionWeightRepo: ProductionWeightRepo;

  @BindRepo(MaterialConsumptionScadaRepo)
  private readonly materialConsumptionScadaRepo: MaterialConsumptionScadaRepo;

  @BindRepo(ProcessRepo)
  private readonly processRepo: ProcessRepo;

  @BindRepo(RecipeProcessItemRepo)
  private readonly recipeProcessItemRepo: RecipeProcessItemRepo;

  @BindRepo(RecipeProcessRepo)
  private readonly recipeProcessRepo: RecipeProcessRepo;

  @BindRepo(ItemRepo)
  private readonly itemRepo: ItemRepo;

  @BindRepo(UomConventionRepo)
  private readonly uomConventionRepo: UomConventionRepo;

  @BindRepo(StationRepo)
  private readonly stationRepo: StationRepo;

  @BindRepo(DeviceRepo)
  private readonly deviceRepo: DeviceRepo;

  @BindRepo(StationDetailRepo)
  private readonly stationDetailRepo: StationDetailRepo;

  @BindRepo(OrganizationUnitRepo)
  private readonly orgUnitRepo: OrganizationUnitRepo;

  async getProductionAreaByOrgUnit(organizationUnitId: string): Promise<any[]> {
    try {
      const data = await this.productionAreaRepo.find({
        where: {
          isActive: true,
          organizationUnitId: organizationUnitId,
        },
      });
      return data.map((i) => ({
        label: i.code + ' - ' + i.name,
        value: i.id,
        code: i.code,
        name: i.name,
      }));
    } catch (error) {
      console.error('Error in getProductionAreaByOrgUnit:', error);
      return [];
    }
  }

  async getProcessArea(lineId: string): Promise<any[]> {
    try {
      const result = await this.orgUnitRepo.find({
        where: { parentId: lineId, isActive: true },
        select: ['id', 'code', 'name', 'parentId', 'storageLocation', 'ingredientLocator'],
      });
      return result.map((i) => ({
        label: i.code + ' - ' + i.name,
        value: i.id,
        code: i.code,
        name: i.name,
        storageLocation: i.storageLocation,
        ingredientLocator: i.ingredientLocator,
      }));
    } catch (error) {
      console.error('Error in get Process Area:', error);
      return [];
    }
  }

  async getPOByProcessArea(processAreaId: string): Promise<any[]> {
    try {
      const result = await this.productionOrderRepo.query(
        `
        SELECT 
          po.id,
          po."orderNo",
          po."planStartDate",
          s.code AS shiftCode,
          s.description AS shiftDescription,
          COUNT(pb.id) AS totalBatch,
          COUNT(pb.id) FILTER (WHERE pb."weighStatus" = 1) AS weighedBatch,
          i.name as productName
        FROM production_order AS po
        LEFT JOIN item AS i ON i.id = po."itemId"
        LEFT JOIN shift AS s ON s.id = po."shiftId"
        LEFT JOIN production_batch AS pb ON pb."orderId" = po.id
        WHERE po."processAreaId" = $1
          AND po."planStartDate" >= CURRENT_DATE
        GROUP BY po.id, po."orderNo", po."planStartDate", s.code, i.name, s.description
        ORDER BY po."orderNo" ASC;
      `,
        [processAreaId],
      );
      return result;
    } catch (error) {
      console.error('Error in getPOByProcessArea:', error);
      return [];
    }
  }

  async getBatchByPO(orderId: string): Promise<any[]> {
    try {
      const result = await this.productionBatchRepo.find({
        where: { orderId, weighStatus: 0 },
        order: { batchNumber: 'ASC' },
        select: ['id', 'batchNumber'],
      });
      return result;
    } catch (error) {
      console.error('Error in getBatchByPO:', error);
      return [];
    }
  }

  async getItem(orderId: string, processId: string, batchId: string): Promise<any[]> {
    try {
      // 1. Tìm danh sách processId theo organizationUnitId
      const processes = await this.processRepo.find({
        where: { organizationUnitId: processId },
        select: ['id'],
      });
      const processIds = processes.map((p) => p.id);

      // 2. Tìm production_order_material theo orderId, lineType = -1 và processId thuộc danh sách
      const result = await this.productionOrderMaterialRepo.find({
        where: {
          orderId: orderId,
          lineType: -1,
          processId: In(processIds),
        },
        order: {
          materialCode: 'ASC',
        },
        relations: ['process'],
      });

      // 3. Tìm các conversion theo materialId
      const conversion = await this.uomConventionRepo.find({
        where: { itemId: In(result.map((i) => i.materialId)) },
      });

      const res = await Promise.all(
        result.map(async (item) => {
          // 4. Tìm các recipe_process theo processAreaId
          const recipeProcesses = await this.recipeProcessRepo.find({
            where: {
              processId: processIds[0],
            },
            select: ['id'],
          });
          const recipeProcessIds = recipeProcesses.map((rp) => rp.id);

          // 5. Tìm recipe_process_item theo itemId và recipeProcessId
          const recipeProcessItems = await this.recipeProcessItemRepo.find({
            where: {
              itemId: item.materialId,
              recipeProcessId: In(recipeProcessIds),
            },
            relations: ['recipeProcess', 'recipeProcess.recipe'],
          });

          // 6. Lấy matchedItem (nếu có)
          const matchedItem = recipeProcessItems.find(
            (i) => i.recipeProcess?.recipe?.processAreaId === processId,
          );

          // 7. Tính tổng trọng lượng đã cân
          const productionWeights = await this.productionWeightRepo.find({
            where: {
              itemId: item.materialId,
              batchId: batchId,
            },
          });

          const totalWeighedQty = productionWeights.reduce(
            (sum, pw) => sum + Number(pw.weighedQty || 0),
            0,
          );

          // 8. Lấy thông tin item để lấy tolerance
          const items = await this.itemRepo.find({
            where: {
              id: item.materialId,
            },
          });
          const itemMap = new Map(items.map((i) => [i.id, i.weighingTolerance]));

          // 9. Chuyển đổi đơn vị nếu cần
          const lstConversion = conversion.filter((o) => o.itemId === item.materialId);
          if (lstConversion.length > 0) {
            const uomBetween = this.findSingleIntermediate(item.trxUomCode, 'Kgs', lstConversion);
            if (uomBetween) {
              matchedItem.quantity = Number(matchedItem.quantity) * uomBetween.totalRate;
              item.trxUom = 'Kgs';
            }
          }

          // 10. Trả về kết quả từng item
          return {
            id: item.materialId,
            materialCode: item.materialCode,
            materialName: item.materialName,
            lineType: item.lineType,
            uom: item.trxUom,
            organizationUnitId: item.process?.organizationUnitId || null,
            weightStandard: matchedItem?.quantity || 0,
            weightQty: Number(totalWeighedQty.toFixed(3)),
            weightTolerance: itemMap.get(item.materialId) || 0,
          };
        }),
      );

      return res;
    } catch (error) {
      console.error('Error in getItem:', error);
      return [];
    }
  }

  async getLot(data: GetLotRequestDto): Promise<any[]> {
    try {
      const inventories = await this.inventoryRepo.find({
        where: {
          plant: data.site,
          subInventory: data.storageLocation,
          locator: data.ingredientLocator,
          materialCode: data.materialCode,
        },
        order: {
          expiredDate: 'ASC',
        },
      });

      const result = inventories.map((i) => {
        return {
          id: i.id,
          materialLotNumber: i.materialLotNumber,
          expiredDate: i.expiredDate,
          productionDate: i.productionDate,
          plant: i.plant,
          subInventory: i.subInventory,
          materialCode: i.materialCode,
        };
      });

      return result;
    } catch (error) {
      console.error('Error in get Lot:', error);
      return [];
    }
  }

  async getStation(processId: string): Promise<any[]> {
    try {
      const result = await this.stationRepo.find({
        where: { processId: processId },
      });
      return result.map((s) => ({ lable: s.description, value: s.id, code: s.code }));
    } catch (error) {
      console.error('Error in get Station:', error);
      return [];
    }
  }

  async getDevice(stationId: string): Promise<any[]> {
    try {
      const stationDetails = await this.stationDetailRepo.find({
        where: { stationId },
      });

      if (!stationDetails.length) return [];

      const deviceIds = stationDetails.map((sd) => sd.deviceId).filter(Boolean);

      if (!deviceIds.length) return [];

      const devices = await this.deviceRepo.find({
        where: { id: In(deviceIds) },
      });

      return devices.map((d) => ({
        lable: d.description,
        value: d.id,
        code: d.code,
        type: d.deviceTypeDetailCode,
        devicePort: d.devicePort,
        deviceIp: d.ipAddress,
      }));
    } catch (error) {
      console.error('Error in getDevice:', error);
      return [];
    }
  }

  async getDataScaleInfo(
    orderId: string,
    batchId: string,
    itemCode: string,
    materialLotNumber: string,
  ): Promise<any> {
    try {
      const res = await this.productionOrderMaterialRepo.queryOne(
        `
        SELECT 
          pom.id as productionMaterialId,
          pom."orderId",
          pom."orderNo",
          pom."materialId" as itemId,
          pom."materialCode" as itemCode,
          pom."trxUom" as "weightedUom",
          pb."batchNumber" as batchNo,
          pb.id as batchId,
          ou."materialHandleType",
          i."materialLotNumber",
          wt."value" as tareValue
        FROM production_order_material as pom
        LEFT JOIN production_batch as pb 
            ON pom."orderId" = pb."orderId"
            AND pb.id = $3
        LEFT JOIN process as pro 
            ON pom."processId" = pro.id
        LEFT JOIN organization_units as ou 
            ON pro."organizationUnitId" = ou.id
        LEFT JOIN inventory as i 
            ON pom."materialCode" = i."materialCode"
            AND i."materialLotNumber" = $4
        LEFT JOIN item 
            ON pom."materialCode" = item."code"
        LEFT JOIN weighing_tare_detail as wtd 
            ON wtd."itemId" = item.id
        LEFT JOIN weighing_tare as wt 
            ON wtd."weighingTareId" = wt.id
        WHERE pom."orderId" = $1
          AND pom."materialCode" = $2

      `,
        [orderId, itemCode, batchId, materialLotNumber],
      );
      return res;
    } catch (error) {
      console.error('Error in getDataScaleInfo:', error);
      return null;
    }
  }

  async createDataScale(data: CreateScaleRequestDto): Promise<any> {
    try {
      const batch = await this.productionBatchRepo.findOne({
        where: { id: data.batchId },
      });

      if (!batch) throw new Error('Không tìm thấy batch');

      const existingRecords = await this.productionWeightRepo.find({
        where: {
          batchId: data.batchId,
          itemId: data.itemId,
        },
      });

      // Tính tổng weighedQty
      const summedWeighed = existingRecords.reduce(
        (total, record) => total + (Number(record.weighedQty) || 0),
        0,
      );

      const newWeighedQty = Number(data.weighedQty) || 0;
      const allowedQty = Number(data.weightStandard) + Number(data.tolerance || 0);

      if (summedWeighed + newWeighedQty > allowedQty) {
        throw new BadRequestException('Tổng trọng lượng đã cân vượt quá giới hạn cho phép!');
      }
      // Nếu không có materialHandleType hoặc materialHandleType = '1'
      // if (!data.materialHandleType || data.materialHandleType === '1') {
      //   if (batch.id !== data.batchId) {
      //     throw new BadRequestException('Batch ID không khớp với Process Area');
      //   }

      //   const newRecord = this.productionWeightRepo.create(data);
      //   const result = await this.productionWeightRepo.save(newRecord);
      //   const dataScada = {
      //     orderNumber: result.orderNo,
      //     orderId: result.orderId,
      //     batchCode: result.batchNo,
      //     batchId: result.batchId,
      //     ingredientCode: result.itemCode,
      //     ingredientId: result.itemId,
      //     lotNumber: result.lotNumber,
      //     qty: result.weighedQty,
      //     uom: result.weightedUom,
      //     datetime: result.datetime,
      //     operatorId: result.createdBy,
      //     transactionType: 'Consumption',
      //   };
      //   await this.materialConsumptionScadaRepo.save(dataScada);
      //   return result;
      // }

      // // Nếu materialHandleType = '2'
      // if (data.materialHandleType === '2') {
      //   return {
      //     message: 'Vui lòng quét mã QR để tiếp tục',
      //     requireScan: true,
      //   };
      // }

      // if (data.materialHandleType === '3') {
      //   const newRecord = this.productionWeightRepo.create(data);
      //   const result = await this.productionWeightRepo.save(newRecord);
      //   // in ra tem cân
      //   if (result) {
      //     const res = {
      //       ...data,
      //       id: result.id,
      //     };
      //     if (!res.qrcode) {
      //       const qrValue = `mes.masancloud.com/${res?.itemCode}/${res?.lotNumber}/${res?.weighedQty}/${res?.packQty}/${res?.id}`;
      //       res.qrcode = qrValue;
      //     }
      //     await this.printerService.print([res], data.deviceIp, data.devicePort);
      //   }
      //   return result;
      // }

      // nếu printType = 1 thì lưu dữ liệu cân và in ra tem cân.
      // nếu printType = 2  thì show popup có 1 textbox yêu cầu scan thông tin QRCode vào, sau khi Scan thì lưu dữ liệu cân.
      // nếu printType = 3  thì lưu dữ liệu cân và lưu dữ liệu material consumption.
      // nếu printType = 4   thì lưu dữ liệu cân nhưng nhưng trường Batch để trống và in ra tem cân.
      if (data.printType === 1) {
        const newRecord = this.productionWeightRepo.create(data);
        const result = await this.productionWeightRepo.save(newRecord);
        // in ra tem cân
        if (result) {
          const res = {
            ...data,
            id: result.id,
          };
          if (!res.qrcode) {
          }
          const qrValue = `mes.masancloud.com/${res?.itemCode}/${res?.lotNumber}/${res?.weighedQty}/${res?.packQty}/${res?.id}`;
          res.qrcode = qrValue;
          await this.printerService.print([res], data.deviceIp, data.devicePort);
        }
        return result;
      }

      if (data.printType === 2) {
        return {
          message: 'Vui lòng quét mã QR để tiếp tục',
          requireScan: true,
        };
      }

      if (data.printType === 3) {
        const newRecord = this.productionWeightRepo.create(data);
        const result = await this.productionWeightRepo.save(newRecord);
        const dataScada = {
          orderNumber: result.orderNo,
          orderId: result.orderId,
          batchCode: result.batchNo,
          batchId: result.batchId,
          ingredientCode: result.itemCode,
          ingredientId: result.itemId,
          lotNumber: result.lotNumber,
          qty: result.weighedQty,
          uom: result.weightedUom,
          datetime: result.datetime,
          operatorId: result.createdBy,
          transactionType: 'Consumption',
          isProcessed: null,
        };
        await this.materialConsumptionScadaRepo.save(dataScada);
        return result;
      }

      if (data.printType === 4) {
        const newRecord = this.productionWeightRepo.create(data);
        const result = await this.productionWeightRepo.save(newRecord);
        // in ra tem cân
        if (result) {
          const res = {
            ...data,
            id: result.id,
          };
          if (!res.qrcode) {
            const qrValue = `mes.masancloud.com/${res?.itemCode}/${res?.lotNumber}/${res?.weighedQty}/${res?.packQty}/${res?.id}`;
            res.qrcode = qrValue;
          }
          await this.printerService.print([res], data.deviceIp, data.devicePort);
        }
        return result;
      }
    } catch (error) {
      console.error('Error in create data scale:', error);
      return { error: error.message || 'Đã xảy ra lỗi không xác định' };
    }
  }

  async listScale(orderId: string, batchId: string): Promise<any[]> {
    try {
      // 1. Lấy thông tin về production order và batch
      const productionOrder = await this.productionOrderRepo.findOne({
        where: { id: orderId },
        select: ['id', 'orderNo', 'recipeId', 'processAreaId'],
      });
      if (!productionOrder) return [];

      const productionBatch = await this.productionBatchRepo.findOne({
        where: { id: batchId, orderId },
        select: ['id', 'batchNumber'],
      });
      if (!productionBatch) return [];

      // 2. Tìm danh sách processId theo organizationUnitId
      const processes = await this.processRepo.find({
        where: { organizationUnitId: productionOrder.processAreaId },
        select: ['id'],
      });
      const processIds = processes.map((p) => p.id);
      if (!processIds.length) return [];

      // 3. Tìm production_order_material
      const materials = await this.productionOrderMaterialRepo.find({
        where: {
          orderId,
          lineType: -1,
          processId: In(processIds),
        },
        order: { materialCode: 'ASC' },
        relations: ['process'],
      });
      if (!materials.length) return [];

      // 4. Tìm các conversion theo materialId
      const conversions = await this.uomConventionRepo.find({
        where: { itemId: In(materials.map((i) => i.materialId)) },
      });

      // 5. Lấy thông tin recipe process
      const recipeProcesses = await this.recipeProcessRepo.find({
        where: { processId: processIds[0] },
        select: ['id'],
      });
      const recipeProcessIds = recipeProcesses.map((rp) => rp.id);

      // 6. Xử lý từng material
      const result = await Promise.all(
        materials.map(async (item) => {
          // 7. Tìm recipe_process_item
          const recipeProcessItems = await this.recipeProcessItemRepo.find({
            where: {
              itemId: item.materialId,
              recipeProcessId: In(recipeProcessIds),
            },
            relations: ['recipeProcess', 'recipeProcess.recipe'],
          });

          // 8. Lấy matchedItem
          const matchedItem = recipeProcessItems.find(
            (i) => i.recipeProcess?.recipe?.processAreaId === productionOrder.processAreaId,
          );

          const productionWeights = await this.productionWeightRepo.find({
            where: {
              itemId: item.materialId,
              batchId,
            },
          });

          // 10. Tính tổng weighedQty cho item
          const totalWeighedQty = productionWeights.reduce(
            (sum, pw) => sum + Number(pw.weighedQty || 0),
            0,
          );

          // 11. Lấy thông tin item để lấy tolerance
          const itemInfo = await this.itemRepo.findOne({
            where: { id: item.materialId },
          });

          // 12. Chuyển đổi đơn vị
          let standardQuantity = matchedItem?.quantity || 0;
          let uom = item.trxUom;

          const lstConversion = conversions.filter((o) => o.itemId === item.materialId);
          if (lstConversion.length > 0) {
            const uomBetween = this.findSingleIntermediate(item.trxUomCode, 'Kgs', lstConversion);
            if (uomBetween && matchedItem) {
              standardQuantity = Number(matchedItem.quantity) * uomBetween.totalRate;
              uom = 'Kgs';
            }
          }

          // 14. Trả về kết quả cho item
          return {
            orderNo: productionOrder.orderNo,
            batchNumber: productionBatch.batchNumber,
            itemId: item.materialId,
            itemCode: item.materialCode,
            itemName: item.materialName,
            weighingTolerance: itemInfo?.weighingTolerance || 0,
            standardQuantity,
            weighedQty: Number(totalWeighedQty.toFixed(3)),
            uom,
          };
        }),
      );

      return result;
    } catch (error) {
      console.error('Error in listScale:', error);
      return [];
    }
  }

  //  để xác nhận đã cân xong cho Batch đang chọn. Khi bấm vào nút này thì cập nhật weighed_status=1 cho batch trong bảng production_batch
  async updateWeighedStatus(batchId: string) {
    await this.productionBatchRepo.update(batchId, { weighStatus: 1 });
    return { message: 'Batch Success' };
  }

  async convertUom(data: ConvertUomReq): Promise<number> {
    try {
      const result = await this.uomConventionRepo.find({
        where: { itemId: data.itemId },
      });

      const uomBetween = this.findSingleIntermediate('Kgs', 'Bao', result);
      if (!uomBetween) {
        throw new Error('Không tìm thấy quy đổi giữa Bao và Kgs');
      }

      const valueKgs = Number(data.value) / uomBetween.totalRate;
      return valueKgs;
    } catch (error) {
      console.error('Error in convertUom:', error);
      throw new Error('Lỗi khi chuyển đổi đơn vị');
    }
  }

  findSingleIntermediate(fromUom, toUom, lstUomConverse) {
    const unitMap = {};
    const conversionMap = {};

    // Xây dựng graph và map chuyển đổi
    lstUomConverse.forEach(({ fromUnit, toUnit, conversion }) => {
      if (!unitMap[fromUnit]) unitMap[fromUnit] = [];
      if (!unitMap[toUnit]) unitMap[toUnit] = [];

      unitMap[fromUnit].push(toUnit);
      unitMap[toUnit].push(fromUnit);

      conversionMap[`${fromUnit}->${toUnit}`] = conversion;
      conversionMap[`${toUnit}->${fromUnit}`] = 1 / conversion;
    });

    // BFS
    const queue = [[fromUom]];
    const visited = new Set();

    while (queue.length > 0) {
      const path = queue.shift();
      const last = path[path.length - 1];

      if (last === toUom) {
        // Tính tổng conversion
        let totalRate = 1;
        const detail = {};

        for (let i = 0; i < path.length - 1; i++) {
          const key = `${path[i]}->${path[i + 1]}`;
          const rate = conversionMap[key];
          totalRate *= rate;
          detail[key] = rate;
        }

        return {
          path,
          totalRate,
          detail,
        };
      }

      if (visited.has(last)) continue;
      visited.add(last);

      for (const neighbor of unitMap[last] || []) {
        if (!visited.has(neighbor)) {
          queue.push([...path, neighbor]);
        }
      }
    }

    return null; // Không tìm được đường chuyển đổi
  }

  async generateData() {
    const data = [];
    for (let i = 0; i < 400000; i++) {
      data.push({
        batchId: '1',
        orderId: '1',
        itemId: '1',
        itemCode: '1',
        itemName: '1',
        weighingTolerance: 1,
        standardQuantity: 1,
        weighedQty: 1,
      });
    }
  }

  async fakeData() {
    const data = await this.generateData();
    return data;
  }
}
