import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsBoolean, IsNotEmpty, IsNumber, IsOptional, IsString } from 'class-validator';
import { PageRequest } from '~/@systems/utils';

export class MonitoringManhourReq extends PageRequest {
  /** Organization Unit level SITE */
  @ApiPropertyOptional({ description: 'Site ID' })
  @IsOptional()
  siteId?: string;

  /** Organization Unit level FACTORY */
  @ApiPropertyOptional({ description: 'Factory ID' })
  @IsOptional()
  factoryId?: string;

  /** Utility meter type */
  @ApiProperty({ description: 'Production Line ID' })
  @IsOptional()
  productionAreaId?: string;

  /** Utility meter code */
  @ApiProperty({ description: 'Meter Code' })
  @IsOptional()
  shiftId?: string;

  @ApiProperty({ description: 'Production Date' })
  @IsOptional()
  productionDate?: Date;
}

export class ManhourTransactionReq {
  @ApiProperty({ description: '<PERSON><PERSON>y giao dịch' })
  @IsNotEmpty()
  date: Date;

  @ApiProperty({ description: 'Shift ID' })
  @IsNotEmpty()
  shiftId: string;

  @ApiProperty({ description: 'Meter ID' })
  @IsNotEmpty()
  meterId: string;

  @ApiProperty({ description: 'Giá trị' })
  @IsNotEmpty()
  value: number;

  @ApiProperty({ description: 'Posted At' })
  @IsOptional()
  postedAt?: Date;

  @ApiProperty({ description: 'Posted' })
  @IsOptional()
  posted?: boolean;
}

export class ListMonitorManhour extends PageRequest {
  @ApiProperty({ description: 'Site ID' })
  @IsOptional()
  productionAreaId?: string;

  @ApiProperty({ description: 'Site ID' })
  @IsOptional()
  siteId?: string;

  @ApiProperty({ description: 'Factory ID' })
  @IsOptional()
  factoryId?: string;

  @ApiProperty({ description: 'Type ID' })
  @IsOptional()
  type?: string;

  @ApiProperty({ description: 'Start Date' })
  @IsOptional()
  startDate?: Date;

  @ApiProperty({ description: 'End Date' })
  @IsOptional()
  endDate?: Date;

  @ApiProperty({ description: 'Shift ID' })
  shiftId: string;
}
