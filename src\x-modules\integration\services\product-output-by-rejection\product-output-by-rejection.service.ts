import { Injectable } from '@nestjs/common';
import { BindRepo } from '~/@core/decorator';
import { MesDxTransformRepo } from '~/repositories/scada';
import moment from 'moment';
import { JwtService } from '@nestjs/jwt';
import {
  ItemRepo,
  UomConventionRepo,
  ProductionOrderRepo,
  ProductionOrderMaterialRepo,
  ProductionRejectionRepo,
  ProcessRepo,
  ProductionOrderMaterialTransactionRepo
} from '~/repositories/primary';
import { In } from 'typeorm';
import { promises } from 'dns';

@Injectable()

export class ProductOutputByRejection {
  constructor(
    private jwtService: JwtService,
  ) {}

  @BindRepo(ItemRepo)
  private itemRepo: ItemRepo;
  @BindRepo(ProductionOrderRepo)
  private productionOrderRepo: ProductionOrderRepo;
  @BindRepo(ProductionOrderMaterialRepo)
  private productionOrderMaterialRepo: ProductionOrderMaterialRepo;
  @BindRepo(ProductionRejectionRepo)
  private readonly productionRejectionRepo: ProductionRejectionRepo;
  @BindRepo(ProcessRepo)
  private readonly processRepo: ProcessRepo;
  @BindRepo(ProductionOrderMaterialTransactionRepo)
  private readonly productionOrderMaterialTransactionRepo: ProductionOrderMaterialTransactionRepo;
  @BindRepo(UomConventionRepo)
  private readonly uomConventionRepo: UomConventionRepo;

  findSingleIntermediate(fromUom: any, toUom: any, lstUomConverse: any) {
    const unitMap = {};
    const conversionMap = {};
  
    // Tạo map các đơn vị kết nối lẫn nhau
    lstUomConverse.forEach(({ fromUnit, toUnit, conversion }) => {
      if (!unitMap[fromUnit]) unitMap[fromUnit] = new Set();
      if (!unitMap[toUnit]) unitMap[toUnit] = new Set();
  
      unitMap[fromUnit].add(toUnit);
      unitMap[toUnit].add(fromUnit); // 2 chiều
      // Lưu tỉ lệ quy đổi theo cả 2 chiều
      conversionMap[`${fromUnit}->${toUnit}`] = conversion;
      conversionMap[`${toUnit}->${fromUnit}`] = 1 / conversion;
    });
  
    const visited = new Set();
    const queue = [{ unit: fromUom, rate: 1 }];
  
    while (queue.length > 0) {
      const { unit, rate } = queue.shift();
      if (unit === toUom) {
        return rate;
      }
  
      visited.add(unit);
  
      const neighbors = unitMap[unit] || new Set();
      for (const neighbor of neighbors) {
        if (!visited.has(neighbor)) {
          const conversionRate = conversionMap[`${unit}->${neighbor}`];
          queue.push({ unit: neighbor, rate: rate * conversionRate });
        }
      }
    }
  
    return null; // Không tìm thấy chuyển đổi
  }

  onHandleTransactionDate(date: any) {
    let getTime = moment().utcOffset(7 * 60);
    if(date) getTime = moment(date, "YYYY-MM-DD HH:mm:ss.SSS Z").utcOffset(7 * 60);
    getTime.startOf('day');
    return getTime.format('YYYY-MM-DD HH:mm:ss.SSS Z')
  }  

  private onCalculateValues(actualTrxQtyValue: any, transactionQtyValue: any, keyAction: string) {
    const getActualTrxQtyValue = isNaN(Number(actualTrxQtyValue)) ? 0 : Number(actualTrxQtyValue);
    const getTransactionQtyValue = isNaN(Number(transactionQtyValue)) ? 0 : Number(transactionQtyValue);
  
    if (keyAction === 'sum') {
      return getActualTrxQtyValue + getTransactionQtyValue;
    } else if (keyAction === 'subtract') {
      return getActualTrxQtyValue - getTransactionQtyValue;
    } else {
      return getActualTrxQtyValue || 0;
    }
  }

  private async onSumActualQtyByPO(
    productionOrderData: any, 
    uomConventionData:any,
    processData: any
  ) {
    let totalResult = 0
    if(productionOrderData && productionOrderData?.length > 0){
      await Promise.all(
        productionOrderData.map(async (productionOrder: any) => {
            const itemData = await this.itemRepo.findOne({ id: productionOrder?.itemId })
            const uomConventionByItem = uomConventionData.filter((uomDetail: any) => uomDetail?.itemId === itemData?.id)
  
            // Tìm kiếm dữ liệu của production_order_material table
            const productionOrderMaterialData = await this.productionOrderMaterialRepo.find({
              where: { 
                  orderId: productionOrder?.id,
                  processId: processData?.id,
                  lineType:  1,
              },
            })
            if(!productionOrderMaterialData || productionOrderMaterialData?.length === 0) return;
  
            await Promise.all(
              productionOrderMaterialData.map(async (prodOrderMaterial) => {
                const UOMConversionData = this.findSingleIntermediate(prodOrderMaterial?.trxUomCode, itemData?.baseUnit, uomConventionByItem) || 1
                totalResult = totalResult + ((!isNaN(Number(prodOrderMaterial.actualTrxQty)) ? Number(prodOrderMaterial.actualTrxQty) : prodOrderMaterial.actualTrxQty || 0) * UOMConversionData)
              })
            )
        })
      )
    }
    return totalResult
  }

  private async onProductionRejectionDetail(prodRejection:any, uomConventionData:any) {
    // Tìm dữ liệu process table
    const processData = await this.processRepo.findOne({
      where: { organizationUnitId: prodRejection?.processAreaId },
      order: { createdDate: "DESC" }
    })
    if(!processData) return;

    if(prodRejection?.productionOrderId){
      // ---- Nếu dòng dữ liệu có production_order_id khác null ----

      // Tìm kiếm dữ liệu của production_order table
      const productionOrderData = await this.productionOrderRepo.findOne({
        where: { id: prodRejection?.productionOrderId },
      })
      if(!productionOrderData) return;

      // Tìm kiếm dữ liệu của production_order_material table
      const productionOrderMaterialData = await this.productionOrderMaterialRepo.findOne({
        where: { 
            orderId: productionOrderData?.id,
            processId: processData?.id,
            materialId: prodRejection?.itemId,
            lineType: In([1,2])
        },
      })
      if(!productionOrderMaterialData) return;
      
      let prodOrderMaterialTransactionObj = {
        orderId: prodRejection?.productionOrderId ? prodRejection?.productionOrderId : '',
        productionOrderMaterialId: productionOrderMaterialData?.id,
        materialId: prodRejection?.itemId,
        transactionDate: prodRejection?.transactionDate ? this.onHandleTransactionDate(prodRejection.transactionDate) : null,
        transactionQty: prodRejection?.quantity,
        lotNumber: productionOrderData?.lotNumber,
        transactionUom: prodRejection?.uomCode,
        createdByUser: '-1',
        lastUpdateBy:-1,
        oeeCal: prodRejection?.oeeCal
      }
      if(productionOrderMaterialData?.lineType.toString() === '1' && prodRejection?.transactionTypeDetailCode.toString() === '2'){
        // TH1: lineType = 1 && transactionTypeDetailCode = 2
        prodOrderMaterialTransactionObj['transactionType'] = prodRejection?.quantity > 0 ? 'WIP_COMPLETION_RETURN' : 'WIP_COMPLETION'
        await this.productionOrderMaterialTransactionRepo.save({...prodOrderMaterialTransactionObj})
        const newActualTrxQty = this.onCalculateValues(
          productionOrderMaterialData?.actualTrxQty, 
          prodOrderMaterialTransactionObj.transactionQty, 
          'subtract'
        );
        await this.productionOrderMaterialRepo.update(
          { id: productionOrderMaterialData.id },
          { actualTrxQty: newActualTrxQty}
        );
      }else if(productionOrderMaterialData?.lineType.toString() === '1' && prodRejection?.transactionTypeDetailCode.toString() === '1'){
        // TH2 line_type = 1 && transactionTypeDetailCode = 1
        prodOrderMaterialTransactionObj['transactionType'] = prodRejection?.quantity > 0 ? 'WIP_COMPLETION' : 'WIP_COMPLETION_RETURN'
        await this.productionOrderMaterialTransactionRepo.save({...prodOrderMaterialTransactionObj})
        const newActualTrxQty = this.onCalculateValues(
          productionOrderMaterialData?.actualTrxQty ?? 0,
          prodOrderMaterialTransactionObj.transactionQty, 
          'sum'
        );
        await this.productionOrderMaterialRepo.update(
          { id: productionOrderMaterialData.id },
          { actualTrxQty: newActualTrxQty }
        );
      }else if(productionOrderMaterialData?.lineType.toString() === '2' && prodRejection?.transactionTypeDetailCode.toString() === '1'){
        // TH3 line_type = 2 && transactionTypeDetailCode = 1        
        prodOrderMaterialTransactionObj['transactionType'] = prodRejection?.quantity > 0 ? 'BYPRODUCT_COMPLETION' : 'BYPRODUCT_COMPLETION_RETURN'
        await this.productionOrderMaterialTransactionRepo.save({...prodOrderMaterialTransactionObj})
        const newActualTrxQty = this.onCalculateValues(
          productionOrderMaterialData?.actualTrxQty, 
          prodOrderMaterialTransactionObj.transactionQty, 
          'sum'
        );
        await this.productionOrderMaterialRepo.update(
          { id: productionOrderMaterialData.id },
          { actualTrxQty: newActualTrxQty}
        );
      }
    }else{
      // ---- Nếu dòng dữ liệu có production_order_id bằng null ----
      if(!prodRejection?.transactionDate) return;

      const productionOrderData = await this.productionOrderRepo.find({
        where: { 
          planStartDate: prodRejection?.transactionDate || '',
          shiftId: prodRejection?.shiftId ? prodRejection?.shiftId : '',
        },
      })
      if(!productionOrderData) return;

      // Tính toán allocationRate 
      // Tính tổng actual_qty cho toàn bộ PO
      const getSumActualQtyByAllPO = await this.onSumActualQtyByPO(productionOrderData, uomConventionData, processData )
      await Promise.all(
        productionOrderData.map(async (productionOrder) => {
          let totalActualByPO = 0
          let allocationRateValue = 0
          // Tìm kiếm dữ liệu base uom
          const itemData = await this.itemRepo.findOne({ id: productionOrder?.itemId })
          const uomConventionByItem = uomConventionData.filter((uomDetail: any) => uomDetail?.itemId === itemData?.id)
          // Tìm các productionOrderMaterial line_type = 1 cho từng PO
          const productionOrderMaterialDataSum = await this.productionOrderMaterialRepo.find({
            where: { 
                orderId: productionOrder?.id,
                processId: processData?.id,
                lineType: 1,
            },
          })
          if(productionOrderMaterialDataSum && productionOrderMaterialDataSum?.length !== 0){
             // Tính giá trị tổng giá trị ActualTrxQty cho từng PO
            await Promise.all(
              productionOrderMaterialDataSum.map(async (prodOrderMaterialSum) => {
                const UOMConversionData = this.findSingleIntermediate(prodOrderMaterialSum?.trxUomCode, itemData?.baseUnit, uomConventionByItem) || 1 
                let getActualTrxQtyValue = (!isNaN(Number(prodOrderMaterialSum?.actualTrxQty)) ? Number(prodOrderMaterialSum?.actualTrxQty) : prodOrderMaterialSum?.actualTrxQty) * UOMConversionData
                totalActualByPO = totalActualByPO + getActualTrxQtyValue
              })
            )

            // Tính giá trị allocationRateValue
            if(getSumActualQtyByAllPO !== 0) allocationRateValue = totalActualByPO / getSumActualQtyByAllPO;
          }

          // Tìm kiếm dữ liệu của production_order_material table
          const productionOrderMaterialData = await this.productionOrderMaterialRepo.find({
            where: { 
                orderId: productionOrder?.id,
                processId: processData?.id,
                materialId: prodRejection?.itemId,
                lineType: In([1,2])
            },
          })
          if(!productionOrderMaterialData || productionOrderMaterialData?.length === 0) return;

          await Promise.all(
            productionOrderMaterialData.map(async (productionOrderMaterial) => {
              let prodOrderMaterialTransactionObj = {
                orderId: productionOrder?.id,
                productionOrderMaterialId: productionOrderMaterial?.id,
                materialId: prodRejection?.itemId,
                transactionDate: prodRejection?.transactionDate ? this.onHandleTransactionDate(prodRejection.transactionDate) : null,
                transactionQty: prodRejection?.quantity * allocationRateValue,
                lotNumber: productionOrder?.lotNumber,
                transactionUom: prodRejection?.uomCode,
                createdByUser: '-1',
                lastUpdateBy:-1,
                oeeCal: prodRejection?.oeeCal
              }
              if(productionOrderMaterial?.lineType.toString() === '1' && prodRejection?.transactionTypeDetailCode.toString() === '2'){
                // transactionTypeDetailCode = 2 && lineType = 1
                prodOrderMaterialTransactionObj['transactionType'] = prodRejection?.quantity > 0 ? 'WIP_COMPLETION_RETURN' : 'WIP_COMPLETION'
                await this.productionOrderMaterialTransactionRepo.save({...prodOrderMaterialTransactionObj})
                const newActualTrxQty = this.onCalculateValues(
                  productionOrderMaterial?.actualTrxQty, 
                  prodOrderMaterialTransactionObj.transactionQty, 
                  'subtract'
                );
                await this.productionOrderMaterialRepo.update(
                  { id: productionOrderMaterial.id },
                  { actualTrxQty: newActualTrxQty }
                );
              }else if(productionOrderMaterial?.lineType.toString() === '1' && prodRejection?.transactionTypeDetailCode.toString() === '1'){
                // transactionTypeDetailCode = 1 && lineType = 1
                prodOrderMaterialTransactionObj['transactionType'] = prodRejection?.quantity > 0 ? 'WIP_COMPLETION' : 'WIP_COMPLETION_RETURN'
                await this.productionOrderMaterialTransactionRepo.save({...prodOrderMaterialTransactionObj})
                const newActualTrxQty = this.onCalculateValues(
                  productionOrderMaterial?.actualTrxQty, 
                  prodOrderMaterialTransactionObj.transactionQty, 
                  'sum'
                );
                await this.productionOrderMaterialRepo.update(
                  { id: productionOrderMaterial.id },
                  { actualTrxQty: newActualTrxQty }
                );
              }else if(productionOrderMaterial?.lineType.toString() === '2' && prodRejection?.transactionTypeDetailCode.toString() === '1'){
                // transactionTypeDetailCode = 1 && lineType = 2
                prodOrderMaterialTransactionObj['transactionType'] = prodRejection?.quantity > 0 ? 'BYPRODUCT COMPLETION' : 'BYPRODUCT COMPLETION_RETURN'
                await this.productionOrderMaterialTransactionRepo.save({...prodOrderMaterialTransactionObj})
                const newActualTrxQty = this.onCalculateValues(
                  productionOrderMaterial?.actualTrxQty, 
                  prodOrderMaterialTransactionObj.transactionQty, 
                  'sum'
                );
                await this.productionOrderMaterialRepo.update(
                  { id: productionOrderMaterial.id },
                  { actualTrxQty: newActualTrxQty }
                );
              }
            })
          )
        })
      );
    }

    await this.productionRejectionRepo.update(
      { id: prodRejection.id },
      { postedTrx: 1 }
    );
  }

  async createProductOutputByRejection() {
    try {
        // set-up data
        const uomConventionData = await this.uomConventionRepo.find()
        // Tìm production_rejection có posted_trx=0, transactionType = 1 || 2
        const productionRejectionData = await this.productionRejectionRepo.find({ where: {postedTrx: 0, transactionTypeDetailCode: In([1, 2]) }})
        if(!productionRejectionData) return;
        
        await Promise.all(
          productionRejectionData.map(async (prodRejection) => {
            return await this.onProductionRejectionDetail(prodRejection, uomConventionData);
          })
        );
    } catch (error) {
      console.log(error, 'error');
    }
  }
}
