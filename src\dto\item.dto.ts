import { ApiProperty } from '@nestjs/swagger';
import { IsOptional } from 'class-validator';
import { PageRequest } from '~/@systems/utils';
import { NSItem } from '~/common/enums/NSItem';
import { ItemEntity } from '~/entities/primary';

export enum OrderField {
  code = 'code',
  name = 'name',
  createdDate = 'createdDate',
  updatedDate = 'updatedDate',
  category = 'category',
  group = 'group',
  brand = 'brand',
  type = 'type',
  status = 'status',
}

export enum OrderFieldType {
  ASC = 'ASC',
  DESC = 'DESC',
}

export class ListItemReq extends PageRequest {
  @ApiProperty({
    example: 'abc',
  })
  @IsOptional()
  name?: string;

  @ApiProperty({
    example: 'ITEM001',
  })
  @IsOptional()
  code?: string;

  @ApiProperty({
    example: 'abc',
  })
  @IsOptional()
  type?: string;

  status?: string;

  @ApiProperty({
    example: '2024-03-25T14:30:00.000Z',
  })
  @IsOptional()
  dateFrom?: Date;

  @ApiProperty({
    example: '2024-03-25T14:30:00.000Z',
  })
  @IsOptional()
  dateTo?: Date;

  @ApiProperty({
    example: true,
  })
  @IsOptional()
  isSyncScada?: boolean;

  @ApiProperty({
    example: '2024-03-25T14:30:00.000Z',
  })
  @IsOptional()
  startSyncDate?: string;

  @ApiProperty({
    example: '2024-03-25T14:30:00.000Z',
  })
  @IsOptional()
  endSyncDate?: string;

  /**
   * Trường sắp xếp
   */
  @ApiProperty({
    description: 'Trường sắp xếp',
    example: OrderField.code,
    enum: OrderField,
    default: OrderField.code,
  })
  order: OrderField;

  /**
   * Kiểu sắp xếp
   */
  @ApiProperty({
    description: 'Thứ tự sắp xếp',
    example: OrderFieldType.ASC,
    enum: OrderFieldType,
    default: OrderFieldType.ASC,
  })
  orderType: OrderFieldType;
}
